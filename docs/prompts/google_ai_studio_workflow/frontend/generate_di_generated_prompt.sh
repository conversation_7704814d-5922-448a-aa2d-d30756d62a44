#!/bin/bash

# Script to generate a comprehensive prompt file containing all di files and main files
# This will be used for AI review of the entire di and main codebase

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# Go up to the Hopen root directory (frontend -> google_ai_studio_workflow -> prompts -> docs -> Hopen)
HOPEN_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"

# Configuration
DI_DIR="$HOPEN_ROOT/hopen/lib/di"
MAIN_FILE="$HOPEN_ROOT/hopen/lib/main.dart"
MAIN_DEV_FILE="$HOPEN_ROOT/hopen/lib/main_dev.dart"
PROMPTS_DIR="$SCRIPT_DIR"
OUTPUT_FILE="di_codebase_review.txt"
TEMP_DIR="/tmp/di_scan"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Starting DI Codebase Scan...${NC}"
echo -e "${YELLOW}📁 Script directory: $SCRIPT_DIR${NC}"
echo -e "${YELLOW}📁 Hopen root: $HOPEN_ROOT${NC}"
echo -e "${YELLOW}📁 DI directory: $DI_DIR${NC}"
echo -e "${YELLOW}📁 Main file: $MAIN_FILE${NC}"
echo -e "${YELLOW}📁 Main dev file: $MAIN_DEV_FILE${NC}"

# Check if directories exist
if [[ ! -d "$DI_DIR" ]]; then
    echo -e "${RED}❌ Error: DI directory not found: $DI_DIR${NC}"
    exit 1
fi

if [[ ! -f "$MAIN_FILE" ]]; then
    echo -e "${RED}❌ Error: Main file not found: $MAIN_FILE${NC}"
    exit 1
fi

if [[ ! -f "$MAIN_DEV_FILE" ]]; then
    echo -e "${RED}❌ Error: Main dev file not found: $MAIN_DEV_FILE${NC}"
    exit 1
fi

# Create temp directory
mkdir -p "$TEMP_DIR"

# Function to process a file
process_file() {
    local file_path="$1"
    local relative_path="${file_path#$HOPEN_ROOT/hopen/lib/}"

    # Skip certain file types
    if [[ "$file_path" == *.git* ]] || [[ "$file_path" == *.DS_Store* ]] || [[ "$file_path" == *node_modules* ]]; then
        return
    fi

    # Get file extension
    local extension="${file_path##*.}"

    # Only process text files
    case "$extension" in
        dart|txt|md|yaml|yml|json|proto|sql|cql|sh|dockerfile|dockerignore|gitignore)
            echo -e "${GREEN}📄 Processing: $relative_path${NC}"

            # Create the labeled content
            echo "=== FILE: $relative_path ===" >> "$TEMP_DIR/content.txt"
            echo "File Type: $extension" >> "$TEMP_DIR/content.txt"
            echo "Full Path: $file_path" >> "$TEMP_DIR/content.txt"
            echo "Last Modified: $(stat -f "%Sm" "$file_path" 2>/dev/null || stat -c "%y" "$file_path" 2>/dev/null || echo "Unknown")" >> "$TEMP_DIR/content.txt"
            echo "File Size: $(wc -c < "$file_path" | tr -d ' ') bytes" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "--- CONTENT START ---" >> "$TEMP_DIR/content.txt"
            cat "$file_path" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "--- CONTENT END ---" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "==========================================" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            ;;
    esac
}

# Function to recursively scan directory
scan_directory() {
    local dir="$1"

    for item in "$dir"/*; do
        if [[ -d "$item" ]]; then
            # It's a directory, recurse
            scan_directory "$item"
        elif [[ -f "$item" ]]; then
            # It's a file, process it
            process_file "$item"
        fi
    done
}

# Create the output file with header
cat > "$TEMP_DIR/content.txt" << EOF
# DI CODEBASE REVIEW PROMPT
# =========================
#
# This file contains the complete content of all files in the DI directory and main files.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: $(date)
# Total files to process: $(find "$DI_DIR" -type f 2>/dev/null | wc -l)
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure
# 2. Analyze the DI patterns and main application setup
# 3. Identify patterns, dependencies, and relationships between components
# 4. Provide insights on dependency injection and initialization patterns
# 5. Note any inconsistencies, missing implementations, or areas for enhancement
# 6. Review the architecture patterns and best practices adherence
# 7. Analyze how the main files set up the application and initialize services
#
# FILE CONTENTS:
# =============

EOF

# Start scanning
echo -e "${YELLOW}📁 Scanning directory: $DI_DIR${NC}"
scan_directory "$DI_DIR"

# Process main files
echo -e "${YELLOW}📄 Processing main files${NC}"
process_file "$MAIN_FILE"
process_file "$MAIN_DEV_FILE"

# Generate summary
echo "" >> "$TEMP_DIR/content.txt"
echo "# SUMMARY" >> "$TEMP_DIR/content.txt"
echo "# =======" >> "$TEMP_DIR/content.txt"
echo "Total files processed: $(grep -c "=== FILE:" "$TEMP_DIR/content.txt")" >> "$TEMP_DIR/content.txt"
echo "File types found:" >> "$TEMP_DIR/content.txt"
grep "File Type:" "$TEMP_DIR/content.txt" | cut -d: -f2 | sort | uniq -c | sed 's/^/  /' >> "$TEMP_DIR/content.txt"

# Create the final output file
mkdir -p "$PROMPTS_DIR"
cp "$TEMP_DIR/content.txt" "$PROMPTS_DIR/$OUTPUT_FILE"

# Clean up
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ Generated di prompt file: $PROMPTS_DIR/$OUTPUT_FILE${NC}"
echo -e "${BLUE}📊 Summary:${NC}"
echo -e "  - Files processed: $(grep -c "=== FILE:" "$PROMPTS_DIR/$OUTPUT_FILE")"
echo -e "  - Output size: $(du -h "$PROMPTS_DIR/$OUTPUT_FILE" | cut -f1)"
echo -e "  - Location: $PROMPTS_DIR/$OUTPUT_FILE"

echo -e "${YELLOW}🎯 Ready for AI review! The file contains all di and main code with proper labeling.${NC}"
