#!/bin/bash

# Script to generate a comprehensive prompt file containing all backend-related logic from the Flutter app
# This will be used for AI review of the entire provider, repositories, and stateful business logic codebase

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# Go up to the Hopen root directory (frontend -> google_ai_studio_workflow -> prompts -> docs -> Hopen)
HOPEN_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"

# Configuration
PROVIDER_DIR="$HOPEN_ROOT/hopen/lib/data"
REPOSITORIES_DIR="$HOPEN_ROOT/hopen/lib/repositories"
STATEFULBL_DIR="$HOPEN_ROOT/hopen/lib/statefulbusinesslogic"
PROMPTS_DIR="$SCRIPT_DIR"
OUTPUT_FILE="backend_logic_codebase_review.txt"
TEMP_DIR="/tmp/backend_logic_scan"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Starting Backend Logic Codebase Scan...${NC}"
echo -e "${YELLOW}📁 Script directory: $SCRIPT_DIR${NC}"
echo -e "${YELLOW}📁 Hopen root: $HOPEN_ROOT${NC}"
echo -e "${YELLOW}📁 Provider directory: $PROVIDER_DIR${NC}"
echo -e "${YELLOW}📁 Repositories directory: $REPOSITORIES_DIR${NC}"
echo -e "${YELLOW}📁 Stateful Business Logic directory: $STATEFULBL_DIR${NC}"

# Check if directories exist
if [[ ! -d "$PROVIDER_DIR" ]]; then
    echo -e "${RED}❌ Error: Provider directory not found: $PROVIDER_DIR${NC}"
    exit 1
fi
if [[ ! -d "$REPOSITORIES_DIR" ]]; then
    echo -e "${RED}❌ Error: Repositories directory not found: $REPOSITORIES_DIR${NC}"
    exit 1
fi
if [[ ! -d "$STATEFULBL_DIR" ]]; then
    echo -e "${RED}❌ Error: Stateful Business Logic directory not found: $STATEFULBL_DIR${NC}"
    exit 1
fi

# Create temp directory
mkdir -p "$TEMP_DIR"

# Function to process a file
process_file() {
    local file_path="$1"
    local base_dir="$2"
    local relative_path="${file_path#$base_dir/}"

    # Skip certain file types
    if [[ "$file_path" == *.git* ]] || [[ "$file_path" == *.DS_Store* ]] || [[ "$file_path" == *node_modules* ]]; then
        return
    fi

    # Get file extension
    local extension="${file_path##*.}"

    # Only process text files
    case "$extension" in
        dart|txt|md|yaml|yml|json|proto|sql|cql|sh|dockerfile|dockerignore|gitignore)
            echo -e "${GREEN}📄 Processing: $relative_path${NC}"

            # Create the labeled content
            echo "=== FILE: $relative_path ===" >> "$TEMP_DIR/content.txt"
            echo "File Type: $extension" >> "$TEMP_DIR/content.txt"
            echo "Full Path: $file_path" >> "$TEMP_DIR/content.txt"
            echo "Last Modified: $(stat -f "%Sm" "$file_path" 2>/dev/null || stat -c "%y" "$file_path" 2>/dev/null || echo "Unknown")" >> "$TEMP_DIR/content.txt"
            echo "File Size: $(wc -c < "$file_path" | tr -d ' ') bytes" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "--- CONTENT START ---" >> "$TEMP_DIR/content.txt"
            cat "$file_path" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "--- CONTENT END ---" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "==========================================" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            ;;
    esac
}

# Function to recursively scan directory
scan_directory() {
    local dir="$1"
    local base_dir="$2"

    for item in "$dir"/*; do
        if [[ -d "$item" ]]; then
            # It's a directory, recurse
            scan_directory "$item" "$base_dir"
        elif [[ -f "$item" ]]; then
            # It's a file, process it
            process_file "$item" "$base_dir"
        fi
    done
}

# Create the output file with header
total_files=$(($(find "$PROVIDER_DIR" -type f | wc -l) + $(find "$REPOSITORIES_DIR" -type f | wc -l) + $(find "$STATEFULBL_DIR" -type f | wc -l)))

cat > "$TEMP_DIR/content.txt" << EOF
# BACKEND LOGIC (PROVIDER, REPOSITORIES, STATEFUL BUSINESS LOGIC) CODEBASE REVIEW PROMPT
# =====================================================================================
#
# This file contains the complete content of all files in the provider, repositories, and stateful business logic directories.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: $(date)
# Total files to process: $total_files
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content, focusing on data providers, repositories, services, and stateful business logic (e.g., BLoCs, Cubits, ViewModels).
# 2. Analyze the data flow, from data sources to the presentation layer.
# 3. Identify patterns, dependencies, and relationships between different components.
# 4. Provide insights on architecture, separation of concerns, error handling, and efficiency.
# 5. Note any inconsistencies, potential bugs, or areas for improvement in the backend-related logic of the Flutter application.
# 6. Review how data is fetched, cached, and managed.
# 7. Analyze the interaction between repositories and data sources, and between stateful business logic and repositories.
#
# FILE CONTENTS:
# =============

EOF

# Start scanning
echo -e "${YELLOW}📁 Scanning directory: $PROVIDER_DIR${NC}"
scan_directory "$PROVIDER_DIR" "$PROVIDER_DIR"
echo -e "${YELLOW}📁 Scanning directory: $REPOSITORIES_DIR${NC}"
scan_directory "$REPOSITORIES_DIR" "$REPOSITORIES_DIR"
echo -e "${YELLOW}📁 Scanning directory: $STATEFULBL_DIR${NC}"
scan_directory "$STATEFULBL_DIR" "$STATEFULBL_DIR"

# Generate summary
echo "" >> "$TEMP_DIR/content.txt"
echo "# SUMMARY" >> "$TEMP_DIR/content.txt"
echo "# =======" >> "$TEMP_DIR/content.txt"
echo "Total files processed: $(grep -c "=== FILE:" "$TEMP_DIR/content.txt")" >> "$TEMP_DIR/content.txt"
echo "File types found:" >> "$TEMP_DIR/content.txt"
grep "File Type:" "$TEMP_DIR/content.txt" | cut -d: -f2 | sort | uniq -c | sed 's/^/  /' >> "$TEMP_DIR/content.txt"

# Create the final output file
mkdir -p "$PROMPTS_DIR"
cp "$TEMP_DIR/content.txt" "$PROMPTS_DIR/$OUTPUT_FILE"

# Clean up
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ Generated backend logic prompt file: $PROMPTS_DIR/$OUTPUT_FILE${NC}"
echo -e "${BLUE}📊 Summary:${NC}"
echo -e "  - Files processed: $(grep -c "=== FILE:" "$PROMPTS_DIR/$OUTPUT_FILE")"
echo -e "  - Output size: $(du -h "$PROMPTS_DIR/$OUTPUT_FILE" | cut -f1)"
echo -e "  - Location: $PROMPTS_DIR/$OUTPUT_FILE"

echo -e "${YELLOW}🎯 Ready for AI review! The file contains all backend-related logic code with proper labeling.${NC}"
