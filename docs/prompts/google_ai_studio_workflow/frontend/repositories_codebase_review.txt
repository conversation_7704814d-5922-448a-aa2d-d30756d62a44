# REPOSITORIES CODEBASE REVIEW PROMPT
# ===================================
# 
# This file contains the complete content of all files in the repositories directory.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: Mon Sep 15 17:53:06 EDT 2025
# Total files to process:       49
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure
# 2. Analyze the repository pattern implementations and their purposes
# 3. Identify patterns, dependencies, and relationships between repositories
# 4. Provide insights on data access patterns, caching, and potential improvements
# 5. Note any inconsistencies, missing implementations, or areas for enhancement
# 6. Review thedata layer architecture and abstraction patterns
# 7. Analyze how repositories interact with the backend services
#
# FILE CONTENTS:
# =============

=== FILE: activity_status/activity_status_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/activity_status/activity_status_repository.dart
Last Modified: Sep 15 12:54:31 2025
File Size: 1413 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/activity_status_model.dart';

/// Repository interface for managing user's activity status settings
///
/// This repository handles the persistence and retrieval of user's activity status preferences.
/// Following the four-layer dependency rule, this interface is defined in the repository layer
/// and implemented in the data layer.
abstract class ActivityStatusRepository {
  /// Get the current activity status settings for a user
  /// Returns null if no settings are found (defaults should be applied)
  Future<Result<ActivityStatusModel?>> getActivityStatus(String userId);

  /// Save the activity status settings for a user
  /// Returns Result<void> for consistent error handling
  Future<Result<void>> saveActivityStatus(ActivityStatusModel activityStatus);

  /// Update the activity status settings for a user
  /// Returns Result with success/failure information
  Future<Result<void>> updateActivityStatus(ActivityStatusModel activityStatus);

  /// Delete the activity status settings for a user
  /// Returns Result<void> for consistent error handling
  Future<Result<void>> deleteActivityStatus(String userId);

  /// Check if activity status settings exist for a user
  /// Returns Result<bool> for consistent error handling
  Future<Result<bool>> hasActivityStatus(String userId);
}

--- CONTENT END ---

==========================================

=== FILE: analytics/analytics_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/analytics/analytics_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1244 bytes

--- CONTENT START ---
abstract class AnalyticsRepository {
  /// Identify a user in PostHog
  Future<void> identify({required String userId, Map<String, dynamic>? traits});

  /// Track an event in PostHog
  Future<void> track({
    required String eventName,
    required String userId,
    Map<String, dynamic>? properties,
  });

  /// Track a page view in PostHog
  Future<void> page({
    required String pageName,
    required String userId,
    Map<String, dynamic>? properties,
  });

  /// Track a screen view in PostHog (for mobile)
  Future<void> screen({
    required String screenName,
    required String userId,
    Map<String, dynamic>? properties,
  });

  /// Group users in PostHog
  Future<void> group({
    required String groupType,
    required String groupId,
    required String userId,
    Map<String, dynamic>? traits,
  });

  /// Alias a user ID with another ID in PostHog
  Future<void> alias({required String userId, required String previousId});

  /// Reset user data in PostHog
  Future<void> reset();

  /// Flush queued events to PostHog
  Future<void> flush();

  /// Enable/disable analytics tracking
  Future<void> setOptOut({required bool optOut});

  /// Check if analytics tracking is enabled
  Future<bool> isOptedOut();
}

--- CONTENT END ---

==========================================

=== FILE: audio_recording/audio_recording_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/audio_recording/audio_recording_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 554 bytes

--- CONTENT START ---
import 'dart:async';

/// Repository interface for audio recording functionality
abstract class AudioRecordingRepository {
  /// Check if the recorder has necessary permissions
  Future<bool> checkPermission();

  /// Initialize the recorder with given settings
  Future<void> initialize();

  /// Start recording audio
  Future<void> startRecording();

  /// Stop recording and return the file path
  Future<String?> stopRecording();

  /// Check if currently recording
  Future<bool> isRecording();

  /// Dispose resources
  Future<void> dispose();
} 
--- CONTENT END ---

==========================================

=== FILE: auth/auth_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/auth/auth_repository.dart
Last Modified: Sep 13 18:08:42 2025
File Size: 3691 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/errors.dart' show AppError;
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

abstract class AuthRepository {
  /// Login with email or username and password
  /// Returns a [UserModel] if successful, otherwise returns an [AppError]
  Future<Result<UserModel>> login({
    required String identifier,
    required String password,
  });

  /// Login with Google
  /// Returns a [UserModel] if successful, otherwise returns an [AppError]
  Future<Result<UserModel>> loginWithGoogle();

  /// Login with Apple
  /// Returns a [UserModel] if successful, otherwise returns an [AppError]
  Future<Result<UserModel>> loginWithApple();

  /// Sign up with email, password, username, firstName and lastName
  /// Returns a [UserModel] if successful, otherwise returns an [AppError]
  Future<Result<UserModel>> signUp({
    required String email,
    required String password,
    required String username,
    required String firstName,
    required String lastName,
    DateTime? birthday,
    String? profilePictureUrl,
    bool notificationsEnabled = false,
  });

  /// Get current logged in user
  /// Returns a [UserModel] if logged in, otherwise returns an [AppError]
  Future<Result<UserModel>> getCurrentUser();

  /// Log out the current user
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> logout();

  /// Update onboarding completion status
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> updateOnboardingStatus({required bool hasCompletedOnboarding});

  /// Initiate password reset process
  /// Sends a password reset email with a secure token to the user's registered email
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> initiatePasswordReset(String email);

  /// Verify password reset code/token
  /// Validates the reset token provided by the user
  /// Returns true if valid, otherwise returns an [AppError]
  Future<Result<bool>> verifyResetCode(String email, String code);

  /// Reset password using a valid reset token
  /// Updates the user's password if the token is valid
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> resetPassword({
    required String token,
    required String newPassword,
    required String email,
  });

  /// Get current authenticated user (synchronous)
  UserModel? get authenticatedUser;

  /// Check if user is authenticated
  bool get isAuthenticated;

  /// Stream of authentication state changes
  Stream<AuthState> get authStateStream;

  /// Resend verification email to user
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> resendVerificationEmail([String? email]);

  /// Verify email using verification token
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> verifyEmail(String token);

  /// Update user password
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> updatePassword({
    required String currentPassword,
    required String newPassword,
  });

  /// Sign out user
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> signOut();

  /// Delete user account permanently
  /// Returns true if successful, otherwise returns an [AppError]
  Future<Result<bool>> deleteAccount({
    required String password,
    required String confirmationText,
    String? reason,
  });
}

/// Authentication state for the stream
enum AuthState {
  authenticated,
  unauthenticated,
  loading,
}

--- CONTENT END ---

==========================================

=== FILE: availability/availability_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/availability/availability_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 966 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/models/availability_result.dart';

/// Repository interface for checking username and email availability
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class AvailabilityRepository {
  /// Check if an email address is available for registration
  /// Returns an AvailabilityResult with availability status and message
  Future<AvailabilityResult> checkEmailAvailability(String email);

  /// Check if a username is available for registration
  /// Returns an AvailabilityResult with availability status and message
  Future<AvailabilityResult> checkUsernameAvailability(String username);

  /// Check both email and username availability in a single call
  /// Returns a map with 'email' and 'username' keys containing AvailabilityResult
  Future<Map<String, AvailabilityResult>> checkBothAvailability({
    required String email,
    required String username,
  });
}

--- CONTENT END ---

==========================================

=== FILE: background_processing/background_processing_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/background_processing/background_processing_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 839 bytes

--- CONTENT START ---
/// Repository interface for background processing operations
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class BackgroundProcessingRepository {
  /// Process pending requests in the background
  Future<void> processPendingRequests();

  /// Schedule a background task
  Future<void> scheduleBackgroundTask(String taskId, Map<String, dynamic> data);

  /// Cancel a scheduled background task
  Future<void> cancelBackgroundTask(String taskId);

  /// Get the status of a background task
  Future<String?> getTaskStatus(String taskId);

  /// Check if background processing is available
  Future<bool> isBackgroundProcessingAvailable();

  /// Initialize background processing
  Future<void> initialize();

  /// Cleanup background processing resources
  Future<void> cleanup();
}

--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_invite_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_invite_request_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2826 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';

/// Invite request status
enum InviteRequestStatus { pending, accepted, declined, expired }

/// Invite request model
class BubbleInviteRequest {
  const BubbleInviteRequest({
    required this.id,
    required this.bubbleId,
    required this.bubbleName,
    required this.inviterId,
    required this.inviterName,
    required this.inviteeId,
    required this.inviteeName,
    required this.invitedAt,
    required this.status,
    this.message,
    this.respondedAt,
    this.expiresAt,
  });
  final String id;
  final String bubbleId;
  final String bubbleName;
  final String inviterId;
  final String inviterName;
  final String inviteeId;
  final String inviteeName;
  final DateTime invitedAt;
  final InviteRequestStatus status;
  final String? message;
  final DateTime? respondedAt;
  final DateTime? expiresAt;
}

/// Repository interface for managing bubble invite requests
abstract class BubbleInviteRequestRepository {
  /// Send an invite to a user for a bubble
  Future<Result<BubbleInviteRequest>> sendInvite(
    String bubbleId,
    String userId,
    String message,
  );

  /// Get pending invites received by a user
  Future<Result<List<BubbleInviteRequest>>> getPendingInvites(String userId);

  /// Get invites sent by a user
  Future<Result<List<BubbleInviteRequest>>> getSentInvites(String userId);

  /// Get all invites for a bubble
  Future<Result<List<BubbleInviteRequest>>> getBubbleInvites(String bubbleId);

  /// Accept an invite
  Future<Result<void>> acceptInvite(String inviteId);

  /// Decline an invite
  Future<Result<void>> declineInvite(String inviteId);

  /// Cancel an invite (by the inviter)
  Future<Result<void>> cancelInvite(String inviteId);

  /// Get invite by ID
  Future<Result<BubbleInviteRequest>> getInvite(String inviteId);

  /// Check if user has pending invite for bubble
  Future<Result<bool>> hasPendingInvite(String bubbleId, String userId);

  /// Resend an invite
  Future<Result<BubbleInviteRequest>> resendInvite(String inviteId);

  /// Get invite history for a bubble
  Future<Result<List<BubbleInviteRequest>>> getInviteHistory(String bubbleId);

  /// Auto-expire old invites
  Future<Result<void>> expireOldInvites();

  /// Get invite statistics for a bubble
  Future<Result<Map<String, int>>> getInviteStatistics(String bubbleId);

  /// Accept an invite request
  Future<Result<void>> acceptInviteRequest(String requestId);

  /// Decline an invite request
  Future<Result<void>> declineInviteRequest(String requestId);

  /// Get bubble details for an invite request
  Future<Result<BubbleInviteRequest>> getBubbleDetails(String bubbleId);

  /// Send an invite request
  Future<Result<void>> sendInviteRequest(
    String bubbleId,
    String inviteeId,
    String? message,
  );
}

--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_join_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_join_request_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2375 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';

/// Join request status
enum JoinRequestStatus { pending, approved, rejected, expired }

/// Join request model
class BubbleJoinRequest {
  const BubbleJoinRequest({
    required this.id,
    required this.bubbleId,
    required this.userId,
    required this.userName,
    required this.requestedAt,
    required this.status,
    this.userAvatarUrl,
    this.message,
    this.respondedAt,
    this.respondedBy,
  });
  final String id;
  final String bubbleId;
  final String userId;
  final String userName;
  final String? userAvatarUrl;
  final DateTime requestedAt;
  final JoinRequestStatus status;
  final String? message;
  final DateTime? respondedAt;
  final String? respondedBy;
}

/// Repository interface for managing bubble join requests
abstract class BubbleJoinRequestRepository {
  /// Send a join request to a bubble
  Future<Result<BubbleJoinRequest>> sendJoinRequest(
    String bubbleId,
    String message,
  );

  /// Get pending join requests for a bubble (for bubble members to review)
  Future<Result<List<BubbleJoinRequest>>> getPendingJoinRequests(
    String bubbleId,
  );

  /// Get join requests sent by a user
  Future<Result<List<BubbleJoinRequest>>> getUserJoinRequests(String userId);

  /// Approve a join request
  Future<Result<void>> approveJoinRequest(String requestId);

  /// Reject a join request
  Future<Result<void>> rejectJoinRequest(String requestId, String reason);

  /// Cancel a join request (by the requester)
  Future<Result<void>> cancelJoinRequest(String requestId);

  /// Get join request by ID
  Future<Result<BubbleJoinRequest>> getJoinRequest(String requestId);

  /// Check if user has pending join request for bubble
  Future<Result<bool>> hasPendingJoinRequest(String bubbleId, String userId);

  /// Get join request history for a bubble
  Future<Result<List<BubbleJoinRequest>>> getJoinRequestHistory(
    String bubbleId,
  );

  /// Auto-expire old join requests
  Future<Result<void>> expireOldJoinRequests();

  /// Get bubble details for a join request
  Future<Result<BubbleJoinRequest>> getBubbleDetails(String bubbleId);

  /// Accept a join request
  Future<Result<void>> acceptJoinRequest(String bubbleId, String requestId);

  /// Decline a join request
  Future<Result<void>> declineJoinRequest(String bubbleId, String requestId);
}

--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_kickout_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_kickout_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1474 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/bubble_kickout_record.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

/// Repository interface for managing bubble kickout operations
abstract class BubbleKickoutRepository {
  /// Kickout a member from a bubble
  Future<Result<void>> kickoutMember({
    required String bubbleId,
    required String memberId,
    String? reason,
  });

  /// Get kickout history for a bubble
  Future<Result<List<BubbleKickoutRecord>>> getKickoutHistory(String bubbleId);

  /// Get kickout history for a user
  Future<Result<List<BubbleKickoutRecord>>> getUserKickoutHistory(String userId);

  /// Check if a user is kicked out from a bubble
  Future<Result<bool>> isUserKickedOut({
    required String bubbleId,
    required String userId,
  });

  /// Get list of users kicked out from a bubble
  Future<Result<List<UserModel>>> getKickedOutUsers(String bubbleId);

  /// Allow a kicked out user to rejoin the bubble
  Future<Result<void>> allowUserToRejoin({
    required String bubbleId,
    required String userId,
  });

  /// Get aggregated kickout statistics for a bubble
  ///
  /// Uses optimized backend GetBubbleStatistics endpoint to retrieve
  /// comprehensive statistics including member counts, pending votes,
  /// vote summaries, and engagement metrics.
  Future<Result<Map<String, dynamic>>> getKickoutStatistics(String bubbleId);
}

--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_propose_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_propose_request_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 686 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';

/// Repository for handling bubble propose requests.
abstract class BubbleProposeRequestRepository {
  /// Accepts a propose request for a new member to join a bubble.
  ///
  /// Returns true if this is the last member to accept the request,
  /// which means the proposed member will be added to the bubble.
  Future<Result<bool>> acceptProposeRequest({
    required String requestId,
    required String proposedMemberId,
  });

  /// Declines a propose request for a new member to join a bubble.
  Future<Result<void>> declineProposeRequest({
    required String requestId,
    required String proposedMemberId,
  });
}

--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_repository.dart
Last Modified: Sep  7 17:28:35 2025
File Size: 6254 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../statefulbusinesslogic/core/models/bubble_member.dart';
import '../../statefulbusinesslogic/core/models/bubble_request_model.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/models/value_objects.dart';

/// Unified repository interface for all bubble-related operations.
/// This consolidates previous duplicate methods into a single, clean interface.
abstract class BubbleRepository {
  // --- Bubble Core CRUD ---
  Future<Result<BubbleEntity>> createBubble({
    required BubbleName name,
    required String description,
    String? locationName,
    double? locationLat,
    double? locationLng,
    int? locationRadius,
    String? customImageUrl,
    String? colorTheme,
    bool? allowInvites,
    bool? requireApproval,
  });

  Future<Result<BubbleEntity>> getBubble(BubbleId bubbleId);

  Future<Result<BubbleEntity?>> getBubbleById(String bubbleId);

  Future<Result<BubbleEntity>> getBubbleDetailsById(BubbleId bubbleId);

  /// Get the current active bubble for the user
  Future<Result<BubbleEntity?>> getCurrentBubble();

  Future<Result<void>> updateBubble(
    BubbleId bubbleId,
    Map<String, dynamic> updates,
  );

  Future<Result<void>> deleteBubble(BubbleId bubbleId);

  // --- Bubble Membership ---
  Future<Result<BubbleEntity>> joinBubble({
    required BubbleId bubbleId,
    UserId? userId,
  });

  Future<Result<void>> leaveBubble({required BubbleId bubbleId, UserId? userId});

  Future<Result<List<UserModel>>> getBubbleMembers(BubbleId bubbleId);

  /// Get bubble members with role information (owner, admin, etc.)
  Future<Result<List<BubbleMember>>> getBubbleMembersWithRoles(BubbleId bubbleId);

  Future<Result<List<BubbleEntity>>> getUserBubbles(UserId userId);

  Future<Result<List<BubbleEntity>>> getBubbles(UserId userId);

  // getNearbyBubbles removed - Backend does not support location-based discovery

  Future<Result<BubbleEntity>> updateMemberStatus({
    required BubbleId bubbleId,
    required UserId memberId,
    bool? isOnline,
    int? unreadMessageCount,
  });

  // --- Bubble Actions & Voting ---
  Future<Result<void>> inviteToBubble({
    required BubbleId bubbleId,
    required UserId inviterId,
    required List<UserId> inviteeIds,
  });

  Future<Result<BubbleEntity>> voteToRemoveMember({
    required BubbleId bubbleId,
    required UserId voterId,
    required UserId targetMemberId,
  });

  Future<Result<void>> kickMemberFromBubble({
    required BubbleId bubbleId,
    required UserId memberId,
  });

  Future<Result<BubbleEntity>> updateBubbleInfo({
    required BubbleId bubbleId,
    BubbleName? name,
    DateTime? endDate,
  });

  Future<Result<void>> markAllMessagesRead({
    required BubbleId bubbleId,
    required UserId memberId,
  });

  Future<Result<void>> proposeMember({
    required BubbleId bubbleId,
    required String memberName,
    required String memberEmail,
  });

  // startCall and endCall removed - Handled by CallRepository

  // --- Unified Bubble Requests ---
  Future<Result<BubbleRequestModel>> createRequest({
    required String bubbleId,
    required String targetId,
    required String type, // 'join', 'invite', 'start', 'kickout'
    String? message,
  });

  Future<Result<BubbleRequestModel>> respondToRequest({
    required String requestId,
    required String status, // 'accepted', 'declined'
  });

  Future<Result<List<BubbleRequestModel>>> getPendingRequests();

  // --- Friend selection after bubble ends ---
  Future<void> selectFriends({
    required String bubbleId,
    required List<String> selectedUserIds,
  });

  // --- Bubble Request Methods ---

  /// Send a bubble start request to a contact
  Future<Result<void>> sendBubbleStartRequest({
    required String recipientId,
    required String bubbleName,
    String? message,
  });

  /// Send a bubble invite request
  Future<Result<void>> sendBubbleInviteRequest({
    required String bubbleId,
    required String inviteeId,
    String? message,
  });

  /// Send a bubble join request
  Future<Result<void>> sendBubbleJoinRequest({
    required String bubbleId,
    String? message,
  });

  Future<Result<void>> acceptBubbleRequest({
    required String requestId,
    required String bubbleId,
    required String userId,
  });

  Future<Result<void>> declineBubbleRequest({
    required String requestId,
    required String bubbleId,
    required String userId,
  });

  /// Get bubble request by ID
  Future<Result<BubbleRequestModel?>> getBubbleRequestById(String requestId);

  // --- Bubble Kickout Request Methods ---
  Future<Result<void>> acceptBubbleKickoutRequest({
    required String requestId,
    required String bubbleId,
    required String targetMemberId,
  });

  Future<Result<void>> declineBubbleKickoutRequest({
    required String requestId,
    required String bubbleId,
    required String targetMemberId,
  });

  // --- Voting Methods for Unanimous Approval ---
  /// Vote on a join request (requires unanimous approval from all members)
  Future<Result<void>> voteOnJoinRequest({
    required String requestId,
    required bool approve,
    String? reason,
  });

  /// Vote on an invite request (requires unanimous approval from all members)
  Future<Result<void>> voteOnInviteRequest({
    required String requestId,
    required bool approve,
    String? reason,
  });

  /// Vote on a kickout request
  Future<Result<void>> voteOnKickoutRequest({
    required String bubbleId,
    required String targetUserId,
    required bool approve,
    String? reason,
  });
}

/// Statistics about a bubble for analytics
class BubbleStatistics {
  const BubbleStatistics({
    required this.totalMessages,
    required this.totalMembers,
    required this.activeMembers,
    required this.averageSessionDuration,
    required this.lastActivity,
    required this.totalCalls,
    required this.totalCallTime,
  });
  final int totalMessages;
  final int totalMembers;
  final int activeMembers;
  final Duration averageSessionDuration;
  final DateTime lastActivity;
  final int totalCalls;
  final Duration totalCallTime;
}

--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_start_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_start_request_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 534 bytes

--- CONTENT START ---
abstract class BubbleStartRequestRepository {
  /// Accepts a start request for a bubble
  /// Returns true if this was the last member needed to accept the request
  Future<bool> acceptStartRequest({
    required String bubbleId,
    required String requesterId,
  });

  /// Declines a start request for a bubble
  Future<void> declineStartRequest({
    required String bubbleId,
    required String requesterId,
  });

  /// Creates a new start request for a bubble
  Future<void> createStartRequest({required String bubbleId});
}

--- CONTENT END ---

==========================================

=== FILE: bubble_history/bubble_history_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble_history/bubble_history_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 3313 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../statefulbusinesslogic/core/models/bubble_history_events.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

/// Sorting options for bubble history
enum BubbleSortBy { dateCreated, dateUpdated, name, memberCount, activity }

/// Repository interface for managing bubble history operations
abstract class BubbleHistoryRepository {
  /// Get complete bubble history for a user
  Future<Result<List<BubbleEntity>>> getBubbleHistory(String userId);

  /// Get active bubbles for a user
  Future<Result<List<BubbleEntity>>> getActiveBubbles(String userId);

  /// Get completed/expired bubbles for a user
  Future<Result<List<BubbleEntity>>> getCompletedBubbles(String userId);

  /// Get members for a specific bubble
  Future<Result<List<UserModel>>> getBubbleMembers(String bubbleId);

  /// Archive a bubble (move to archived status)
  Future<Result<void>> archiveBubble(String bubbleId);

  /// Unarchive a bubble
  Future<Result<void>> unarchiveBubble(String bubbleId);

  /// Add a bubble to favorites
  Future<Result<void>> favoriteBubble(String bubbleId);

  /// Remove a bubble from favorites
  Future<Result<void>> unfavoriteBubble(String bubbleId);

  /// Get favorite bubbles for a user
  Future<Result<List<BubbleEntity>>> getFavoriteBubbles(String userId);

  /// Search bubble history by name or description
  Future<Result<List<BubbleEntity>>> searchBubbleHistory(
    String userId,
    String query,
  );

  /// Filter bubble history by date range
  Future<Result<List<BubbleEntity>>> filterBubblesByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  );

  /// Sort bubble history
  Future<Result<List<BubbleEntity>>> sortBubbleHistory(
    String userId,
    BubbleSortBy sortBy, {
    bool ascending = true,
  });

  /// Get bubble statistics for analytics
  Future<Result<Map<String, dynamic>>> getBubbleStatistics(String userId);

  /// Clear bubble history cache
  Future<Result<void>> clearCache();

  /// Join a bubble
  Future<Result<void>> joinBubble(String bubbleId);

  /// Leave a bubble
  Future<Result<void>> leaveBubble(String bubbleId);

  /// Vote to remove a member from a bubble
  Future<Result<void>> voteToRemoveMember(String bubbleId, String memberId);

  /// Get user bubble history (alias for getBubbleHistory for compatibility)
  Future<Result<List<BubbleEntity>>> getUserBubbleHistory(String userId) =>
      getBubbleHistory(userId);

  Future<Result<BubbleEntity>> getBubbleById(String bubbleId);
  Future<Result<void>> deleteBubbleFromHistory(String bubbleId, String userId);
  Future<Result<void>> clearBubbleHistory(String userId);
  Future<Result<List<BubbleEntity>>> getRecentBubbles(
    String userId, {
    int limit = 10,
  });

  /// Get member history events for a specific bubble
  /// Returns a list of member join/leave/kick/role change events
  Future<Result<List<MemberHistoryEvent>>> getBubbleMemberHistory(
    String bubbleId,
  );

  /// Get activity history events for a specific bubble
  /// Returns a list of all activities (messages, calls, status changes, etc.)
  Future<Result<List<ActivityEvent>>> getBubbleActivityHistory(
    String bubbleId,
  );
}

--- CONTENT END ---

==========================================

=== FILE: call/call_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/call/call_repository.dart
Last Modified: Sep 15 09:13:37 2025
File Size: 6602 bytes

--- CONTENT START ---
import 'dart:async';

import 'package:flutter_webrtc/flutter_webrtc.dart';

import '../../generated/grpc/call.pb.dart' as call_proto;
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/call_management_models.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';


// Call Repository Events - moved from data layer to repository layer
class RemoteStreamAdded {
  RemoteStreamAdded({
    required this.callId,
    required this.participantId,
    required this.stream,
  });
  final String callId;
  final String participantId;
  final MediaStream stream;
}

class IceConnectionStateChanged {
  IceConnectionStateChanged({
    required this.callId,
    required this.participantId,
    required this.state,
  });
  final String callId;
  final String participantId;
  final RTCIceConnectionState state;
}

class CallErrorOccurred {
  CallErrorOccurred({
    required this.callId,
    required this.error,
    required this.stackTrace,
  });
  final String callId;
  final dynamic error;
  final StackTrace stackTrace;
}

class CallOutcomeOccurred {
  CallOutcomeOccurred({required this.callId, required this.outcome});
  final String callId;
  final String outcome;
}

class JoinCallAcknowledged {
  JoinCallAcknowledged({required this.callId, required this.participants});
  final String callId;
  final List<UserModel> participants;
}

class JoinCallFailed {
  JoinCallFailed({required this.callId, required this.reason});
  final String callId;
  final String reason;
}

class IncomingOfferOccurred {
  IncomingOfferOccurred({
    required this.callId,
    required this.callerId,
    required this.callerName,
    required this.isGroup,
    required this.isVideoOffered,
    required this.isAudioOffered,
    required this.isScreenShareOffered,
    required this.remoteOfferSdp,
    this.callerAvatar,
    this.groupId,
    this.groupName,
    this.groupAvatarUrl,
  });
  final String callId;
  final String callerId;
  final String callerName;
  final String? callerAvatar;
  final bool isGroup;
  final bool isVideoOffered;
  final bool isAudioOffered;
  final bool isScreenShareOffered;
  final String? groupId;
  final String? groupName;
  final String? groupAvatarUrl;
  final String remoteOfferSdp;
}

class CallAccepted {
  CallAccepted({
    required this.callId,
    required this.participantId,
    required this.localStream,
  });
  final String callId;
  final String participantId;
  final MediaStream? localStream;
}

// Abstract Call Repository Interface
abstract class CallRepository {
  // Basic call operations
  Future<Map<String, dynamic>> initiateCall({
    required String targetId,
    required bool isGroup,
    required bool withVideo,
    required bool withScreenShare,
  });

  Future<void> acceptCall({
    required String callId,
    required bool withVideo,
    required Map<String, dynamic> remoteOfferSdp,
    required String callerId,
    required bool isGroupCall,
    String? groupId,
  });

  Future<void> rejectCall({required String callId, String? targetUserId});
  Future<void> endCall({required String callId, String? targetUserId});
  Future<Map<String, dynamic>> joinCall({
    required String bubbleId,
    required String callId,
    required bool withVideo,
    required bool withScreenShare,
  });

  // Media controls
  Future<void> setMuted(String callId, {required bool muted});
  Future<void> setVideoEnabled(String callId, {required bool enabled});
  Future<void> switchCamera(String callId);
  Future<void> startScreenShare(String callId);
  Future<void> stopScreenShare(String callId);

  // Stream management
  Stream<MediaStream?> getLocalStream(String callId);
  Stream<Map<String, MediaStream>> getRemoteStreams(String callId);

  // Call state
  Future<bool> isCallActive(String callId);
  Future<List<UserModel>> getCallParticipants(String callId);

  // Call information
  Future<Result<call_proto.CallInfo>> getCallInfo(String callId);

  // Participant management
  Future<void> addParticipant(String callId, String participantId);
  Future<void> removeParticipant(String callId, String participantId);
  Future<void> updateParticipantMuteStatus({
    required String callId,
    required String participantId,
    required bool isMuted,
  });

  // Advanced Group Call Management

  // Call layout management
  Future<Result<void>> setCallLayout(String callId, CallLayoutType layoutType, Map<String, String> settings);
  Future<Result<CallLayoutInfo>> getCallLayout(String callId);

  // Active speaker management
  Future<Result<void>> setActiveSpeaker(String callId, String activeSpeakerId);
  Future<Result<ActiveSpeakerInfo>> getActiveSpeaker(String callId);

  // Quality profile management
  Future<Result<void>> setCallQualityProfile(String callId, CallQualityProfileInfo qualityProfile);
  Future<Result<CallQualityProfileInfo>> getCallQualityProfile(String callId);

  // Participant quality management
  Future<Result<void>> updateParticipantQuality(String callId, String participantId, ParticipantQualitySettings qualitySettings);

  // Call state validation for proactive UI synchronization
  Future<CallStateValidationResult> validateCallState(String bubbleId, String userId);

  /// Leave a call (different from ending - for participants leaving group calls)
  Future<void> leaveCall({required String callId});

  /// Update call client state for backend monitoring
  Future<void> updateCallClientState({
    required String callId,
    required bool isBackground,
    required DateTime timestamp,
  });

  /// Get cached user for display purposes
  UserModel? getCachedUser(String userId);
}

// Extended Call Repository Interface with event streams
abstract class CallRepositoryExtended extends CallRepository {
  // Event streams for real-time updates
  Stream<RemoteStreamAdded> get onRemoteStreamAdded;
  Stream<IceConnectionStateChanged> get onIceConnectionStateChanged;
  Stream<CallErrorOccurred> get onCallErrorOccurred;
  Stream<CallOutcomeOccurred> get onCallOutcomeOccurred;
  Stream<JoinCallAcknowledged> get onJoinCallAcknowledged;
  Stream<JoinCallFailed> get onJoinCallFailed;
  Stream<IncomingOfferOccurred> get onIncomingOfferOccurred;
  Stream<CallAccepted> get onCallAccepted;

  // Cleanup method
  void dispose();
}

/// Result of call state validation for proactive UI synchronization
class CallStateValidationResult {
  const CallStateValidationResult({
    required this.hasActiveCall,
    this.callInfo,
    this.error,
  });

  final bool hasActiveCall;
  final call_proto.CallInfo? callInfo;
  final String? error;
}

--- CONTENT END ---

==========================================

=== FILE: chat/chat_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/chat/chat_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 3812 bytes

--- CONTENT START ---
import 'dart:io';

import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/chat_message.dart';

abstract class ChatRepository {
  /// Get information about a chat
  ///
  /// Uses optimized backend GetChatInfo endpoint to retrieve comprehensive
  /// chat metadata including member count, name, avatar, and unread status.
  ///
  /// [chatType] - Type of chat: 'bubble' or 'conversation'
  Future<Result<Map<String, dynamic>>> getChatInfo(String chatId, {
    String chatType = 'bubble',
  });

  /// Get messages for a chat
  Future<Result<List<ChatMessage>>> getMessages(
    String chatId, {
    int limit = 50,
    String? before,
  });

  /// Get a specific message by ID
  Future<Result<ChatMessage?>> getMessageById(String messageId);

  /// Send a text message to a chat
  Future<Result<ChatMessage>> sendMessage(
    String chatId,
    String content,
    String senderId,
  );

  /// Send a media message to a chat
  Future<Result<ChatMessage>> sendMediaMessage({
    required String chatId,
    required String senderId,
    required File mediaFile,
    required String mediaType,
    String? caption,
    String? replyToMessageId,
  });

  /// Update a message's status (delivered, read, failed)
  /// CRITICAL: Now requires createdAt timestamp for backend optimization
  Future<Result<void>> markMessageAsRead({
    required String messageId,
    required String userId,
    required DateTime createdAt,
    String? conversationId,
  });

  /// Delete a message
  /// CRITICAL: Now requires createdAt timestamp for backend optimization
  Future<Result<void>> deleteMessage({
    required String messageId,
    required String userId,
    required DateTime createdAt,
    String? bubbleId,
  });

  /// Search for messages in a chat
  Future<Result<List<ChatMessage>>> searchMessages(String chatId, String query);

  /// Connect to real-time chat services for a specific chat
  Future<void> connectToChat(String chatId);

  /// Disconnect from real-time chat services for a specific chat
  Future<void> disconnectFromChat(String chatId);

  /// Stream of new messages for a specific chat
  Stream<ChatMessage> messageStream(String chatId);

  /// Stream of typing status updates for a specific chat
  Stream<Map<String, dynamic>> typingStatusStream(String chatId);

  /// Stream of other chat-related events/status updates for a specific chat
  Stream<Map<String, dynamic>> chatEventsStream(String chatId);

  /// Send typing status for the current user
  Future<void> sendTypingStatus(String chatId, String userId, {required bool isTyping});

  /// Mark all messages as read
  Future<Result<void>> markAllMessagesAsRead(String chatId, String userId);

  /// Edit a message
  /// CRITICAL: Now requires createdAt timestamp for backend optimization
  Future<Result<ChatMessage>> editMessage({
    required String messageId,
    required String newContent,
    required String userId,
    required DateTime createdAt,
    String? bubbleId,
  });

  /// Get unread count
  Future<Result<int>> getUnreadCount(String chatId, String userId);

  /// Stream of unread count
  Stream<int> unreadCountStream(String chatId, String userId);

  /// Upload media file and return URL
  Future<Result<String>> uploadMedia(File mediaFile, String mediaType);

  /// Start voice recording
  Future<Result<bool>> startVoiceRecording();

  /// Stop voice recording and return the recorded file
  Future<Result<File>> stopVoiceRecording();

  /// Add a reaction to a message
  Future<Result<void>> addReaction(String messageId, String reactionType, String userId);

  /// Remove a reaction from a message
  Future<Result<void>> removeReaction(String messageId, String reactionType, String userId);

  /// Dispose resources and close streams
  Future<void> dispose();
}

--- CONTENT END ---

==========================================

=== FILE: contacts/contact_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/contacts/contact_request_repository.dart
Last Modified: Sep  5 13:59:15 2025
File Size: 7689 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

/// Repository interface for managing contact requests
/// 
/// This repository handles all contact request operations including
/// sending, accepting, declining, and managing contact relationships.
abstract class ContactRequestRepository {
  /// Send a contact request to another user
  Future<Result<void>> sendContactRequest({
    required String fromUserId,
    required String toUserId,
    String? message,
  });
  
  /// Accept a contact request
  Future<Result<void>> acceptContactRequest({
    required String requestId,
    required String fromUserId,
    required String toUserId,
  });
  
  /// Decline a contact request
  Future<Result<void>> declineContactRequest({
    required String requestId,
    required String fromUserId,
    required String toUserId,
    String? reason,
  });
  
  /// Get pending contact requests sent by the current user
  Future<Result<List<ContactRequest>>> getSentContactRequests();
  
  /// Get pending contact requests received by the current user
  Future<Result<List<ContactRequest>>> getReceivedContactRequests();
  
  /// Get all contact requests (sent and received)
  Future<Result<List<ContactRequest>>> getAllContactRequests();
  
  /// Get a specific contact request by ID
  Future<Result<ContactRequest?>> getContactRequest(String requestId);
  
  /// Cancel a sent contact request
  Future<Result<void>> cancelContactRequest(String requestId);
  
  /// Get contact request history with pagination
  Future<Result<List<ContactRequest>>> getContactRequestHistory({
    int page = 0,
    int pageSize = 20,
    String direction = 'both', // 'sent', 'received', 'both'
    List<String> statuses = const ['pending', 'accepted', 'declined', 'cancelled'],
  });
  
  /// Check if there's a pending contact request between two users
  Future<Result<ContactRequest?>> getPendingRequestBetweenUsers({
    required String userId1,
    required String userId2,
  });
  
  /// Get contact request statistics
  Future<Result<ContactRequestStats>> getContactRequestStats();
  
  /// Clear old/expired contact requests
  Future<Result<void>> clearExpiredRequests();
  
  /// Block a user (prevents future contact requests)
  Future<Result<void>> blockUser(String userId);
  
  /// Unblock a user
  Future<Result<void>> unblockUser(String userId);
  
  /// Get list of blocked users
  Future<Result<List<UserModel>>> getBlockedUsers();
  
  /// Check if a user is blocked
  Future<Result<bool>> isUserBlocked(String userId);
}

/// Model representing a contact request
class ContactRequest {
  const ContactRequest({
    required this.id,
    required this.fromUserId,
    required this.toUserId,
    required this.status,
    required this.createdAt,
    this.message,
    this.respondedAt,
    this.expiresAt,
    this.fromUser,
    this.toUser,
  });

  /// Create from JSON
  factory ContactRequest.fromJson(Map<String, dynamic> json) => ContactRequest(
      id: json['id'] as String,
      fromUserId: json['fromUserId'] as String,
      toUserId: json['toUserId'] as String,
      status: ContactRequestStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ContactRequestStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      message: json['message'] as String?,
      respondedAt: json['respondedAt'] != null
          ? DateTime.parse(json['respondedAt'] as String)
          : null,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      fromUser: json['fromUser'] != null
          ? UserModel.fromJson(json['fromUser'] as Map<String, dynamic>)
          : null,
      toUser: json['toUser'] != null
          ? UserModel.fromJson(json['toUser'] as Map<String, dynamic>)
          : null,
    );

  final String id;
  final String fromUserId;
  final String toUserId;
  final ContactRequestStatus status;
  final DateTime createdAt;
  final String? message;
  final DateTime? respondedAt;
  final DateTime? expiresAt;
  final UserModel? fromUser;
  final UserModel? toUser;



  /// Convert to JSON
  Map<String, dynamic> toJson() => {
      'id': id,
      'fromUserId': fromUserId,
      'toUserId': toUserId,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'message': message,
      'respondedAt': respondedAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'fromUser': fromUser?.toJson(),
      'toUser': toUser?.toJson(),
    };

  /// Create a copy with updated fields
  ContactRequest copyWith({
    String? id,
    String? fromUserId,
    String? toUserId,
    ContactRequestStatus? status,
    DateTime? createdAt,
    String? message,
    DateTime? respondedAt,
    DateTime? expiresAt,
    UserModel? fromUser,
    UserModel? toUser,
  }) => ContactRequest(
      id: id ?? this.id,
      fromUserId: fromUserId ?? this.fromUserId,
      toUserId: toUserId ?? this.toUserId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      message: message ?? this.message,
      respondedAt: respondedAt ?? this.respondedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      fromUser: fromUser ?? this.fromUser,
      toUser: toUser ?? this.toUser,
    );

  /// Check if the request is expired
  bool get isExpired {
    if (expiresAt == null) {
      return false;
    }
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if the request is pending
  bool get isPending => status == ContactRequestStatus.pending && !isExpired;

  @override
  String toString() => 'ContactRequest(id: $id, fromUserId: $fromUserId, toUserId: $toUserId, status: $status, createdAt: $createdAt)';
}

/// Status of a contact request
enum ContactRequestStatus {
  pending,
  accepted,
  declined,
  cancelled,
  expired,
}

/// Statistics for contact requests
class ContactRequestStats {
  const ContactRequestStats({
    required this.totalSent,
    required this.totalReceived,
    required this.pendingSent,
    required this.pendingReceived,
    required this.acceptedSent,
    required this.acceptedReceived,
    required this.declinedSent,
    required this.declinedReceived,
  });

  /// Create from JSON
  factory ContactRequestStats.fromJson(Map<String, dynamic> json) => ContactRequestStats(
      totalSent: json['totalSent'] as int,
      totalReceived: json['totalReceived'] as int,
      pendingSent: json['pendingSent'] as int,
      pendingReceived: json['pendingReceived'] as int,
      acceptedSent: json['acceptedSent'] as int,
      acceptedReceived: json['acceptedReceived'] as int,
      declinedSent: json['declinedSent'] as int,
      declinedReceived: json['declinedReceived'] as int,
    );

  final int totalSent;
  final int totalReceived;
  final int pendingSent;
  final int pendingReceived;
  final int acceptedSent;
  final int acceptedReceived;
  final int declinedSent;
  final int declinedReceived;

  /// Total pending requests
  int get totalPending => pendingSent + pendingReceived;

  /// Total accepted requests
  int get totalAccepted => acceptedSent + acceptedReceived;

  /// Total declined requests
  int get totalDeclined => declinedSent + declinedReceived;



  /// Convert to JSON
  Map<String, dynamic> toJson() => {
      'totalSent': totalSent,
      'totalReceived': totalReceived,
      'pendingSent': pendingSent,
      'pendingReceived': pendingReceived,
      'acceptedSent': acceptedSent,
      'acceptedReceived': acceptedReceived,
      'declinedSent': declinedSent,
      'declinedReceived': declinedReceived,
    };
}

--- CONTENT END ---

==========================================

=== FILE: contacts/contacts_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/contacts/contacts_repository.dart
Last Modified: Sep  4 20:47:09 2025
File Size: 7348 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../statefulbusinesslogic/core/models/contact_request.dart';
import '../../statefulbusinesslogic/core/models/relationship_type.dart';
import '../../statefulbusinesslogic/core/models/user_contact.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

/// Cache-first repository interface for managing contacts and contact requests
///
/// This repository consolidates all contact-related functionality including:
/// - Contact management (cache-first)
/// - Contact requests and invitations
/// - Contact search and filtering
/// - Real-time contact status updates
///
/// This repository prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.
abstract class ContactsRepository {
  // ============================================================================
  // Core Contact Operations
  // ============================================================================

  /// Get all contacts for the current user
  Future<Result<List<UserContact>>> getContacts();

  /// Add a contact
  Future<Result<void>> addContact(
    String userId, {
    RelationshipType relationshipType = RelationshipType.contact,
  });

  /// Remove a contact
  Future<Result<void>> removeContact(String contactId);

  /// Block a contact
  Future<Result<void>> blockContact(String contactId);

  /// Update contact relationship
  Future<Result<void>> updateContactRelationship({
    required String contactId,
    required RelationshipType relationshipType,
  });

  /// Check if a user is already a contact of the current user
  Future<Result<bool>> isContact(String userId);

  // ============================================================================
  // Contact Request Operations
  // ============================================================================

  /// Send a contact request to another user
  Future<Result<ContactRequestModel?>> sendContactRequest({
    required String recipientId,
    String? message,
  });

  /// Get pending contact requests received by current user (from cache)
  Future<Result<List<ContactRequestModel>>> getPendingReceivedRequests();

  /// Get pending contact requests sent by current user (from cache)
  Future<Result<List<ContactRequestModel>>> getPendingSentRequests();

  /// Accept a contact request
  Future<Result<void>> acceptContactRequest(String requestId);

  /// Decline a contact request
  Future<Result<void>> declineContactRequest(String requestId);

  /// Cancel a contact request (by sender)
  Future<Result<void>> cancelContactRequest(String requestId);

  /// Get contact request by ID (from cache first)
  Future<Result<ContactRequestModel?>> getContactRequest(String requestId);

  /// Get relationship status with another user
  Future<Result<RelationshipType>> getRelationshipStatus(String userId);

  /// Get received contact requests (for use cases)
  Future<Result<List<UserModel>>> getReceivedContactRequests();

  /// Get sent contact requests (for use cases)
  Future<Result<List<UserModel>>> getSentContactRequests();

  /// Check if there's a pending request between two users (from cache)
  Future<Result<bool>> hasPendingRequest(String userId1, String userId2);

  /// Get contact request history (from cache)
  Future<Result<List<ContactRequestModel>>> getContactRequestHistory();

  // ============================================================================
  // Search and Discovery
  // ============================================================================

  /// Search users (including non-contacts) by query (first name, last name, username)
  Future<Result<List<UserContact>>> searchUsers(String query);

  /// Search contacts only by query (first name, last name, username)
  Future<Result<List<UserContact>>> searchContacts(String query);

  /// Search for users to send contact requests (always fresh)
  Future<Result<List<UserModel>>> searchUsersForContactRequests(String query);

  /// Get mutual contacts between current user and another user
  Future<Result<List<UserModel>>> getMutualContacts(String userId);

  /// Get suggested contacts based on mutual friends
  Future<Result<List<UserModel>>> getSuggestedContacts();

  // ============================================================================
  // Bubble Requests
  // ============================================================================

  /// Send a bubble start request to a contact (When the viewing user the target user are not in a bubble)
  Future<Result<void>> sendBubbleStartRequest({
    required String contactId,
    String? message,
  });

  /// Send a bubble invite request to a contact (When the viewing user is in a bubble with less than five members and the target user is not in a bubble)
  Future<Result<void>> sendBubbleInviteRequest({
    required String bubbleId,
    required String contactId,
    String? message,
  });

  /// Send a bubble join request for a contact (When the viewing user is not in a bubble and the target user is in a bubble with less than five members)
  Future<Result<void>> sendBubbleJoinRequest({
    required String bubbleId,
    String? message,
  });

  // ============================================================================
  // Real-time Updates
  // ============================================================================

  /// Stream of pending received requests (real-time updates)
  Stream<List<ContactRequestModel>> get pendingReceivedRequestsStream;

  /// Stream of pending sent requests (real-time updates)
  Stream<List<ContactRequestModel>> get pendingSentRequestsStream;

  /// Stream of contacts updates (real-time updates)
  Stream<List<UserContact>> get contactsStream;

  // ============================================================================
  // Utility Operations
  // ============================================================================

  /// Auto-expire old contact requests
  Future<Result<void>> expireOldRequests();

  /// Set the current user ID for the repository
  /// This is typically called after authentication to initialize the repository
  void setCurrentUserId(String userId);

  /// Refresh data from remote source
  Future<void> refreshFromRemote();

  /// Dispose of resources and close streams
  void dispose();

  // ============================================================================
  // Cache Management Methods
  // ============================================================================

  /// Initialize the repository cache
  Future<void> initialize();

  /// Clear all cached data
  Future<void> clearCache();

  /// Check if repository has cached data
  Future<bool> get hasCachedData;

  /// Get the timestamp of the last remote refresh
  DateTime? get lastRemoteRefresh;

  // ============================================================================
  // Real-time Event Handling
  // ============================================================================

  /// Handle MQTT events for real-time updates
  Future<void> handleMqttEvent(MqttEvent event);

  /// Stream of MQTT events
  Stream<MqttEvent> get eventStream;

  /// Process real-time events
  Future<void> processRealTimeEvent(MqttEvent event);
}

--- CONTENT END ---

==========================================

=== FILE: content_report/content_report_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/content_report/content_report_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 3651 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/content_report_models.dart';

/// Repository interface for content reporting functionality
abstract class ContentReportRepository {
  /// Report content (messages, bubbles, media, etc.)
  /// 
  /// [reporterId] - ID of the user reporting the content
  /// [contentId] - ID of the content being reported
  /// [contentType] - Type of content (message, bubble, media, etc.)
  /// [category] - Category of the report (spam, harassment, etc.)
  /// [reason] - Reason for the report
  /// [description] - Optional detailed description
  /// [evidenceUrls] - Optional list of evidence URLs (screenshots, etc.)
  /// [metadata] - Optional additional metadata
  /// 
  /// Returns [Result<String>] with the report ID on success
  Future<Result<String>> reportContent({
    required String reporterId,
    required String contentId,
    required ContentType contentType,
    required ReportCategory category,
    required String reason,
    String? description,
    List<String>? evidenceUrls,
    Map<String, String>? metadata,
  });

  /// Get a specific content report by ID
  /// 
  /// [reportId] - ID of the report to retrieve
  /// 
  /// Returns [Result<ContentReportModel>] with the report details
  Future<Result<ContentReportModel>> getContentReport(String reportId);

  /// List content reports with filtering and pagination
  /// 
  /// [page] - Page number (1-based)
  /// [pageSize] - Number of reports per page
  /// [status] - Optional filter by report status
  /// [contentType] - Optional filter by content type
  /// [category] - Optional filter by report category
  /// [reporterId] - Optional filter by reporter ID
  /// [fromDate] - Optional filter by creation date (from)
  /// [toDate] - Optional filter by creation date (to)
  /// 
  /// Returns [Result<ContentReportListResponse>] with paginated results
  Future<Result<ContentReportListResponse>> listContentReports({
    required int page,
    required int pageSize,
    ReportStatus? status,
    ContentType? contentType,
    ReportCategory? category,
    String? reporterId,
    DateTime? fromDate,
    DateTime? toDate,
  });

  /// Update content report status (admin only)
  /// 
  /// [reportId] - ID of the report to update
  /// [adminId] - ID of the admin updating the status
  /// [status] - New status for the report
  /// [resolutionNotes] - Optional resolution notes
  /// 
  /// Returns [Result<void>] indicating success or failure
  Future<Result<void>> updateContentReportStatus({
    required String reportId,
    required String adminId,
    required ReportStatus status,
    String? resolutionNotes,
  });

  /// Delete content report (admin only)
  /// 
  /// [reportId] - ID of the report to delete
  /// [adminId] - ID of the admin deleting the report
  /// 
  /// Returns [Result<void>] indicating success or failure
  Future<Result<void>> deleteContentReport({
    required String reportId,
    required String adminId,
  });
}

/// Response model for listing content reports
class ContentReportListResponse {
  const ContentReportListResponse({
    required this.reports,
    required this.totalCount,
    required this.page,
    required this.pageSize,
  });

  final List<ContentReportModel> reports;
  final int totalCount;
  final int page;
  final int pageSize;

  /// Check if there are more pages available
  bool get hasNextPage => page * pageSize < totalCount;

  /// Check if there is a previous page
  bool get hasPreviousPage => page > 1;

  /// Get the total number of pages
  int get totalPages => (totalCount / pageSize).ceil();
}

--- CONTENT END ---

==========================================

=== FILE: dialog_state/dialog_state_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/dialog_state/dialog_state_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1858 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/models/pending_dialog_model.dart';

/// Repository interface for dialog state management
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class DialogStateRepository {
  /// Save a pending dialog to persistent storage
  Future<void> savePendingDialog(PendingDialog dialog);

  /// Load all pending dialogs from persistent storage
  Future<List<PendingDialog>> loadPendingDialogs();

  /// Remove a dialog from persistent storage
  Future<void> removePendingDialog(String dialogId);

  /// Clear all pending dialogs
  Future<void> clearAllPendingDialogs();

  /// Check if a dialog with the given ID exists
  Future<bool> hasDialog(String dialogId);

  /// Update an existing dialog
  Future<void> updateDialog(PendingDialog dialog);

  /// Get dialogs by type
  Future<List<PendingDialog>> getDialogsByType(String type);

  /// Get dialogs by priority
  Future<List<PendingDialog>> getDialogsByPriority(int minPriority);

  /// Mark a dialog as shown
  Future<void> markDialogShown(String requestId, String requestType);

  /// Check if a dialog has been shown
  Future<bool> hasDialogBeenShown(String requestId, String requestType);

  /// Increment retry count for a dialog
  Future<void> incrementDialogRetry(String requestId, String requestType);

  /// Add a pending dialog with full data
  Future<void> addPendingDialog(
    String requestId,
    String requestType,
    Map<String, dynamic> requestData, {
    DialogPriority priority = DialogPriority.normal,
    DateTime? expiresAt,
  });

  /// Get dialog metrics
  Future<Map<String, dynamic>> getDialogMetrics();

  /// Clear all dialog state
  Future<void> clearAllDialogState();

  /// Remove expired dialogs based on optional 'expiresAt' in dialog metadata
  Future<void> cleanupExpiredDialogs();
}

--- CONTENT END ---

==========================================

=== FILE: friendship/friend_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/friendship/friend_request_repository.dart
Last Modified: Sep  3 15:39:59 2025
File Size: 3913 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';

/// Cache-first repository interface for managing auto-generated friend requests from bubble expiry
///
/// This repository handles the specific functionality of friend requests that are
/// automatically generated when bubbles expire, which is distinct from general friendship management.
///
/// This repository prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.
abstract class FriendRequestRepository {
  // ============================================================================
  // Auto-Generated Friend Request Operations
  // ============================================================================

  /// Get pending friend requests for the current user (from cache)
  Future<Result<List<Map<String, dynamic>>>> getPendingFriendRequests();

  /// Accept a friend request by ID
  Future<Result<void>> acceptFriendRequest(String requestId);

  /// Decline a friend request by ID
  Future<Result<void>> declineFriendRequest(String requestId);

  /// Get a specific friend request by ID (from cache first)
  Future<Result<Map<String, dynamic>>> getFriendRequest(String requestId);

  /// Get list of current friends from auto-generated requests (from cache)
  Future<Result<List<String>>> getFriends();

  /// Get friend request statistics
  Future<Result<Map<String, dynamic>>> getRequestStats();

  /// Clear old/rejected friend requests
  Future<Result<void>> clearOldRequests();

  /// Get friend request history with pagination and filtering
  ///
  /// Returns complete history including pending, accepted, declined, and expired requests.
  /// Uses optimized backend GetFriendRequestHistory endpoint.
  ///
  /// [direction] - Filter by direction: 'sent', 'received', or 'both'
  /// [statuses] - Filter by status: ['pending', 'accepted', 'declined', 'expired']
  Future<Result<List<Map<String, dynamic>>>> getRequestHistory({
    int page = 0,
    int pageSize = 20,
    String direction = 'both',
    List<String> statuses = const ['pending', 'accepted', 'declined', 'expired'],
  });

  // ============================================================================
  // Real-time Updates
  // ============================================================================

  /// Stream of pending friend requests (real-time updates)
  Stream<List<Map<String, dynamic>>> get pendingFriendRequestsStream;

  /// Stream of friend request statistics
  Stream<Map<String, dynamic>> get requestStatsStream;

  // ============================================================================
  // Event Handling
  // ============================================================================

  /// Handle friend request MQTT events
  Future<void> handleMqttEvent(MqttEvent event);

  /// Process friend request events and update cache
  Future<void> processFriendRequestEvent(MqttEvent event);

  // ============================================================================
  // Cache Management Methods
  // ============================================================================

  /// Initialize the repository cache
  Future<void> initialize();

  /// Clear all cached data
  Future<void> clearCache();

  /// Check if repository has cached data
  Future<bool> get hasCachedData;

  /// Get the timestamp of the last remote refresh
  DateTime? get lastRemoteRefresh;

  /// Refresh data from remote source
  Future<void> refreshFromRemote();

  // ============================================================================
  // Real-time Event Handling
  // ============================================================================

  /// Stream of MQTT events
  Stream<MqttEvent> get eventStream;

  /// Process real-time events
  Future<void> processRealTimeEvent(MqttEvent event);
}

--- CONTENT END ---

==========================================

=== FILE: friendship/friendship_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/friendship/friendship_repository.dart
Last Modified: Sep  9 16:07:17 2025
File Size: 7136 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

/// Cache-first repository interface for managing friend relationships and operations
///
/// This repository consolidates all friend-related functionality including:
/// - Friend list management (cache-first)
/// - Friend requests (auto-generated from bubble expiry)
/// - Friend search and filtering
/// - Real-time friend status updates
///
/// This repository prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.
abstract class FriendshipRepository {
  // ============================================================================
  // Core Friend List Operations
  // ============================================================================

  /// Get current user's friend list (from cache first)
  Future<Result<List<UserModel>>> getFriendList();

  /// Get a specific friend by ID (from cache first)
  Future<Result<UserModel>> getFriend(String friendId);

  /// Get all users who are friends with the current user (alias for getFriendList)
  Future<Result<List<UserModel>>> getFriends();

  /// Add a new friend to the list
  Future<Result<void>> addFriend(String friendId);

  /// Add a maybe friend (system-generated from bubble expiry)
  /// Note: This method should not be used for manual maybe friend creation.
  /// Maybe friends are automatically created by the system when bubbles expire.
  Future<Result<void>> addMaybeFriend(String userId);

  /// Remove a friend from the list
  Future<Result<void>> removeFriend(String friendId);

  /// Check if a user is in the friend list
  Future<Result<bool>> isFriend(String userId);

  /// Check if a user is a friend of the current user (alias for isFriend)
  Future<Result<bool>> isUserFriend(String userId);

  // ============================================================================
  // Friend Counts and Statistics
  // ============================================================================

  /// Get friend count
  Future<Result<int>> getFriendCount();

  /// Get online friends count
  Future<Result<int>> getOnlineFriendsCount();

  // ============================================================================
  // Friend Search and Filtering
  // ============================================================================

  /// Search friends by name or username
  Future<Result<List<UserModel>>> searchFriends(String query);

  /// Get friends with pagination
  Future<Result<List<UserModel>>> getFriendsPaginated({
    required int page,
    required int pageSize,
  });

  /// Get friends who are currently online
  Future<Result<List<UserModel>>> getOnlineFriends();

  /// Get friends who are currently in a bubble
  Future<Result<List<UserModel>>> getFriendsInBubble();

  /// Get mutual friends between current user and another user
  Future<Result<List<UserModel>>> getMutualFriends(String userId);

  /// Get recently active friends (sorted by last activity)
  Future<Result<List<UserModel>>> getRecentlyActiveFriends({
    required int limit,
  });

  // DEPRECATED: getFriendsByActivityLevel removed
  // Use getFriends with appropriate filters instead

  /// Get friends by status
  Future<Result<List<UserModel>>> getFriendsByStatus(String status);

  // ============================================================================
  // Real-time Updates and Synchronization
  // ============================================================================

  /// Sync friend list from remote to local cache
  Future<Result<void>> syncFriendList();

  /// Refresh friends data from the server
  Future<Result<void>> refreshFriends();

  /// Stream of friend list updates (real-time updates)
  Stream<List<UserModel>> get friendListStream;

  /// Stream of online friends count
  Stream<int> get onlineFriendsCountStream;

  // ============================================================================
  // Event Handling
  // ============================================================================

  /// Handle friendship MQTT events
  Future<void> handleMqttEvent(MqttEvent event);

  /// Process friendship events and update cache
  Future<void> processFriendshipEvent(MqttEvent event);

  // ============================================================================
  // Friend Discovery and Suggestions (from former BubbleFriendSelectionRepository)
  // ============================================================================

  /// Get former members of a bubble for friend selection
  ///
  /// Uses optimized backend GetFormerBubbleMembers endpoint with relationship context.
  /// Returns users who were previously in the bubble with their current friendship status.
  ///
  /// [since] - Optional filter to only get members who left after this time
  Future<Result<List<UserModel>>> getFormerMembers(String bubbleId, String userId, {
    int page = 0,
    int pageSize = 20,
    DateTime? since,
  });

  // ============================================================================
  // Friend Request Operations (Auto-generated from bubble expiry)
  // ============================================================================

  /// Accept a friend request (auto-generated from bubble expiry)
  Future<Result<void>> acceptFriendRequest(String requestId);

  /// Decline a friend request (converts to contact relationship)
  Future<Result<void>> declineFriendRequest(String requestId);

  /// Get received friend requests (auto-generated from bubble expiry)
  Future<Result<List<UserModel>>> getReceivedFriendRequests();

  /// Get maybe friends (users from expired bubbles who haven't decided)
  Future<Result<List<UserModel>>> getMaybeFriends();

  /// Accept friend request relationship (between users who are already maybe_friends)
  Future<Result<void>> acceptFriendRequestRelationship(String currentUserId, String otherUserId);

  /// Decline friend request relationship (converts to contact)
  Future<Result<void>> declineFriendRequestRelationship(
    String currentUserId,
    String otherUserId,
    String reason,
  );

  // ============================================================================
  // Cache Management Methods
  // ============================================================================

  /// Initialize the repository cache
  Future<void> initialize();

  /// Clear all cached data
  Future<void> clearCache();

  /// Check if repository has cached data
  Future<bool> get hasCachedData;

  /// Get the timestamp of the last remote refresh
  DateTime? get lastRemoteRefresh;

  /// Refresh data from remote source
  Future<void> refreshFromRemote();

  // ============================================================================
  // Real-time Event Handling
  // ============================================================================

  /// Stream of MQTT events
  Stream<MqttEvent> get eventStream;

  /// Process real-time events
  Future<void> processRealTimeEvent(MqttEvent event);
}
--- CONTENT END ---

==========================================

=== FILE: image_processing/image_processing_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/image_processing/image_processing_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1764 bytes

--- CONTENT START ---
import 'dart:typed_data';

import 'package:image_picker/image_picker.dart';

import '../../statefulbusinesslogic/core/models/image_processing_result.dart';

/// Repository interface for image processing operations
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class ImageProcessingRepository {
  /// Pick an image from the gallery with validation
  Future<XFile?> pickFromGallery();

  /// Take a photo with the camera with validation
  Future<XFile?> takePhoto();

  /// Process an image file with compression and format conversion
  Future<Uint8List> processImageFromFile(XFile imageFile);

  /// Process image bytes with compression and format conversion
  Future<Uint8List> processImageFromBytes(Uint8List imageBytes);

  /// Validate an image file before processing
  Future<void> validateImageFile(XFile imageFile);

  /// Get image information without processing
  Future<ImageInfo> getImageInfo(String imagePath);

  /// Process image in isolate for heavy operations
  Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  });
}

/// Image information class
class ImageInfo {

  const ImageInfo({
    required this.width,
    required this.height,
    required this.fileSizeBytes,
    required this.format,
  });
  final int width;
  final int height;
  final int fileSizeBytes;
  final String format;

  double get fileSizeMB => fileSizeBytes / (1024 * 1024);

  bool get isSquare => width == height;

  bool get isValidDimensions =>
      width >= 640 &&
      height >= 640 &&
      width <= 8192 &&
      height <= 8192;
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_chat_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_chat_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 779 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/models/chat_message.dart'
    as stateful_models; // For the statefulbusinesslogic model

abstract class AbstractChatMessageRepository {
  Future<void> saveMessage(stateful_models.ChatMessage message, String chatId);
  Future<List<stateful_models.ChatMessage>> getMessages(String chatId);
  Stream<List<stateful_models.ChatMessage>> watchMessages(String chatId);
  Future<void> deleteMessage(String messageId, String chatId);
  Future<void> deleteAllMessagesForChat(String chatId);
  Future<void> saveMessages(String chatId, List<stateful_models.ChatMessage> messages);
  Future<List<stateful_models.ChatMessage>> searchMessages(String chatId, String query);
  // Potentially other methods like updateMessageStatus, getMessageById etc.
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_chat_metadata_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_chat_metadata_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 243 bytes

--- CONTENT START ---
abstract class AbstractChatMetadataRepository {
  Future<void> saveChatMetadata(String chatId, Map<String, dynamic> metadata);
  Future<Map<String, dynamic>?> getChatMetadata(String chatId);
  Future<void> deleteChatMetadata(String chatId);
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_draft_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_draft_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 216 bytes

--- CONTENT START ---
abstract class AbstractDraftMessageRepository {
  Future<void> saveDraftMessage(String chatId, String draftText);
  Future<String?> getDraftMessage(String chatId);
  Future<void> deleteDraftMessage(String chatId);
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_fcm_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_fcm_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 649 bytes

--- CONTENT START ---
// Domain model for an FCM message (can be simpler than the full RemoteMessage)
class DomainFcmMessage {
  DomainFcmMessage({
    required this.data,
    required this.timestamp,
    this.id,
    this.notificationTitle,
    this.notificationBody,
  });
  final String? id; // Auto-generated by Drift or original FCM ID
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? notificationTitle;
  final String? notificationBody;
}

abstract class AbstractFcmMessageRepository {
  Future<void> saveFcmMessage(DomainFcmMessage message);
  Future<List<DomainFcmMessage>> getFcmMessages();
  Future<void> deleteAllFcmMessages();
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_profile_picture_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_profile_picture_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1224 bytes

--- CONTENT START ---
/// Abstract repository interface for profile picture caching operations
///
/// This interface defines the contract for profile picture caching operations
/// following the Repository pattern in Clean Architecture.
///
/// NOTE: This is different from ProfilePictureRepository which handles upload/processing.
/// This repository specifically handles local caching and cache management.
///
/// The implementation should be in the Data layer, not directly accessible
/// from the Presentation layer.
abstract class AbstractProfilePictureCacheRepository {
  /// Get the local path for a user's profile picture
  /// Returns the cached local path if available, otherwise downloads and caches
  Future<String?> getProfilePicturePath(String userId, String imageUrl);
  
  /// Clear old cache entries based on age
  Future<void> clearOldCache(Duration maxAge);
  
  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats();
  
  /// Clear all cache entries
  Future<void> clearAllCache();
  
  /// Update the last accessed time for a profile picture
  Future<void> updateLastAccessed(String userId);
  
  /// Check if a profile picture is cached for a user
  Future<bool> isProfilePictureCached(String userId);
} 
--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_unread_count_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_unread_count_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 372 bytes

--- CONTENT START ---
abstract class AbstractUnreadCountRepository {
  Future<void> saveUnreadCount(String chatId, int count);
  Future<int> getUnreadCount(String chatId);
  Future<void> incrementUnreadCount(String chatId);
  Future<void> clearUnreadCount(String chatId);
  // Potentially a stream to watch unread counts for a chat or all chats
  Stream<int> watchUnreadCount(String chatId);
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_chat_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_chat_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 5622 bytes

--- CONTENT START ---
import 'package:drift/drift.dart';

import '../../statefulbusinesslogic/core/db/app_database.dart';
import '../../statefulbusinesslogic/core/models/chat_message.dart';
import '../../statefulbusinesslogic/core/models/media_type.dart';
import '../../statefulbusinesslogic/core/models/message_delivery_status.dart';
import 'abstract_chat_message_repository.dart';

class DriftChatMessageRepository extends AbstractChatMessageRepository {
  DriftChatMessageRepository(this._database);
  final AppDatabase _database;

  MessageType _mapMediaTypeToMessageType(MediaType mediaType) {
    switch (mediaType) {
      case MediaType.image:
        return MessageType.image;
      case MediaType.video:
        return MessageType.video;
      case MediaType.audio:
        return MessageType.audio;
      case MediaType.text:
      default:
        return MessageType.text;
    }
  }

  MessageStatus _mapDeliveryStatusToMessageStatus(MessageDeliveryStatus status) {
    switch (status) {
      case MessageDeliveryStatus.sending:
        return MessageStatus.sending;
      case MessageDeliveryStatus.sent:
        return MessageStatus.sent;
      case MessageDeliveryStatus.delivered:
        return MessageStatus.delivered;
      case MessageDeliveryStatus.read:
        return MessageStatus.read;
      case MessageDeliveryStatus.failed:
        return MessageStatus.failed;
    }
  }

  MessageDeliveryStatus _mapMessageStatusToDeliveryStatus(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return MessageDeliveryStatus.sending;
      case MessageStatus.sent:
        return MessageDeliveryStatus.sent;
      case MessageStatus.delivered:
        return MessageDeliveryStatus.delivered;
      case MessageStatus.read:
        return MessageDeliveryStatus.read;
      case MessageStatus.failed:
        return MessageDeliveryStatus.failed;
    }
  }

  ChatMessage _toDomain(DriftChatMessage driftMessage) => ChatMessage(
      id: driftMessage.id,
      content: driftMessage.textContent,
      senderId: driftMessage.senderId,
      timestamp: driftMessage.timestamp, // CRITICAL: This is the createdAt timestamp
      bubbleId: driftMessage.bubbleId,
      type: _mapMediaTypeToMessageType(driftMessage.mediaType),
      status: _mapDeliveryStatusToMessageStatus(driftMessage.deliveryStatus),
      mediaType: driftMessage.mediaType,
      mediaUrl: driftMessage.mediaUrl,
      replyToMessageId: driftMessage.replyToMessageId,
      isEdited: driftMessage.isEdited,
      isDeleted: driftMessage.isDeleted,
    );

  DriftChatMessage _fromDomain(
    ChatMessage domainMessage,
    String chatId,
  ) => DriftChatMessage(
    id: domainMessage.id,
    chatId: chatId,
    textContent: domainMessage.content,
    senderId: domainMessage.senderId,
    timestamp: domainMessage.timestamp, // CRITICAL: This is the createdAt timestamp
    bubbleId: domainMessage.bubbleId,
    conversationId: domainMessage.bubbleId, // Use bubbleId as conversationId
    mediaType: domainMessage.mediaType ?? MediaType.text,
    mediaUrl: domainMessage.mediaUrl,
    replyToMessageId: domainMessage.replyToMessageId,
    isEdited: domainMessage.isEdited,
    isDeleted: domainMessage.isDeleted,
    updatedAt: domainMessage.isEdited ? DateTime.now() : null,
    deliveryStatus: _mapMessageStatusToDeliveryStatus(domainMessage.status),
    isForwarded: false, // Default to not forwarded
  );



  @override
  Future<void> saveMessage(
    ChatMessage message,
    String chatId,
  ) async {
    final driftMessage = _fromDomain(message, chatId);
    await _database.into(_database.chatMessages).insert(driftMessage);
  }

  @override
  Future<List<ChatMessage>> getMessages(String chatId) async {
    final query =
        _database.select(_database.chatMessages)
          ..where((tbl) => tbl.chatId.equals(chatId))
          ..orderBy([(tbl) => OrderingTerm.asc(tbl.timestamp)]);

    final driftMessages = await query.get();
    return driftMessages.map(_toDomain).toList();
  }

  @override
  Stream<List<ChatMessage>> watchMessages(String chatId) {
    final query =
        _database.select(_database.chatMessages)
          ..where((tbl) => tbl.chatId.equals(chatId))
          ..orderBy([(tbl) => OrderingTerm.asc(tbl.timestamp)]);

    return query.watch().map(
      (driftMessages) => driftMessages.map(_toDomain).toList(),
    );
  }

  @override
  Future<void> deleteMessage(String messageId, String chatId) async {
    await (_database.delete(_database.chatMessages)
      ..where((tbl) => tbl.id.equals(messageId))).go();
  }

  @override
  Future<void> deleteAllMessagesForChat(String chatId) async {
    await (_database.delete(_database.chatMessages)
      ..where((tbl) => tbl.chatId.equals(chatId))).go();
  }

  @override
  Future<void> saveMessages(String chatId, List<ChatMessage> messages) async {
    if (messages.isEmpty) {
      return;
    }
    final driftMessages = messages.map((m) => _fromDomain(m, chatId)).toList();
    // Use batch insert with conflict replace by id
    await _database.batch((batch) {
      batch.insertAllOnConflictUpdate(_database.chatMessages, driftMessages);
    });
  }

  @override
  Future<List<ChatMessage>> searchMessages(String chatId, String query) async {
    final likeQuery = '%${query.toLowerCase()}%';
    final table = _database.chatMessages;
    final driftMessages = await (_database.select(table)
          ..where((tbl) => tbl.chatId.equals(chatId) & tbl.textContent.lower().like(likeQuery))
          ..orderBy([(tbl) => OrderingTerm.desc(tbl.timestamp)]))
        .get();
    return driftMessages.map(_toDomain).toList();
  }
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_chat_metadata_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_chat_metadata_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1186 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/db/app_database.dart';
import 'abstract_chat_metadata_repository.dart';

class DriftChatMetadataRepository implements AbstractChatMetadataRepository {
  DriftChatMetadataRepository({required ChatMetadataDao chatMetadataDao})
    : _chatMetadataDao = chatMetadataDao;
  final ChatMetadataDao _chatMetadataDao;

  @override
  Future<void> saveChatMetadata(
    String chatId,
    Map<String, dynamic> metadata,
  ) async {
    // The JsonMapConverter is applied at the table definition level in app_database.dart
    // So, the `metadata` field in DriftChatMetadata expects Map<String, dynamic> directly.
    final driftMetadata = DriftChatMetadata(chatId: chatId, metadata: metadata);
    await _chatMetadataDao.insertOrUpdateChatMetadata(driftMetadata);
  }

  @override
  Future<Map<String, dynamic>?> getChatMetadata(String chatId) async {
    final driftMetadata = await _chatMetadataDao.getChatMetadata(chatId);
    return driftMetadata?.metadata;
  }

  @override
  Future<void> deleteChatMetadata(String chatId) async {
    // Delete chat metadata using the DAO's delete method
    await _chatMetadataDao.deleteChatMetadata(chatId);
  }
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_draft_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_draft_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1032 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/db/app_database.dart';
import 'abstract_draft_message_repository.dart';

class DriftDraftMessageRepository implements AbstractDraftMessageRepository {
  DriftDraftMessageRepository({required DraftMessageDao draftMessageDao})
    : _draftMessageDao = draftMessageDao;
  final DraftMessageDao _draftMessageDao;

  @override
  Future<void> saveDraftMessage(String chatId, String draftText) async {
    if (draftText.isEmpty) {
      // Consistent with Hive behavior: delete if empty
      await _draftMessageDao.deleteDraftMessage(chatId);
    } else {
      await _draftMessageDao.insertOrUpdateDraftMessage(
        DriftDraftMessage(chatId: chatId, draftText: draftText),
      );
    }
  }

  @override
  Future<String?> getDraftMessage(String chatId) async {
    final draft = await _draftMessageDao.getDraftMessage(chatId);
    return draft?.draftText;
  }

  @override
  Future<void> deleteDraftMessage(String chatId) async {
    await _draftMessageDao.deleteDraftMessage(chatId);
  }
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_fcm_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_fcm_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2501 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/db/app_database.dart';
import 'abstract_fcm_message_repository.dart';

class DriftFcmMessageRepository implements AbstractFcmMessageRepository {
  DriftFcmMessageRepository({required FcmMessageDao fcmMessageDao})
    : _fcmMessageDao = fcmMessageDao;
  final FcmMessageDao _fcmMessageDao;

  // --- Mappers ---
  DomainFcmMessage _toDomain(DriftFcmMessage driftMessage) => DomainFcmMessage(
    // Assuming Drift's auto-incremented `id` is IntColumn, so convert to String if DomainFcmMessage.id is String.
    // If DriftFcmMessage.id is already the FCM messageId (TextColumn nullable), then no need to map `messageId` separately.
    // Let's assume Drift `id` is the auto-incremented one for this mapping.
    id:
        driftMessage.id
            .toString(), // Or use `driftMessage.messageId` if that's preferred for Domain
    data:
        driftMessage
            .data, // Already Map<String, dynamic> due to JsonMapConverter
    timestamp: driftMessage.receivedTimestamp,
    notificationTitle: driftMessage.notificationTitle,
    notificationBody: driftMessage.notificationBody,
  );

  // Note: The Drift FcmMessages table has `id` (auto-increment int) and `messageId` (original FCM ID, nullable string).
  // The DomainFcmMessage only has one `id` field. We need to decide how to map this.
  // For saving, we'll likely get the original FCM ID in DomainFcmMessage.id.
  DriftFcmMessage _fromDomain(
    DomainFcmMessage domainMessage,
  ) => DriftFcmMessage(
    // `id` in DriftFcmMessage is auto-incrementing, so we set it with Value.absent()
    id: 0, // This will be ignored since id is autoIncrement=true
    messageId:
        domainMessage
            .id, // This assumes DomainFcmMessage.id holds the original FCM Message ID
    data: domainMessage.data,
    receivedTimestamp: domainMessage.timestamp,
    notificationTitle: domainMessage.notificationTitle,
    notificationBody: domainMessage.notificationBody,
  );
  // --- End Mappers ---

  @override
  Future<void> saveFcmMessage(DomainFcmMessage message) async {
    final driftMessage = _fromDomain(message);
    await _fcmMessageDao.insertFcmMessage(driftMessage);
  }

  @override
  Future<List<DomainFcmMessage>> getFcmMessages() async {
    final driftMessages = await _fcmMessageDao.getAllFcmMessages();
    return driftMessages.map(_toDomain).toList();
  }

  @override
  Future<void> deleteAllFcmMessages() async {
    await _fcmMessageDao.deleteAllFcmMessages();
  }
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_unread_count_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_unread_count_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1428 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/db/app_database.dart';
import 'abstract_unread_count_repository.dart';

class DriftUnreadCountRepository implements AbstractUnreadCountRepository {
  DriftUnreadCountRepository({required UnreadCountDao unreadCountDao})
    : _unreadCountDao = unreadCountDao;
  final UnreadCountDao _unreadCountDao;

  @override
  Future<void> saveUnreadCount(String chatId, int count) async {
    await _unreadCountDao.insertOrUpdateUnreadCount(
      DriftUnreadCount(chatId: chatId, count: count),
    );
  }

  @override
  Future<int> getUnreadCount(String chatId) async {
    final unread = await _unreadCountDao.getUnreadCount(chatId);
    return unread?.count ?? 0;
  }

  @override
  Future<void> incrementUnreadCount(String chatId) async {
    final currentCount = await getUnreadCount(chatId);
    await saveUnreadCount(chatId, currentCount + 1);
  }

  @override
  Future<void> clearUnreadCount(String chatId) async {
    await saveUnreadCount(chatId, 0);
  }

  @override
  Stream<int> watchUnreadCount(String chatId) =>
      // The DAO's getUnreadCount returns Future<DriftUnreadCount?>.
      // To make this a stream, the DAO method itself would need to return a Stream,
      // e.g., using .watchSingleOrNull().
      // Let's add such a method to UnreadCountDao.
      _unreadCountDao
          .watchUnreadCountStream(chatId)
          .map((driftCount) => driftCount?.count ?? 0);
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/local_storage_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/local_storage_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 545 bytes

--- CONTENT START ---
abstract class LocalStorageRepository {
  Future<void> saveString(String key, String value);
  Future<String?> getString(String key);
  Future<void> saveBool(String key, {required bool value});
  Future<bool?> getBool(String key);
  Future<void> saveInt(String key, int value);
  Future<int?> getInt(String key);
  Future<void> saveDouble(String key, double value);
  Future<double?> getDouble(String key);
  Future<void> remove(String key);
  Future<void> clear();
  Future<bool> containsKey(String key);
  Future<List<String>> getAllKeys();
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/secure_storage_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/secure_storage_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2352 bytes

--- CONTENT START ---
/// Repository interface for secure storage operations
/// 
/// This repository handles the secure storage and retrieval of sensitive data
/// such as authentication tokens, encryption keys, and other security-critical information.
/// 
/// Following the four-layer dependency rule, this interface is defined in the repository layer
/// and implemented in the data layer.
abstract class SecureStorageRepository {
  /// Store a string value securely
  Future<void> saveString(String key, String value);

  /// Retrieve a string value securely
  Future<String?> getString(String key);

  /// Store a boolean value securely
  Future<void> saveBool(String key, {required bool value});

  /// Retrieve a boolean value securely
  Future<bool?> getBool(String key);

  /// Store an integer value securely
  Future<void> saveInt(String key, int value);

  /// Retrieve an integer value securely
  Future<int?> getInt(String key);

  /// Store a double value securely
  Future<void> saveDouble(String key, double value);

  /// Retrieve a double value securely
  Future<double?> getDouble(String key);

  /// Remove a specific key-value pair
  Future<void> remove(String key);

  /// Clear all secure storage
  Future<void> clear();

  /// Check if a key exists
  Future<bool> containsKey(String key);

  /// Get all stored keys
  Future<List<String>> getAllKeys();

  /// Store authentication token securely
  Future<void> saveAuthToken(String token);

  /// Retrieve authentication token securely
  Future<String?> getAuthToken();

  /// Remove authentication token
  Future<void> removeAuthToken();

  /// Store refresh token securely
  Future<void> saveRefreshToken(String token);

  /// Retrieve refresh token securely
  Future<String?> getRefreshToken();

  /// Remove refresh token
  Future<void> removeRefreshToken();

  /// Store user session data securely
  Future<void> saveUserSession(Map<String, dynamic> sessionData);

  /// Retrieve user session data securely
  Future<Map<String, dynamic>?> getUserSession();

  /// Remove user session data
  Future<void> removeUserSession();

  /// Store encryption key securely
  Future<void> saveEncryptionKey(String keyId, String key);

  /// Retrieve encryption key securely
  Future<String?> getEncryptionKey(String keyId);

  /// Remove encryption key
  Future<void> removeEncryptionKey(String keyId);
}

--- CONTENT END ---

==========================================

=== FILE: local_storage/user_settings_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/user_settings_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1044 bytes

--- CONTENT START ---
/// Repository interface for managing user settings in local storage
/// 
/// This repository handles the persistence and retrieval of user-specific settings
/// such as preferences, configurations, and other local data.
/// 
/// Following the four-layer dependency rule, this interface is defined in the repository layer
/// and implemented in the data layer.
abstract class UserSettingsRepository {
  /// Get a setting value by key
  /// Returns null if the setting doesn't exist
  Future<T?> getSetting<T>(String key);

  /// Save a setting value with the given key
  /// Returns true if successful, false otherwise
  Future<bool> saveSetting<T>(String key, T value);

  /// Delete a setting by key
  /// Returns true if successful, false otherwise
  Future<bool> deleteSetting(String key);

  /// Check if a setting exists
  Future<bool> hasSetting(String key);

  /// Clear all settings
  /// Returns true if successful, false otherwise
  Future<bool> clearAllSettings();

  /// Get all setting keys
  Future<List<String>> getAllKeys();
}

--- CONTENT END ---

==========================================

=== FILE: notification/notification_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/notification/notification_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1359 bytes

--- CONTENT START ---
import 'dart:async';
import '../../statefulbusinesslogic/core/models/notification_model.dart';

/// Repository interface for notification storage and retrieval
abstract class NotificationRepository {
  /// Save a notification
  Future<void> saveNotification(Notification notification);
  
  /// Get all notifications for the current user
  Future<List<Notification>> getNotifications();
  
  /// Get unread notifications count
  Future<int> getUnreadCount();
  
  /// Mark a notification as read
  Future<void> markAsRead(String notificationId);
  
  /// Mark all notifications as read
  Future<void> markAllAsRead();
  
  /// Delete a notification
  Future<void> deleteNotification(String notificationId);
  
  /// Clear all notifications
  Future<void> clearAll();
  
  /// Get notifications by category
  Future<List<Notification>> getNotificationsByCategory(NotificationCategory category);
  
  /// Get notifications by type
  Future<List<Notification>> getNotificationsByType(String type);
  
  /// Get notifications stream for real-time updates
  Stream<List<Notification>> notificationsStream();
  
  /// Get unread count stream for real-time updates
  Stream<int> unreadCountStream();
  
  /// Mark notification as unread
  Future<void> markAsUnread(String notificationId);
  
  /// Delete all notifications
  Future<void> deleteAllNotifications();
}

--- CONTENT END ---

==========================================

=== FILE: notification/toast_service.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/notification/toast_service.dart
Last Modified: Sep 14 23:33:48 2025
File Size: 587 bytes

--- CONTENT START ---
/// Toast service interface for showing user notifications
/// This follows clean architecture by providing an abstraction for toast functionality
abstract class ToastService {
  /// Show an error toast message
  void showErrorToast(String message);

  /// Show a warning toast message  
  void showWarningToast(String message);

  /// Show an info toast message
  void showInfoToast(String message);

  /// Show a success toast message
  void showSuccessToast(String message);

  /// Show an info toast with title and description
  void showInfo(String title, {String? description});
}

--- CONTENT END ---

==========================================

=== FILE: notification_permission/notification_permission_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/notification_permission/notification_permission_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 208 bytes

--- CONTENT START ---
// ignore: one_member_abstracts
abstract class NotificationPermissionRepository {
  /// Requests system notification permission and returns whether it was granted.
  Future<bool> requestSystemPermission();
} 
--- CONTENT END ---

==========================================

=== FILE: presence/presence_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/presence/presence_repository.dart
Last Modified: Sep 11 17:56:48 2025
File Size: 11481 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/typing_indicator.dart';
import '../../statefulbusinesslogic/core/models/user_with_presence.dart';

/// Presence status - simplified to binary boolean system
/// Use isPresent: true for online, false for offline
@Deprecated('Use bool isPresent instead. true = online, false = offline')
enum PresenceStatus {
  online,
  offline,
}

class UserPresenceInfo {

  const UserPresenceInfo({
    required this.userId,
    required this.isPresent,
    required this.lastSeen,
    required this.updatedAt,
    this.statusMessage,
    this.currentActivity,
    this.currentBubbleId,
    this.deviceType = 'unknown',
    this.isTyping = false,
    this.activeSessions = const [],
  });
  final String userId;
  final bool isPresent; // true = online, false = offline
  final DateTime lastSeen;
  final DateTime updatedAt;
  final String? statusMessage;
  final String? currentActivity;
  final String? currentBubbleId;
  final String deviceType;
  final bool isTyping;
  final List<String> activeSessions;

  // Convenience getters for backward compatibility
  bool get isOnline => isPresent;
  bool get isOffline => !isPresent;

  @Deprecated('Use isPresent instead')
  PresenceStatus get status => PresenceStatus.online;

  UserPresenceInfo copyWith({
    String? userId,
    bool? isPresent,
    DateTime? lastSeen,
    DateTime? updatedAt,
    String? statusMessage,
    String? currentActivity,
    String? currentBubbleId,
    String? deviceType,
    bool? isTyping,
    List<String>? activeSessions,
  }) => UserPresenceInfo(
      userId: userId ?? this.userId,
      isPresent: isPresent ?? this.isPresent,
      lastSeen: lastSeen ?? this.lastSeen,
      updatedAt: updatedAt ?? this.updatedAt,
      statusMessage: statusMessage ?? this.statusMessage,
      currentActivity: currentActivity ?? this.currentActivity,
      currentBubbleId: currentBubbleId ?? this.currentBubbleId,
      deviceType: deviceType ?? this.deviceType,
      isTyping: isTyping ?? this.isTyping,
      activeSessions: activeSessions ?? this.activeSessions,
    );
}

/// Typing status information
class TypingStatus {

  const TypingStatus({
    required this.userId,
    required this.bubbleId,
    required this.isTyping, this.channelId,
    this.startedTypingAt,
    this.expiresAt,
  });
  final String userId;
  final String bubbleId;
  final String? channelId;
  final bool isTyping;
  final DateTime? startedTypingAt;
  final DateTime? expiresAt;
}

/// Presence statistics
class PresenceStats {

  const PresenceStats({
    required this.totalUsers,
    required this.onlineUsers,
    required this.awayUsers,
    required this.busyUsers,
    required this.offlineUsers,
    required this.onlinePercentage,
    required this.statusDistribution,
    required this.mostActiveUsers,
    required this.lastUpdated,
  });
  final int totalUsers;
  final int onlineUsers;
  final int awayUsers;
  final int busyUsers;
  final int offlineUsers;
  final double onlinePercentage;
  final Map<String, int> statusDistribution;
  final List<String> mostActiveUsers;
  final DateTime lastUpdated;
}

/// Presence history entry
class PresenceHistoryEntry {

  const PresenceHistoryEntry({
    required this.userId,
    required this.isPresent,
    required this.timestamp,
    required this.deviceType, this.statusMessage,
    this.activity,
  });
  final String userId;
  final bool isPresent;
  final DateTime timestamp;
  final String? statusMessage;
  final String? activity;
  final String deviceType;
}

/// Repository interface for managing user presence and online status
/// 
/// This repository handles all presence-related operations including
/// real-time status updates, typing indicators, and presence history.
/// Following the four-layer dependency rule, this interface is defined
/// in the repository layer and implemented in the data layer.
abstract class PresenceRepository {
  // ============================================================================
  // Core Presence Operations
  // ============================================================================

  /// Get current presence status for a single user
  Future<Result<UserPresenceInfo?>> getUserPresence(String userId);

  /// Update user presence status
  Future<Result<UserPresenceInfo>> updateUserPresence({
    required String userId,
    required bool isPresent,
    String? statusMessage,
    String? currentActivity,
    String? currentBubbleId,
    String? deviceType,
  });

  /// Get presence for multiple users (batch operation)
  Future<Result<Map<String, UserPresenceInfo>>> getBatchPresence(
    List<String> userIds,
  );

  /// Set user status to online
  Future<Result<UserPresenceInfo>> setUserOnline(
    String userId, {
    String? statusMessage,
  });

  /// Set user status to away
  Future<Result<UserPresenceInfo>> setUserAway(
    String userId, {
    String? awayMessage,
  });

  /// Set user status to offline
  Future<Result<void>> setUserOffline(String userId);

  /// Update user activity (for MQTT event handling)
  Future<Result<void>> updateUserActivity(String userId, String activity);

  /// Update typing status (for MQTT event handling)
  Future<Result<void>> updateTypingStatus(String userId, String conversationId, {required bool isTyping});



  // ============================================================================
  // Real-time Presence Streams
  // ============================================================================

  /// Stream of presence updates for specific users
  Stream<Map<String, UserPresenceInfo>> watchPresenceUpdates(
    List<String> userIds,
  );

  /// Stream of typing indicators for a conversation
  Stream<TypingIndicator> watchTypingIndicators(String conversationId);

  /// Stream of presence updates for all friends/contacts
  Stream<Map<String, UserPresenceInfo>> watchFriendsPresence(String userId);

  /// Stream of presence updates for a specific user
  /// This enables real-time presence monitoring for individual users
  Stream<UserPresenceInfo> watchUserPresence(String userId);

  // Bubble-level presence is not tracked; only users have presence.

  /// Subscribe to presence updates for specific users
  Future<Result<void>> subscribeToPresenceUpdates({
    required String userId,
    required List<String> targetUserIds,
  });

  /// Unsubscribe from presence updates for specific users
  Future<Result<void>> unsubscribeFromPresenceUpdates({
    required String userId,
    required List<String> targetUserIds,
  });



  // ============================================================================
  // Typing Indicators
  // ============================================================================

  /// Set typing status for a user in a bubble
  Future<Result<void>> setTypingStatus({
    required String userId,
    required String bubbleId,
    required bool isTyping, String? channelId,
  });

  /// Get typing users in a bubble
  Future<Result<List<TypingStatus>>> getTypingUsersInBubble(String bubbleId);

  /// Stream of typing status updates for a bubble
  Stream<List<TypingStatus>> watchTypingStatus(String bubbleId);

  /// Clear typing status for a user
  Future<Result<void>> clearTypingStatus(String userId, String bubbleId);

  // ============================================================================
  // Online Users & Activity
  // ============================================================================

  /// Get list of currently online users
  Future<Result<List<UserPresenceInfo>>> getOnlineUsers({
    String? bubbleId,
    int limit = 50,
    int offset = 0,
  });

  /// Get online friends for a user with pagination
  ///
  /// Efficiently retrieves only friends who are currently online.
  /// Requires backend implementation of GetOnlineFriends endpoint.
  Future<Result<List<UserWithPresence>>> getOnlineFriends(String userId, {
    int page = 0,
    int pageSize = 20,
  });

  /// Get online contacts for a user with pagination
  ///
  /// Efficiently retrieves only contacts who are currently online.
  /// Requires backend implementation of GetOnlineContacts endpoint.
  Future<Result<List<UserWithPresence>>> getOnlineContacts(String userId, {
    int page = 0,
    int pageSize = 20,
  });

  /// Get online bubblers (users in same bubble) with pagination
  ///
  /// Efficiently retrieves only bubble members who are currently online.
  /// Requires backend implementation of GetOnlineBubblers endpoint.
  Future<Result<List<UserWithPresence>>> getOnlineBubblers(String bubbleId, {
    int page = 0,
    int pageSize = 20,
  });

  /// Check if a user is currently online
  Future<Result<bool>> isUserOnline(String userId);

  /// Get online status for multiple users
  Future<Result<Map<String, bool>>> getMultipleUserOnlineStatus(
    List<String> userIds,
  );

  /// Get count of online users in a bubble
  Future<Result<int>> getOnlineUsersCount(String bubbleId);

  // ============================================================================
  // Presence History & Statistics
  // ============================================================================

  /// Get presence history for a user
  Future<Result<List<PresenceHistoryEntry>>> getPresenceHistory({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
    int limit = 100,
  });

  /// Get presence statistics
  Future<Result<PresenceStats>> getPresenceStats({
    String? bubbleId,
    String period = 'daily', // hourly, daily, weekly
  });

  /// Get most active users
  Future<Result<List<String>>> getMostActiveUsers({
    Duration timeWindow = const Duration(hours: 24),
    int limit = 10,
  });

  // ============================================================================
  // Heartbeat & Session Management
  // ============================================================================

  /// Send presence heartbeat
  Future<Result<void>> sendHeartbeat(String userId);

  /// Update last seen timestamp
  Future<Result<void>> updateLastSeen(String userId);

  /// Register device session
  Future<Result<void>> registerDeviceSession({
    required String userId,
    required String deviceId,
    required String deviceType,
    String? deviceName,
  });

  /// Unregister device session
  Future<Result<void>> unregisterDeviceSession({
    required String userId,
    required String deviceId,
  });

  /// End device session
  Future<Result<void>> endDeviceSession(String userId, String deviceId);

  /// Get active sessions for a user
  Future<Result<List<String>>> getUserActiveSessions(String userId);

  // Bubble-specific presence APIs are not part of user presence tracking.

  // ============================================================================
  // Composite Operations
  // ============================================================================

  /// Get user with presence information
  Future<Result<UserWithPresence?>> getUserWithPresence(String userId);

  /// Get multiple users with presence information
  Future<Result<List<UserWithPresence>>> getUsersWithPresence(
    List<String> userIds,
  );

  /// Initialize presence tracking for a user
  Future<Result<void>> initializePresence(String userId);

  /// Clean up presence data for a user
  Future<Result<void>> cleanupPresence(String userId);

  /// Refresh presence data from remote
  Future<Result<void>> refreshPresence(String userId);
}

--- CONTENT END ---

==========================================

=== FILE: profile_picture/profile_picture_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/profile_picture/profile_picture_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1162 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/models/profile_picture_result.dart' as result_model;

/// Repository interface for profile picture operations
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class ProfilePictureRepository {
  /// Pick an image from the gallery and upload it
  /// Returns a ProfilePictureResult with the upload status and URL
  Future<result_model.ProfilePictureResult> pickFromGallery();

  /// Take a photo with the camera and upload it
  /// Returns a ProfilePictureResult with the upload status and URL
  Future<result_model.ProfilePictureResult> takePhoto();

  /// Remove/delete a profile picture
  /// Returns true if successful, false otherwise
  Future<bool> removeProfilePicture(String imageUrl);

  /// Validate an image file before upload
  /// Returns validation result with any error messages
  Future<result_model.ProfilePictureResult> validateImage(String imagePath);

  /// Upload a locally stored profile picture (after account creation).
  /// Returns the public URL if successful, otherwise null.
  Future<String?> uploadLocalProfilePicture(String localPath);
}

--- CONTENT END ---

==========================================

=== FILE: report/report_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/report/report_repository.dart
Last Modified: Sep 15 12:55:29 2025
File Size: 1985 bytes

--- CONTENT START ---
import '../../../statefulbusinesslogic/core/error/result.dart';

/// Repository interface for general reporting functionality (bugs, features, feedback, users)
///
/// Note: For content reporting (messages, bubbles, media), use ContentReportRepository instead.
/// This separation ensures proper handling of different report types with appropriate endpoints.
///
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class ReportRepository {
  /// Submit a bug report
  /// Returns Result<String> containing the report ID on success
  Future<Result<String>> submitBugReport({
    required String title,
    required String description,
    String? userEmail,
  });

  /// Submit a feature request
  /// Returns Result<String> containing the request ID on success
  Future<Result<String>> submitFeatureRequest({
    required String title,
    required String description,
    String? userEmail,
  });

  /// Submit general feedback
  /// Returns Result<String> containing the feedback ID on success
  Future<Result<String>> submitFeedback({
    required String title,
    required String description,
    String? userEmail,
  });

  /// Submit user report
  /// Returns Result<String> containing the report ID on success
  Future<Result<String>> submitUserReport({
    required String reporterId,
    required String reportedUserId,
    required String reason,
    required String description,
    required String category,
    Map<String, String>? evidence,
  });

  /// Report user
  /// Returns Result<String> containing the report ID on success
  Future<Result<String>> reportUser({
    required String reporterId,
    required String reportedUserId,
    required String reason,
    required String description,
    required String category,
  });

  /// Remove contact
  /// Returns Result<void> for consistent error handling
  Future<Result<void>> removeContact({
    required String userId,
    required String contactId,
  });
}

--- CONTENT END ---

==========================================

=== FILE: resilience/resilience_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/resilience/resilience_repository.dart
Last Modified: Sep  5 18:20:17 2025
File Size: 6366 bytes

--- CONTENT START ---
/// Unified resilience repository interface
///
/// This interface consolidates all resilience patterns (retry, circuit breaker,
/// idempotency, operation-specific handling) into a single, cohesive system.
/// Following Clean Architecture, this interface exists in the repository layer.
abstract class ResilienceRepository {
  /// Execute an operation with comprehensive resilience patterns
  Future<T> executeWithResilience<T>(
    Future<T> Function() operation, {
    required String operationName,
    ResilienceConfig? config,
    Map<String, dynamic>? metadata,
  });

  /// Execute with retry logic only
  Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    required String operationName,
    RetryConfig? config,
  });

  /// Execute with circuit breaker protection
  Future<T> executeWithCircuitBreaker<T>(
    Future<T> Function() operation, {
    required String operationName,
    CircuitBreakerConfig? config,
  });

  /// Execute with idempotency support
  Future<T> executeWithIdempotency<T>(
    Future<T> Function() operation, {
    required String operationName,
    required String idempotencyKey,
    Duration? cacheDuration,
  });

  /// Execute gRPC-specific operations with optimized resilience
  Future<T> executeGrpcOperation<T>(
    Future<T> Function() operation, {
    required String operationName,
    GrpcResilienceConfig? config,
  });

  /// Execute network operations with network-specific resilience
  Future<T> executeNetworkOperation<T>(
    Future<T> Function() operation, {
    required String operationName,
    NetworkResilienceConfig? config,
  });

  /// Execute storage operations with storage-specific resilience
  Future<T> executeStorageOperation<T>(
    Future<T> Function() operation, {
    required String operationName,
    StorageResilienceConfig? config,
  });

  /// Get circuit breaker state for monitoring
  CircuitBreakerState getCircuitBreakerState(String operationName);

  /// Reset circuit breaker for an operation
  void resetCircuitBreaker(String operationName);

  /// Get resilience statistics for monitoring
  Map<String, dynamic> getResilienceStats();

  /// Reset all resilience state
  void resetAll();
}

/// Comprehensive resilience configuration
class ResilienceConfig {
  const ResilienceConfig({
    this.retry = const RetryConfig(),
    this.circuitBreaker = const CircuitBreakerConfig(),
    this.enableIdempotency = false,
    this.idempotencyCacheDuration = const Duration(minutes: 5),
    this.enableMetrics = true,
  });

  final RetryConfig retry;
  final CircuitBreakerConfig circuitBreaker;
  final bool enableIdempotency;
  final Duration idempotencyCacheDuration;
  final bool enableMetrics;
}

/// Retry configuration
class RetryConfig {
  const RetryConfig({
    this.maxAttempts = 3,
    this.initialDelay = const Duration(milliseconds: 100),
    this.maxDelay = const Duration(seconds: 30),
    this.backoffMultiplier = 2.0,
    this.jitterFactor = 0.1,
    this.shouldRetry,
  });

  final int maxAttempts;
  final Duration initialDelay;
  final Duration maxDelay;
  final double backoffMultiplier;
  final double jitterFactor;
  final bool Function(Object error)? shouldRetry;

  /// Calculate delay for a given attempt with exponential backoff and jitter
  Duration calculateDelay(int attempt) {
    if (attempt <= 0) {
      return initialDelay;
    }

    final exponentialDelay = initialDelay.inMilliseconds *
        (backoffMultiplier * attempt);
    final clampedDelay = exponentialDelay.clamp(
      initialDelay.inMilliseconds.toDouble(),
      maxDelay.inMilliseconds.toDouble(),
    );

    // Add jitter to prevent thundering herd
    final jitter = clampedDelay * jitterFactor * (0.5 - (DateTime.now().millisecondsSinceEpoch % 1000) / 1000);

    return Duration(milliseconds: (clampedDelay + jitter).round());
  }
}

/// Circuit breaker configuration
class CircuitBreakerConfig {
  const CircuitBreakerConfig({
    this.failureThreshold = 5,
    this.successThreshold = 3,
    this.timeout = const Duration(seconds: 60),
    this.minimumThroughput = 10,
  });

  final int failureThreshold;
  final int successThreshold;
  final Duration timeout;
  final int minimumThroughput;
}

/// gRPC-specific resilience configuration
class GrpcResilienceConfig extends ResilienceConfig {
  const GrpcResilienceConfig({
    super.retry = const RetryConfig(),
    super.circuitBreaker = const CircuitBreakerConfig(),
    super.enableIdempotency = true,
    this.enableDeadlineHandling = true,
    this.deadlineTimeout = const Duration(seconds: 30),
  });

  final bool enableDeadlineHandling;
  final Duration deadlineTimeout;
}

/// Network-specific resilience configuration
class NetworkResilienceConfig extends ResilienceConfig {
  const NetworkResilienceConfig({
    super.retry = const RetryConfig(
      initialDelay: Duration(milliseconds: 200),
    ),
    super.circuitBreaker = const CircuitBreakerConfig(
      failureThreshold: 3,
      timeout: Duration(seconds: 30),
    ),
    this.enableConnectivityCheck = true,
  });

  final bool enableConnectivityCheck;
}

/// Storage-specific resilience configuration
class StorageResilienceConfig extends ResilienceConfig {
  const StorageResilienceConfig({
    super.retry = const RetryConfig(
      maxAttempts: 2,
      initialDelay: Duration(milliseconds: 500),
    ),
    super.circuitBreaker = const CircuitBreakerConfig(
      failureThreshold: 3,
      timeout: Duration(seconds: 15),
    ),
    this.enableStorageHealthCheck = true,
  });

  final bool enableStorageHealthCheck;
}

/// Circuit breaker states
enum CircuitBreakerState {
  closed,
  open,
  halfOpen,
}

/// Resilience operation result with metadata
class ResilienceResult<T> {
  const ResilienceResult({
    required this.data,
    required this.metadata,
  });

  final T data;
  final ResilienceMetadata metadata;
}

/// Metadata about resilience operation execution
class ResilienceMetadata {
  const ResilienceMetadata({
    required this.operationName,
    required this.attempts,
    required this.totalDuration,
    required this.circuitBreakerState,
    this.fromCache = false,
    this.retryDelays = const [],
  });

  final String operationName;
  final int attempts;
  final Duration totalDuration;
  final CircuitBreakerState circuitBreakerState;
  final bool fromCache;
  final List<Duration> retryDelays;
}

--- CONTENT END ---

==========================================

=== FILE: security/security_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/security/security_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 246 bytes

--- CONTENT START ---
// ignore: one_member_abstracts
abstract class SecurityRepository {
  /// Checks if the given password appears in known breach datasets.
  /// Returns true if compromised, false otherwise.
  Future<bool> checkPasswordBreach(String password);
}



--- CONTENT END ---

==========================================

=== FILE: social_analytics/social_analytics_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/social_analytics/social_analytics_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2110 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

/// Repository interface for social analytics and optimization
///
/// This repository provides optimized, denormalized data for social features
/// that would be expensive to compute in real-time from normalized data.
abstract class SocialAnalyticsRepository {
  /// Get enhanced profile with comprehensive relationship data
  Future<Result<Map<String, dynamic>>> getEnhancedProfile({
    required String userId,
  });

  /// Get comprehensive profile analytics including mutual connections
  Future<Result<Map<String, dynamic>>> getProfileAnalytics({
    required String targetUserId,
  });

  /// Get mutual friends between current user and target user (optimized)
  /// Returns UserModel objects for direct use in UI
  Future<Result<List<UserModel>>> getMutualFriends(String targetUserId);

  /// Get mutual contacts between current user and target user (optimized)
  /// Returns UserModel objects for direct use in UI
  Future<Result<List<UserModel>>> getMutualContacts(String targetUserId);

  /// Get mutual friends between current user and target user (legacy)
  /// Returns user IDs for backward compatibility
  Future<Result<List<String>>> getMutualFriendsIds({
    required String targetUserId,
  });

  /// Get mutual contacts between current user and target user (legacy)
  /// Returns user IDs for backward compatibility
  Future<Result<List<String>>> getMutualContactsIds({
    required String targetUserId,
  });

  /// Get common bubbles between current user and target user
  Future<Result<List<Map<String, dynamic>>>> getCommonBubbles({
    required String targetUserId,
  });

  /// Get connection strength between current user and target user
  Future<Result<Map<String, dynamic>>> getConnectionStrength({
    required String targetUserId,
  });

  /// Get social graph data for current user
  Future<Result<Map<String, dynamic>>> getSocialGraph();

  /// Get engagement metrics for current user
  Future<Result<Map<String, dynamic>>> getEngagementMetrics();
} 
--- CONTENT END ---

==========================================

=== FILE: storage/storage_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/storage/storage_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1633 bytes

--- CONTENT START ---
import 'dart:typed_data';

abstract class StorageRepository {
  /// Upload a file to MinIO storage
  Future<String> uploadFile({
    required String bucketName,
    required String fileName,
    required Uint8List fileData,
    required String contentType,
    Map<String, String>? metadata,
  });

  /// Download a file from MinIO storage
  Future<Uint8List> downloadFile({
    required String bucketName,
    required String fileName,
  });

  /// Delete a file from MinIO storage
  Future<void> deleteFile({
    required String bucketName,
    required String fileName,
  });

  /// Get a presigned URL for file download
  Future<String> getPresignedUrl({
    required String bucketName,
    required String fileName,
    Duration expiry = const Duration(hours: 1),
  });

  /// Check if a file exists in MinIO storage
  Future<bool> fileExists({
    required String bucketName,
    required String fileName,
  });

  /// List files in a bucket with optional prefix
  Future<List<String>> listFiles({required String bucketName, String? prefix});

  /// Get file metadata from MinIO storage
  Future<Map<String, String>> getFileMetadata({
    required String bucketName,
    required String fileName,
  });

  /// Update file metadata in MinIO storage
  Future<void> updateFileMetadata({
    required String bucketName,
    required String fileName,
    required Map<String, String> metadata,
  });

  /// Copy a file within MinIO storage
  Future<void> copyFile({
    required String sourceBucketName,
    required String sourceFileName,
    required String destinationBucketName,
    required String destinationFileName,
  });
}

--- CONTENT END ---

==========================================

=== FILE: sync/sync_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/sync/sync_repository.dart
Last Modified: Sep 15 13:30:21 2025
File Size: 6240 bytes

--- CONTENT START ---
/// Sync Repository Interface (Repository Layer)
/// 
/// Defines the contract for initial data synchronization using HTTP/3-first approach.
/// This follows the four-layer dependency rule - Repository layer defines contracts
/// that are implemented in the Data layer.
library;

import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/sync_models.dart';

/// Repository interface for HTTP/3-first initial data synchronization
/// 
/// This repository handles the bulk data fetch that occurs after successful login,
/// using HTTP/3 for optimal performance with automatic fallback to HTTP/2.
/// 
/// Key responsibilities:
/// - Fetch all user data in a single HTTP/3 request
/// - Establish MQTT connection for real-time updates  
/// - Store data in local Drift database atomically
/// - Provide sync metrics and performance data
abstract class SyncRepository {
  /// Perform initial bulk data synchronization
  /// 
  /// This method orchestrates the complete sync process:
  /// 1. Fetches all user data via HTTP/3 (with HTTP/2 fallback)
  /// 2. Establishes MQTT connection for real-time updates (parallel)
  /// 3. Stores data in local Drift database (atomic transaction)
  /// 4. Returns comprehensive sync result with metrics
  /// 
  /// [config] Optional configuration for sync behavior
  /// 
  /// Returns [Result<SyncResult>] containing:
  /// - Success/failure status
  /// - Performance metrics (duration, protocol used, etc.)
  /// - MQTT connection status
  /// - Data synchronization details
  /// 
  /// Example usage:
  /// ```dart
  /// final result = await syncRepository.performInitialSync();
  /// if (result.isSuccess) {
  ///   final syncResult = result.data!;
  ///   debugPrint('Sync completed in ${syncResult.syncDurationMs}ms using ${syncResult.protocolUsed}');
  /// }
  /// ```
  Future<Result<SyncResult>> performInitialSync([SyncConfig? config]);

  /// Get current sync status
  /// 
  /// Returns the current status of any ongoing sync operation.
  /// Useful for showing progress indicators in the UI.
  SyncStatus get currentStatus;

  /// Stream of sync status updates
  /// 
  /// Provides real-time updates of sync progress for UI reactivity.
  /// Emits [SyncStatus] values as the sync progresses through different phases.
  Stream<SyncStatus> get statusStream;

  /// Get sync metrics from the last operation
  /// 
  /// Returns detailed performance metrics from the most recent sync,
  /// or null if no sync has been performed yet.
  SyncMetrics? get lastSyncMetrics;

  /// Cancel any ongoing sync operation
  ///
  /// Attempts to gracefully cancel the current sync operation.
  /// Returns Result<bool> indicating if cancellation was successful.
  Future<Result<bool>> cancelSync();

  /// Check if sync is currently in progress
  bool get isSyncing;

  /// Get the last successful sync timestamp
  ///
  /// Returns the timestamp of the last successful sync operation,
  /// or null if no successful sync has occurred.
  DateTime? get lastSuccessfulSync;

  /// Get incremental sync data since last sync
  ///
  /// Fetches only the data that has changed since the last sync timestamp.
  /// This is more efficient than a full sync for regular updates.
  ///
  /// [userId] The user ID to sync data for
  /// [lastSyncTimestamp] The timestamp of the last successful sync
  /// [dataTypes] List of data types to sync (e.g., ['contacts', 'bubbles'])
  /// [deviceId] The device ID for conflict resolution
  ///
  /// Returns [Result<SyncResult>] containing the incremental sync data
  Future<Result<SyncResult>> getSyncData(
    String userId, {
    DateTime? lastSyncTimestamp,
    List<String>? dataTypes,
    String? deviceId,
  });

  /// Push local changes to the server
  ///
  /// Uploads local changes that haven't been synchronized yet.
  /// Handles conflict detection and resolution.
  ///
  /// [userId] The user ID pushing the data
  /// [syncData] The local changes to push
  /// [deviceId] The device ID for conflict resolution
  ///
  /// Returns [Result<List<SyncConflict>>] containing any conflicts that need resolution
  Future<Result<List<SyncConflict>>> pushSyncData(
    String userId,
    Map<String, dynamic> syncData, {
    String? deviceId,
  });

  /// Get current sync status from server
  ///
  /// Retrieves the current synchronization status for the user.
  /// Useful for checking if a sync is needed or in progress.
  ///
  /// [userId] The user ID to check status for
  ///
  /// Returns [Result<SyncStatusInfo>] containing the current sync status
  Future<Result<SyncStatusInfo>> getSyncStatus(String userId);

  /// Force a sync refresh
  ///
  /// Triggers a forced synchronization, bypassing normal sync intervals.
  /// Useful for manual refresh or when data inconsistencies are detected.
  ///
  /// [userId] The user ID to force sync for
  /// [deviceId] The device ID requesting the sync
  ///
  /// Returns [Result<SyncResult>] containing the sync result
  Future<Result<SyncResult>> forceSyncRefresh(String userId, {String? deviceId});

  /// Queue local mutation for offline sync
  ///
  /// Stores a local mutation that will be synced when connectivity is available.
  /// This enables offline-first functionality for user actions.
  ///
  /// [userId] The user ID performing the mutation
  /// [dataType] The type of data being mutated (e.g., 'contact', 'bubble')
  /// [entityId] The ID of the entity being mutated
  /// [operation] The operation type ('create', 'update', 'delete')
  /// [data] The mutation data
  /// [metadata] Optional metadata for the mutation
  ///
  /// Returns [Result<void>] indicating success or failure
  Future<Result<void>> queueLocalMutation({
    required String userId,
    required String dataType,
    required String entityId,
    required String operation,
    required Map<String, dynamic> data,
    Map<String, dynamic>? metadata,
  });

  /// Get pending local mutations
  ///
  /// Retrieves all pending mutations for a user that need to be synced.
  ///
  /// [userId] The user ID to get mutations for
  ///
  /// Returns [Result<List<dynamic>>] containing the pending mutations
  Future<Result<List<dynamic>>> getPendingMutations(String userId);
}

--- CONTENT END ---

==========================================

=== FILE: user/user_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/user/user_repository.dart
Last Modified: Sep  7 20:15:25 2025
File Size: 2936 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/relationship_status.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

abstract class UserRepository {
  /// Gets a user by their ID with additional details.
  Future<UserModel?> getUserById(String userId);

  /// Gets the current user with additional details.
  /// Use getCurrentUserSafe for a typed response instead.
  Future<UserModel?> getCurrentUser();

  /// Same as [getCurrentUser] but returns a typed success/failure wrapper.
  Future<Result<UserModel>> getCurrentUserSafe();

  String? get currentUserId;

  Future<UserModel?> getUser(String userId);
  Future<UserModel?> getEnhancedUser(String userId);
  Future<Map<String, dynamic>> getUserInfo(String userId);
  Future<List<UserModel>> getUsers(List<String> userIds);
  Future<List<UserModel>> getAllUsers();
  Future<List<UserModel>> getFriends(String userId);

  Future<void> createUser(UserModel user);
  Future<void> updateUser(UserModel user);

  Future<List<UserModel>> findUsers({
    String? firstName,
    String? lastName,
    String? email,
    // Potentially other filter criteria
  });

  // getUsersInBubble moved to BubbleRepository where it belongs (SRP compliance)
  Future<Map<String, dynamic>> getGroupInfo(String groupId);

  Future<List<UserModel>> getMutualFriends(String userId1, String userId2);
  Future<List<UserModel>> getMutualContacts(String userId1, String userId2);

  /// Get comprehensive relationship status between two users
  ///
  /// This method provides a unified view of the relationship between the current user
  /// and target user, including primary relationship type, blocking status, pending
  /// requests, and bubble membership information.
  ///
  /// Returns a [Result] containing [RelationshipStatus] with all relationship details
  /// needed for client-side button state logic and relationship display.
  Future<Result<RelationshipStatus>> getUserRelationshipStatus(
    String currentUserId,
    String targetUserId,
  );

  Future<void> addFriend(String userId, String friendId);

  // Contact request methods
  Future<bool> sendContactRequest({required String fromUserId, required String toUserId});
  Future<bool> acceptContactRequest({required String fromUserId, required String toUserId});
  Future<bool> rejectContactRequest({required String fromUserId, required String toUserId});

  // User management methods
  Future<bool> removeContact({required String userId, required String contactId});
  Future<Result<void>> blockUser(String targetUserId);
  Future<bool> unblockUser({required String userId, required String targetUserId});
  Future<Result<void>> reportUser(String targetUserId, String reason);

  // Account management methods
  Future<bool> deleteAccount({required String userId});
  Future<bool> deactivateAccount({required String userId, required String reason});
}

--- CONTENT END ---

==========================================

=== FILE: webrtc/webrtc_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/webrtc/webrtc_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2720 bytes

--- CONTENT START ---
import 'package:flutter_webrtc/flutter_webrtc.dart';

abstract class WebRTCRepository {
  /// Initialize WebRTC and get user media
  Future<MediaStream> initializeUserMedia({
    bool audio = true,
    bool video = true,
  });

  /// Create a peer connection with configuration
  Future<RTCPeerConnection> createPeerConnection({
    required Map<String, dynamic> configuration,
    Map<String, dynamic>? constraints,
  });

  /// Create an offer
  Future<RTCSessionDescription> createOffer({
    required RTCPeerConnection peerConnection,
    Map<String, dynamic>? constraints,
  });

  /// Create an answer
  Future<RTCSessionDescription> createAnswer({
    required RTCPeerConnection peerConnection,
    Map<String, dynamic>? constraints,
  });

  /// Set local description
  Future<void> setLocalDescription({
    required RTCPeerConnection peerConnection,
    required RTCSessionDescription description,
  });

  /// Set remote description
  Future<void> setRemoteDescription({
    required RTCPeerConnection peerConnection,
    required RTCSessionDescription description,
  });

  /// Add ICE candidate
  Future<void> addIceCandidate({
    required RTCPeerConnection peerConnection,
    required RTCIceCandidate candidate,
  });

  /// Get stats from peer connection
  Future<List<StatsReport>> getStats({
    required RTCPeerConnection peerConnection,
    MediaStreamTrack? track,
  });

  /// Switch camera (for mobile)
  Future<bool> switchCamera({required MediaStreamTrack videoTrack});

  /// Enable/disable track
  Future<void> setTrackEnabled({
    required MediaStreamTrack track,
    required bool enabled,
  });

  /// Start screen sharing
  Future<MediaStream> startScreenShare();

  /// Stop screen sharing
  Future<void> stopScreenShare({required MediaStream screenStream});

  /// Clean up resources
  Future<void> dispose({
    required RTCPeerConnection peerConnection,
    required MediaStream localStream,
  });

  /// Get available media devices
  Future<List<MediaDeviceInfo>> enumerateDevices();

  /// Set audio output device (speaker)
  Future<void> setAudioOutput({required String deviceId});

  /// Handle connection state changes
  Stream<RTCPeerConnectionState> onConnectionStateChange({
    required RTCPeerConnection peerConnection,
  });

  /// Handle ICE candidate events
  Stream<RTCIceCandidate> onIceCandidate({
    required RTCPeerConnection peerConnection,
  });

  /// Handle negotiation needed events
  Stream<void> onNegotiationNeeded({required RTCPeerConnection peerConnection});

  /// Handle track events
  Stream<MediaStream> onTrack({required RTCPeerConnection peerConnection});

  /// Dispose all resources when the repository is no longer needed
  void disposeAll();
}

--- CONTENT END ---

==========================================


# SUMMARY
# =======
Total files processed: 49
File types found:
    49  dart
