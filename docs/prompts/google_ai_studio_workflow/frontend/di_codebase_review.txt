# DI CODEBASE REVIEW PROMPT
# =========================
#
# This file contains the complete content of all files in the DI directory and main files.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: Sat Sep  6 19:50:56 EDT 2025
# Total files to process:        8
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure
# 2. Analyze the DI patterns and main application setup
# 3. Identify patterns, dependencies, and relationships between components
# 4. Provide insights on dependency injection and initialization patterns
# 5. Note any inconsistencies, missing implementations, or areas for enhancement
# 6. Review the architecture patterns and best practices adherence
# 7. Analyze how the main files set up the application and initialize services
#
# FILE CONTENTS:
# =============

=== FILE: di/injection_container.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/di/injection_container.dart
Last Modified: Sep  6 19:32:20 2025
File Size: 21289 bytes

--- CONTENT START ---
/// Centralized Dependency Injection Container
/// 
/// This file contains all data layer imports and concrete implementations,
/// maintaining Clean Architecture by keeping these imports out of the business logic layer.
/// 
/// The business logic layer should only import repository interfaces and domain models.

import 'package:riverpod_annotation/riverpod_annotation.dart';

// Data layer imports - ONLY allowed in DI layer
import '../data/datasources/auth_remote_data_source.dart';
import '../data/datasources/bubble_remote_data_source.dart';
import '../data/datasources/call_remote_data_source.dart';
import '../data/datasources/presence_remote_data_source.dart';
import '../data/datasources/realtime_remote_data_source.dart';
import '../data/datasources/search_remote_data_source.dart';
import '../data/datasources/sync_remote_data_source.dart';
import '../data/datasources/user_remote_data_source.dart';

// Repository implementations - ONLY allowed in DI layer
import '../data/repositories/activity_status/activity_status_repository_impl.dart';
import '../data/repositories/audio_recording/audio_recording_repository_impl.dart';
import '../data/repositories/auth/auth_repository_impl.dart';
import '../data/repositories/availability/availability_repository_impl.dart';
import '../data/repositories/bubble/cached_bubble_invite_request_repository_impl.dart';
import '../data/repositories/bubble/cached_bubble_join_request_repository_impl.dart';
import '../data/repositories/bubble/cached_bubble_kickout_repository_impl.dart';
import '../data/repositories/bubble/cached_bubble_repository_impl.dart';
import '../data/repositories/bubble_history/cached_bubble_history_repository_impl.dart';
import '../data/repositories/call/call_repository_impl.dart';
import '../data/repositories/chat/cached_chat_repository_impl.dart';
import '../data/repositories/contacts/cached_contacts_repository_impl.dart';
import '../data/repositories/dialog_state/dialog_state_repository_impl.dart';
import '../data/repositories/friendship/cached_friend_request_repository_impl.dart';
import '../data/repositories/friendship/cached_friendship_repository_impl.dart';
import '../data/repositories/local_storage/user_settings_repository_impl.dart';
import '../data/repositories/notification/notification_repository_impl.dart';
import '../data/repositories/notification_permission/notification_permission_repository_impl.dart';
import '../data/repositories/presence/cached_presence_repository_impl.dart';
import '../data/repositories/profile_picture/cached_profile_picture_repository_impl.dart';
import '../data/repositories/report/report_repository_impl.dart';
import '../data/repositories/sync/sync_repository_impl.dart';
import '../data/repositories/user/cached_user_repository_impl.dart';
import '../data/repositories/resilience/resilience_repository_impl.dart';

// Service implementations - ONLY allowed in DI layer
import '../data/services/api/grpc_client_service.dart';
import '../data/services/audio/audio_recording_service.dart';
import '../data/services/auth/availability_service.dart';
import '../data/services/auth/backend_auth_service.dart';
import '../data/services/auth/kratos_auth_service.dart';
import '../data/services/call/platform_call_handler.dart';
import '../data/services/connectivity/connectivity_service.dart';
import '../data/services/fcm/notification_service_fcm.dart';
import '../data/services/language/language_service.dart';
import '../data/services/local_storage/local_storage_service.dart';
import '../data/services/media/media_upload_service.dart';
import '../data/services/mqtt/mqtt_event_handler_impl.dart';
import '../data/services/mqtt/mqtt_only_real_time_service.dart';
import '../data/services/notification/notification_integration_service.dart';
import '../data/services/notification/notification_orchestrator.dart';
import '../data/services/notification/notification_service.dart';
import '../data/services/notification/notification_service_impl.dart';
import '../data/services/performance/startup_performance_service.dart';
import '../data/services/presence/presence_service.dart';
import '../data/services/real_time/real_time_service_manager.dart';
import '../data/services/security/security_service.dart';
import '../data/services/storage/storage_service.dart' as storage;
import '../data/services/user/user_profile_preloader_service.dart';
import '../data/services/webrtc/webrtc_service.dart';

// Repository interfaces - These are the contracts that business logic layer uses
import '../repositories/activity_status/activity_status_repository.dart';
import '../repositories/audio_recording/audio_recording_repository.dart';
import '../repositories/auth/auth_repository.dart';
import '../repositories/availability/availability_repository.dart';
import '../repositories/bubble/bubble_invite_request_repository.dart';
import '../repositories/bubble/bubble_join_request_repository.dart';
import '../repositories/bubble/bubble_kickout_repository.dart';
import '../repositories/bubble/bubble_repository.dart';
import '../repositories/bubble_history/bubble_history_repository.dart';
import '../repositories/call/call_repository.dart';
import '../repositories/chat/chat_repository.dart';
import '../repositories/contacts/contacts_repository.dart';
import '../repositories/dialog_state/dialog_state_repository.dart';
import '../repositories/friendship/friend_request_repository.dart';
import '../repositories/friendship/friendship_repository.dart';
import '../repositories/local_storage/user_settings_repository.dart';
import '../repositories/notification/notification_repository.dart';
import '../repositories/notification_permission/notification_permission_repository.dart';
import '../repositories/presence/presence_repository.dart';
import '../repositories/profile_picture/profile_picture_repository.dart';
import '../repositories/report/report_repository.dart';
import '../repositories/sync/sync_repository.dart';
import '../repositories/user/user_repository.dart';
import '../repositories/resilience/resilience_repository.dart';

// Business logic imports - Only models, errors, and interfaces allowed
import '../statefulbusinesslogic/core/services/logging_service.dart';

part 'injection_container.g.dart';

// =============================================================================
// CORE SERVICE PROVIDERS
// =============================================================================

@Riverpod(keepAlive: true)
GrpcClientService grpcClientService(GrpcClientServiceRef ref) {
  return GrpcClientService();
}

@Riverpod(keepAlive: true)
ResilienceRepository resilienceRepository(ResilienceRepositoryRef ref) {
  return ResilienceRepositoryImpl();
}

// =============================================================================
// DATA SOURCE PROVIDERS
// =============================================================================

@Riverpod(keepAlive: true)
AuthRemoteDataSource authRemoteDataSource(AuthRemoteDataSourceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  final kratosAuthService = ref.watch(kratosAuthServiceProvider);
  final resilienceRepository = ref.watch(resilienceRepositoryProvider);
  return AuthRemoteDataSourceImpl(grpcClient, kratosAuthService)
    ..setResilienceService(resilienceRepository);
}

@Riverpod(keepAlive: true)
BubbleRemoteDataSource bubbleRemoteDataSource(BubbleRemoteDataSourceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  final resilienceRepository = ref.watch(resilienceRepositoryProvider);
  // Create a function to get current user ID
  String getCurrentUserId() {
    // This should be implemented to get the current authenticated user ID
    // For now, return a placeholder - this needs to be properly implemented
    return 'current-user-id'; // TODO: Implement proper user ID retrieval
  }
  return BubbleRemoteDataSourceImpl(grpcClient, getCurrentUserId)
    ..setResilienceService(resilienceRepository);
}

@Riverpod(keepAlive: true)
CallRemoteDataSource callRemoteDataSource(CallRemoteDataSourceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  final resilienceRepository = ref.watch(resilienceRepositoryProvider);
  return CallRemoteDataSourceImpl(grpcClient)
    ..setResilienceService(resilienceRepository);
}

@Riverpod(keepAlive: true)
PresenceRemoteDataSource presenceRemoteDataSource(PresenceRemoteDataSourceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  final resilienceRepository = ref.watch(resilienceRepositoryProvider);
  return PresenceRemoteDataSourceImpl(grpcClient)
    ..setResilienceService(resilienceRepository);
}

@Riverpod(keepAlive: true)
RealtimeRemoteDataSource realtimeRemoteDataSource(RealtimeRemoteDataSourceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  final resilienceRepository = ref.watch(resilienceRepositoryProvider);
  return RealtimeRemoteDataSourceImpl(grpcClient)
    ..setResilienceService(resilienceRepository);
}

@Riverpod(keepAlive: true)
SearchRemoteDataSource searchRemoteDataSource(SearchRemoteDataSourceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  final resilienceRepository = ref.watch(resilienceRepositoryProvider);
  return SearchRemoteDataSourceImpl(grpcClient)
    ..setResilienceService(resilienceRepository);
}

@Riverpod(keepAlive: true)
SyncRemoteDataSource syncRemoteDataSource(SyncRemoteDataSourceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  final resilienceRepository = ref.watch(resilienceRepositoryProvider);
  return SyncRemoteDataSourceImpl(grpcClient)
    ..setResilienceService(resilienceRepository);
}

@Riverpod(keepAlive: true)
UserRemoteDataSource userRemoteDataSource(UserRemoteDataSourceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  final resilienceRepository = ref.watch(resilienceRepositoryProvider);
  return UserRemoteDataSourceImpl(grpcClient)
    ..setResilienceService(resilienceRepository);
}

// =============================================================================
// REPOSITORY PROVIDERS
// =============================================================================

@Riverpod(keepAlive: true)
AuthRepository authRepository(AuthRepositoryRef ref) {
  final remoteDataSource = ref.watch(authRemoteDataSourceProvider);
  return AuthRepositoryImpl(remoteDataSource);
}

@Riverpod(keepAlive: true)
BubbleRepository bubbleRepository(BubbleRepositoryRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  final mqttService = ref.watch(mqttRealTimeServiceProvider);
  return CachedBubbleRepositoryImpl(remoteDataSource, localStorageService, mqttService);
}

@Riverpod(keepAlive: true)
UserRepository userRepository(UserRepositoryRef ref) {
  final remoteDataSource = ref.watch(userRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedUserRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
CallRepository callRepository(CallRepositoryRef ref) {
  final remoteDataSource = ref.watch(callRemoteDataSourceProvider);
  return CallRepositoryImpl(remoteDataSource);
}

@Riverpod(keepAlive: true)
ChatRepository chatRepository(ChatRepositoryRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedChatRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
ContactsRepository contactsRepository(ContactsRepositoryRef ref) {
  final remoteDataSource = ref.watch(userRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  final mqttService = ref.watch(mqttRealTimeServiceProvider);
  return CachedContactsRepositoryImpl(remoteDataSource, localStorageService, mqttService);
}

@Riverpod(keepAlive: true)
FriendshipRepository friendshipRepository(FriendshipRepositoryRef ref) {
  final remoteDataSource = ref.watch(userRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedFriendshipRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
FriendRequestRepository friendRequestRepository(FriendRequestRepositoryRef ref) {
  final remoteDataSource = ref.watch(userRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  final mqttService = ref.watch(mqttRealTimeServiceProvider);
  return CachedFriendRequestRepositoryImpl(remoteDataSource, localStorageService, mqttService);
}

@Riverpod(keepAlive: true)
PresenceRepository presenceRepository(PresenceRepositoryRef ref) {
  final remoteDataSource = ref.watch(presenceRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedPresenceRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
BubbleJoinRequestRepository bubbleJoinRequestRepository(BubbleJoinRequestRepositoryRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleJoinRequestRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
BubbleInviteRequestRepository bubbleInviteRequestRepository(BubbleInviteRequestRepositoryRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleInviteRequestRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
BubbleKickoutRepository bubbleKickoutRepository(BubbleKickoutRepositoryRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleKickoutRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
BubbleHistoryRepository bubbleHistoryRepository(BubbleHistoryRepositoryRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleHistoryRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
ActivityStatusRepository activityStatusRepository(ActivityStatusRepositoryRef ref) {
  final remoteDataSource = ref.watch(userRemoteDataSourceProvider);
  return ActivityStatusRepositoryImpl(remoteDataSource);
}

@Riverpod(keepAlive: true)
AudioRecordingRepository audioRecordingRepository(AudioRecordingRepositoryRef ref) {
  final audioService = ref.watch(audioRecordingServiceProvider);
  return AudioRecordingRepositoryImpl(audioService);
}

@Riverpod(keepAlive: true)
AvailabilityRepository availabilityRepository(AvailabilityRepositoryRef ref) {
  final availabilityService = ref.watch(availabilityServiceProvider);
  return AvailabilityRepositoryImpl(availabilityService);
}

@Riverpod(keepAlive: true)
DialogStateRepository dialogStateRepository(DialogStateRepositoryRef ref) {
  final localStorageService = ref.watch(localStorageServiceProvider);
  return DialogStateRepositoryImpl(localStorageService);
}

@Riverpod(keepAlive: true)
NotificationRepository notificationRepository(NotificationRepositoryRef ref) {
  final notificationService = ref.watch(notificationServiceProvider);
  return NotificationRepositoryImpl(notificationService);
}

@Riverpod(keepAlive: true)
NotificationPermissionRepository notificationPermissionRepository(NotificationPermissionRepositoryRef ref) {
  final notificationService = ref.watch(notificationServiceProvider);
  return NotificationPermissionRepositoryImpl(notificationService);
}

@Riverpod(keepAlive: true)
ProfilePictureRepository profilePictureRepository(ProfilePictureRepositoryRef ref) {
  final remoteDataSource = ref.watch(userRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedProfilePictureRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
ReportRepository reportRepository(ReportRepositoryRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  return ReportRepositoryImpl(grpcClient);
}

@Riverpod(keepAlive: true)
SyncRepository syncRepository(SyncRepositoryRef ref) {
  final remoteDataSource = ref.watch(syncRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return SyncRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
UserSettingsRepository userSettingsRepository(UserSettingsRepositoryRef ref) {
  final localStorageService = ref.watch(localStorageServiceProvider);
  return UserSettingsRepositoryImpl(localStorageService);
}

// =============================================================================
// SERVICE PROVIDERS
// =============================================================================

@Riverpod(keepAlive: true)
LocalStorageService localStorageService(LocalStorageServiceRef ref) {
  return LocalStorageService();
}

@Riverpod(keepAlive: true)
AudioRecordingService audioRecordingService(AudioRecordingServiceRef ref) {
  return AudioRecordingService();
}

@Riverpod(keepAlive: true)
AvailabilityService availabilityService(AvailabilityServiceRef ref) {
  final authService = ref.watch(backendAuthServiceProvider);
  return AvailabilityService(authService);
}

@Riverpod(keepAlive: true)
BackendAuthService backendAuthService(BackendAuthServiceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  return BackendAuthService(grpcClient);
}

@Riverpod(keepAlive: true)
KratosAuthService kratosAuthService(KratosAuthServiceRef ref) {
  return KratosAuthService();
}

@Riverpod(keepAlive: true)
PlatformCallHandler platformCallHandler(PlatformCallHandlerRef ref) {
  return PlatformCallHandler();
}

@Riverpod(keepAlive: true)
ConnectivityService connectivityService(ConnectivityServiceRef ref) {
  return ConnectivityService();
}

@Riverpod(keepAlive: true)
NotificationServiceFcm notificationServiceFcm(NotificationServiceFcmRef ref) {
  return NotificationServiceFcm();
}

@Riverpod(keepAlive: true)
LanguageService languageService(LanguageServiceRef ref) {
  return LanguageService();
}

@Riverpod(keepAlive: true)
MediaUploadService mediaUploadService(MediaUploadServiceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  return MediaUploadService(grpcClient);
}

@Riverpod(keepAlive: true)
MqttEventHandlerImpl mqttEventHandler(MqttEventHandlerRef ref) {
  return MqttEventHandlerImpl();
}

@Riverpod(keepAlive: true)
MqttOnlyRealTimeService mqttRealTimeService(MqttRealTimeServiceRef ref) {
  final eventHandler = ref.watch(mqttEventHandlerProvider);
  return MqttOnlyRealTimeService(eventHandler);
}

@Riverpod(keepAlive: true)
NotificationIntegrationService notificationIntegrationService(NotificationIntegrationServiceRef ref) {
  final fcmService = ref.watch(notificationServiceFcmProvider);
  return NotificationIntegrationService(fcmService);
}

@Riverpod(keepAlive: true)
NotificationOrchestrator notificationOrchestrator(NotificationOrchestratorRef ref) {
  final integrationService = ref.watch(notificationIntegrationServiceProvider);
  return NotificationOrchestrator(integrationService);
}

@Riverpod(keepAlive: true)
NotificationService notificationService(NotificationServiceRef ref) {
  final orchestrator = ref.watch(notificationOrchestratorProvider);
  return NotificationServiceImpl(orchestrator);
}

@Riverpod(keepAlive: true)
StartupPerformanceService startupPerformanceService(StartupPerformanceServiceRef ref) {
  return StartupPerformanceService();
}

@Riverpod(keepAlive: true)
PresenceService presenceService(PresenceServiceRef ref) {
  final remoteDataSource = ref.watch(presenceRemoteDataSourceProvider);
  return PresenceService(remoteDataSource);
}

@Riverpod(keepAlive: true)
RealTimeServiceManager realTimeServiceManager(RealTimeServiceManagerRef ref) {
  final mqttService = ref.watch(mqttRealTimeServiceProvider);
  return RealTimeServiceManager(mqttService);
}

@Riverpod(keepAlive: true)
SecurityService securityService(SecurityServiceRef ref) {
  return SecurityService();
}

@Riverpod(keepAlive: true)
storage.StorageService storageService(StorageServiceRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  return storage.StorageService(grpcClient);
}

@Riverpod(keepAlive: true)
UserProfilePreloaderService userProfilePreloaderService(UserProfilePreloaderServiceRef ref) {
  final userRepository = ref.watch(userRepositoryProvider);
  return UserProfilePreloaderService(userRepository);
}

@Riverpod(keepAlive: true)
WebRtcService webRtcService(WebRtcServiceRef ref) {
  return WebRtcService();
}

// =============================================================================
// SERVICE INTERFACE PROVIDERS
// =============================================================================

// NOTE: The business logic layer already has the service interfaces.
// The data layer services should implement these interfaces directly.
// For now, we'll provide the data layer services directly until they implement the interfaces.

--- CONTENT END ---

==========================================

=== FILE: di/injection_container.g.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/di/injection_container.g.dart
Last Modified: Sep  6 14:46:42 2025
File Size: 69984 bytes

--- CONTENT START ---
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'injection_container.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(grpcClientService)
const grpcClientServiceProvider = GrpcClientServiceProvider._();

final class GrpcClientServiceProvider
    extends
        $FunctionalProvider<
          GrpcClientService,
          GrpcClientService,
          GrpcClientService
        >
    with $Provider<GrpcClientService> {
  const GrpcClientServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'grpcClientServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$grpcClientServiceHash();

  @$internal
  @override
  $ProviderElement<GrpcClientService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  GrpcClientService create(Ref ref) {
    return grpcClientService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(GrpcClientService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<GrpcClientService>(value),
    );
  }
}

String _$grpcClientServiceHash() => r'de6d721d14661223e6d7e00f6f299393c6c22c95';

@ProviderFor(resilienceRepository)
const resilienceRepositoryProvider = ResilienceRepositoryProvider._();

final class ResilienceRepositoryProvider
    extends
        $FunctionalProvider<
          ResilienceRepository,
          ResilienceRepository,
          ResilienceRepository
        >
    with $Provider<ResilienceRepository> {
  const ResilienceRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'resilienceRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$resilienceRepositoryHash();

  @$internal
  @override
  $ProviderElement<ResilienceRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ResilienceRepository create(Ref ref) {
    return resilienceRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ResilienceRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ResilienceRepository>(value),
    );
  }
}

String _$resilienceRepositoryHash() =>
    r'c129b9cdabad0ccfba86daa33822ddd0efee416a';

@ProviderFor(authRemoteDataSource)
const authRemoteDataSourceProvider = AuthRemoteDataSourceProvider._();

final class AuthRemoteDataSourceProvider
    extends
        $FunctionalProvider<
          AuthRemoteDataSource,
          AuthRemoteDataSource,
          AuthRemoteDataSource
        >
    with $Provider<AuthRemoteDataSource> {
  const AuthRemoteDataSourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'authRemoteDataSourceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$authRemoteDataSourceHash();

  @$internal
  @override
  $ProviderElement<AuthRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AuthRemoteDataSource create(Ref ref) {
    return authRemoteDataSource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AuthRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AuthRemoteDataSource>(value),
    );
  }
}

String _$authRemoteDataSourceHash() =>
    r'8ed35ce88697d3ce56c30652a227770f0b7a1146';

@ProviderFor(bubbleRemoteDataSource)
const bubbleRemoteDataSourceProvider = BubbleRemoteDataSourceProvider._();

final class BubbleRemoteDataSourceProvider
    extends
        $FunctionalProvider<
          BubbleRemoteDataSource,
          BubbleRemoteDataSource,
          BubbleRemoteDataSource
        >
    with $Provider<BubbleRemoteDataSource> {
  const BubbleRemoteDataSourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleRemoteDataSourceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleRemoteDataSourceHash();

  @$internal
  @override
  $ProviderElement<BubbleRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleRemoteDataSource create(Ref ref) {
    return bubbleRemoteDataSource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleRemoteDataSource>(value),
    );
  }
}

String _$bubbleRemoteDataSourceHash() =>
    r'59b6244e9123f52776339a360e24b7f016840182';

@ProviderFor(callRemoteDataSource)
const callRemoteDataSourceProvider = CallRemoteDataSourceProvider._();

final class CallRemoteDataSourceProvider
    extends
        $FunctionalProvider<
          CallRemoteDataSource,
          CallRemoteDataSource,
          CallRemoteDataSource
        >
    with $Provider<CallRemoteDataSource> {
  const CallRemoteDataSourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callRemoteDataSourceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callRemoteDataSourceHash();

  @$internal
  @override
  $ProviderElement<CallRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  CallRemoteDataSource create(Ref ref) {
    return callRemoteDataSource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(CallRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<CallRemoteDataSource>(value),
    );
  }
}

String _$callRemoteDataSourceHash() =>
    r'6edf36eb6f31cfdfc63108679e2917105fca5e5d';

@ProviderFor(presenceRemoteDataSource)
const presenceRemoteDataSourceProvider = PresenceRemoteDataSourceProvider._();

final class PresenceRemoteDataSourceProvider
    extends
        $FunctionalProvider<
          PresenceRemoteDataSource,
          PresenceRemoteDataSource,
          PresenceRemoteDataSource
        >
    with $Provider<PresenceRemoteDataSource> {
  const PresenceRemoteDataSourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'presenceRemoteDataSourceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$presenceRemoteDataSourceHash();

  @$internal
  @override
  $ProviderElement<PresenceRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  PresenceRemoteDataSource create(Ref ref) {
    return presenceRemoteDataSource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PresenceRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<PresenceRemoteDataSource>(value),
    );
  }
}

String _$presenceRemoteDataSourceHash() =>
    r'ac1ac28703da0415c34fd9a32ceecb6d5163c4d0';

@ProviderFor(realtimeRemoteDataSource)
const realtimeRemoteDataSourceProvider = RealtimeRemoteDataSourceProvider._();

final class RealtimeRemoteDataSourceProvider
    extends
        $FunctionalProvider<
          RealtimeRemoteDataSource,
          RealtimeRemoteDataSource,
          RealtimeRemoteDataSource
        >
    with $Provider<RealtimeRemoteDataSource> {
  const RealtimeRemoteDataSourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'realtimeRemoteDataSourceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$realtimeRemoteDataSourceHash();

  @$internal
  @override
  $ProviderElement<RealtimeRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  RealtimeRemoteDataSource create(Ref ref) {
    return realtimeRemoteDataSource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(RealtimeRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<RealtimeRemoteDataSource>(value),
    );
  }
}

String _$realtimeRemoteDataSourceHash() =>
    r'e97200ae9a6e94a94c2fced429ef0db6e0a93518';

@ProviderFor(searchRemoteDataSource)
const searchRemoteDataSourceProvider = SearchRemoteDataSourceProvider._();

final class SearchRemoteDataSourceProvider
    extends
        $FunctionalProvider<
          SearchRemoteDataSource,
          SearchRemoteDataSource,
          SearchRemoteDataSource
        >
    with $Provider<SearchRemoteDataSource> {
  const SearchRemoteDataSourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'searchRemoteDataSourceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$searchRemoteDataSourceHash();

  @$internal
  @override
  $ProviderElement<SearchRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SearchRemoteDataSource create(Ref ref) {
    return searchRemoteDataSource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SearchRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SearchRemoteDataSource>(value),
    );
  }
}

String _$searchRemoteDataSourceHash() =>
    r'af760c8a90716082833cdf5d05414c992e6aa924';

@ProviderFor(syncRemoteDataSource)
const syncRemoteDataSourceProvider = SyncRemoteDataSourceProvider._();

final class SyncRemoteDataSourceProvider
    extends
        $FunctionalProvider<
          SyncRemoteDataSource,
          SyncRemoteDataSource,
          SyncRemoteDataSource
        >
    with $Provider<SyncRemoteDataSource> {
  const SyncRemoteDataSourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncRemoteDataSourceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncRemoteDataSourceHash();

  @$internal
  @override
  $ProviderElement<SyncRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SyncRemoteDataSource create(Ref ref) {
    return syncRemoteDataSource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SyncRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SyncRemoteDataSource>(value),
    );
  }
}

String _$syncRemoteDataSourceHash() =>
    r'23f94f2cc40c35f0b3ac2f264f8aef3e2bebd295';

@ProviderFor(userRemoteDataSource)
const userRemoteDataSourceProvider = UserRemoteDataSourceProvider._();

final class UserRemoteDataSourceProvider
    extends
        $FunctionalProvider<
          UserRemoteDataSource,
          UserRemoteDataSource,
          UserRemoteDataSource
        >
    with $Provider<UserRemoteDataSource> {
  const UserRemoteDataSourceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userRemoteDataSourceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userRemoteDataSourceHash();

  @$internal
  @override
  $ProviderElement<UserRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  UserRemoteDataSource create(Ref ref) {
    return userRemoteDataSource(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserRemoteDataSource>(value),
    );
  }
}

String _$userRemoteDataSourceHash() =>
    r'5ee4879a88c9feaad634915f262416ab848e3b94';

@ProviderFor(authRepository)
const authRepositoryProvider = AuthRepositoryProvider._();

final class AuthRepositoryProvider
    extends $FunctionalProvider<AuthRepository, AuthRepository, AuthRepository>
    with $Provider<AuthRepository> {
  const AuthRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'authRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$authRepositoryHash();

  @$internal
  @override
  $ProviderElement<AuthRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AuthRepository create(Ref ref) {
    return authRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AuthRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AuthRepository>(value),
    );
  }
}

String _$authRepositoryHash() => r'470c99b690ead1be8857668cf09b68f40dac1fb2';

@ProviderFor(bubbleRepository)
const bubbleRepositoryProvider = BubbleRepositoryProvider._();

final class BubbleRepositoryProvider
    extends
        $FunctionalProvider<
          BubbleRepository,
          BubbleRepository,
          BubbleRepository
        >
    with $Provider<BubbleRepository> {
  const BubbleRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleRepositoryHash();

  @$internal
  @override
  $ProviderElement<BubbleRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  BubbleRepository create(Ref ref) {
    return bubbleRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleRepository>(value),
    );
  }
}

String _$bubbleRepositoryHash() => r'da8c8697744d497b8b3231f3d50abe09a98a8331';

@ProviderFor(userRepository)
const userRepositoryProvider = UserRepositoryProvider._();

final class UserRepositoryProvider
    extends $FunctionalProvider<UserRepository, UserRepository, UserRepository>
    with $Provider<UserRepository> {
  const UserRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userRepositoryHash();

  @$internal
  @override
  $ProviderElement<UserRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  UserRepository create(Ref ref) {
    return userRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserRepository>(value),
    );
  }
}

String _$userRepositoryHash() => r'e68977cb77a8533007250363459c8b43d1038d7a';

@ProviderFor(callRepository)
const callRepositoryProvider = CallRepositoryProvider._();

final class CallRepositoryProvider
    extends $FunctionalProvider<CallRepository, CallRepository, CallRepository>
    with $Provider<CallRepository> {
  const CallRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callRepositoryHash();

  @$internal
  @override
  $ProviderElement<CallRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  CallRepository create(Ref ref) {
    return callRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(CallRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<CallRepository>(value),
    );
  }
}

String _$callRepositoryHash() => r'3df44106997d14cf1143f4e83c0a6b3a9c06c470';

@ProviderFor(chatRepository)
const chatRepositoryProvider = ChatRepositoryProvider._();

final class ChatRepositoryProvider
    extends $FunctionalProvider<ChatRepository, ChatRepository, ChatRepository>
    with $Provider<ChatRepository> {
  const ChatRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'chatRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$chatRepositoryHash();

  @$internal
  @override
  $ProviderElement<ChatRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  ChatRepository create(Ref ref) {
    return chatRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ChatRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ChatRepository>(value),
    );
  }
}

String _$chatRepositoryHash() => r'1785382dc22260a7a35469dfe25ff3dc1bfb63b6';

@ProviderFor(contactsRepository)
const contactsRepositoryProvider = ContactsRepositoryProvider._();

final class ContactsRepositoryProvider
    extends
        $FunctionalProvider<
          ContactsRepository,
          ContactsRepository,
          ContactsRepository
        >
    with $Provider<ContactsRepository> {
  const ContactsRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'contactsRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$contactsRepositoryHash();

  @$internal
  @override
  $ProviderElement<ContactsRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ContactsRepository create(Ref ref) {
    return contactsRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ContactsRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ContactsRepository>(value),
    );
  }
}

String _$contactsRepositoryHash() =>
    r'3f9c57cbf0992e043b921ff295c5ab54706e9ab3';

@ProviderFor(friendshipRepository)
const friendshipRepositoryProvider = FriendshipRepositoryProvider._();

final class FriendshipRepositoryProvider
    extends
        $FunctionalProvider<
          FriendshipRepository,
          FriendshipRepository,
          FriendshipRepository
        >
    with $Provider<FriendshipRepository> {
  const FriendshipRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'friendshipRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$friendshipRepositoryHash();

  @$internal
  @override
  $ProviderElement<FriendshipRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  FriendshipRepository create(Ref ref) {
    return friendshipRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(FriendshipRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<FriendshipRepository>(value),
    );
  }
}

String _$friendshipRepositoryHash() =>
    r'761ddf7bbc4974a3aa557db97ea2e34a78a8eb25';

@ProviderFor(friendRequestRepository)
const friendRequestRepositoryProvider = FriendRequestRepositoryProvider._();

final class FriendRequestRepositoryProvider
    extends
        $FunctionalProvider<
          FriendRequestRepository,
          FriendRequestRepository,
          FriendRequestRepository
        >
    with $Provider<FriendRequestRepository> {
  const FriendRequestRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'friendRequestRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$friendRequestRepositoryHash();

  @$internal
  @override
  $ProviderElement<FriendRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  FriendRequestRepository create(Ref ref) {
    return friendRequestRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(FriendRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<FriendRequestRepository>(value),
    );
  }
}

String _$friendRequestRepositoryHash() =>
    r'1d693bcbb138513b351156f06ac90609ba52ab83';

@ProviderFor(presenceRepository)
const presenceRepositoryProvider = PresenceRepositoryProvider._();

final class PresenceRepositoryProvider
    extends
        $FunctionalProvider<
          PresenceRepository,
          PresenceRepository,
          PresenceRepository
        >
    with $Provider<PresenceRepository> {
  const PresenceRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'presenceRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$presenceRepositoryHash();

  @$internal
  @override
  $ProviderElement<PresenceRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  PresenceRepository create(Ref ref) {
    return presenceRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PresenceRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<PresenceRepository>(value),
    );
  }
}

String _$presenceRepositoryHash() =>
    r'780b6b0b390bb2f9bb5d25c305c7b6fb754b80c3';

@ProviderFor(bubbleJoinRequestRepository)
const bubbleJoinRequestRepositoryProvider =
    BubbleJoinRequestRepositoryProvider._();

final class BubbleJoinRequestRepositoryProvider
    extends
        $FunctionalProvider<
          BubbleJoinRequestRepository,
          BubbleJoinRequestRepository,
          BubbleJoinRequestRepository
        >
    with $Provider<BubbleJoinRequestRepository> {
  const BubbleJoinRequestRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleJoinRequestRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleJoinRequestRepositoryHash();

  @$internal
  @override
  $ProviderElement<BubbleJoinRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleJoinRequestRepository create(Ref ref) {
    return bubbleJoinRequestRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleJoinRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleJoinRequestRepository>(value),
    );
  }
}

String _$bubbleJoinRequestRepositoryHash() =>
    r'823275834c00dbc96240b881cbbccc91f176747a';

@ProviderFor(bubbleInviteRequestRepository)
const bubbleInviteRequestRepositoryProvider =
    BubbleInviteRequestRepositoryProvider._();

final class BubbleInviteRequestRepositoryProvider
    extends
        $FunctionalProvider<
          BubbleInviteRequestRepository,
          BubbleInviteRequestRepository,
          BubbleInviteRequestRepository
        >
    with $Provider<BubbleInviteRequestRepository> {
  const BubbleInviteRequestRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleInviteRequestRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleInviteRequestRepositoryHash();

  @$internal
  @override
  $ProviderElement<BubbleInviteRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleInviteRequestRepository create(Ref ref) {
    return bubbleInviteRequestRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleInviteRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleInviteRequestRepository>(
        value,
      ),
    );
  }
}

String _$bubbleInviteRequestRepositoryHash() =>
    r'7c6db117fabb77c1eeac24a6abfe9100fae97f71';

@ProviderFor(bubbleKickoutRepository)
const bubbleKickoutRepositoryProvider = BubbleKickoutRepositoryProvider._();

final class BubbleKickoutRepositoryProvider
    extends
        $FunctionalProvider<
          BubbleKickoutRepository,
          BubbleKickoutRepository,
          BubbleKickoutRepository
        >
    with $Provider<BubbleKickoutRepository> {
  const BubbleKickoutRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleKickoutRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleKickoutRepositoryHash();

  @$internal
  @override
  $ProviderElement<BubbleKickoutRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleKickoutRepository create(Ref ref) {
    return bubbleKickoutRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleKickoutRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleKickoutRepository>(value),
    );
  }
}

String _$bubbleKickoutRepositoryHash() =>
    r'802840dd9c33636dae6fd4e01d20aabba00ec6cf';

@ProviderFor(bubbleHistoryRepository)
const bubbleHistoryRepositoryProvider = BubbleHistoryRepositoryProvider._();

final class BubbleHistoryRepositoryProvider
    extends
        $FunctionalProvider<
          BubbleHistoryRepository,
          BubbleHistoryRepository,
          BubbleHistoryRepository
        >
    with $Provider<BubbleHistoryRepository> {
  const BubbleHistoryRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleHistoryRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleHistoryRepositoryHash();

  @$internal
  @override
  $ProviderElement<BubbleHistoryRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleHistoryRepository create(Ref ref) {
    return bubbleHistoryRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleHistoryRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleHistoryRepository>(value),
    );
  }
}

String _$bubbleHistoryRepositoryHash() =>
    r'425df3b3b8af4c785f99abfebe7955c9ced756d7';

@ProviderFor(activityStatusRepository)
const activityStatusRepositoryProvider = ActivityStatusRepositoryProvider._();

final class ActivityStatusRepositoryProvider
    extends
        $FunctionalProvider<
          ActivityStatusRepository,
          ActivityStatusRepository,
          ActivityStatusRepository
        >
    with $Provider<ActivityStatusRepository> {
  const ActivityStatusRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'activityStatusRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$activityStatusRepositoryHash();

  @$internal
  @override
  $ProviderElement<ActivityStatusRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ActivityStatusRepository create(Ref ref) {
    return activityStatusRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ActivityStatusRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ActivityStatusRepository>(value),
    );
  }
}

String _$activityStatusRepositoryHash() =>
    r'50d98078b55aff95b9cb42ec3726b27a2b6dcdae';

@ProviderFor(audioRecordingRepository)
const audioRecordingRepositoryProvider = AudioRecordingRepositoryProvider._();

final class AudioRecordingRepositoryProvider
    extends
        $FunctionalProvider<
          AudioRecordingRepository,
          AudioRecordingRepository,
          AudioRecordingRepository
        >
    with $Provider<AudioRecordingRepository> {
  const AudioRecordingRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'audioRecordingRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$audioRecordingRepositoryHash();

  @$internal
  @override
  $ProviderElement<AudioRecordingRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AudioRecordingRepository create(Ref ref) {
    return audioRecordingRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AudioRecordingRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AudioRecordingRepository>(value),
    );
  }
}

String _$audioRecordingRepositoryHash() =>
    r'7201038bd33f615ce7f53cff96ed8908d7f0de57';

@ProviderFor(availabilityRepository)
const availabilityRepositoryProvider = AvailabilityRepositoryProvider._();

final class AvailabilityRepositoryProvider
    extends
        $FunctionalProvider<
          AvailabilityRepository,
          AvailabilityRepository,
          AvailabilityRepository
        >
    with $Provider<AvailabilityRepository> {
  const AvailabilityRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availabilityRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availabilityRepositoryHash();

  @$internal
  @override
  $ProviderElement<AvailabilityRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AvailabilityRepository create(Ref ref) {
    return availabilityRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AvailabilityRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AvailabilityRepository>(value),
    );
  }
}

String _$availabilityRepositoryHash() =>
    r'fdef52d84e6c16092a7bec77561567a6205767c9';

@ProviderFor(dialogStateRepository)
const dialogStateRepositoryProvider = DialogStateRepositoryProvider._();

final class DialogStateRepositoryProvider
    extends
        $FunctionalProvider<
          DialogStateRepository,
          DialogStateRepository,
          DialogStateRepository
        >
    with $Provider<DialogStateRepository> {
  const DialogStateRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'dialogStateRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$dialogStateRepositoryHash();

  @$internal
  @override
  $ProviderElement<DialogStateRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  DialogStateRepository create(Ref ref) {
    return dialogStateRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DialogStateRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DialogStateRepository>(value),
    );
  }
}

String _$dialogStateRepositoryHash() =>
    r'1c81c0110ac5645802b8ea5a5bf59e6e9190f7b2';

@ProviderFor(notificationRepository)
const notificationRepositoryProvider = NotificationRepositoryProvider._();

final class NotificationRepositoryProvider
    extends
        $FunctionalProvider<
          NotificationRepository,
          NotificationRepository,
          NotificationRepository
        >
    with $Provider<NotificationRepository> {
  const NotificationRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationRepositoryHash();

  @$internal
  @override
  $ProviderElement<NotificationRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationRepository create(Ref ref) {
    return notificationRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationRepository>(value),
    );
  }
}

String _$notificationRepositoryHash() =>
    r'915d9b56698b271eeb5acb841543f334d0b55113';

@ProviderFor(notificationPermissionRepository)
const notificationPermissionRepositoryProvider =
    NotificationPermissionRepositoryProvider._();

final class NotificationPermissionRepositoryProvider
    extends
        $FunctionalProvider<
          NotificationPermissionRepository,
          NotificationPermissionRepository,
          NotificationPermissionRepository
        >
    with $Provider<NotificationPermissionRepository> {
  const NotificationPermissionRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationPermissionRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationPermissionRepositoryHash();

  @$internal
  @override
  $ProviderElement<NotificationPermissionRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationPermissionRepository create(Ref ref) {
    return notificationPermissionRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationPermissionRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationPermissionRepository>(
        value,
      ),
    );
  }
}

String _$notificationPermissionRepositoryHash() =>
    r'be2e198d362ee408c71e939be43267f94142194e';

@ProviderFor(profilePictureRepository)
const profilePictureRepositoryProvider = ProfilePictureRepositoryProvider._();

final class ProfilePictureRepositoryProvider
    extends
        $FunctionalProvider<
          ProfilePictureRepository,
          ProfilePictureRepository,
          ProfilePictureRepository
        >
    with $Provider<ProfilePictureRepository> {
  const ProfilePictureRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePictureRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePictureRepositoryHash();

  @$internal
  @override
  $ProviderElement<ProfilePictureRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ProfilePictureRepository create(Ref ref) {
    return profilePictureRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ProfilePictureRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ProfilePictureRepository>(value),
    );
  }
}

String _$profilePictureRepositoryHash() =>
    r'9b1758a286bfd2608452e0cf2d2aa053601773ee';

@ProviderFor(reportRepository)
const reportRepositoryProvider = ReportRepositoryProvider._();

final class ReportRepositoryProvider
    extends
        $FunctionalProvider<
          ReportRepository,
          ReportRepository,
          ReportRepository
        >
    with $Provider<ReportRepository> {
  const ReportRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'reportRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$reportRepositoryHash();

  @$internal
  @override
  $ProviderElement<ReportRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  ReportRepository create(Ref ref) {
    return reportRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ReportRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ReportRepository>(value),
    );
  }
}

String _$reportRepositoryHash() => r'5486ed9309126151c0df5428d48d4a3fcf00ee41';

@ProviderFor(syncRepository)
const syncRepositoryProvider = SyncRepositoryProvider._();

final class SyncRepositoryProvider
    extends $FunctionalProvider<SyncRepository, SyncRepository, SyncRepository>
    with $Provider<SyncRepository> {
  const SyncRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncRepositoryHash();

  @$internal
  @override
  $ProviderElement<SyncRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  SyncRepository create(Ref ref) {
    return syncRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SyncRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SyncRepository>(value),
    );
  }
}

String _$syncRepositoryHash() => r'124bcc41ee27dd73a4499f18408385431a3df956';

@ProviderFor(userSettingsRepository)
const userSettingsRepositoryProvider = UserSettingsRepositoryProvider._();

final class UserSettingsRepositoryProvider
    extends
        $FunctionalProvider<
          UserSettingsRepository,
          UserSettingsRepository,
          UserSettingsRepository
        >
    with $Provider<UserSettingsRepository> {
  const UserSettingsRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userSettingsRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userSettingsRepositoryHash();

  @$internal
  @override
  $ProviderElement<UserSettingsRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  UserSettingsRepository create(Ref ref) {
    return userSettingsRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserSettingsRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserSettingsRepository>(value),
    );
  }
}

String _$userSettingsRepositoryHash() =>
    r'c4a0d6e9a9135255b03cfd743142c164746a4e6a';

@ProviderFor(localStorageService)
const localStorageServiceProvider = LocalStorageServiceProvider._();

final class LocalStorageServiceProvider
    extends
        $FunctionalProvider<
          LocalStorageService,
          LocalStorageService,
          LocalStorageService
        >
    with $Provider<LocalStorageService> {
  const LocalStorageServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'localStorageServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$localStorageServiceHash();

  @$internal
  @override
  $ProviderElement<LocalStorageService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  LocalStorageService create(Ref ref) {
    return localStorageService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(LocalStorageService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<LocalStorageService>(value),
    );
  }
}

String _$localStorageServiceHash() =>
    r'6525ccaf39abf6ac2b1ef94fb09e75db9480f7d1';

@ProviderFor(audioRecordingService)
const audioRecordingServiceProvider = AudioRecordingServiceProvider._();

final class AudioRecordingServiceProvider
    extends
        $FunctionalProvider<
          AudioRecordingService,
          AudioRecordingService,
          AudioRecordingService
        >
    with $Provider<AudioRecordingService> {
  const AudioRecordingServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'audioRecordingServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$audioRecordingServiceHash();

  @$internal
  @override
  $ProviderElement<AudioRecordingService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AudioRecordingService create(Ref ref) {
    return audioRecordingService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AudioRecordingService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AudioRecordingService>(value),
    );
  }
}

String _$audioRecordingServiceHash() =>
    r'fcb29b77a0f56501a5dc0d94f04881341f0318e9';

@ProviderFor(availabilityService)
const availabilityServiceProvider = AvailabilityServiceProvider._();

final class AvailabilityServiceProvider
    extends
        $FunctionalProvider<
          AvailabilityService,
          AvailabilityService,
          AvailabilityService
        >
    with $Provider<AvailabilityService> {
  const AvailabilityServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availabilityServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availabilityServiceHash();

  @$internal
  @override
  $ProviderElement<AvailabilityService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AvailabilityService create(Ref ref) {
    return availabilityService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AvailabilityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AvailabilityService>(value),
    );
  }
}

String _$availabilityServiceHash() =>
    r'ba6950a147520f3ea5b5ff421a5b91f172eb0e60';

@ProviderFor(backendAuthService)
const backendAuthServiceProvider = BackendAuthServiceProvider._();

final class BackendAuthServiceProvider
    extends
        $FunctionalProvider<
          BackendAuthService,
          BackendAuthService,
          BackendAuthService
        >
    with $Provider<BackendAuthService> {
  const BackendAuthServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'backendAuthServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$backendAuthServiceHash();

  @$internal
  @override
  $ProviderElement<BackendAuthService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BackendAuthService create(Ref ref) {
    return backendAuthService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BackendAuthService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BackendAuthService>(value),
    );
  }
}

String _$backendAuthServiceHash() =>
    r'ed4bf7f7770c2b81fb0f72c8ce5c14eb48c339ff';

@ProviderFor(kratosAuthService)
const kratosAuthServiceProvider = KratosAuthServiceProvider._();

final class KratosAuthServiceProvider
    extends
        $FunctionalProvider<
          KratosAuthService,
          KratosAuthService,
          KratosAuthService
        >
    with $Provider<KratosAuthService> {
  const KratosAuthServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'kratosAuthServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$kratosAuthServiceHash();

  @$internal
  @override
  $ProviderElement<KratosAuthService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  KratosAuthService create(Ref ref) {
    return kratosAuthService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(KratosAuthService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<KratosAuthService>(value),
    );
  }
}

String _$kratosAuthServiceHash() => r'5445f2c390e2eeac88194400da5471e9f2642a55';

@ProviderFor(platformCallHandler)
const platformCallHandlerProvider = PlatformCallHandlerProvider._();

final class PlatformCallHandlerProvider
    extends
        $FunctionalProvider<
          PlatformCallHandler,
          PlatformCallHandler,
          PlatformCallHandler
        >
    with $Provider<PlatformCallHandler> {
  const PlatformCallHandlerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'platformCallHandlerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$platformCallHandlerHash();

  @$internal
  @override
  $ProviderElement<PlatformCallHandler> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  PlatformCallHandler create(Ref ref) {
    return platformCallHandler(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PlatformCallHandler value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<PlatformCallHandler>(value),
    );
  }
}

String _$platformCallHandlerHash() =>
    r'36c15731ef3458845e756e840a4df1fd19976907';

@ProviderFor(connectivityService)
const connectivityServiceProvider = ConnectivityServiceProvider._();

final class ConnectivityServiceProvider
    extends
        $FunctionalProvider<
          ConnectivityService,
          ConnectivityService,
          ConnectivityService
        >
    with $Provider<ConnectivityService> {
  const ConnectivityServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'connectivityServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$connectivityServiceHash();

  @$internal
  @override
  $ProviderElement<ConnectivityService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ConnectivityService create(Ref ref) {
    return connectivityService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ConnectivityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ConnectivityService>(value),
    );
  }
}

String _$connectivityServiceHash() =>
    r'7d8643f65cf5ec40a8aab54860bd76686712274d';

@ProviderFor(notificationServiceFcm)
const notificationServiceFcmProvider = NotificationServiceFcmProvider._();

final class NotificationServiceFcmProvider
    extends
        $FunctionalProvider<
          NotificationServiceFcm,
          NotificationServiceFcm,
          NotificationServiceFcm
        >
    with $Provider<NotificationServiceFcm> {
  const NotificationServiceFcmProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationServiceFcmProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationServiceFcmHash();

  @$internal
  @override
  $ProviderElement<NotificationServiceFcm> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationServiceFcm create(Ref ref) {
    return notificationServiceFcm(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationServiceFcm value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationServiceFcm>(value),
    );
  }
}

String _$notificationServiceFcmHash() =>
    r'4b816a86539ce51c801c83457d4d85d1f6f17f33';

@ProviderFor(languageService)
const languageServiceProvider = LanguageServiceProvider._();

final class LanguageServiceProvider
    extends
        $FunctionalProvider<LanguageService, LanguageService, LanguageService>
    with $Provider<LanguageService> {
  const LanguageServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'languageServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$languageServiceHash();

  @$internal
  @override
  $ProviderElement<LanguageService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  LanguageService create(Ref ref) {
    return languageService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(LanguageService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<LanguageService>(value),
    );
  }
}

String _$languageServiceHash() => r'9e1f2433da670bab41bb996e1ebe489254c2394e';

@ProviderFor(mediaUploadService)
const mediaUploadServiceProvider = MediaUploadServiceProvider._();

final class MediaUploadServiceProvider
    extends
        $FunctionalProvider<
          MediaUploadService,
          MediaUploadService,
          MediaUploadService
        >
    with $Provider<MediaUploadService> {
  const MediaUploadServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mediaUploadServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mediaUploadServiceHash();

  @$internal
  @override
  $ProviderElement<MediaUploadService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  MediaUploadService create(Ref ref) {
    return mediaUploadService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MediaUploadService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MediaUploadService>(value),
    );
  }
}

String _$mediaUploadServiceHash() =>
    r'17ca6b9e995b581214fc174e66b2633cb60c0fa5';

@ProviderFor(mqttEventHandler)
const mqttEventHandlerProvider = MqttEventHandlerProvider._();

final class MqttEventHandlerProvider
    extends
        $FunctionalProvider<
          MqttEventHandlerImpl,
          MqttEventHandlerImpl,
          MqttEventHandlerImpl
        >
    with $Provider<MqttEventHandlerImpl> {
  const MqttEventHandlerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mqttEventHandlerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mqttEventHandlerHash();

  @$internal
  @override
  $ProviderElement<MqttEventHandlerImpl> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  MqttEventHandlerImpl create(Ref ref) {
    return mqttEventHandler(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MqttEventHandlerImpl value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MqttEventHandlerImpl>(value),
    );
  }
}

String _$mqttEventHandlerHash() => r'c909e7644da7dc05de311a0c53b89ca88f5db350';

@ProviderFor(mqttRealTimeService)
const mqttRealTimeServiceProvider = MqttRealTimeServiceProvider._();

final class MqttRealTimeServiceProvider
    extends
        $FunctionalProvider<
          MqttOnlyRealTimeService,
          MqttOnlyRealTimeService,
          MqttOnlyRealTimeService
        >
    with $Provider<MqttOnlyRealTimeService> {
  const MqttRealTimeServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mqttRealTimeServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mqttRealTimeServiceHash();

  @$internal
  @override
  $ProviderElement<MqttOnlyRealTimeService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  MqttOnlyRealTimeService create(Ref ref) {
    return mqttRealTimeService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MqttOnlyRealTimeService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MqttOnlyRealTimeService>(value),
    );
  }
}

String _$mqttRealTimeServiceHash() =>
    r'9dd29fa14c864b799c27630334827fcf49c4b971';

@ProviderFor(notificationIntegrationService)
const notificationIntegrationServiceProvider =
    NotificationIntegrationServiceProvider._();

final class NotificationIntegrationServiceProvider
    extends
        $FunctionalProvider<
          NotificationIntegrationService,
          NotificationIntegrationService,
          NotificationIntegrationService
        >
    with $Provider<NotificationIntegrationService> {
  const NotificationIntegrationServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationIntegrationServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationIntegrationServiceHash();

  @$internal
  @override
  $ProviderElement<NotificationIntegrationService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationIntegrationService create(Ref ref) {
    return notificationIntegrationService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationIntegrationService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationIntegrationService>(
        value,
      ),
    );
  }
}

String _$notificationIntegrationServiceHash() =>
    r'a2cc67cd3fef3e2e0dfce53e88d507e9da1643ff';

@ProviderFor(notificationOrchestrator)
const notificationOrchestratorProvider = NotificationOrchestratorProvider._();

final class NotificationOrchestratorProvider
    extends
        $FunctionalProvider<
          NotificationOrchestrator,
          NotificationOrchestrator,
          NotificationOrchestrator
        >
    with $Provider<NotificationOrchestrator> {
  const NotificationOrchestratorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationOrchestratorProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationOrchestratorHash();

  @$internal
  @override
  $ProviderElement<NotificationOrchestrator> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationOrchestrator create(Ref ref) {
    return notificationOrchestrator(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationOrchestrator value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationOrchestrator>(value),
    );
  }
}

String _$notificationOrchestratorHash() =>
    r'cad029e278517c08794e2e160692f62b29c57090';

@ProviderFor(notificationService)
const notificationServiceProvider = NotificationServiceProvider._();

final class NotificationServiceProvider
    extends
        $FunctionalProvider<
          NotificationService,
          NotificationService,
          NotificationService
        >
    with $Provider<NotificationService> {
  const NotificationServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationServiceHash();

  @$internal
  @override
  $ProviderElement<NotificationService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationService create(Ref ref) {
    return notificationService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationService>(value),
    );
  }
}

String _$notificationServiceHash() =>
    r'61a84762889d24f2c037851e2d31838e9238cac6';

@ProviderFor(startupPerformanceService)
const startupPerformanceServiceProvider = StartupPerformanceServiceProvider._();

final class StartupPerformanceServiceProvider
    extends
        $FunctionalProvider<
          StartupPerformanceService,
          StartupPerformanceService,
          StartupPerformanceService
        >
    with $Provider<StartupPerformanceService> {
  const StartupPerformanceServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'startupPerformanceServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$startupPerformanceServiceHash();

  @$internal
  @override
  $ProviderElement<StartupPerformanceService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  StartupPerformanceService create(Ref ref) {
    return startupPerformanceService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(StartupPerformanceService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<StartupPerformanceService>(value),
    );
  }
}

String _$startupPerformanceServiceHash() =>
    r'585ade7555e1589794aaac5b58d8fea3a735aba3';

@ProviderFor(presenceService)
const presenceServiceProvider = PresenceServiceProvider._();

final class PresenceServiceProvider
    extends
        $FunctionalProvider<PresenceService, PresenceService, PresenceService>
    with $Provider<PresenceService> {
  const PresenceServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'presenceServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$presenceServiceHash();

  @$internal
  @override
  $ProviderElement<PresenceService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  PresenceService create(Ref ref) {
    return presenceService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PresenceService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<PresenceService>(value),
    );
  }
}

String _$presenceServiceHash() => r'6cf7edb1e707d6533c21e51f2b4b921e5d5f312a';

@ProviderFor(realTimeServiceManager)
const realTimeServiceManagerProvider = RealTimeServiceManagerProvider._();

final class RealTimeServiceManagerProvider
    extends
        $FunctionalProvider<
          RealTimeServiceManager,
          RealTimeServiceManager,
          RealTimeServiceManager
        >
    with $Provider<RealTimeServiceManager> {
  const RealTimeServiceManagerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'realTimeServiceManagerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$realTimeServiceManagerHash();

  @$internal
  @override
  $ProviderElement<RealTimeServiceManager> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  RealTimeServiceManager create(Ref ref) {
    return realTimeServiceManager(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(RealTimeServiceManager value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<RealTimeServiceManager>(value),
    );
  }
}

String _$realTimeServiceManagerHash() =>
    r'76bf06f612fbcdc2cc4744dbc2d9734f45d39eca';

@ProviderFor(securityService)
const securityServiceProvider = SecurityServiceProvider._();

final class SecurityServiceProvider
    extends
        $FunctionalProvider<SecurityService, SecurityService, SecurityService>
    with $Provider<SecurityService> {
  const SecurityServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'securityServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$securityServiceHash();

  @$internal
  @override
  $ProviderElement<SecurityService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  SecurityService create(Ref ref) {
    return securityService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SecurityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SecurityService>(value),
    );
  }
}

String _$securityServiceHash() => r'705966c0bb12b85f88ab62516c4def6a55590a1a';

@ProviderFor(storageService)
const storageServiceProvider = StorageServiceProvider._();

final class StorageServiceProvider
    extends
        $FunctionalProvider<
          storage.StorageService,
          storage.StorageService,
          storage.StorageService
        >
    with $Provider<storage.StorageService> {
  const StorageServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'storageServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$storageServiceHash();

  @$internal
  @override
  $ProviderElement<storage.StorageService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  storage.StorageService create(Ref ref) {
    return storageService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(storage.StorageService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<storage.StorageService>(value),
    );
  }
}

String _$storageServiceHash() => r'73c2def0523c9459b1d5271a95113f2a80554b0f';

@ProviderFor(userProfilePreloaderService)
const userProfilePreloaderServiceProvider =
    UserProfilePreloaderServiceProvider._();

final class UserProfilePreloaderServiceProvider
    extends
        $FunctionalProvider<
          UserProfilePreloaderService,
          UserProfilePreloaderService,
          UserProfilePreloaderService
        >
    with $Provider<UserProfilePreloaderService> {
  const UserProfilePreloaderServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userProfilePreloaderServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userProfilePreloaderServiceHash();

  @$internal
  @override
  $ProviderElement<UserProfilePreloaderService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  UserProfilePreloaderService create(Ref ref) {
    return userProfilePreloaderService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserProfilePreloaderService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserProfilePreloaderService>(value),
    );
  }
}

String _$userProfilePreloaderServiceHash() =>
    r'b16a44a9cb7d285b2cdd5eab127f57c1760dfba5';

@ProviderFor(webRtcService)
const webRtcServiceProvider = WebRtcServiceProvider._();

final class WebRtcServiceProvider
    extends $FunctionalProvider<WebRtcService, WebRtcService, WebRtcService>
    with $Provider<WebRtcService> {
  const WebRtcServiceProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'webRtcServiceProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$webRtcServiceHash();

  @$internal
  @override
  $ProviderElement<WebRtcService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  WebRtcService create(Ref ref) {
    return webRtcService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(WebRtcService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<WebRtcService>(value),
    );
  }
}

String _$webRtcServiceHash() => r'1167c1483c20c64cfe999ed6fc9d392de1a0f71e';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

--- CONTENT END ---

==========================================

=== FILE: di/modules/auth_module.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/di/modules/auth_module.dart
Last Modified: Sep  6 13:15:39 2025
File Size: 2186 bytes

--- CONTENT START ---
/// Authentication Module for Dependency Injection
/// 
/// Contains all authentication-related providers and their dependencies.
/// This module is part of the Clean Architecture DI layer.

import 'package:riverpod_annotation/riverpod_annotation.dart';

// Repository interfaces (allowed in DI layer)
import '../../repositories/auth/auth_repository.dart';
import '../../repositories/availability/availability_repository.dart';

// Data layer imports (only allowed in DI layer)
import '../../data/datasources/auth_remote_data_source.dart';
import '../../data/repositories/auth/auth_repository_impl.dart';
import '../../data/repositories/availability/availability_repository_impl.dart';
import '../../data/services/auth/availability_service.dart';
import '../../data/services/auth/backend_auth_service.dart';
import '../../data/services/auth/kratos_auth_service.dart';

// Core DI imports
import '../injection_container.dart';

part 'auth_module.g.dart';

// =============================================================================
// AUTHENTICATION MODULE PROVIDERS
// =============================================================================

@Riverpod(keepAlive: true)
AuthRepository authRepositoryModule(AuthRepositoryModuleRef ref) {
  final remoteDataSource = ref.watch(authRemoteDataSourceProvider);
  return AuthRepositoryImpl(remoteDataSource);
}

@Riverpod(keepAlive: true)
AvailabilityRepository availabilityRepositoryModule(AvailabilityRepositoryModuleRef ref) {
  final availabilityService = ref.watch(availabilityServiceModuleProvider);
  return AvailabilityRepositoryImpl(availabilityService);
}

@Riverpod(keepAlive: true)
AvailabilityService availabilityServiceModule(AvailabilityServiceModuleRef ref) {
  final authService = ref.watch(backendAuthServiceModuleProvider);
  return AvailabilityService(authService);
}

@Riverpod(keepAlive: true)
BackendAuthService backendAuthServiceModule(BackendAuthServiceModuleRef ref) {
  final grpcClient = ref.watch(grpcClientServiceProvider);
  return BackendAuthService(grpcClient);
}

@Riverpod(keepAlive: true)
KratosAuthService kratosAuthServiceModule(KratosAuthServiceModuleRef ref) {
  return KratosAuthService();
}

--- CONTENT END ---

==========================================

=== FILE: di/modules/auth_module.g.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/di/modules/auth_module.g.dart
Last Modified: Sep  6 14:46:42 2025
File Size: 6828 bytes

--- CONTENT START ---
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(authRepositoryModule)
const authRepositoryModuleProvider = AuthRepositoryModuleProvider._();

final class AuthRepositoryModuleProvider
    extends $FunctionalProvider<AuthRepository, AuthRepository, AuthRepository>
    with $Provider<AuthRepository> {
  const AuthRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'authRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$authRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<AuthRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AuthRepository create(Ref ref) {
    return authRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AuthRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AuthRepository>(value),
    );
  }
}

String _$authRepositoryModuleHash() =>
    r'1e379293d4de5f9cf12aaa054db0da1a352e453b';

@ProviderFor(availabilityRepositoryModule)
const availabilityRepositoryModuleProvider =
    AvailabilityRepositoryModuleProvider._();

final class AvailabilityRepositoryModuleProvider
    extends
        $FunctionalProvider<
          AvailabilityRepository,
          AvailabilityRepository,
          AvailabilityRepository
        >
    with $Provider<AvailabilityRepository> {
  const AvailabilityRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availabilityRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availabilityRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<AvailabilityRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AvailabilityRepository create(Ref ref) {
    return availabilityRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AvailabilityRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AvailabilityRepository>(value),
    );
  }
}

String _$availabilityRepositoryModuleHash() =>
    r'3039d6322a9af590c79f8ae4ed0f7419bf3f0ae7';

@ProviderFor(availabilityServiceModule)
const availabilityServiceModuleProvider = AvailabilityServiceModuleProvider._();

final class AvailabilityServiceModuleProvider
    extends
        $FunctionalProvider<
          AvailabilityService,
          AvailabilityService,
          AvailabilityService
        >
    with $Provider<AvailabilityService> {
  const AvailabilityServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availabilityServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availabilityServiceModuleHash();

  @$internal
  @override
  $ProviderElement<AvailabilityService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AvailabilityService create(Ref ref) {
    return availabilityServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AvailabilityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AvailabilityService>(value),
    );
  }
}

String _$availabilityServiceModuleHash() =>
    r'a71c26a3bc6f6d26a20e169b2a974e67a5af7ad3';

@ProviderFor(backendAuthServiceModule)
const backendAuthServiceModuleProvider = BackendAuthServiceModuleProvider._();

final class BackendAuthServiceModuleProvider
    extends
        $FunctionalProvider<
          BackendAuthService,
          BackendAuthService,
          BackendAuthService
        >
    with $Provider<BackendAuthService> {
  const BackendAuthServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'backendAuthServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$backendAuthServiceModuleHash();

  @$internal
  @override
  $ProviderElement<BackendAuthService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BackendAuthService create(Ref ref) {
    return backendAuthServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BackendAuthService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BackendAuthService>(value),
    );
  }
}

String _$backendAuthServiceModuleHash() =>
    r'283f620d33cea46c8367215b993c66ab0419d59c';

@ProviderFor(kratosAuthServiceModule)
const kratosAuthServiceModuleProvider = KratosAuthServiceModuleProvider._();

final class KratosAuthServiceModuleProvider
    extends
        $FunctionalProvider<
          KratosAuthService,
          KratosAuthService,
          KratosAuthService
        >
    with $Provider<KratosAuthService> {
  const KratosAuthServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'kratosAuthServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$kratosAuthServiceModuleHash();

  @$internal
  @override
  $ProviderElement<KratosAuthService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  KratosAuthService create(Ref ref) {
    return kratosAuthServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(KratosAuthService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<KratosAuthService>(value),
    );
  }
}

String _$kratosAuthServiceModuleHash() =>
    r'f1b06310eb27925cf6b649a0baf2dcf59a608932';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

--- CONTENT END ---

==========================================

=== FILE: di/modules/bubble_module.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/di/modules/bubble_module.dart
Last Modified: Sep  6 13:15:52 2025
File Size: 3204 bytes

--- CONTENT START ---
/// Bubble Module for Dependency Injection
/// 
/// Contains all bubble-related providers and their dependencies.
/// This module is part of the Clean Architecture DI layer.

import 'package:riverpod_annotation/riverpod_annotation.dart';

// Repository interfaces (allowed in DI layer)
import '../../repositories/bubble/bubble_repository.dart';
import '../../repositories/bubble/bubble_join_request_repository.dart';
import '../../repositories/bubble/bubble_invite_request_repository.dart';
import '../../repositories/bubble/bubble_kickout_repository.dart';
import '../../repositories/bubble_history/bubble_history_repository.dart';

// Data layer imports (only allowed in DI layer)
import '../../data/datasources/bubble_remote_data_source.dart';
import '../../data/repositories/bubble/cached_bubble_repository_impl.dart';
import '../../data/repositories/bubble/cached_bubble_join_request_repository_impl.dart';
import '../../data/repositories/bubble/cached_bubble_invite_request_repository_impl.dart';
import '../../data/repositories/bubble/cached_bubble_kickout_repository_impl.dart';
import '../../data/repositories/bubble_history/cached_bubble_history_repository_impl.dart';

// Core DI imports
import '../injection_container.dart';

part 'bubble_module.g.dart';

// =============================================================================
// BUBBLE MODULE PROVIDERS
// =============================================================================

@Riverpod(keepAlive: true)
BubbleRepository bubbleRepositoryModule(BubbleRepositoryModuleRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
BubbleJoinRequestRepository bubbleJoinRequestRepositoryModule(BubbleJoinRequestRepositoryModuleRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleJoinRequestRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
BubbleInviteRequestRepository bubbleInviteRequestRepositoryModule(BubbleInviteRequestRepositoryModuleRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleInviteRequestRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
BubbleKickoutRepository bubbleKickoutRepositoryModule(BubbleKickoutRepositoryModuleRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleKickoutRepositoryImpl(remoteDataSource, localStorageService);
}

@Riverpod(keepAlive: true)
BubbleHistoryRepository bubbleHistoryRepositoryModule(BubbleHistoryRepositoryModuleRef ref) {
  final remoteDataSource = ref.watch(bubbleRemoteDataSourceProvider);
  final localStorageService = ref.watch(localStorageServiceProvider);
  return CachedBubbleHistoryRepositoryImpl(remoteDataSource, localStorageService);
}

--- CONTENT END ---

==========================================

=== FILE: di/modules/bubble_module.g.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/di/modules/bubble_module.g.dart
Last Modified: Sep  6 14:46:42 2025
File Size: 7395 bytes

--- CONTENT START ---
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bubble_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(bubbleRepositoryModule)
const bubbleRepositoryModuleProvider = BubbleRepositoryModuleProvider._();

final class BubbleRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleRepository,
          BubbleRepository,
          BubbleRepository
        >
    with $Provider<BubbleRepository> {
  const BubbleRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  BubbleRepository create(Ref ref) {
    return bubbleRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleRepository>(value),
    );
  }
}

String _$bubbleRepositoryModuleHash() =>
    r'f49b8801ca601849008f8c27dbcdca3a2c3dc573';

@ProviderFor(bubbleJoinRequestRepositoryModule)
const bubbleJoinRequestRepositoryModuleProvider =
    BubbleJoinRequestRepositoryModuleProvider._();

final class BubbleJoinRequestRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleJoinRequestRepository,
          BubbleJoinRequestRepository,
          BubbleJoinRequestRepository
        >
    with $Provider<BubbleJoinRequestRepository> {
  const BubbleJoinRequestRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleJoinRequestRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$bubbleJoinRequestRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleJoinRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleJoinRequestRepository create(Ref ref) {
    return bubbleJoinRequestRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleJoinRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleJoinRequestRepository>(value),
    );
  }
}

String _$bubbleJoinRequestRepositoryModuleHash() =>
    r'5945137143eaf490fd809abcd03fe5edbd5d3c74';

@ProviderFor(bubbleInviteRequestRepositoryModule)
const bubbleInviteRequestRepositoryModuleProvider =
    BubbleInviteRequestRepositoryModuleProvider._();

final class BubbleInviteRequestRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleInviteRequestRepository,
          BubbleInviteRequestRepository,
          BubbleInviteRequestRepository
        >
    with $Provider<BubbleInviteRequestRepository> {
  const BubbleInviteRequestRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleInviteRequestRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$bubbleInviteRequestRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleInviteRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleInviteRequestRepository create(Ref ref) {
    return bubbleInviteRequestRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleInviteRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleInviteRequestRepository>(
        value,
      ),
    );
  }
}

String _$bubbleInviteRequestRepositoryModuleHash() =>
    r'be96025bb83034f4af90d36f9f8c396efc5a8943';

@ProviderFor(bubbleKickoutRepositoryModule)
const bubbleKickoutRepositoryModuleProvider =
    BubbleKickoutRepositoryModuleProvider._();

final class BubbleKickoutRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleKickoutRepository,
          BubbleKickoutRepository,
          BubbleKickoutRepository
        >
    with $Provider<BubbleKickoutRepository> {
  const BubbleKickoutRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleKickoutRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleKickoutRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleKickoutRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleKickoutRepository create(Ref ref) {
    return bubbleKickoutRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleKickoutRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleKickoutRepository>(value),
    );
  }
}

String _$bubbleKickoutRepositoryModuleHash() =>
    r'd506cc2afc094d6e58dec791998e9add29934697';

@ProviderFor(bubbleHistoryRepositoryModule)
const bubbleHistoryRepositoryModuleProvider =
    BubbleHistoryRepositoryModuleProvider._();

final class BubbleHistoryRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleHistoryRepository,
          BubbleHistoryRepository,
          BubbleHistoryRepository
        >
    with $Provider<BubbleHistoryRepository> {
  const BubbleHistoryRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleHistoryRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleHistoryRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleHistoryRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleHistoryRepository create(Ref ref) {
    return bubbleHistoryRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleHistoryRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleHistoryRepository>(value),
    );
  }
}

String _$bubbleHistoryRepositoryModuleHash() =>
    r'dc8ad4e7f38fae799732195dcd56ccd8d31337ef';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

--- CONTENT END ---

==========================================

=== FILE: di/modules/core_module.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/di/modules/core_module.dart
Last Modified: Sep  6 13:16:03 2025
File Size: 1914 bytes

--- CONTENT START ---
/// Core Module for Dependency Injection
/// 
/// Contains core services and utilities that are used across the application.
/// This module is part of the Clean Architecture DI layer.

import 'package:riverpod_annotation/riverpod_annotation.dart';

// Data layer imports (only allowed in DI layer)
import '../../data/services/api/grpc_client_service.dart';
import '../../data/services/connectivity/connectivity_service.dart';
import '../../data/services/local_storage/local_storage_service.dart';
import '../../data/services/security/security_service.dart';
import '../../data/services/performance/startup_performance_service.dart';
import '../../data/repositories/resilience/resilience_repository_impl.dart';

// Repository interfaces (allowed in DI layer)
import '../../repositories/resilience/resilience_repository.dart';

part 'core_module.g.dart';

// =============================================================================
// CORE MODULE PROVIDERS
// =============================================================================

@Riverpod(keepAlive: true)
GrpcClientService grpcClientServiceModule(GrpcClientServiceModuleRef ref) {
  return GrpcClientService();
}

@Riverpod(keepAlive: true)
ConnectivityService connectivityServiceModule(ConnectivityServiceModuleRef ref) {
  return ConnectivityService();
}

@Riverpod(keepAlive: true)
LocalStorageService localStorageServiceModule(LocalStorageServiceModuleRef ref) {
  return LocalStorageService();
}

@Riverpod(keepAlive: true)
SecurityService securityServiceModule(SecurityServiceModuleRef ref) {
  return SecurityService();
}

@Riverpod(keepAlive: true)
StartupPerformanceService startupPerformanceServiceModule(StartupPerformanceServiceModuleRef ref) {
  return StartupPerformanceService();
}

@Riverpod(keepAlive: true)
ResilienceRepository resilienceRepositoryModule(ResilienceRepositoryModuleRef ref) {
  return ResilienceRepositoryImpl();
}

--- CONTENT END ---

==========================================

=== FILE: di/modules/core_module.g.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/di/modules/core_module.g.dart
Last Modified: Sep  6 14:46:42 2025
File Size: 8246 bytes

--- CONTENT START ---
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'core_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(grpcClientServiceModule)
const grpcClientServiceModuleProvider = GrpcClientServiceModuleProvider._();

final class GrpcClientServiceModuleProvider
    extends
        $FunctionalProvider<
          GrpcClientService,
          GrpcClientService,
          GrpcClientService
        >
    with $Provider<GrpcClientService> {
  const GrpcClientServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'grpcClientServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$grpcClientServiceModuleHash();

  @$internal
  @override
  $ProviderElement<GrpcClientService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  GrpcClientService create(Ref ref) {
    return grpcClientServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(GrpcClientService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<GrpcClientService>(value),
    );
  }
}

String _$grpcClientServiceModuleHash() =>
    r'd8804b55e65baf3cbb924359069d46d1ab124d62';

@ProviderFor(connectivityServiceModule)
const connectivityServiceModuleProvider = ConnectivityServiceModuleProvider._();

final class ConnectivityServiceModuleProvider
    extends
        $FunctionalProvider<
          ConnectivityService,
          ConnectivityService,
          ConnectivityService
        >
    with $Provider<ConnectivityService> {
  const ConnectivityServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'connectivityServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$connectivityServiceModuleHash();

  @$internal
  @override
  $ProviderElement<ConnectivityService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ConnectivityService create(Ref ref) {
    return connectivityServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ConnectivityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ConnectivityService>(value),
    );
  }
}

String _$connectivityServiceModuleHash() =>
    r'1ee93eff212f361fe954a9b85a205bbdd9e928e9';

@ProviderFor(localStorageServiceModule)
const localStorageServiceModuleProvider = LocalStorageServiceModuleProvider._();

final class LocalStorageServiceModuleProvider
    extends
        $FunctionalProvider<
          LocalStorageService,
          LocalStorageService,
          LocalStorageService
        >
    with $Provider<LocalStorageService> {
  const LocalStorageServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'localStorageServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$localStorageServiceModuleHash();

  @$internal
  @override
  $ProviderElement<LocalStorageService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  LocalStorageService create(Ref ref) {
    return localStorageServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(LocalStorageService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<LocalStorageService>(value),
    );
  }
}

String _$localStorageServiceModuleHash() =>
    r'983b614b1e90872d9d1e829a15a082a2fa5da542';

@ProviderFor(securityServiceModule)
const securityServiceModuleProvider = SecurityServiceModuleProvider._();

final class SecurityServiceModuleProvider
    extends
        $FunctionalProvider<SecurityService, SecurityService, SecurityService>
    with $Provider<SecurityService> {
  const SecurityServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'securityServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$securityServiceModuleHash();

  @$internal
  @override
  $ProviderElement<SecurityService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  SecurityService create(Ref ref) {
    return securityServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SecurityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SecurityService>(value),
    );
  }
}

String _$securityServiceModuleHash() =>
    r'15bc727fb5077e04872e2a495290e608c0ebae9d';

@ProviderFor(startupPerformanceServiceModule)
const startupPerformanceServiceModuleProvider =
    StartupPerformanceServiceModuleProvider._();

final class StartupPerformanceServiceModuleProvider
    extends
        $FunctionalProvider<
          StartupPerformanceService,
          StartupPerformanceService,
          StartupPerformanceService
        >
    with $Provider<StartupPerformanceService> {
  const StartupPerformanceServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'startupPerformanceServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$startupPerformanceServiceModuleHash();

  @$internal
  @override
  $ProviderElement<StartupPerformanceService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  StartupPerformanceService create(Ref ref) {
    return startupPerformanceServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(StartupPerformanceService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<StartupPerformanceService>(value),
    );
  }
}

String _$startupPerformanceServiceModuleHash() =>
    r'd65d54380313837da6772121e9516cd8eecbf9a0';

@ProviderFor(resilienceRepositoryModule)
const resilienceRepositoryModuleProvider =
    ResilienceRepositoryModuleProvider._();

final class ResilienceRepositoryModuleProvider
    extends
        $FunctionalProvider<
          ResilienceRepository,
          ResilienceRepository,
          ResilienceRepository
        >
    with $Provider<ResilienceRepository> {
  const ResilienceRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'resilienceRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$resilienceRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<ResilienceRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ResilienceRepository create(Ref ref) {
    return resilienceRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ResilienceRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ResilienceRepository>(value),
    );
  }
}

String _$resilienceRepositoryModuleHash() =>
    r'18246cc94fc26cae28e1cdbd985acb6829428eea';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

--- CONTENT END ---

==========================================

=== FILE: main.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/main.dart
Last Modified: Sep  6 00:20:44 2025
File Size: 53180 bytes

--- CONTENT START ---
import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


import 'data/services/auth/kratos_auth_service.dart';
import 'data/services/config/centralized_config_service.dart';
import 'data/services/core/app_context_manager.dart';
import 'data/services/fcm/firebase_initialization_service.dart';
import 'presentation/providers/shader_provider.dart';
import 'presentation/router/app_router.dart' as app_router;
import 'presentation/router/app_router.dart';
import 'presentation/widgets/connectivity_toast_listener.dart';
import 'presentation/widgets/error/fatal_error_screen.dart';
import 'presentation/widgets/requests/bubble_invite_request_dialog.dart';
import 'presentation/widgets/requests/bubble_join_request_dialog.dart';
import 'presentation/widgets/requests/bubble_kickout_request_dialog.dart';
import 'presentation/widgets/requests/bubble_propose_request_dialog.dart';
import 'presentation/widgets/requests/bubble_start_request_dialog.dart';
import 'presentation/widgets/requests/contact_request_dialog.dart';
import 'presentation/theme/app_theme.dart';
import 'presentation/widgets/requests/friends_choice_dialog.dart';
import 'statefulbusinesslogic/core/events/dialog_event.dart';
import 'statefulbusinesslogic/core/models/bubble_request_model.dart';
import 'statefulbusinesslogic/core/models/contact_request.dart';
import 'statefulbusinesslogic/core/models/user_model.dart';
import 'statefulbusinesslogic/core/models/value_objects.dart';
import 'presentation/theme/app_theme.dart';
import 'statefulbusinesslogic/core/services/logging_service.dart';
import 'statefulbusinesslogic/core/utils/bubble_member_converter.dart';
// Riverpod 3.0 providers
import 'statefulbusinesslogic/riverpod/providers.dart';

// Global error display key for catching unhandled errors
final GlobalKey<ScaffoldMessengerState> rootScaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

// Assume 'router' is your GoRouter instance, configured with navigatorKey
// This might be initialized in a separate file and imported, or here directly.
// For this example, we will assume 'router' is passed to MyApp after being configured.

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Register FCM background handler at bootstrap (guard for non-web platforms)
  if (!kIsWeb) {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  // Force portrait orientation globally by default.
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // Initialize and validate configuration with fail-fast behavior
  final configService = CentralizedConfigService();

  try {
    // Validate all configurations before proceeding
    configService.validateAll();
    LoggingService.success('✅ Configuration validation passed');
  } on StateError catch (e) {
    LoggingService.failure('❌ CRITICAL: Configuration validation failed');
    LoggingService.failure('Error: ${e.message}');
    LoggingService.failure('The application cannot start with invalid configuration.');
    LoggingService.failure('Please check your environment variables and configuration settings.');

    // Show error dialog and exit
    runApp(ConfigurationErrorApp(error: e.message));
    return;
  } on Exception catch (e) {
    LoggingService.failure('❌ CRITICAL: Unexpected error during configuration validation');
    LoggingService.failure('Error: $e');

    // Show error dialog and exit
    runApp(ConfigurationErrorApp(error: e.toString()));
    return;
  }

  // Print configuration for debugging (only after validation passes)
  LoggingService.info('=== App Configuration ===');
  LoggingService.info('Environment: ${configService.environment}');
  LoggingService.info('Host IP: ${configService.effectiveHostIP}');
  LoggingService.info('gRPC Config: ${configService.grpcConfig.fullUrl}');
  LoggingService.info('MinIO Config: ${configService.minioConfig.fullUrl}');
  LoggingService.info('========================');

  // Set up global error handler before any async operations
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    LoggingService.error(
      'Flutter error: ${details.exception}',
      error: details.exception,
      stackTrace: details.stack,
    );

    // Show a visible error indicator on the app after the current frame
    SchedulerBinding.instance.addPostFrameCallback((_) {
      try {
        if (rootScaffoldMessengerKey.currentState?.mounted ?? false) {
          rootScaffoldMessengerKey.currentState?.showSnackBar(
            SnackBar(
              content: Text('Error: ${details.exception}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 10),
            ),
          );
        }
      } on Exception catch (e) {
        LoggingService.warning('Failed to show error snackbar: $e');
      }
    });
  };

  // ✅ ENABLED: Ory Stack authentication initialization
  try {
    await KratosAuthService().initialize();
    LoggingService.success('Ory Stack authentication initialized successfully');
    final kratosUrl = configService.isDevelopment
        ? 'http://${configService.effectiveHostIP}:4433'
        : configService.isStaging
            ? 'https://auth.staging.hopen.app'
            : 'https://auth.hopenapp.com';
    final hydraUrl = configService.isDevelopment
        ? 'http://${configService.effectiveHostIP}:4444'
        : configService.isStaging
            ? 'https://oauth.staging.hopen.app'
            : 'https://oauth.hopenapp.com';
    LoggingService.info('   Kratos URL: $kratosUrl');
    LoggingService.info('   Hydra URL: $hydraUrl');
  } on Exception catch (e, s) {
    LoggingService.failure(
      'FAILED to initialize Ory Stack',
      error: e,
      stackTrace: s,
    );
    // Continue without Ory Stack if it fails
  }

  // Initialize Firebase using the centralized service
  final firebaseService = FirebaseInitializationService();
  var firebaseInitialized = false;

  if (kIsWeb) {
    // Web configuration - using Firebase project ID from environment
    const firebaseProjectId = String.fromEnvironment(
      'FIREBASE_PROJECT_ID',
      defaultValue: 'hopen-id',
    );
    firebaseInitialized = await firebaseService.initialize(
      options: const FirebaseOptions(
        apiKey:
            'AIzaSyBqJVJKQQQQQQQQQQQQQQQQQQQQQQQQQQQ', // Replace with your actual web API key
        authDomain: '$firebaseProjectId.firebaseapp.com',
        projectId: firebaseProjectId,
        storageBucket: '$firebaseProjectId.appspot.com',
        messagingSenderId: '257996495540', // Your messaging sender ID
        appId:
            '1:257996495540:web:your-web-app-id', // Replace with your actual web app ID
      ),
    );
  } else {
    // Mobile platforms - use default configuration
    firebaseInitialized = await firebaseService.initialize();
  }

  if (firebaseInitialized) {
    LoggingService.success('Firebase initialized successfully');
  } else {
    LoggingService.warning(
      'Firebase initialization failed: ${firebaseService.initializationError}',
    );
    LoggingService.info('App will continue without Firebase features');
  }

  // Initialize dependency injection with retry mechanism
  var diInitialized = false;
  var retryCount = 0;
  const maxRetries = 3;
  
  while (!diInitialized && retryCount < maxRetries) {
    try {
      // Initialize pure Riverpod app (performance monitoring starts here)
      final container = ProviderContainer();
      await container.read(appInitializerProvider.future);
      LoggingService.success('Pure Riverpod app initialization completed successfully');

      // MQTT telemetry is built-in via getHealthMetrics() method

      // Profile startup phases - mark dependency injection complete
      container.read(startupPerformanceServiceProvider).completeOperation('dependency_injection');
      LoggingService.success('All dependencies are ready');
      
      diInitialized = true;

    // Router will be created in the app widget with Riverpod context
    LoggingService.info('Router will be created with Riverpod context');

    // ActivityStatusService already initialized through AppInitializer
    try {
      LoggingService.info('ActivityStatusService already initialized through Riverpod');
      LoggingService.success('ActivityStatusService ready');
    } on Exception catch (e, stackTrace) {
      LoggingService.failure(
        'FAILED TO INITIALIZE ActivityStatusService in main()',
        error: e,
        stackTrace: stackTrace,
        tag: 'ActivityStatus',
      );
      LoggingService.warning(
        'Activity status functionality will not work properly',
      );
    }
    } on Exception catch (e, s) {
      retryCount++;
      LoggingService.failure(
        'FAILED to initialize dependency injection (attempt $retryCount/$maxRetries)',
        error: e,
        stackTrace: s,
      );
      
      if (retryCount >= maxRetries) {
        // Show fatal error screen after max retries
        runApp(FatalErrorScreen(
          error: e,
          onRetry: main, // Allow user to retry
        ));
        return;
      }
      
      // Wait before retrying
      await Future<void>.delayed(Duration(seconds: retryCount * 2));
    }
  }
  
  if (!diInitialized) {
    // This shouldn't happen, but handle it gracefully
    runApp(FatalErrorScreen(
      error: Exception('Failed to initialize after $maxRetries attempts'),
      onRetry: main,
    ));
    return;
  }

  // IncomingCallListener functionality is now integrated into RealTimeNotificationService
  if (kDebugMode) {
    LoggingService.success(
      'Call notifications are handled by RealTimeNotificationService',
    );
  }

  // Initialize platform call handler for native incoming calls
  if (!kIsWeb) {
    try {
      LoggingService.info(
        'PlatformCallHandler already initialized through Riverpod AppInitializer',
      );
      LoggingService.success('PlatformCallHandler ready');
    } on Exception catch (e, stackTrace) {
      LoggingService.failure(
        'FAILED TO INITIALIZE PlatformCallHandler in main()',
        error: e,
        stackTrace: stackTrace,
        tag: 'PlatformCalls',
      );
      LoggingService.warning('Background call handling will not work properly');
    }
  }

  // Note: NotificationServiceFCM is now initialized through dependency injection
  // when first accessed, ensuring proper Firebase initialization timing

  // RealTimeServiceManager already initialized through AppInitializer
  try {
    LoggingService.info('RealTimeServiceManager already initialized through Riverpod');
    LoggingService.success(
      'Unified RealTimeServiceManager ready',
    );
  } on Exception catch (e, stackTrace) {
    LoggingService.failure(
      'FAILED TO INITIALIZE Unified RealTimeServiceManager in main()',
      error: e,
      stackTrace: stackTrace,
      tag: 'RealTimeServices',
    );
    LoggingService.warning('Real-time notifications will not work properly');
  }

  // Note: PresenceService will be initialized when user authenticates
  // This ensures presence is only active for authenticated users

  // Contact request notifications are now handled by the unified RealTimeNotificationService
  // which is initialized automatically when users log in

  // Configure system UI for edge-to-edge display
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  // Enable edge-to-edge display (extend UI under system navigation bars)
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  // Mark startup as complete before launching the app
  try {
    final container = ProviderContainer();
    container.read(appInitializerProvider.notifier).markStartupComplete();
  } on Exception catch (e) {
    LoggingService.warning('Failed to mark startup complete', error: e);
  }

  // Load shaders and run app with Riverpod
  try {
    LoggingService.info('🎨 Loading shaders...');
    final shaderCollection = await ShaderService.loadShaders();
    LoggingService.info('✅ Shaders loaded successfully');

    runApp(
      ProviderScope(
        child: ShaderProvider(
          shaderCollection: shaderCollection,
          child: const MyApp(),
        ),
      ),
    );
  } on Exception catch (e, s) {
    LoggingService.error('FAILED to load shaders: $e');
    LoggingService.error('Stack trace: $s');

    // Run a simplified error app instead
    runApp(ErrorApp(error: e.toString(), stackTrace: s.toString()));
  }
}

// Simple error display app as a fallback when the main app fails to initialize
class ErrorApp extends StatelessWidget {
  const ErrorApp({required this.error, required this.stackTrace, super.key});
  final String error;
  final String stackTrace;

  @override
  Widget build(BuildContext context) => MaterialApp(
    title: 'Hopen Error',
    theme: ThemeData(
      primarySwatch: Colors.blue,
      scaffoldBackgroundColor: const Color(0xFF0A2955),
    ),
    home: Scaffold(
      appBar: AppBar(title: const Text('Hopen Initialization Error')),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'The app encountered an error during initialization:',
                style: TextStyle(fontSize: 18, color: Colors.white),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  error,
                  style: const TextStyle(fontSize: 16, color: Colors.white),
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Stack Trace:',
                style: TextStyle(fontSize: 16, color: Colors.white70),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      stackTrace,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

// Configuration error display app for configuration validation failures
class ConfigurationErrorApp extends StatelessWidget {
  const ConfigurationErrorApp({required this.error, super.key});
  final String error;

  @override
  Widget build(BuildContext context) => MaterialApp(
    title: 'Hopen Configuration Error',
    theme: ThemeData(
      primarySwatch: Colors.red,
      scaffoldBackgroundColor: const Color(0xFF0A2955),
    ),
    home: Scaffold(
      appBar: AppBar(
        title: const Text('Configuration Error'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Configuration Validation Failed',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'The application cannot start because the configuration is invalid:',
                style: TextStyle(fontSize: 18, color: Colors.white),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.5)),
                ),
                child: Text(
                  error,
                  style: const TextStyle(fontSize: 16, color: Colors.white),
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'To fix this issue:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                '1. Check your environment variables\n'
                '2. Verify network configuration settings\n'
                '3. Ensure all required configuration values are set\n'
                '4. Restart the application after making changes',
                style: TextStyle(fontSize: 16, color: Colors.white70),
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    // Exit the app
                    SystemNavigator.pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Exit Application',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> with RestorationMixin {
  late StreamSubscription<DialogEvent> dialogEventSubscription;
  final shownDialogs =
      <String>{}; // Track shown dialogs to prevent duplicates

  // State restoration properties
  final hasUnshownRequests = RestorableBool(false);
  final lastShownRequestId = RestorableStringN(null);

  @override
  String? get restorationId => 'main_app';

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    registerForRestoration(hasUnshownRequests, 'has_unshown_requests');
    registerForRestoration(lastShownRequestId, 'last_shown_request_id');

    if (initialRestore && hasUnshownRequests.value) {
      // Schedule request recovery after restoration
      WidgetsBinding.instance.addPostFrameCallback((_) {
        recoverPendingRequests();
      });
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize notification orchestrator through Riverpod
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Note: NotificationOrchestrator will be injected via Riverpod when needed
      // notificationOrchestrator = ref.read(notificationOrchestratorProvider);
      // dialogEventSubscription = notificationOrchestrator.dialogEventStream
      //     .listen(handleDialogEvent);
    });

    // Initialize state restoration service
    // Note: RequestStateRestorationService is now injected via Riverpod

    // Check for existing pending requests after a longer delay to ensure MaterialApp is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 5), checkForExistingPendingRequests);
    });
  }

  @override
  void dispose() {
    dialogEventSubscription.cancel();
    hasUnshownRequests.dispose();
    lastShownRequestId.dispose();
    super.dispose();
  }

  /// Recover pending requests after state restoration
  Future<void> recoverPendingRequests() async {
    try {
      LoggingService.info(
        'MyApp: Recovering pending requests after state restoration...',
      );

      // Restore request state
      // Note: RequestStateRestorationService will be implemented when Riverpod providers are generated
      // final restorationService = ref.read(requestStateRestorationServiceProvider);
      // final result = await restorationService.restoreRequestState();

      // Reset restoration flag
      hasUnshownRequests.value = false;

      LoggingService.success('MyApp: Request recovery completed');
    } on Exception catch (e, stackTrace) {
      LoggingService.error(
        'MyApp: Failed to recover pending requests: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Check for existing pending requests using the enhanced dialog system
  Future<void> checkForExistingPendingRequests() async {
    try {
      LoggingService.info(
        'MyApp: Checking for existing pending requests with enhanced dialog system',
      );

      // Process any pending dialogs that may have been saved
      // EnhancedDialogManager removed - using DialogOrchestratorService instead

      LoggingService.success('MyApp: Pending request check completed');
    } on Exception catch (e, stackTrace) {
      LoggingService.error(
        'MyApp: Error in pending request check: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Handle dialog events from NotificationOrchestrator
  void handleDialogEvent(DialogEvent event) {
    LoggingService.info('🔔 MyApp: Received dialog event: ${event.type}');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      switch (event.type) {
        case 'contact_request':
          if (event.data is ContactRequestModel) {
            final request = event.data as ContactRequestModel;
            showContactRequestDialog(context, {
              'requestId': request.id,
              'fromUserId': request.requesterId,
              'timestamp': request.createdAt.toIso8601String(),
              'message': request.message,
            });
          }
          break;
        case 'bubble_request':
          if (event.data is BubbleRequestModel) {
            final request = event.data as BubbleRequestModel;
            showBubbleStartRequestDialog(context, {
              'requestId': request.id,
              'fromUserId': request.requesterId,
              'timestamp': request.createdAt.toIso8601String(),
            });
          }
          break;
        case 'bubble_invite_request_received':
          if (event.data is BubbleRequestModel) {
            final request = event.data as BubbleRequestModel;
            showBubbleInviteRequestDialog(context, {
              'requestId': request.id,
              'fromUserId': request.requesterId,
              'bubbleId': request.bubbleId,
              'timestamp': request.createdAt.toIso8601String(),
            });
          }
          break;
        case 'bubble_join_request_received':
          if (event.data is BubbleRequestModel) {
            final request = event.data as BubbleRequestModel;
            showBubbleJoinRequestDialog(context, {
              'requestId': request.id,
              'fromUserId': request.requesterId,
              'bubbleId': request.bubbleId,
              'timestamp': request.createdAt.toIso8601String(),
              'message': request.reason,
            });
          }
          break;
        case 'bubble_kickout_request_received':
          if (event.data is BubbleRequestModel) {
            final request = event.data as BubbleRequestModel;
            showBubbleKickoutRequestDialog(context, {
              'requestId': request.id,
              'bubbleId': request.bubbleId,
              'timestamp': request.createdAt.toIso8601String(),
            });
          }
          break;
        case 'bubble_propose_request':
          if (event.data is BubbleRequestModel) {
            final request = event.data as BubbleRequestModel;
            showBubbleProposeRequestDialog(context, {
              'requestId': request.id,
              'bubbleId': request.bubbleId,
              'fromUserId': request.requesterId,
              'timestamp': request.createdAt.toIso8601String(),
            });
          }
          break;
        case 'friend_request_received':
          // Friend requests are auto-generated at bubble expiry - no manual dialog needed
          // Users receive auto-generated requests and accept/decline through the friends system
          break;
        case 'bubble_expired':
          if (event.data is Map<String, dynamic>) {
            showFriendsChoiceDialog(context, event.data as Map<String, dynamic>);
          }
          break;
        default:
          LoggingService.info('🔔 MyApp: Unknown dialog event type: ${event.type}');
      }
    });
  }

  /// Show contact request dialog when notification is received
  Future<void> showContactRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) async {
    final requestId = data['requestId'] ?? data['id'] ?? 'temp_request_id';
    final requesterId =
        data['fromUserId'] ?? data['requester_id'] ?? data['senderId'] ?? '';
    final requestTimestamp =
        DateTime.tryParse(
          data['timestamp']?.toString() ?? data['sentAt']?.toString() ?? '',
        ) ??
        DateTime.now();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'contact_request_$requestId';

    // Check if this dialog has already been shown
    if (shownDialogs.contains(dialogKey)) {
      LoggingService.info(
        '🔔 MyApp: Contact request dialog for $requesterId already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    shownDialogs.add(dialogKey);
    LoggingService.info(
      '🔔 MyApp: Showing contact request dialog for $requesterId (key: $dialogKey)',
    );

    // Fetch user profile data to get profile picture and full name
    String? requesterName;
    String? requesterUsername;
    String? requesterProfilePicUrl;
    
    try {
      final container = ProviderScope.containerOf(context);
      final userDataSource = container.read(userRemoteDataSourceProvider);
      final userProfile = await userDataSource.getUserProfile(requesterId.toString());

      requesterName = '${userProfile.firstName} ${userProfile.lastName}'.trim();
      requesterUsername = userProfile.username;
      requesterProfilePicUrl = userProfile.avatarUrl;
      
      LoggingService.info('🔔 MyApp: Fetched user profile for $requesterId: $requesterName');
    } on Exception catch (e) {
      LoggingService.warning('🔔 MyApp: Failed to fetch user profile for $requesterId: $e');
      requesterName = 'Unknown User';
    }

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null || !navigatorContext.mounted) {
      LoggingService.info('🔔 MyApp: Navigator context not available or not mounted, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      shownDialogs.remove(dialogKey);
      return;
    }

    unawaited(showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      builder: (dialogContext) => ContactRequestDialog(
          requestId: requestId.toString(),
          requesterId: requesterId.toString(),
          requesterName: requesterName ?? 'Unknown User',
          requesterUsername: requesterUsername,
          requesterProfilePicUrl: requesterProfilePicUrl,
          requestTimestamp: requestTimestamp,
        ),
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      shownDialogs.remove(dialogKey);
      LoggingService.info(
        '🔔 MyApp: Contact request dialog dismissed, removed key: $dialogKey',
      );
    }));
  }

  /// Show bubble start request dialog
  Future<void> showBubbleStartRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) async {
    final requestId =
        (data['requestId'] ?? data['id'] ?? 'temp_bubble_start_request').toString();
    final requesterId = data['fromUserId'] ?? data['requester_id'] ?? '';
    final requestTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_start_request_$requestId';

    // Check if this dialog has already been shown
    if (shownDialogs.contains(dialogKey)) {
      LoggingService.info(
        '🔔 MyApp: Bubble start request dialog for $requesterId already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    shownDialogs.add(dialogKey);
    LoggingService.info(
      '🔔 MyApp: Showing bubble start request dialog for $requesterId (key: $dialogKey)',
    );

    // Fetch user profile data to get profile picture and full name
    String? requesterName;
    String? requesterUsername;
    String? requesterProfilePicUrl;
    
    try {
      final container = ProviderScope.containerOf(context);
      final userDataSource = container.read(userRemoteDataSourceProvider);
      final userProfile = await userDataSource.getUserProfile(requesterId.toString());

      requesterName = '${userProfile.firstName} ${userProfile.lastName}'.trim();
      requesterUsername = userProfile.username;
      requesterProfilePicUrl = userProfile.avatarUrl;
      
      LoggingService.info('🔔 MyApp: Fetched user profile for $requesterId: $requesterName');
    } on Exception catch (e) {
      LoggingService.warning('🔔 MyApp: Failed to fetch user profile for $requesterId: $e');
      requesterName = 'Unknown User';
    }

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null || !navigatorContext.mounted) {
      LoggingService.info('🔔 MyApp: Navigator context not available or not mounted, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      shownDialogs.remove(dialogKey);
      return;
    }

    unawaited(showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      builder: (dialogContext) => BubbleStartRequestDialog(
          requestId: requestId,
          requesterId: requesterId.toString(),
          requesterName: requesterName ?? 'Unknown User',
          requesterUsername: requesterUsername,
          requesterProfilePicUrl: requesterProfilePicUrl,
          requestTimestamp: requestTimestamp,
        ),
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      shownDialogs.remove(dialogKey);
      LoggingService.info(
        '🔔 MyApp: Bubble start request dialog dismissed, removed key: $dialogKey',
      );
    }));
  }

  /// Show bubble invite request dialog
  Future<void> showBubbleInviteRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) async {
    final requestId =
        data['requestId'] ?? data['id'] ?? 'temp_bubble_invite_request';
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';
    final inviterId = data['fromUserId'] ?? data['inviter_id'] ?? '';
    final inviteTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_invite_request_$requestId';

    // Check if this dialog has already been shown
    if (shownDialogs.contains(dialogKey)) {
      LoggingService.info(
        '🔔 MyApp: Bubble invite request dialog for $inviterId already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    shownDialogs.add(dialogKey);

    // Fetch user profile data to get profile picture and full name
    String? inviterName;
    
    try {
      final container = ProviderScope.containerOf(context);
      final userDataSource = container.read(userRemoteDataSourceProvider);
      final userProfile = await userDataSource.getUserProfile(inviterId.toString());

      inviterName = '${userProfile.firstName} ${userProfile.lastName}'.trim();
      
      LoggingService.info('🔔 MyApp: Fetched user profile for $inviterId: $inviterName');
    } on Exception catch (e) {
      LoggingService.warning('🔔 MyApp: Failed to fetch user profile for $inviterId: $e');
      inviterName = 'Unknown User';
    }

    // Create a list of bubble members (starting with the inviter)
    final members = [
      BubbleMemberConverter.createSimple(
        userId: inviterId.toString(),
        bubbleId: bubbleId.toString(),
        joinedAt: inviteTimestamp.toUtc(),
      ),
    ];

    LoggingService.info(
      '🔔 MyApp: Showing bubble invite request dialog for $inviterName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null || !navigatorContext.mounted) {
      LoggingService.info('🔔 MyApp: Navigator context not available or not mounted, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      shownDialogs.remove(dialogKey);
      return;
    }

    unawaited(showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      builder: (dialogContext) => BubbleInviteRequestDialog(
          bubbleId: bubbleId.toString(),
          bubbleName: bubbleName.toString(),
          members: members,
          requestId: requestId.toString(),
        ),
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      shownDialogs.remove(dialogKey);
      LoggingService.info(
        '🔔 MyApp: Bubble invite request dialog dismissed, removed key: $dialogKey',
      );
    }));
  }

  /// Show bubble join request dialog
  Future<void> showBubbleJoinRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) async {
    final requestId =
        data['requestId'] ?? data['request_id'] ?? 'temp_request_id';
    final requesterId = data['fromUserId'] ?? data['requester_id'] ?? '';
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';
    final requestTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();
    final message = data['message']?.toString();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_join_request_$requestId';

    // Check if this dialog has already been shown
    if (shownDialogs.contains(dialogKey)) {
      LoggingService.info(
        '🔔 MyApp: Bubble join request dialog for $requesterId already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    shownDialogs.add(dialogKey);

    // Capture container before async operations
    final container = ProviderScope.containerOf(context);

    // Fetch user profile data to get profile picture and full name
    String? requesterName;

    try {
      final userDataSource = container.read(userRemoteDataSourceProvider);
      final userProfile = await userDataSource.getUserProfile(requesterId.toString());

      requesterName = '${userProfile.firstName} ${userProfile.lastName}'.trim();

      LoggingService.info('🔔 MyApp: Fetched user profile for $requesterId: $requesterName');
    } on Exception catch (e) {
      LoggingService.warning('🔔 MyApp: Failed to fetch user profile for $requesterId: $e');
      requesterName = 'Unknown User';
    }

    // Fetch actual bubble members from repository
    final bubbleRepository = container.read(bubbleRepositoryProvider);
    final bubbleIdResult = BubbleId.create(bubbleId.toString());
    if (bubbleIdResult.isFailure) {
      LoggingService.error('Invalid bubble ID: ${bubbleId.toString()}');
      return;
    }
    final membersResult = await bubbleRepository.getBubbleMembers(bubbleIdResult.data);

    final members = membersResult.isSuccess
        ? membersResult.data.map((member) => BubbleMemberConverter.createSimple(
            userId: member.id,
            bubbleId: bubbleId.toString(),
            joinedAt: member.lastSeen ?? DateTime.now(),
          )).toList()
        : [
            // Fallback: just the requester if we can't fetch members
            BubbleMemberConverter.createSimple(
              userId: requesterId.toString(),
              bubbleId: bubbleId.toString(),
              joinedAt: requestTimestamp.toUtc(),
            ),
          ];

    LoggingService.info(
      '🔔 MyApp: Showing bubble join request dialog for $requesterName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null || !navigatorContext.mounted) {
      LoggingService.info('🔔 MyApp: Navigator context not available or not mounted, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      shownDialogs.remove(dialogKey);
      return;
    }

    unawaited(showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      builder: (dialogContext) => BubbleJoinRequestDialog(
          requestId: requestId.toString(),
          requesterId: requesterId.toString(),
          requesterName: requesterName ?? 'Unknown User',
          bubbleId: bubbleId.toString(),
          bubbleName: bubbleName.toString(),
          members: members,
          message: message,
        ),
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      shownDialogs.remove(dialogKey);
      LoggingService.info(
        '🔔 MyApp: Bubble join request dialog dismissed, removed key: $dialogKey',
      );
    }));
  }

  /// Show bubble kickout request dialog
  Future<void> showBubbleKickoutRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) async {
    final requestId =
        data['requestId'] ?? data['request_id'] ?? 'temp_request_id';
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';
    final targetMemberId =
        data['targetMemberId'] ?? data['target_member_id'] ?? '';
    final requesterId = data['fromUserId'] ?? data['requester_id'] ?? '';
    final requestTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_kickout_request_$requestId';

    // Check if this dialog has already been shown
    if (shownDialogs.contains(dialogKey)) {
      LoggingService.info(
        '🔔 MyApp: Bubble kickout request dialog for $targetMemberId already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    shownDialogs.add(dialogKey);

    // Fetch user profile data to get profile picture and full name
    String? targetMemberName;
    String? requesterName;
    
    try {
      final container = ProviderScope.containerOf(context);
      final userDataSource = container.read(userRemoteDataSourceProvider);

      // Fetch target member profile
      final targetUserProfile = await userDataSource.getUserProfile(targetMemberId.toString());
      targetMemberName = '${targetUserProfile.firstName} ${targetUserProfile.lastName}'.trim();

      // Fetch requester profile
      final requesterUserProfile = await userDataSource.getUserProfile(requesterId.toString());
      requesterName = '${requesterUserProfile.firstName} ${requesterUserProfile.lastName}'.trim();
      
      LoggingService.info('🔔 MyApp: Fetched user profiles - Target: $targetMemberName, Requester: $requesterName');
    } on Exception catch (e) {
      LoggingService.warning('🔔 MyApp: Failed to fetch user profiles: $e');
      targetMemberName = 'Unknown User';
      requesterName = 'Unknown User';
    }

    // Create a list of bubble members (target member and requester)
    final members = [
      BubbleMemberConverter.createSimple(
        userId: targetMemberId.toString(),
        bubbleId: bubbleId.toString(),
        joinedAt: requestTimestamp.toUtc(),
      ),
      BubbleMemberConverter.createSimple(
        userId: requesterId.toString(),
        bubbleId: bubbleId.toString(),
        joinedAt: requestTimestamp.toUtc(),
      ),
    ];

    LoggingService.info(
      '🔔 MyApp: Showing bubble kickout request dialog for $targetMemberName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null || !navigatorContext.mounted) {
      LoggingService.info('🔔 MyApp: Navigator context not available or not mounted, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      shownDialogs.remove(dialogKey);
      return;
    }

    unawaited(showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      builder: (dialogContext) => BubbleKickoutRequestDialog(
          requestId: requestId.toString(),
          bubbleName: bubbleName.toString(),
          members: members,
        ),
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      shownDialogs.remove(dialogKey);
      LoggingService.info(
        '🔔 MyApp: Bubble kickout request dialog dismissed, removed key: $dialogKey',
      );
    }));
  }

  /// Show bubble propose request dialog
  Future<void> showBubbleProposeRequestDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) async {
    final requestId =
        data['requestId'] ?? data['id'] ?? 'temp_bubble_propose_request';
    final proposedMemberId =
        data['proposedMemberId'] ?? data['proposed_member_id'] ?? '';
    final proposerId = data['fromUserId'] ?? data['proposer_id'] ?? '';
    final proposeTimestamp =
        DateTime.tryParse(data['timestamp']?.toString() ?? '') ??
        DateTime.now();
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'bubble_propose_request_$requestId';

    // Check if this dialog has already been shown
    if (shownDialogs.contains(dialogKey)) {
      LoggingService.info(
        '🔔 MyApp: Bubble propose request dialog for $proposedMemberId already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    shownDialogs.add(dialogKey);

    // Fetch user profile data to get profile picture and full name
    String? proposedMemberName;
    String? proposerName;
    
    try {
      final container = ProviderScope.containerOf(context);
      final userDataSource = container.read(userRemoteDataSourceProvider);

      // Fetch proposed member profile
      final proposedUserProfile = await userDataSource.getUserProfile(proposedMemberId.toString());
      proposedMemberName = '${proposedUserProfile.firstName} ${proposedUserProfile.lastName}'.trim();

      // Fetch proposer profile
      final proposerUserProfile = await userDataSource.getUserProfile(proposerId.toString());
      proposerName = '${proposerUserProfile.firstName} ${proposerUserProfile.lastName}'.trim();
      
      LoggingService.info('🔔 MyApp: Fetched user profiles - Proposed: $proposedMemberName, Proposer: $proposerName');
    } on Exception catch (e) {
      LoggingService.warning('🔔 MyApp: Failed to fetch user profiles: $e');
      proposedMemberName = 'Unknown User';
      proposerName = 'Unknown User';
    }

    // Create a list of bubble members (proposed member and proposer)
    final members = [
      BubbleMemberConverter.createSimple(
        userId: proposedMemberId.toString(),
        bubbleId: bubbleId.toString(),
        joinedAt: proposeTimestamp.toUtc(),
      ),
      BubbleMemberConverter.createSimple(
        userId: proposerId.toString(),
        bubbleId: bubbleId.toString(),
        joinedAt: proposeTimestamp.toUtc(),
      ),
    ];

    LoggingService.info(
      '🔔 MyApp: Showing bubble propose request dialog for $proposedMemberName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null || !navigatorContext.mounted) {
      LoggingService.info('🔔 MyApp: Navigator context not available or not mounted, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      shownDialogs.remove(dialogKey);
      return;
    }

    unawaited(showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      builder: (dialogContext) => BubbleProposeRequestDialog(
          requestId: requestId.toString(),
          bubbleId: bubbleId.toString(),
          bubbleName: bubbleName.toString(),
          members: members,
          proposedMemberId: proposedMemberId.toString(),
        ),
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      shownDialogs.remove(dialogKey);
      LoggingService.info(
        '🔔 MyApp: Bubble propose request dialog dismissed, removed key: $dialogKey',
      );
    }));
  }

  // _showFriendRequestDialog removed - friend requests are auto-generated at bubble expiry
  // Users receive auto-generated friend requests and handle them through the friends system

  /// Show friends choice dialog when bubble expires
  void showFriendsChoiceDialog(
    BuildContext context,
    Map<String, dynamic> data,
  ) {
    final requestId =
        data['requestId'] ?? data['id'] ?? 'temp_friends_choice_request';
    final bubbleId = data['bubbleId'] ?? data['bubble_id'] ?? '';
    final bubbleName = data['bubbleName'] ?? data['bubble_name'] ?? '';
    final formerMembersData =
        data['formerMembers'] as List<Map<String, dynamic>>? ?? [];

    // Create unique dialog key to prevent duplicates
    final dialogKey = 'friends_choice_$requestId';

    // Check if this dialog has already been shown
    if (shownDialogs.contains(dialogKey)) {
      LoggingService.info(
        '🔔 MyApp: Friends choice dialog for bubble $bubbleName already shown, skipping duplicate',
      );
      return;
    }

    // Mark this dialog as shown
    shownDialogs.add(dialogKey);

    // Convert former members data to UserModel list
    final formerMembers =
        formerMembersData.map((memberData) => UserModel(
            id: memberData['id']?.toString() ?? '',
            firstName:
                memberData['firstName']?.toString() ??
                memberData['first_name']?.toString(),
            lastName:
                memberData['lastName']?.toString() ??
                memberData['last_name']?.toString(),
            username: memberData['username']?.toString(),
            profilePictureUrl:
                memberData['profilePictureUrl']?.toString() ??
                memberData['profile_pic_url']?.toString(),
            email: memberData['email']?.toString(),
          )).toList();

    LoggingService.info(
      '🔔 MyApp: Showing friends choice dialog for bubble: $bubbleName (key: $dialogKey)',
    );

    // Use the global navigator key to ensure we have the correct context with MaterialLocalizations
    final navigatorContext = app_router.rootNavigatorKey.currentContext;
    if (navigatorContext == null) {
      LoggingService.info('🔔 MyApp: Navigator context not available, cannot show dialog');
      // Remove from shown dialogs since we couldn't show it
      shownDialogs.remove(dialogKey);
      return;
    }

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: false,
      builder: (dialogContext) => FriendsChoiceDialog(
          bubbleId: bubbleId.toString(),
          bubbleName: bubbleName.toString(),
          formerMembers: formerMembers,
        ),
    ).then((_) {
      // Remove from shown dialogs when dialog is dismissed
      shownDialogs.remove(dialogKey);
      LoggingService.info(
        '🔔 MyApp: Friends choice dialog dismissed, removed key: $dialogKey',
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    // Create router with Riverpod context
    final router = createRouter(ref);

    return Consumer(
      builder: (context, ref, child) {
            // Listen to auth state changes with Riverpod
            ref.listen<AsyncValue<AuthState>>(authControllerProvider, (previous, next) {
              next.whenData((authState) {
                if (authState is AuthAuthenticated) {
                  // Trigger user profile preloading immediately when user becomes authenticated
                  if (authState.user.id.isNotEmpty) {
                    LoggingService.info(
                      '👤 MyApp: User authenticated, triggering profile preload for ${authState.user.id}',
                    );
                    try {
                      ProviderScope.containerOf(context)
                          .read(userProfilePreloaderServiceProvider)
                          .preloadUserData(authState.user);
                    } on Exception catch (e) {
                      LoggingService.info('👤 MyApp: Error triggering profile preload: $e');
                    }
                  }
                } else if (authState is AuthUnauthenticated) {
                  LoggingService.info(
                    '🔓 MyApp: User logged out, auth state changed to unauthenticated',
                  );
                  // Router will handle navigation automatically via refreshListenable
                }
              });
            });

            return MaterialApp.router(
                scaffoldMessengerKey: rootScaffoldMessengerKey,
                title: 'Hopen',
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: ref.watch(themeControllerProvider),
                routerConfig: router,
                debugShowCheckedModeBanner: false,
                localizationsDelegates: const [
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                builder: (context, child) {
                  LoggingService.info(
                    'MaterialApp.router builder called - creating AppContextProvider',
                  );
                  return AppContextManager(
                    child: NavigatorContextProvider(
                      child: ConnectivityToastListener(
                        child: child ?? const SizedBox.shrink(),
                      ),
                    ),
                  );
                },
              );
        },
    );
  }
}

// Handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Ensure Firebase is initialized
  await Firebase.initializeApp();
  
  // Log the background message
  // Note: We can't use LoggingService here as DI is not initialized in background isolate
  debugPrint('Handling background message: ${message.messageId}');
  debugPrint('Message data: ${message.data}');
  debugPrint('Message notification: ${message.notification?.title}');
  
  // FCM will automatically show the notification on Android/iOS
  // Additional processing can be done here if needed
}

// Remove the default MyHomePage and related code below this line

--- CONTENT END ---

==========================================

=== FILE: main_dev.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/main_dev.dart
Last Modified: Sep  5 18:55:17 2025
File Size: 4314 bytes

--- CONTENT START ---
// This file serves as an alternative entry point for the application,
// specifically configured to use the `device_preview` package during development.
// It allows developers to easily test the UI on various simulated devices.
// 
// WEB COMPATIBILITY: This version excludes platform-specific dependencies
// that cause JNI compilation errors on web platforms.

// ignore_for_file: depend_on_referenced_packages

import 'dart:async';

import 'package:device_preview/device_preview.dart'; // The device preview package - dev dependency
import 'package:firebase_core/firebase_core.dart'; // Firebase initialization
import 'package:firebase_messaging/firebase_messaging.dart'; // Firebase messaging
import 'package:flutter/foundation.dart'; // Provides kReleaseMode, kIsWeb constants
import 'package:flutter/material.dart'; // Core Flutter framework widgets
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'data/services/auth/kratos_auth_service.dart';
import 'data/services/config/centralized_config_service.dart';
// Removed injection_container_refactored.dart - using Riverpod for dependency injection
import 'main.dart';
import 'statefulbusinesslogic/core/services/logging_service.dart';
import 'statefulbusinesslogic/riverpod/dependencies.dart';




// Global error display key for catching unhandled errors
final GlobalKey<ScaffoldMessengerState> rootScaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Register FCM background handler at bootstrap (guard for non-web platforms)
  if (!kIsWeb) {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  // Force portrait orientation globally by default.
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // Print configuration for debugging
  final configService = CentralizedConfigService();
  LoggingService.info('=== App Configuration ===');
  LoggingService.info('Environment: ${configService.environment}');
  LoggingService.info('Host IP: ${configService.effectiveHostIP}');
  LoggingService.info('gRPC Config: ${configService.grpcConfig.fullUrl}');
  LoggingService.info('MinIO Config: ${configService.minioConfig.fullUrl}');
  LoggingService.info('========================');

  // ✅ ENABLED: Ory Stack authentication initialization
  try {
    await KratosAuthService().initialize();
    LoggingService.success('Ory Stack authentication initialized successfully');
    final kratosUrl = configService.isDevelopment
        ? 'http://${configService.effectiveHostIP}:4433'
        : configService.isStaging
            ? 'https://auth.staging.hopen.app'
            : 'https://auth.hopenapp.com';
    final hydraUrl = configService.isDevelopment
        ? 'http://${configService.effectiveHostIP}:4444'
        : configService.isStaging
            ? 'https://oauth.staging.hopen.app'
            : 'https://oauth.hopenapp.com';
    LoggingService.info('   Kratos URL: $kratosUrl');
    LoggingService.info('   Hydra URL: $hydraUrl');
  } on Exception catch (e, s) {
    LoggingService.failure(
      'FAILED to initialize Ory Stack',
      error: e,
      stackTrace: s,
    );
    // Continue without Ory Stack if it fails
  }

  // Initialize dependency injection with Riverpod
  try {
    final container = ProviderContainer();
    await container.read(appInitializerProvider.future);
    LoggingService.success('Pure Riverpod dependency injection initialized successfully');
  } on Exception catch (e, s) {
    LoggingService.failure(
      'FAILED to initialize Riverpod dependency injection',
      error: e,
      stackTrace: s,
    );
    // Continue without full DI if it fails in dev mode
  }

  // Run app with DevicePreview and ProviderScope
  runApp(
    ProviderScope(
      child: DevicePreview(
        builder: (context) => const MyAppDev(),
      ),
    ),
  );
}

/// Development version of MyApp that uses device_preview
class MyAppDev extends MyApp {
  const MyAppDev({super.key});
}

// Handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  debugPrint('Handling background message: ${message.messageId}');
}

--- CONTENT END ---

==========================================


# SUMMARY
# =======
Total files processed: 10
File types found:
    10  dart
