# MIGRATIONS CODEBASE REVIEW PROMPT
# =================================
# 
# This file contains the complete content of all files in the migrations directory.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: Mon Sep 15 17:52:55 EDT 2025
# Total files to process:       10
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure
# 2. Analyze the database migration patterns and schema evolution
# 3. Identify patterns, dependencies, and relationships between migrations
# 4. Provide insights on database design, migration strategies, and potential improvements
# 5. Note any inconsistencies, missing implementations, or areas for enhancement
# 6. Review migration ordering, rollback strategies, and data integrity
#
# FILE CONTENTS:
# =============

=== FILE: postgresql/001_core_schema.down.sql ===
File Type: sql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/postgresql/001_core_schema.down.sql
Last Modified: Sep 13 19:33:55 2025
File Size: 6919 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION ROLLBACK: Complete Database Schema
-- Description: Safely remove ALL database tables in proper dependency order
-- Version: 2.0 (Complete schema refactoring rollback)
-- Author: System Refactoring
-- Date: 2025-01-25
-- Rollback Safety: Safe - removes all tables in reverse dependency order
-- Performance Impact: N/A - rollback operation
-- Dependencies: None (this removes everything)
-- ============================================================================
--
-- Purpose: Following database guidelines from docs/roadmap/guidelines/how_to_write_databases.md
-- This rollback removes all PostgreSQL tables in the correct dependency order
-- to ensure clean rollback without foreign key constraint violations.
--
-- ============================================================================

BEGIN;

-- ============================================================================
-- DROP TRIGGERS (in reverse order)
-- ============================================================================

-- Realtime messaging triggers
-- Note: Message-related triggers are handled by ScyllaDB realtime service
DROP TRIGGER IF EXISTS trigger_conversations_updated_at ON conversations;
DROP TRIGGER IF EXISTS trigger_bubble_members_updated_at ON bubble_members;
DROP TRIGGER IF EXISTS trigger_bubbles_updated_at ON bubbles;

-- Core triggers
DROP TRIGGER IF EXISTS trigger_user_relationships_updated_at ON user_relationships;
DROP TRIGGER IF EXISTS trigger_user_presence_sync ON user_presence;
DROP TRIGGER IF EXISTS trigger_user_presence_updated_at ON user_presence;
DROP TRIGGER IF EXISTS trigger_user_roles_updated_at ON user_roles;
DROP TRIGGER IF EXISTS trigger_password_reset_tokens_updated_at ON password_reset_tokens;
DROP TRIGGER IF EXISTS trigger_user_sessions_updated_at ON user_sessions;
DROP TRIGGER IF EXISTS trigger_users_updated_at ON users;

-- ============================================================================
-- DROP FUNCTIONS
-- ============================================================================

-- Realtime messaging functions
-- Note: Message-related functions are handled by ScyllaDB realtime service
DROP FUNCTION IF EXISTS get_or_create_conversation(UUID, UUID);
DROP FUNCTION IF EXISTS is_bubble_member(UUID, UUID);

-- Core functions
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS sync_user_presence();
DROP FUNCTION IF EXISTS update_user_last_active(UUID);
DROP FUNCTION IF EXISTS get_relationship_status(UUID, UUID, relationship_type);
DROP FUNCTION IF EXISTS are_users_friends(UUID, UUID);

-- ============================================================================
-- DROP INDEXES (explicit cleanup)
-- ============================================================================

-- User relationships indexes
DROP INDEX IF EXISTS idx_user_relationships_pending;
DROP INDEX IF EXISTS idx_user_relationships_source_bubble;
DROP INDEX IF EXISTS idx_user_relationships_bidirectional;
DROP INDEX IF EXISTS idx_user_relationships_to_user;
DROP INDEX IF EXISTS idx_user_relationships_from_user;

-- User roles indexes
DROP INDEX IF EXISTS idx_user_roles_assigned_by;
DROP INDEX IF EXISTS idx_user_roles_role_active;
DROP INDEX IF EXISTS idx_user_roles_user_active;

-- User presence indexes
DROP INDEX IF EXISTS idx_user_presence_last_seen;
DROP INDEX IF EXISTS idx_user_presence_is_present;

-- Password reset tokens indexes
DROP INDEX IF EXISTS idx_password_reset_tokens_unused;
DROP INDEX IF EXISTS idx_password_reset_tokens_expires_at;
DROP INDEX IF EXISTS idx_password_reset_tokens_user_id;
DROP INDEX IF EXISTS idx_password_reset_tokens_token;

-- User sessions indexes
DROP INDEX IF EXISTS idx_user_sessions_session_id;
DROP INDEX IF EXISTS idx_user_sessions_expires_at;
DROP INDEX IF EXISTS idx_user_sessions_active;
DROP INDEX IF EXISTS idx_user_sessions_user_status;

-- Users table indexes
DROP INDEX IF EXISTS idx_users_notification_settings;
DROP INDEX IF EXISTS idx_users_is_verified;
DROP INDEX IF EXISTS idx_users_language_preference;
DROP INDEX IF EXISTS idx_users_birthday_month_day;
DROP INDEX IF EXISTS idx_users_data_deletion_scheduled;
DROP INDEX IF EXISTS idx_users_country_residence;
DROP INDEX IF EXISTS idx_users_last_active;
DROP INDEX IF EXISTS idx_users_is_present;
DROP INDEX IF EXISTS idx_users_status_active;
DROP INDEX IF EXISTS idx_users_username_active;
DROP INDEX IF EXISTS idx_users_email_active;
DROP INDEX IF EXISTS idx_users_search_text;

-- Realtime messaging indexes
-- Note: Message-related indexes are handled by ScyllaDB realtime service
DROP INDEX IF EXISTS idx_conversations_last_message;
DROP INDEX IF EXISTS idx_conversations_participant_2;
DROP INDEX IF EXISTS idx_conversations_participant_1;
DROP INDEX IF EXISTS idx_bubble_members_joined_at;
DROP INDEX IF EXISTS idx_bubble_members_user_id;
DROP INDEX IF EXISTS idx_bubble_members_bubble_id;
DROP INDEX IF EXISTS idx_bubbles_name_search;
DROP INDEX IF EXISTS idx_bubbles_status_created_at;
DROP INDEX IF EXISTS idx_bubbles_created_by;

-- ============================================================================
-- DROP TABLES (in reverse dependency order)
-- ============================================================================

-- Realtime messaging tables (in dependency order)
-- Note: Messages, pinned messages, polls, and poll votes are handled by ScyllaDB realtime service
DROP TABLE IF EXISTS conversations CASCADE;
DROP TABLE IF EXISTS bubble_members CASCADE;
DROP TABLE IF EXISTS bubbles CASCADE;

-- Core tables
DROP TABLE IF EXISTS user_relationships CASCADE;
DROP TABLE IF EXISTS user_presence CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS password_reset_tokens CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- ============================================================================
-- DROP TYPES (in reverse dependency order)
-- ============================================================================

-- Realtime messaging types
DROP TYPE IF EXISTS bubble_member_role;
DROP TYPE IF EXISTS bubble_member_status;
DROP TYPE IF EXISTS bubble_status;
-- Note: message_type enum removed as messages are handled by ScyllaDB

-- Core types
DROP TYPE IF EXISTS data_retention_policy;
DROP TYPE IF EXISTS session_status;
DROP TYPE IF EXISTS relationship_status;
DROP TYPE IF EXISTS relationship_type;
DROP TYPE IF EXISTS user_status;

-- ============================================================================
-- DROP EXTENSIONS (only if no other objects depend on them)
-- ============================================================================

-- Note: We don't drop extensions as other migrations or objects might depend on them
-- DROP EXTENSION IF EXISTS "pg_trgm";
-- DROP EXTENSION IF EXISTS "uuid-ossp";

COMMIT;

--- CONTENT END ---

==========================================

=== FILE: postgresql/001_core_schema.up.sql ===
File Type: sql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/postgresql/001_core_schema.up.sql
Last Modified: Sep 13 19:33:01 2025
File Size: 21274 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION: Core Schema
-- Description: Core user, authentication, and relationship tables
-- Version: 1.0 (Consolidated from migrations 000001-000004, 000031)
-- Date: 2025-01-19
-- ============================================================================

BEGIN;

-- ============================================================================
-- EXTENSIONS AND TYPES
-- ============================================================================

-- PostgreSQL Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Core ENUM Types
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended', 'deleted');
CREATE TYPE relationship_type AS ENUM ('friend', 'contact', 'blocked', 'maybe_friend');
CREATE TYPE relationship_status AS ENUM ('pending', 'accepted', 'declined', 'expired');
CREATE TYPE session_status AS ENUM ('active', 'inactive', 'expired');
CREATE TYPE data_retention_policy AS ENUM ('standard', 'minimal', 'extended');

-- Realtime Messaging ENUM Types (removed message_type as messages table is handled by ScyllaDB)
-- Note: bubble_status, bubble_member_status, and bubble_member_role are defined in migration 002

-- ============================================================================
-- CORE USERS TABLE
-- ============================================================================

CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(40) UNIQUE,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    display_name VARCHAR(200),
    avatar_bucket_name VARCHAR(100),
    avatar_object_key VARCHAR(500),
    avatar_url VARCHAR(500),
    date_of_birth DATE,
    
    -- Status and Permissions
    status user_status DEFAULT 'active',
    is_premium BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    is_private BOOLEAN DEFAULT false,
    is_banned BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    is_present BOOLEAN DEFAULT false,
    
    -- Preferences and Settings
    language_preference VARCHAR(10) DEFAULT 'en',
    notification_settings JSONB DEFAULT '{}',
    onboarding_completed BOOLEAN DEFAULT false,
    website TEXT,
    location_privacy_level VARCHAR(20) DEFAULT 'friends' CHECK (
        location_privacy_level IN ('public', 'friends', 'bubbles', 'custom', 'private')
    ),
    
    -- Location and Compliance
    creation_location VARCHAR(2), -- ISO 3166-1 alpha-2 country code
    country_residence VARCHAR(2), -- ISO 3166-1 alpha-2 country code
    data_retention_policy data_retention_policy DEFAULT 'standard',
    
    -- Timestamps
    banned_at TIMESTAMP WITH TIME ZONE,
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    data_deletion_requested_at TIMESTAMP WITH TIME ZONE,
    data_deletion_scheduled_at TIMESTAMP WITH TIME ZONE,
    data_deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_username_format CHECK (username ~* '^[a-zA-Z0-9_]{3,40}$'),
    CONSTRAINT users_valid_country_codes CHECK (
        creation_location IS NULL OR length(creation_location) = 2
    ),
    CONSTRAINT users_retention_deletion_logic CHECK (
        (data_deletion_requested_at IS NULL) OR 
        (data_deletion_scheduled_at IS NULL OR data_deletion_scheduled_at > data_deletion_requested_at)
    )
);

-- ============================================================================
-- USER AUTHENTICATION TABLES
-- ============================================================================

-- User Sessions
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    status session_status DEFAULT 'active',
    is_active BOOLEAN DEFAULT true,
    ip_address INET,
    user_agent TEXT,
    last_login_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT user_sessions_valid_expiry CHECK (expires_at > created_at)
);

-- Password Reset Tokens
CREATE TABLE password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT password_reset_tokens_valid_expiry CHECK (expires_at > created_at),
    CONSTRAINT password_reset_tokens_usage_logic CHECK (
        used_at IS NULL OR used_at >= created_at
    )
);

-- User Roles
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    role_id VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    assigned_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    revoked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT user_roles_unique_active_role UNIQUE (user_id, role_id, is_active),
    CONSTRAINT user_roles_revocation_logic CHECK (
        revoked_at IS NULL OR revoked_at >= assigned_at
    )
);

-- ============================================================================
-- USER PRESENCE TABLE
-- ============================================================================

CREATE TABLE user_presence (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    is_present BOOLEAN NOT NULL DEFAULT false,
    custom_status VARCHAR(100),

    -- Device and location
    device_type VARCHAR(50),
    device_id VARCHAR(255),
    ip_address INET,
    location_country VARCHAR(2),

    -- Timing
    last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- USER RELATIONSHIPS TABLE (CONSOLIDATED)
-- ============================================================================

CREATE TABLE user_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    relationship_type relationship_type NOT NULL DEFAULT 'friend',
    status relationship_status DEFAULT 'pending',
    source_bubble_id UUID, -- References bubbles(bubble_id) - foreign key will be added in migration 002
    
    -- Bidirectional acceptance tracking
    from_user_accepted BOOLEAN DEFAULT false,
    to_user_accepted BOOLEAN DEFAULT false,
    from_user_accepted_at TIMESTAMP WITH TIME ZONE,
    to_user_accepted_at TIMESTAMP WITH TIME ZONE,
    
    -- Request metadata
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT user_relationships_no_self_relationship CHECK (from_user_id != to_user_id),
    CONSTRAINT user_relationships_unique_relationship UNIQUE (from_user_id, to_user_id, relationship_type),
    CONSTRAINT user_relationships_symmetric_order CHECK (
        relationship_type = 'blocked' OR from_user_id < to_user_id
    ),
    CONSTRAINT user_relationships_acceptance_logic CHECK (
        (status != 'accepted') OR 
        (from_user_accepted = true AND to_user_accepted = true AND accepted_at IS NOT NULL)
    ),
    CONSTRAINT user_relationships_expiry_logic CHECK (
        expires_at IS NULL OR expires_at > created_at
    )
);

-- ============================================================================
-- REALTIME MESSAGING TABLES
-- ============================================================================
-- Note: bubbles and bubble_members tables are defined in migration 002 with proper schema
-- Note: messages table is handled by ScyllaDB realtime service for optimal performance

-- Conversations (Direct Messages)
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    participant_1_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    participant_2_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE,

    -- Constraints
    UNIQUE(participant_1_id, participant_2_id),
    CONSTRAINT conversations_different_participants CHECK (participant_1_id != participant_2_id),
    CONSTRAINT conversations_ordered_participants CHECK (participant_1_id < participant_2_id)
);

-- Pinned Messages (handled by ScyllaDB realtime service)
-- Note: Pinned messages functionality is implemented in ScyllaDB for consistency with messaging

-- Polls (handled by ScyllaDB realtime service)
-- Note: Polls functionality is implemented in ScyllaDB for consistency with messaging

-- Poll Votes (handled by ScyllaDB realtime service)
-- Note: Poll votes functionality is implemented in ScyllaDB for consistency with messaging

-- ============================================================================
-- INDEXES FOR OPTIMAL PERFORMANCE
-- ============================================================================

-- Users table indexes
CREATE INDEX idx_users_search_text ON users USING gin (
    (first_name || ' ' || last_name || ' ' || COALESCE(username, '')) gin_trgm_ops
);
CREATE INDEX idx_users_email_active ON users (email) WHERE is_active = true;
CREATE INDEX idx_users_username_active ON users (username) WHERE is_active = true AND username IS NOT NULL;
CREATE INDEX idx_users_status_active ON users (status, is_active, last_active_at DESC);
CREATE INDEX idx_users_is_present ON users (is_present) WHERE is_present = true;
CREATE INDEX idx_users_last_active ON users (last_active_at DESC) WHERE is_active = true;
CREATE INDEX idx_users_country_residence ON users (country_residence) WHERE country_residence IS NOT NULL;
CREATE INDEX idx_users_data_deletion_scheduled ON users (data_deletion_scheduled_at) WHERE data_deletion_scheduled_at IS NOT NULL;
CREATE INDEX idx_users_birthday_month_day ON users (EXTRACT(month FROM date_of_birth), EXTRACT(day FROM date_of_birth));
CREATE INDEX idx_users_language_preference ON users (language_preference);
CREATE INDEX idx_users_is_verified ON users (is_verified) WHERE is_verified = true;
-- GIN index for JSONB notification_settings to enable efficient queries on notification preferences
CREATE INDEX idx_users_notification_settings ON users USING gin (notification_settings);

-- User sessions indexes
CREATE INDEX idx_user_sessions_user_status ON user_sessions (user_id, status, expires_at);
CREATE INDEX idx_user_sessions_active ON user_sessions (user_id, is_active, last_login_at DESC) WHERE is_active = true;
CREATE INDEX idx_user_sessions_expires_at ON user_sessions (expires_at);
CREATE INDEX idx_user_sessions_session_id ON user_sessions (session_id, status);

-- Password reset tokens indexes
CREATE INDEX idx_password_reset_tokens_token ON password_reset_tokens (token, expires_at);
CREATE INDEX idx_password_reset_tokens_user_id ON password_reset_tokens (user_id, expires_at);
CREATE INDEX idx_password_reset_tokens_expires_at ON password_reset_tokens (expires_at);
CREATE INDEX idx_password_reset_tokens_unused ON password_reset_tokens (user_id, expires_at) WHERE used_at IS NULL;

-- User roles indexes
CREATE INDEX idx_user_roles_user_active ON user_roles (user_id, is_active) WHERE is_active = true;
CREATE INDEX idx_user_roles_role_active ON user_roles (role_id, is_active) WHERE is_active = true;
CREATE INDEX idx_user_roles_assigned_by ON user_roles (assigned_by, assigned_at DESC);

-- User presence indexes
CREATE INDEX idx_user_presence_is_present ON user_presence (is_present, last_seen_at DESC);
CREATE INDEX idx_user_presence_last_seen ON user_presence (last_seen_at DESC);

-- User relationships indexes
CREATE INDEX idx_user_relationships_from_user ON user_relationships (from_user_id, status, relationship_type, created_at DESC);
CREATE INDEX idx_user_relationships_to_user ON user_relationships (to_user_id, status, relationship_type, created_at DESC);
CREATE INDEX idx_user_relationships_bidirectional ON user_relationships (
    LEAST(from_user_id, to_user_id),
    GREATEST(from_user_id, to_user_id),
    relationship_type,
    status
);
-- Note: idx_user_relationships_source_bubble index will be created in migration 002
CREATE INDEX idx_user_relationships_pending ON user_relationships (relationship_type, status, created_at DESC) WHERE status = 'pending';

-- Realtime messaging indexes
-- Note: Bubble-related indexes are defined in migration 002
-- Note: Message indexes are handled by ScyllaDB realtime service

-- Conversations
CREATE INDEX idx_conversations_participant_1 ON conversations (participant_1_id, last_message_at DESC);
CREATE INDEX idx_conversations_participant_2 ON conversations (participant_2_id, last_message_at DESC);
CREATE INDEX idx_conversations_last_message ON conversations (last_message_at DESC);

-- Pinned messages, polls, and poll votes indexes handled by ScyllaDB realtime service

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to check if users are friends
CREATE OR REPLACE FUNCTION are_users_friends(user1_id UUID, user2_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_relationships
        WHERE ((from_user_id = user1_id AND to_user_id = user2_id) OR
               (from_user_id = user2_id AND to_user_id = user1_id))
        AND relationship_type = 'friend'
        AND status = 'accepted'
    );
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get relationship status between users
CREATE OR REPLACE FUNCTION get_relationship_status(user1_id UUID, user2_id UUID, rel_type relationship_type DEFAULT 'friend')
RETURNS relationship_status AS $$
DECLARE
    result relationship_status;
BEGIN
    SELECT status INTO result
    FROM user_relationships
    WHERE ((from_user_id = user1_id AND to_user_id = user2_id) OR
           (from_user_id = user2_id AND to_user_id = user1_id))
    AND relationship_type = rel_type
    LIMIT 1;

    RETURN COALESCE(result, 'pending');
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to update user last_active_at
CREATE OR REPLACE FUNCTION update_user_last_active(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE users
    SET last_active_at = NOW(), updated_at = NOW()
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to synchronize presence status between users and user_presence tables
CREATE OR REPLACE FUNCTION sync_user_presence()
RETURNS TRIGGER AS $$
BEGIN
    -- Update users.is_present when user_presence.is_present changes
    UPDATE users
    SET is_present = NEW.is_present, updated_at = NOW()
    WHERE user_id = NEW.user_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Note: is_bubble_member function is defined in migration 002

-- Function to get conversation between two users
CREATE OR REPLACE FUNCTION get_or_create_conversation(user1_uuid UUID, user2_uuid UUID)
RETURNS UUID AS $$
DECLARE
    conversation_uuid UUID;
    participant1 UUID;
    participant2 UUID;
BEGIN
    -- Ensure consistent ordering
    IF user1_uuid < user2_uuid THEN
        participant1 := user1_uuid;
        participant2 := user2_uuid;
    ELSE
        participant1 := user2_uuid;
        participant2 := user1_uuid;
    END IF;

    -- Try to find existing conversation
    SELECT id INTO conversation_uuid
    FROM conversations
    WHERE participant_1_id = participant1 AND participant_2_id = participant2;

    -- Create if doesn't exist
    IF conversation_uuid IS NULL THEN
        INSERT INTO conversations (participant_1_id, participant_2_id)
        VALUES (participant1, participant2)
        RETURNING id INTO conversation_uuid;
    END IF;

    RETURN conversation_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to update conversation last message timestamp (handled by ScyllaDB realtime service)
-- Note: Conversation updates are managed by the realtime service

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Trigger function for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers to all tables
CREATE TRIGGER trigger_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_sessions_updated_at BEFORE UPDATE ON user_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_password_reset_tokens_updated_at BEFORE UPDATE ON password_reset_tokens FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_roles_updated_at BEFORE UPDATE ON user_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_presence_updated_at BEFORE UPDATE ON user_presence FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_presence_sync AFTER INSERT OR UPDATE ON user_presence FOR EACH ROW EXECUTE FUNCTION sync_user_presence();
CREATE TRIGGER trigger_user_relationships_updated_at BEFORE UPDATE ON user_relationships FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Realtime messaging triggers
-- Note: Bubble-related triggers are defined in migration 002
-- Note: Message-related triggers are handled by ScyllaDB realtime service
CREATE TRIGGER trigger_conversations_updated_at BEFORE UPDATE ON conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- TABLE COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE users IS 'Core user profiles with authentication data, preferences, and GDPR compliance fields';
COMMENT ON TABLE user_sessions IS 'Active user sessions for authentication and session management';
COMMENT ON TABLE password_reset_tokens IS 'Temporary tokens for secure password reset functionality';
COMMENT ON TABLE user_roles IS 'User role assignments for authorization and permission management';
COMMENT ON TABLE user_presence IS 'User presence tracking with simplified boolean status system';
COMMENT ON TABLE user_relationships IS 'Consolidated user relationships (friends, contacts, blocked users, maybe_friends)';

-- Realtime messaging table comments
-- Note: Bubble-related table comments are defined in migration 002
-- Note: Messages, pinned messages, polls, and poll votes are handled by ScyllaDB realtime service
COMMENT ON TABLE conversations IS 'Direct message conversations between two users';

COMMENT ON COLUMN users.user_id IS 'Primary key - unique user identifier';
COMMENT ON COLUMN users.username IS 'Optional unique username (3-40 alphanumeric characters and underscores)';
COMMENT ON COLUMN users.email IS 'Required unique email address with format validation';
COMMENT ON COLUMN users.data_retention_policy IS 'GDPR compliance: standard (7 years), minimal (1 year), or extended (indefinite)';
COMMENT ON COLUMN users.status IS 'User account status for moderation and lifecycle management';
COMMENT ON COLUMN users.is_present IS 'Boolean presence status: true = online, false = offline';
COMMENT ON COLUMN user_presence.is_present IS 'Boolean presence status: true = online, false = offline';
COMMENT ON COLUMN user_relationships.relationship_type IS 'Type of relationship: friend, contact, blocked, or maybe_friend';
COMMENT ON COLUMN user_relationships.source_bubble_id IS 'Optional bubble where the relationship originated';

-- Realtime messaging column comments
-- Note: Bubble-related column comments are defined in migration 002
-- Note: Message-related column comments are handled by ScyllaDB realtime service
COMMENT ON COLUMN conversations.participant_1_id IS 'First participant (always the smaller UUID for consistency)';
COMMENT ON COLUMN conversations.participant_2_id IS 'Second participant (always the larger UUID for consistency)';

COMMIT;

--- CONTENT END ---

==========================================

=== FILE: postgresql/002_bubble_social_schema.down.sql ===
File Type: sql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/postgresql/002_bubble_social_schema.down.sql
Last Modified: Sep  6 16:26:39 2025
File Size: 5645 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION ROLLBACK: Bubble and Social Features Schema
-- Description: Safely remove bubble system, requests, voting, and social interaction tables
-- Version: 1.0 (Consolidated rollback)
-- Date: 2025-01-19
-- ============================================================================

BEGIN;

-- ============================================================================
-- DROP TRIGGERS (in reverse order)
-- ============================================================================

DROP TRIGGER IF EXISTS trigger_kickout_records_updated_at ON kickout_records;
DROP TRIGGER IF EXISTS trigger_bubble_request_votes_updated_at ON bubble_request_votes;
DROP TRIGGER IF EXISTS trigger_bubble_member_count_update ON bubble_members;
DROP TRIGGER IF EXISTS trigger_bubble_kickout_votes_updated_at ON bubble_kickout_votes;
DROP TRIGGER IF EXISTS trigger_bubble_requests_updated_at ON bubble_requests;
DROP TRIGGER IF EXISTS trigger_bubble_members_updated_at ON bubble_members;
DROP TRIGGER IF EXISTS trigger_bubbles_updated_at ON bubbles;

-- ============================================================================
-- DROP FUNCTIONS
-- ============================================================================

DROP FUNCTION IF EXISTS log_bubble_activity(UUID, UUID, activity_event_type, TEXT, JSONB);
DROP FUNCTION IF EXISTS update_bubble_member_count();
DROP FUNCTION IF EXISTS get_bubble_member_count(UUID);
DROP FUNCTION IF EXISTS is_bubble_member(UUID, UUID);

-- ============================================================================
-- DROP FOREIGN KEY CONSTRAINTS
-- ============================================================================

ALTER TABLE user_relationships DROP CONSTRAINT IF EXISTS fk_user_relationships_source_bubble;

-- ============================================================================
-- DROP INDEXES (explicit cleanup)
-- ============================================================================

-- Bubble history snapshots indexes
DROP INDEX IF EXISTS idx_bubble_history_created_at;
DROP INDEX IF EXISTS idx_bubble_history_triggered_by;
DROP INDEX IF EXISTS idx_bubble_history_bubble_type;

-- Bubble activity history indexes
DROP INDEX IF EXISTS idx_bubble_activity_related_user;
DROP INDEX IF EXISTS idx_bubble_activity_event_type;
DROP INDEX IF EXISTS idx_bubble_activity_user_time;
DROP INDEX IF EXISTS idx_bubble_activity_bubble_time;

-- Bubble request votes indexes
DROP INDEX IF EXISTS idx_bubble_request_votes_vote_type;
DROP INDEX IF EXISTS idx_bubble_request_votes_user;
DROP INDEX IF EXISTS idx_bubble_request_votes_request;

-- Kickout records indexes
DROP INDEX IF EXISTS idx_kickout_records_ban_check;
DROP INDEX IF EXISTS idx_kickout_records_rejoin_time;
DROP INDEX IF EXISTS idx_kickout_records_bubble_time;
DROP INDEX IF EXISTS idx_kickout_records_kicked_by;
DROP INDEX IF EXISTS idx_kickout_records_target_user;
DROP INDEX IF EXISTS idx_kickout_records_bubble_target;

-- Bubble kickout votes indexes
DROP INDEX IF EXISTS idx_bubble_kickout_votes_target;
DROP INDEX IF EXISTS idx_bubble_kickout_votes_voter;
DROP INDEX IF EXISTS idx_bubble_kickout_votes_bubble_target;

-- Bubble requests indexes
DROP INDEX IF EXISTS idx_bubble_requests_pending;
DROP INDEX IF EXISTS idx_bubble_requests_expires_at;
DROP INDEX IF EXISTS idx_bubble_requests_type_status;
DROP INDEX IF EXISTS idx_bubble_requests_target;
DROP INDEX IF EXISTS idx_bubble_requests_requester;
DROP INDEX IF EXISTS idx_bubble_requests_bubble_status;

-- Bubble members indexes
DROP INDEX IF EXISTS idx_bubble_members_left_at;
DROP INDEX IF EXISTS idx_bubble_members_invited_by;
DROP INDEX IF EXISTS idx_bubble_members_role;
DROP INDEX IF EXISTS idx_bubble_members_user_active;
DROP INDEX IF EXISTS idx_bubble_members_bubble_status;

-- Bubbles table indexes
DROP INDEX IF EXISTS idx_bubbles_most_ancient_member;
DROP INDEX IF EXISTS idx_bubbles_location_updated_at;
DROP INDEX IF EXISTS idx_bubbles_location;
DROP INDEX IF EXISTS idx_bubbles_member_count;
DROP INDEX IF EXISTS idx_bubbles_name_search;
DROP INDEX IF EXISTS idx_bubbles_expires_at;
DROP INDEX IF EXISTS idx_bubbles_invite_code;
DROP INDEX IF EXISTS idx_bubbles_public;
DROP INDEX IF EXISTS idx_bubbles_status_active;
DROP INDEX IF EXISTS idx_bubbles_created_by;

-- ============================================================================
-- DROP TABLES (in reverse dependency order)
-- ============================================================================

-- Drop role management tables first (newest additions)
DROP TABLE IF EXISTS role_permissions CASCADE;
DROP TABLE IF EXISTS permissions CASCADE;
DROP TABLE IF EXISTS roles CASCADE;

-- Drop bubble tables
DROP TABLE IF EXISTS kickout_records CASCADE;
DROP TABLE IF EXISTS bubble_request_votes CASCADE;
DROP TABLE IF EXISTS bubble_history_snapshots CASCADE;
DROP TABLE IF EXISTS bubble_activity_history CASCADE;
DROP TABLE IF EXISTS bubble_kickout_votes CASCADE;
DROP TABLE IF EXISTS bubble_requests CASCADE;
DROP TABLE IF EXISTS bubble_members CASCADE;
DROP TABLE IF EXISTS bubbles CASCADE;

-- ============================================================================
-- DROP TYPES (in reverse dependency order)
-- ============================================================================

DROP TYPE IF EXISTS activity_event_type;
DROP TYPE IF EXISTS vote_type;
DROP TYPE IF EXISTS request_status;
DROP TYPE IF EXISTS bubble_request_type;
DROP TYPE IF EXISTS bubble_member_role;
DROP TYPE IF EXISTS bubble_member_status;
DROP TYPE IF EXISTS bubble_status;

COMMIT;

--- CONTENT END ---

==========================================

=== FILE: postgresql/002_bubble_social_schema.up.sql ===
File Type: sql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/postgresql/002_bubble_social_schema.up.sql
Last Modified: Sep 13 12:00:28 2025
File Size: 32618 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION: Bubble and Social Features Schema
-- Description: Bubble system, requests, voting, and social interaction tables
-- Version: 1.0 (Consolidated from migrations 000003, 000012, 000016, 000022, 000032)
-- Date: 2025-01-19
-- ============================================================================

BEGIN;

-- ============================================================================
-- EXTENSIONS SETUP
-- ============================================================================

-- Extensions will be added here when needed for location features

-- ============================================================================
-- BUBBLE SYSTEM ENUMS
-- ============================================================================

CREATE TYPE bubble_status AS ENUM ('active', 'expired', 'dissolved', 'archived');
CREATE TYPE bubble_member_status AS ENUM ('active', 'left', 'removed', 'pending', 'accepted', 'declined');
CREATE TYPE bubble_member_role AS ENUM ('member', 'admin', 'creator');
CREATE TYPE bubble_request_type AS ENUM ('invite', 'join', 'propose', 'kick', 'start');
CREATE TYPE request_status AS ENUM ('pending', 'approved', 'rejected', 'expired', 'invitee_accepted', 'member_accepted');
CREATE TYPE vote_type AS ENUM ('approve', 'reject');
CREATE TYPE activity_event_type AS ENUM (
    'MESSAGE', 'MEMBER_JOIN', 'MEMBER_LEAVE', 'CALL_START', 'CALL_END',
    'BUBBLE_CREATED', 'BUBBLE_UPDATED', 'MEDIA_SHARED', 'REACTION_ADDED',
    'REACTION_REMOVED', 'MESSAGE_EDITED', 'MESSAGE_DELETED', 'MEMBER_ROLE_CHANGED',
    'BUBBLE_ARCHIVED', 'BUBBLE_UNARCHIVED'
);

-- ============================================================================
-- CORE BUBBLES TABLE
-- ============================================================================

CREATE TABLE bubbles (
    bubble_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users(user_id) ON DELETE RESTRICT,
    status bubble_status DEFAULT 'active',
    
    -- Capacity and membership
    max_members INTEGER DEFAULT 10 CHECK (max_members > 0 AND max_members <= 50),
    current_member_count INTEGER DEFAULT 1 CHECK (current_member_count >= 0),
    
    -- Visibility and access
    is_public BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    invite_code VARCHAR(20) UNIQUE,
    
    -- Media and customization
    avatar_url TEXT,
    background_color VARCHAR(7), -- Hex color code

    -- Location tracking
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_updated_at TIMESTAMP WITH TIME ZONE,
    most_ancient_member_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    location_source VARCHAR(50) DEFAULT 'ancient_member',

    -- Lifecycle timestamps
    expires_at TIMESTAMP WITH TIME ZONE,
    archived_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bubbles_valid_member_count CHECK (current_member_count <= max_members),
    CONSTRAINT bubbles_valid_expiry CHECK (expires_at IS NULL OR expires_at > created_at),
    CONSTRAINT bubbles_archived_logic CHECK (
        (is_archived = false AND archived_at IS NULL) OR 
        (is_archived = true AND archived_at IS NOT NULL)
    ),
    CONSTRAINT bubbles_valid_hex_color CHECK (
        background_color IS NULL OR background_color ~* '^#[0-9A-Fa-f]{6}$'
    ),
    CONSTRAINT bubbles_valid_coordinates CHECK (
        (latitude IS NULL AND longitude IS NULL) OR
        (latitude IS NOT NULL AND longitude IS NOT NULL AND
         latitude >= -90 AND latitude <= 90 AND
         longitude >= -180 AND longitude <= 180)
    ),
    CONSTRAINT bubbles_location_source_valid CHECK (
        location_source IN ('ancient_member', 'creator', 'manual', 'system')
    )
);

-- ============================================================================
-- BUBBLE MEMBERS TABLE
-- ============================================================================

CREATE TABLE bubble_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    status bubble_member_status DEFAULT 'pending',
    role bubble_member_role DEFAULT 'member',
    
    -- Membership lifecycle
    invited_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    removed_at TIMESTAMP WITH TIME ZONE,
    removed_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    
    -- Member preferences
    is_muted BOOLEAN DEFAULT false,
    notification_level VARCHAR(20) DEFAULT 'all' CHECK (notification_level IN ('all', 'mentions', 'none')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bubble_members_unique_user_bubble UNIQUE (bubble_id, user_id),
    CONSTRAINT bubble_members_lifecycle_logic CHECK (
        (status IN ('active', 'pending') AND left_at IS NULL AND removed_at IS NULL) OR
        (status = 'left' AND left_at IS NOT NULL AND removed_at IS NULL) OR
        (status = 'removed' AND removed_at IS NOT NULL AND removed_by IS NOT NULL)
    ),
    CONSTRAINT bubble_members_role_logic CHECK (
        (role = 'creator' AND invited_by IS NULL) OR
        (role IN ('member', 'admin') AND invited_by IS NOT NULL)
    )
);

-- ============================================================================
-- BUBBLE REQUESTS TABLE
-- ============================================================================

CREATE TABLE bubble_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    requester_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES users(user_id) ON DELETE CASCADE, -- NULL for join requests
    request_type bubble_request_type NOT NULL,
    status request_status DEFAULT 'pending',
    
    -- Request content
    message TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Lifecycle
    expires_at TIMESTAMP WITH TIME ZONE,
    responded_at TIMESTAMP WITH TIME ZONE,
    responded_by UUID REFERENCES users(user_id) ON DELETE SET NULL,

    -- Two-step approval process tracking
    initial_acceptor_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    initial_accepted_at TIMESTAMP WITH TIME ZONE,

    -- FIFO Queue System
    queue_position INTEGER DEFAULT NULL, -- Position in FIFO queue (1-based), NULL for active requests
    queue_status VARCHAR(20) DEFAULT 'active' CHECK (queue_status IN ('active', 'queued')), -- Whether request is active or queued

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bubble_requests_target_logic CHECK (
        (request_type IN ('join', 'propose', 'start') AND target_user_id IS NULL) OR
        (request_type IN ('invite', 'kick') AND target_user_id IS NOT NULL)
    ),
    CONSTRAINT bubble_requests_response_logic CHECK (
        (status = 'pending' AND responded_at IS NULL AND responded_by IS NULL) OR
        (status != 'pending' AND responded_at IS NOT NULL)
    ),
    CONSTRAINT bubble_requests_valid_expiry CHECK (
        expires_at IS NULL OR expires_at > created_at
    )
);

-- ============================================================================
-- BUBBLE KICKOUT VOTES TABLE
-- ============================================================================

CREATE TABLE bubble_kickout_votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    target_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    voter_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    vote vote_type NOT NULL,
    reason TEXT,
    
    -- Vote metadata
    is_anonymous BOOLEAN DEFAULT false,
    weight INTEGER DEFAULT 1 CHECK (weight > 0),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bubble_kickout_votes_unique_voter UNIQUE (bubble_id, target_user_id, voter_id),
    CONSTRAINT bubble_kickout_votes_no_self_vote CHECK (target_user_id != voter_id)
);

-- ============================================================================
-- BUBBLE ACTIVITY HISTORY TABLE
-- ============================================================================

CREATE TABLE bubble_activity_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    event_type activity_event_type NOT NULL,
    
    -- Event content and metadata
    content TEXT,
    content_type VARCHAR(50),
    metadata JSONB DEFAULT '{}',
    
    -- Related entities
    related_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    related_message_id UUID,
    
    -- Event context
    ip_address INET,
    user_agent TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bubble_activity_valid_content_type CHECK (
        content_type IS NULL OR content_type IN ('text', 'media', 'system', 'action')
    )
);

-- ============================================================================
-- BUBBLE HISTORY SNAPSHOTS TABLE
-- ============================================================================

CREATE TABLE bubble_history_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    snapshot_type VARCHAR(50) NOT NULL,
    
    -- Snapshot data
    snapshot_data JSONB NOT NULL,
    member_count INTEGER NOT NULL,
    active_member_count INTEGER NOT NULL,
    
    -- Snapshot metadata
    triggered_by UUID REFERENCES users(user_id) ON DELETE SET NULL,
    trigger_event VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bubble_history_valid_snapshot_type CHECK (
        snapshot_type IN ('daily', 'weekly', 'event_triggered', 'manual')
    ),
    CONSTRAINT bubble_history_valid_member_counts CHECK (
        member_count >= 0 AND active_member_count >= 0 AND active_member_count <= member_count
    )
);

-- ============================================================================
-- BUBBLE REQUEST VOTES TABLE
-- ============================================================================
-- This table tracks votes for bubble join/invite requests to implement
-- the unanimous voting requirement specified in the business logic.

CREATE TABLE bubble_request_votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id UUID NOT NULL REFERENCES bubble_requests(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    vote vote_type NOT NULL, -- 'approve' or 'reject' (reusing existing enum)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bubble_request_votes_unique_voter UNIQUE (request_id, user_id),
    CONSTRAINT bubble_request_votes_valid_vote CHECK (vote IN ('approve', 'reject'))
);

-- ============================================================================
-- KICKOUT RECORDS TABLE
-- ============================================================================
-- This table tracks when users are kicked out of bubbles to implement
-- the ban enforcement logic. Users who are kicked out are banned from
-- rejoining unless allowed_to_rejoin_at is set (for temporary bans).

CREATE TABLE kickout_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    target_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    target_user_name VARCHAR(255) NOT NULL,
    kicked_by_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    kicked_by_user_name VARCHAR(255) NOT NULL,
    kicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reason TEXT,
    allowed_to_rejoin_at TIMESTAMP WITH TIME ZONE, -- NULL = permanent ban, future date = temporary ban
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT kickout_records_no_self_kick CHECK (target_user_id != kicked_by_user_id),
    CONSTRAINT kickout_records_valid_rejoin_time CHECK (
        allowed_to_rejoin_at IS NULL OR allowed_to_rejoin_at > kicked_at
    )
);

-- ============================================================================
-- INDEXES FOR OPTIMAL PERFORMANCE
-- ============================================================================

-- Bubbles table indexes
CREATE INDEX idx_bubbles_created_by ON bubbles (created_by, created_at DESC);
CREATE INDEX idx_bubbles_status_active ON bubbles (status, is_archived, created_at DESC) WHERE status = 'active';
CREATE INDEX idx_bubbles_public ON bubbles (is_public, status, created_at DESC) WHERE is_public = true;
CREATE INDEX idx_bubbles_invite_code ON bubbles (invite_code) WHERE invite_code IS NOT NULL;
CREATE INDEX idx_bubbles_expires_at ON bubbles (expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_bubbles_name_search ON bubbles USING gin (to_tsvector('english', name));
CREATE INDEX idx_bubbles_member_count ON bubbles (current_member_count, max_members);
-- Standard indexes for location data (PostGIS spatial index will be added later when implementing location features)
CREATE INDEX idx_bubbles_latitude ON bubbles (latitude) WHERE latitude IS NOT NULL;
CREATE INDEX idx_bubbles_longitude ON bubbles (longitude) WHERE longitude IS NOT NULL;
CREATE INDEX idx_bubbles_location_updated_at ON bubbles (location_updated_at DESC) WHERE location_updated_at IS NOT NULL;
CREATE INDEX idx_bubbles_most_ancient_member ON bubbles (most_ancient_member_id) WHERE most_ancient_member_id IS NOT NULL;

-- Bubble members indexes
CREATE INDEX idx_bubble_members_bubble_status ON bubble_members (bubble_id, status, joined_at DESC);
CREATE INDEX idx_bubble_members_user_active ON bubble_members (user_id, status) WHERE status = 'active';
CREATE INDEX idx_bubble_members_role ON bubble_members (bubble_id, role, status);
CREATE INDEX idx_bubble_members_invited_by ON bubble_members (invited_by, created_at DESC);
CREATE INDEX idx_bubble_members_left_at ON bubble_members (left_at DESC) WHERE left_at IS NOT NULL;

-- Bubble requests indexes
CREATE INDEX idx_bubble_requests_bubble_status ON bubble_requests (bubble_id, status, created_at DESC);
CREATE INDEX idx_bubble_requests_requester ON bubble_requests (requester_id, status, created_at DESC);
CREATE INDEX idx_bubble_requests_target ON bubble_requests (target_user_id, status, created_at DESC) WHERE target_user_id IS NOT NULL;
CREATE INDEX idx_bubble_requests_type_status ON bubble_requests (request_type, status, created_at DESC);
CREATE INDEX idx_bubble_requests_expires_at ON bubble_requests (expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_bubble_requests_pending ON bubble_requests (bubble_id, request_type, status) WHERE status = 'pending';
CREATE INDEX idx_bubble_requests_initial_acceptance ON bubble_requests (bubble_id, status, initial_accepted_at) WHERE status IN ('invitee_accepted', 'member_accepted');

-- Bubble kickout votes indexes
CREATE INDEX idx_bubble_kickout_votes_bubble_target ON bubble_kickout_votes (bubble_id, target_user_id, created_at DESC);
CREATE INDEX idx_bubble_kickout_votes_voter ON bubble_kickout_votes (voter_id, created_at DESC);
CREATE INDEX idx_bubble_kickout_votes_target ON bubble_kickout_votes (target_user_id, vote, created_at DESC);

-- Bubble activity history indexes
CREATE INDEX idx_bubble_activity_bubble_time ON bubble_activity_history (bubble_id, created_at DESC);
CREATE INDEX idx_bubble_activity_user_time ON bubble_activity_history (user_id, created_at DESC) WHERE user_id IS NOT NULL;
CREATE INDEX idx_bubble_activity_event_type ON bubble_activity_history (event_type, created_at DESC);
CREATE INDEX idx_bubble_activity_related_user ON bubble_activity_history (related_user_id, created_at DESC) WHERE related_user_id IS NOT NULL;

-- Bubble history snapshots indexes
CREATE INDEX idx_bubble_history_bubble_type ON bubble_history_snapshots (bubble_id, snapshot_type, created_at DESC);
CREATE INDEX idx_bubble_history_triggered_by ON bubble_history_snapshots (triggered_by, created_at DESC) WHERE triggered_by IS NOT NULL;
CREATE INDEX idx_bubble_history_created_at ON bubble_history_snapshots (created_at DESC);

-- Bubble request votes indexes
CREATE INDEX idx_bubble_request_votes_request ON bubble_request_votes (request_id, created_at DESC);
CREATE INDEX idx_bubble_request_votes_user ON bubble_request_votes (user_id, created_at DESC);
CREATE INDEX idx_bubble_request_votes_vote_type ON bubble_request_votes (vote, created_at DESC);

-- Kickout records indexes
CREATE INDEX idx_kickout_records_bubble_target ON kickout_records (bubble_id, target_user_id, kicked_at DESC);
CREATE INDEX idx_kickout_records_target_user ON kickout_records (target_user_id, kicked_at DESC);
CREATE INDEX idx_kickout_records_kicked_by ON kickout_records (kicked_by_user_id, kicked_at DESC);
CREATE INDEX idx_kickout_records_bubble_time ON kickout_records (bubble_id, kicked_at DESC);
CREATE INDEX idx_kickout_records_rejoin_time ON kickout_records (allowed_to_rejoin_at) WHERE allowed_to_rejoin_at IS NOT NULL;

-- Composite index for ban checking (most common query)
-- Note: Removed NOW() from predicate as it's not immutable
CREATE INDEX idx_kickout_records_ban_check ON kickout_records (bubble_id, target_user_id, allowed_to_rejoin_at);

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to check if user is bubble member
CREATE OR REPLACE FUNCTION is_bubble_member(bubble_uuid UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM bubble_members
        WHERE bubble_id = bubble_uuid
        AND user_id = user_uuid
        AND status = 'active'
    );
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get bubble member count
CREATE OR REPLACE FUNCTION get_bubble_member_count(bubble_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    member_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO member_count
    FROM bubble_members
    WHERE bubble_id = bubble_uuid
    AND status = 'active';

    RETURN COALESCE(member_count, 0);
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to update bubble member count
CREATE OR REPLACE FUNCTION update_bubble_member_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE bubbles
        SET current_member_count = get_bubble_member_count(NEW.bubble_id),
            updated_at = NOW()
        WHERE bubble_id = NEW.bubble_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE bubbles
        SET current_member_count = get_bubble_member_count(OLD.bubble_id),
            updated_at = NOW()
        WHERE bubble_id = OLD.bubble_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to log bubble activity
CREATE OR REPLACE FUNCTION log_bubble_activity(
    bubble_uuid UUID,
    user_uuid UUID,
    event_type_param activity_event_type,
    content_param TEXT DEFAULT NULL,
    metadata_param JSONB DEFAULT '{}'
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO bubble_activity_history (
        bubble_id, user_id, event_type, content, metadata
    ) VALUES (
        bubble_uuid, user_uuid, event_type_param, content_param, metadata_param
    );
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Apply updated_at triggers to all tables
CREATE TRIGGER trigger_bubbles_updated_at BEFORE UPDATE ON bubbles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_bubble_members_updated_at BEFORE UPDATE ON bubble_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_bubble_requests_updated_at BEFORE UPDATE ON bubble_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_bubble_kickout_votes_updated_at BEFORE UPDATE ON bubble_kickout_votes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Triggers for new tables
CREATE TRIGGER trigger_bubble_request_votes_updated_at 
    BEFORE UPDATE ON bubble_request_votes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_kickout_records_updated_at 
    BEFORE UPDATE ON kickout_records 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to automatically update bubble member count
CREATE TRIGGER trigger_bubble_member_count_update
    AFTER INSERT OR UPDATE OR DELETE ON bubble_members
    FOR EACH ROW EXECUTE FUNCTION update_bubble_member_count();

-- ============================================================================
-- FOREIGN KEY CONSTRAINTS (DEFERRED)
-- ============================================================================

-- Add foreign key constraint to user_relationships.source_bubble_id
ALTER TABLE user_relationships
ADD CONSTRAINT fk_user_relationships_source_bubble
FOREIGN KEY (source_bubble_id) REFERENCES bubbles(bubble_id) ON DELETE SET NULL;

-- ============================================================================
-- TABLE COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE bubbles IS 'Core bubble table for temporary social groups with lifecycle management';
COMMENT ON TABLE bubble_members IS 'Bubble membership with roles, status tracking, and notification preferences';
COMMENT ON TABLE bubble_requests IS 'All types of bubble requests (invite, join, kick, propose, start) with unified workflow';
COMMENT ON TABLE bubble_kickout_votes IS 'Voting system for democratic bubble member removal';
COMMENT ON TABLE bubble_activity_history IS 'Comprehensive activity log for all bubble events and interactions';
COMMENT ON TABLE bubble_history_snapshots IS 'Periodic snapshots of bubble state for analytics and recovery';

COMMENT ON COLUMN bubbles.invite_code IS 'Optional unique code for easy bubble joining';
COMMENT ON COLUMN bubble_members.notification_level IS 'Member notification preferences: all, mentions, or none';
COMMENT ON COLUMN bubble_requests.metadata IS 'Flexible JSON metadata for request-specific data';
COMMENT ON COLUMN bubble_requests.initial_acceptor_id IS 'User who performed the initial acceptance (invitee for invites, member for joins)';
COMMENT ON COLUMN bubble_requests.initial_accepted_at IS 'Timestamp when initial acceptance occurred';
COMMENT ON COLUMN bubble_activity_history.related_message_id IS 'Reference to message ID for message-related events';

-- ============================================================================
-- ROLE MANAGEMENT INFRASTRUCTURE
-- ============================================================================

-- ============================================================================
-- ROLES TABLE - Master role definitions
-- ============================================================================

CREATE TABLE roles (
    id VARCHAR(50) PRIMARY KEY, -- Role identifier (e.g., 'user', 'admin', 'moderator')
    name VARCHAR(100) NOT NULL UNIQUE, -- Human-readable role name
    description TEXT, -- Role description for admin interfaces
    is_default BOOLEAN NOT NULL DEFAULT false, -- Only one role can be default
    is_system BOOLEAN NOT NULL DEFAULT false, -- System roles cannot be deleted
    is_active BOOLEAN NOT NULL DEFAULT true, -- Soft delete support

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(user_id) ON DELETE SET NULL,

    -- Constraints
    CONSTRAINT roles_valid_id CHECK (id ~ '^[a-z][a-z0-9_]*$'), -- lowercase, alphanumeric + underscore
    CONSTRAINT roles_name_not_empty CHECK (LENGTH(TRIM(name)) > 0)
);

-- ============================================================================
-- PERMISSIONS TABLE - Master permission definitions
-- ============================================================================

CREATE TABLE permissions (
    id VARCHAR(50) PRIMARY KEY, -- Permission identifier (e.g., 'user.read', 'bubble.create')
    name VARCHAR(100) NOT NULL UNIQUE, -- Human-readable permission name
    description TEXT, -- Permission description
    resource VARCHAR(50) NOT NULL, -- Resource type (e.g., 'user', 'bubble', 'message')
    action VARCHAR(50) NOT NULL, -- Action type (e.g., 'read', 'write', 'delete', 'admin')
    is_system BOOLEAN NOT NULL DEFAULT false, -- System permissions cannot be deleted
    is_active BOOLEAN NOT NULL DEFAULT true, -- Soft delete support

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT permissions_valid_id CHECK (id ~ '^[a-z][a-z0-9_]*\.[a-z][a-z0-9_]*$'), -- format: resource.action
    CONSTRAINT permissions_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT permissions_resource_not_empty CHECK (LENGTH(TRIM(resource)) > 0),
    CONSTRAINT permissions_action_not_empty CHECK (LENGTH(TRIM(action)) > 0)
);

-- ============================================================================
-- ROLE_PERMISSIONS TABLE - Many-to-many relationship
-- ============================================================================

CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id VARCHAR(50) NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id VARCHAR(50) NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by UUID REFERENCES users(user_id) ON DELETE SET NULL,

    -- Constraints
    CONSTRAINT role_permissions_unique_pair UNIQUE (role_id, permission_id)
);

-- ============================================================================
-- ROLE MANAGEMENT INDEXES FOR PERFORMANCE
-- ============================================================================

-- Roles table indexes
CREATE INDEX idx_roles_is_default ON roles (is_default) WHERE is_default = true;
CREATE INDEX idx_roles_is_active ON roles (is_active) WHERE is_active = true;
CREATE INDEX idx_roles_created_at ON roles (created_at DESC);

-- Permissions table indexes
CREATE INDEX idx_permissions_resource ON permissions (resource);
CREATE INDEX idx_permissions_action ON permissions (action);
CREATE INDEX idx_permissions_resource_action ON permissions (resource, action);
CREATE INDEX idx_permissions_is_active ON permissions (is_active) WHERE is_active = true;

-- Role permissions table indexes
CREATE INDEX idx_role_permissions_role_id ON role_permissions (role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions (permission_id);

-- ============================================================================
-- ROLE MANAGEMENT CONSTRAINTS TO ENSURE DATA INTEGRITY
-- ============================================================================

-- Ensure only one role can be marked as default
CREATE UNIQUE INDEX idx_roles_single_default ON roles (is_default) WHERE is_default = true;

-- ============================================================================
-- SEED BASIC ROLES AND PERMISSIONS
-- ============================================================================

-- Insert basic system roles
INSERT INTO roles (id, name, description, is_system, is_active, created_at) VALUES
    ('user', 'User', 'Standard user with basic permissions', true, true, NOW()),
    ('admin', 'Administrator', 'Full system administrator with all permissions', true, true, NOW()),
    ('moderator', 'Moderator', 'Content moderator with limited admin permissions', true, true, NOW());

-- Insert basic system permissions
INSERT INTO permissions (id, name, description, resource, action, is_system, is_active, created_at) VALUES
    -- User permissions
    ('user.read', 'Read User Data', 'View user profiles and basic information', 'user', 'read', true, true, NOW()),
    ('user.write', 'Update User Data', 'Update own user profile and settings', 'user', 'write', true, true, NOW()),
    ('user.admin', 'Administer Users', 'Full user management including ban/unban', 'user', 'admin', true, true, NOW()),

    -- Bubble permissions
    ('bubble.read', 'Read Bubbles', 'View bubble information and participate', 'bubble', 'read', true, true, NOW()),
    ('bubble.write', 'Manage Bubbles', 'Create, update, and manage bubbles', 'bubble', 'write', true, true, NOW()),
    ('bubble.admin', 'Administer Bubbles', 'Full bubble administration including deletion', 'bubble', 'admin', true, true, NOW()),

    -- Message permissions
    ('message.read', 'Read Messages', 'View messages in accessible bubbles', 'message', 'read', true, true, NOW()),
    ('message.write', 'Send Messages', 'Send messages and media in bubbles', 'message', 'write', true, true, NOW()),
    ('message.moderate', 'Moderate Messages', 'Delete inappropriate messages', 'message', 'moderate', true, true, NOW()),

    -- System permissions
    ('system.admin', 'System Administration', 'Full system administration access', 'system', 'admin', true, true, NOW());

-- Assign permissions to roles
INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES
    -- User role permissions (basic user functionality)
    ('user', 'user.read', NOW()),
    ('user', 'user.write', NOW()),
    ('user', 'bubble.read', NOW()),
    ('user', 'bubble.write', NOW()),
    ('user', 'message.read', NOW()),
    ('user', 'message.write', NOW()),

    -- Moderator role permissions (user permissions + moderation)
    ('moderator', 'user.read', NOW()),
    ('moderator', 'user.write', NOW()),
    ('moderator', 'bubble.read', NOW()),
    ('moderator', 'bubble.write', NOW()),
    ('moderator', 'bubble.admin', NOW()),
    ('moderator', 'message.read', NOW()),
    ('moderator', 'message.write', NOW()),
    ('moderator', 'message.moderate', NOW()),

    -- Admin role permissions (all permissions)
    ('admin', 'user.read', NOW()),
    ('admin', 'user.write', NOW()),
    ('admin', 'user.admin', NOW()),
    ('admin', 'bubble.read', NOW()),
    ('admin', 'bubble.write', NOW()),
    ('admin', 'bubble.admin', NOW()),
    ('admin', 'message.read', NOW()),
    ('admin', 'message.write', NOW()),
    ('admin', 'message.moderate', NOW()),
    ('admin', 'system.admin', NOW());

-- Set the configured default role as the database default
-- This reads from the current configuration and sets it in the database
UPDATE roles
SET is_default = true
WHERE id = 'user'; -- This matches the current config.yaml default_role_name

-- ============================================================================
-- ROLE MANAGEMENT TABLE COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE roles IS 'Master role definitions with default role support for authorization system';
COMMENT ON TABLE permissions IS 'Master permission definitions for fine-grained access control';
COMMENT ON TABLE role_permissions IS 'Many-to-many relationship between roles and permissions';

COMMENT ON COLUMN roles.id IS 'Unique role identifier used in code and configuration';
COMMENT ON COLUMN roles.is_default IS 'Indicates the default role assigned to new users (only one allowed)';
COMMENT ON COLUMN roles.is_system IS 'System roles cannot be deleted through admin interfaces';
COMMENT ON COLUMN permissions.id IS 'Permission identifier in format resource.action (e.g., user.read)';
COMMENT ON COLUMN permissions.resource IS 'Resource type this permission applies to';
COMMENT ON COLUMN permissions.action IS 'Action type this permission allows';

COMMIT;

--- CONTENT END ---

==========================================

=== FILE: postgresql/003_media_analytics_schema.down.sql ===
File Type: sql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/postgresql/003_media_analytics_schema.down.sql
Last Modified: Sep 13 19:34:26 2025
File Size: 5370 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION ROLLBACK: Media Storage and Analytics Schema
-- Description: Safely remove media files, analytics, search indexes, and supporting features
-- Version: 1.0 (Consolidated rollback)
-- Date: 2025-01-19
-- ============================================================================

BEGIN;

-- ============================================================================
-- DROP VIEWS
-- ============================================================================

DROP VIEW IF EXISTS media_statistics;
DROP VIEW IF EXISTS user_search_results;

-- ============================================================================
-- DROP TRIGGERS (in reverse order)
-- ============================================================================

DROP TRIGGER IF EXISTS trigger_update_message_search_vector ON message_search_index;
DROP TRIGGER IF EXISTS trigger_update_user_search_vector ON user_search_index;
DROP TRIGGER IF EXISTS trigger_message_search_index_updated_at ON message_search_index;
DROP TRIGGER IF EXISTS trigger_user_search_index_updated_at ON user_search_index;
DROP TRIGGER IF EXISTS trigger_system_analytics_updated_at ON system_analytics;
DROP TRIGGER IF EXISTS trigger_bubble_analytics_updated_at ON bubble_analytics;
DROP TRIGGER IF EXISTS trigger_user_analytics_updated_at ON user_analytics;
DROP TRIGGER IF EXISTS trigger_media_files_updated_at ON media_files;

-- ============================================================================
-- DROP FUNCTIONS
-- ============================================================================

DROP FUNCTION IF EXISTS cleanup_expired_media_files();
DROP FUNCTION IF EXISTS get_user_storage_usage(UUID);
DROP FUNCTION IF EXISTS calculate_search_score(tsquery, UUID, UUID);
DROP FUNCTION IF EXISTS update_message_search_vector();
DROP FUNCTION IF EXISTS update_user_search_vector();

-- ============================================================================
-- DROP INDEXES (explicit cleanup)
-- ============================================================================

-- Message search index indexes
DROP INDEX IF EXISTS idx_message_search_mentions;
DROP INDEX IF EXISTS idx_message_search_has_media;
DROP INDEX IF EXISTS idx_message_search_full_text;
DROP INDEX IF EXISTS idx_message_search_sender;
DROP INDEX IF EXISTS idx_message_search_conversation;
DROP INDEX IF EXISTS idx_message_search_bubble;

-- User search index indexes
DROP INDEX IF EXISTS idx_user_search_bubbles;
DROP INDEX IF EXISTS idx_user_search_blocked_users;
DROP INDEX IF EXISTS idx_user_search_friends;
DROP INDEX IF EXISTS idx_user_search_contacts;
DROP INDEX IF EXISTS idx_user_search_last_active;
DROP INDEX IF EXISTS idx_user_search_private;
DROP INDEX IF EXISTS idx_user_search_active;
DROP INDEX IF EXISTS idx_user_search_full_text;
DROP INDEX IF EXISTS idx_user_search_display_name;
DROP INDEX IF EXISTS idx_user_search_username;

-- System analytics indexes
DROP INDEX IF EXISTS idx_system_analytics_date;

-- Bubble analytics indexes
DROP INDEX IF EXISTS idx_bubble_analytics_active_members;
DROP INDEX IF EXISTS idx_bubble_analytics_messages_count;
DROP INDEX IF EXISTS idx_bubble_analytics_date;
DROP INDEX IF EXISTS idx_bubble_analytics_bubble_date;

-- User analytics indexes
DROP INDEX IF EXISTS idx_user_analytics_active_minutes;
DROP INDEX IF EXISTS idx_user_analytics_messages_sent;
DROP INDEX IF EXISTS idx_user_analytics_date;
DROP INDEX IF EXISTS idx_user_analytics_user_date;

-- Media files indexes
DROP INDEX IF EXISTS idx_media_files_storage_location;
DROP INDEX IF EXISTS idx_media_files_expires_at;
DROP INDEX IF EXISTS idx_media_files_public;
DROP INDEX IF EXISTS idx_media_files_content_hash;
DROP INDEX IF EXISTS idx_media_files_media_type;
DROP INDEX IF EXISTS idx_media_files_status;
DROP INDEX IF EXISTS idx_media_files_uploaded_by;

-- ============================================================================
-- DROP ANALYTICS TABLES (in reverse dependency order)
-- ============================================================================

-- Drop analytics tables first (they reference core tables)
DROP TABLE IF EXISTS system_downtime_logs CASCADE;
-- Note: message_analytics table removed as messages are handled by ScyllaDB
DROP TABLE IF EXISTS call_analytics CASCADE;
DROP TABLE IF EXISTS user_bubble_analytics CASCADE;
DROP TABLE IF EXISTS contact_analytics CASCADE;
DROP TABLE IF EXISTS friendship_analytics CASCADE;
DROP TABLE IF EXISTS analytics_user_bubbles CASCADE;
DROP TABLE IF EXISTS analytics_user_engagement CASCADE;
DROP TABLE IF EXISTS analytics_user_activities CASCADE;
DROP TABLE IF EXISTS analytics_user_profiles CASCADE;

-- Drop core analytics tables
DROP TABLE IF EXISTS message_search_index CASCADE;
DROP TABLE IF EXISTS user_search_index CASCADE;
DROP TABLE IF EXISTS system_analytics CASCADE;
DROP TABLE IF EXISTS bubble_analytics CASCADE;
DROP TABLE IF EXISTS user_analytics CASCADE;
DROP TABLE IF EXISTS media_files CASCADE;

-- ============================================================================
-- DROP TYPES (in reverse dependency order)
-- ============================================================================

DROP TYPE IF EXISTS storage_provider;
DROP TYPE IF EXISTS media_status;
DROP TYPE IF EXISTS media_type;

COMMIT;

--- CONTENT END ---

==========================================

=== FILE: postgresql/003_media_analytics_schema.up.sql ===
File Type: sql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/postgresql/003_media_analytics_schema.up.sql
Last Modified: Sep 13 19:34:13 2025
File Size: 33634 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION: Media Storage and Analytics Schema
-- Description: Media files, analytics, search indexes, and supporting features
-- Version: 1.0 (Consolidated from migrations 000005, 000015, 000025, 000026)
-- Date: 2025-01-19
-- ============================================================================

BEGIN;

-- ============================================================================
-- MEDIA STORAGE ENUMS
-- ============================================================================

CREATE TYPE media_type AS ENUM ('image', 'video', 'audio', 'document', 'other');
CREATE TYPE media_status AS ENUM ('uploading', 'processing', 'ready', 'failed', 'deleted');
CREATE TYPE storage_provider AS ENUM ('minio', 's3', 'gcs', 'azure');

-- ============================================================================
-- MEDIA FILES TABLE
-- ============================================================================

CREATE TABLE media_files (
    file_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    uploaded_by UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- File metadata
    original_filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL CHECK (file_size > 0),
    mime_type VARCHAR(100) NOT NULL,
    media_type media_type NOT NULL,
    status media_status DEFAULT 'uploading',
    
    -- Storage information
    storage_provider storage_provider DEFAULT 'minio',
    bucket_name VARCHAR(100) NOT NULL,
    object_key VARCHAR(500) NOT NULL,
    public_url TEXT,
    
    -- File processing
    thumbnail_url TEXT,
    preview_url TEXT,
    processing_metadata JSONB DEFAULT '{}',
    
    -- Content analysis
    content_hash VARCHAR(64), -- SHA-256 hash for deduplication
    duration_seconds INTEGER, -- For audio/video files
    width INTEGER, -- For images/videos
    height INTEGER, -- For images/videos
    
    -- Access control
    is_public BOOLEAN DEFAULT false,
    access_permissions JSONB DEFAULT '{}',
    
    -- Lifecycle
    expires_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT media_files_valid_dimensions CHECK (
        (width IS NULL AND height IS NULL) OR (width > 0 AND height > 0)
    ),
    CONSTRAINT media_files_valid_duration CHECK (
        duration_seconds IS NULL OR duration_seconds > 0
    ),
    CONSTRAINT media_files_unique_storage_location UNIQUE (storage_provider, bucket_name, object_key),
    CONSTRAINT media_files_deletion_logic CHECK (
        (status != 'deleted' AND deleted_at IS NULL) OR 
        (status = 'deleted' AND deleted_at IS NOT NULL)
    )
);

-- ============================================================================
-- ANALYTICS TABLES
-- ============================================================================

-- User Analytics (Aggregated Daily)
CREATE TABLE user_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    date DATE NOT NULL,
    
    -- Activity metrics
    messages_sent INTEGER DEFAULT 0,
    bubbles_joined INTEGER DEFAULT 0,
    bubbles_created INTEGER DEFAULT 0,
    friends_added INTEGER DEFAULT 0,
    
    -- Engagement metrics
    active_minutes INTEGER DEFAULT 0,
    sessions_count INTEGER DEFAULT 0,
    reactions_given INTEGER DEFAULT 0,
    reactions_received INTEGER DEFAULT 0,
    
    -- Media metrics
    media_uploaded INTEGER DEFAULT 0,
    media_shared INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT user_analytics_unique_user_date UNIQUE (user_id, date),
    CONSTRAINT user_analytics_non_negative_metrics CHECK (
        messages_sent >= 0 AND bubbles_joined >= 0 AND bubbles_created >= 0 AND
        friends_added >= 0 AND active_minutes >= 0 AND sessions_count >= 0 AND
        reactions_given >= 0 AND reactions_received >= 0 AND
        media_uploaded >= 0 AND media_shared >= 0
    )
);

-- Bubble Analytics (Aggregated Daily)
CREATE TABLE bubble_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    date DATE NOT NULL,
    
    -- Activity metrics
    messages_count INTEGER DEFAULT 0,
    active_members_count INTEGER DEFAULT 0,
    new_members_count INTEGER DEFAULT 0,
    left_members_count INTEGER DEFAULT 0,
    
    -- Engagement metrics
    reactions_count INTEGER DEFAULT 0,
    media_shared_count INTEGER DEFAULT 0,
    calls_initiated INTEGER DEFAULT 0,
    total_call_duration_minutes INTEGER DEFAULT 0,
    
    -- Peak activity
    peak_concurrent_members INTEGER DEFAULT 0,
    peak_activity_hour INTEGER CHECK (peak_activity_hour >= 0 AND peak_activity_hour <= 23),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT bubble_analytics_unique_bubble_date UNIQUE (bubble_id, date),
    CONSTRAINT bubble_analytics_non_negative_metrics CHECK (
        messages_count >= 0 AND active_members_count >= 0 AND new_members_count >= 0 AND
        left_members_count >= 0 AND reactions_count >= 0 AND media_shared_count >= 0 AND
        calls_initiated >= 0 AND total_call_duration_minutes >= 0 AND peak_concurrent_members >= 0
    )
);

-- System Analytics (Global Daily Metrics)
CREATE TABLE system_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE UNIQUE NOT NULL,
    
    -- User metrics
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    premium_users INTEGER DEFAULT 0,
    
    -- Bubble metrics
    total_bubbles INTEGER DEFAULT 0,
    active_bubbles INTEGER DEFAULT 0,
    new_bubbles INTEGER DEFAULT 0,
    
    -- Activity metrics
    total_messages INTEGER DEFAULT 0,
    total_media_uploads INTEGER DEFAULT 0,
    total_calls INTEGER DEFAULT 0,
    total_call_duration_minutes INTEGER DEFAULT 0,
    
    -- Storage metrics
    total_storage_used_bytes BIGINT DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT system_analytics_non_negative_metrics CHECK (
        total_users >= 0 AND active_users >= 0 AND new_users >= 0 AND premium_users >= 0 AND
        total_bubbles >= 0 AND active_bubbles >= 0 AND new_bubbles >= 0 AND
        total_messages >= 0 AND total_media_uploads >= 0 AND total_calls >= 0 AND
        total_call_duration_minutes >= 0 AND total_storage_used_bytes >= 0
    )
);

-- ============================================================================
-- SEARCH INDEX TABLES
-- ============================================================================

-- User Search Index (Optimized for Full-Text Search)
-- Note: Denormalized relationship arrays removed to follow normalization principles
-- Relationships should be queried from user_relationships and bubble_members tables
CREATE TABLE user_search_index (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    username VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    display_name VARCHAR(255),
    email VARCHAR(255),
    avatar_url TEXT,

    -- Status flags (kept as they are user properties, not relationships)
    is_active BOOLEAN DEFAULT true,
    is_private BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    is_premium BOOLEAN DEFAULT false,

    -- Full-text search vector
    search_vector tsvector,

    -- Search metadata
    search_score FLOAT DEFAULT 0.0,
    last_active_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Message Search Index (For Chat History Search)
CREATE TABLE message_search_index (
    message_id UUID PRIMARY KEY,
    bubble_id UUID REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    conversation_id UUID, -- For direct messages
    sender_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- Message content
    content TEXT NOT NULL,
    content_type VARCHAR(50) DEFAULT 'text',
    
    -- Search vector
    search_vector tsvector,
    
    -- Message metadata
    has_media BOOLEAN DEFAULT false,
    has_mentions BOOLEAN DEFAULT false,
    mention_user_ids UUID[] DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT message_search_index_context_check CHECK (
        (bubble_id IS NOT NULL AND conversation_id IS NULL) OR
        (bubble_id IS NULL AND conversation_id IS NOT NULL)
    )
);

-- ============================================================================
-- INDEXES FOR OPTIMAL PERFORMANCE
-- ============================================================================

-- Media files indexes
CREATE INDEX idx_media_files_uploaded_by ON media_files (uploaded_by, created_at DESC);
CREATE INDEX idx_media_files_status ON media_files (status, created_at DESC);
CREATE INDEX idx_media_files_media_type ON media_files (media_type, status, created_at DESC);
CREATE INDEX idx_media_files_content_hash ON media_files (content_hash) WHERE content_hash IS NOT NULL;
CREATE INDEX idx_media_files_public ON media_files (is_public, status, created_at DESC) WHERE is_public = true;
CREATE INDEX idx_media_files_expires_at ON media_files (expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_media_files_storage_location ON media_files (storage_provider, bucket_name);

-- User analytics indexes
CREATE INDEX idx_user_analytics_user_date ON user_analytics (user_id, date DESC);
CREATE INDEX idx_user_analytics_date ON user_analytics (date DESC);
CREATE INDEX idx_user_analytics_messages_sent ON user_analytics (date, messages_sent DESC);
CREATE INDEX idx_user_analytics_active_minutes ON user_analytics (date, active_minutes DESC);

-- Bubble analytics indexes
CREATE INDEX idx_bubble_analytics_bubble_date ON bubble_analytics (bubble_id, date DESC);
CREATE INDEX idx_bubble_analytics_date ON bubble_analytics (date DESC);
CREATE INDEX idx_bubble_analytics_messages_count ON bubble_analytics (date, messages_count DESC);
CREATE INDEX idx_bubble_analytics_active_members ON bubble_analytics (date, active_members_count DESC);

-- System analytics indexes
CREATE INDEX idx_system_analytics_date ON system_analytics (date DESC);

-- User search index indexes
CREATE INDEX idx_user_search_username ON user_search_index USING gin(to_tsvector('english', username));
CREATE INDEX idx_user_search_display_name ON user_search_index USING gin(to_tsvector('english', display_name));
CREATE INDEX idx_user_search_full_text ON user_search_index USING gin(search_vector);
CREATE INDEX idx_user_search_active ON user_search_index (is_active) WHERE is_active = true;
CREATE INDEX idx_user_search_private ON user_search_index (is_private) WHERE is_private = false;
CREATE INDEX idx_user_search_last_active ON user_search_index (last_active_at DESC);

-- Message search index indexes
CREATE INDEX idx_message_search_bubble ON message_search_index (bubble_id, created_at DESC) WHERE bubble_id IS NOT NULL;
CREATE INDEX idx_message_search_conversation ON message_search_index (conversation_id, created_at DESC) WHERE conversation_id IS NOT NULL;
CREATE INDEX idx_message_search_sender ON message_search_index (sender_id, created_at DESC);
CREATE INDEX idx_message_search_full_text ON message_search_index USING gin(search_vector);
CREATE INDEX idx_message_search_has_media ON message_search_index (has_media, created_at DESC) WHERE has_media = true;
CREATE INDEX idx_message_search_mentions ON message_search_index USING gin(mention_user_ids);

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to update user search vector
CREATE OR REPLACE FUNCTION update_user_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english',
        COALESCE(NEW.username, '') || ' ' ||
        COALESCE(NEW.first_name, '') || ' ' ||
        COALESCE(NEW.last_name, '') || ' ' ||
        COALESCE(NEW.display_name, '') || ' ' ||
        COALESCE(NEW.email, '')
    );
    NEW.updated_at := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update message search vector
CREATE OR REPLACE FUNCTION update_message_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', COALESCE(NEW.content, ''));
    NEW.updated_at := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate search relevance score (normalized version)
CREATE OR REPLACE FUNCTION calculate_search_score(
    search_query tsquery,
    target_user_id UUID,
    searcher_id UUID
) RETURNS FLOAT AS $$
DECLARE
    score FLOAT := 0.0;
    relationship_score FLOAT := 0.0;
    is_friend BOOLEAN := false;
    is_contact BOOLEAN := false;
    shares_bubble BOOLEAN := false;
BEGIN
    -- Base relevance score from full-text search
    SELECT ts_rank(search_vector, search_query) INTO score
    FROM user_search_index
    WHERE user_id = target_user_id;

    -- Check if users are friends (normalized query)
    SELECT EXISTS (
        SELECT 1 FROM user_relationships
        WHERE ((from_user_id = searcher_id AND to_user_id = target_user_id) OR
               (from_user_id = target_user_id AND to_user_id = searcher_id))
        AND relationship_type = 'friend'
        AND status = 'accepted'
    ) INTO is_friend;

    -- Check if users are contacts (normalized query)
    SELECT EXISTS (
        SELECT 1 FROM user_relationships
        WHERE ((from_user_id = searcher_id AND to_user_id = target_user_id) OR
               (from_user_id = target_user_id AND to_user_id = searcher_id))
        AND relationship_type = 'contact'
        AND status = 'accepted'
    ) INTO is_contact;

    -- Check if users share a bubble (normalized query)
    SELECT EXISTS (
        SELECT 1 FROM bubble_members bm1
        JOIN bubble_members bm2 ON bm1.bubble_id = bm2.bubble_id
        WHERE bm1.user_id = searcher_id AND bm1.status = 'active'
        AND bm2.user_id = target_user_id AND bm2.status = 'active'
    ) INTO shares_bubble;

    -- Calculate relationship-based scoring
    relationship_score := CASE
        WHEN is_friend THEN 10.0
        WHEN is_contact THEN 8.0
        WHEN shares_bubble THEN 6.0
        ELSE 1.0
    END;

    -- Combine scores (full-text relevance + relationship bonus)
    RETURN COALESCE(score, 0.0) + COALESCE(relationship_score, 1.0);
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get file storage usage by user
CREATE OR REPLACE FUNCTION get_user_storage_usage(user_uuid UUID)
RETURNS BIGINT AS $$
DECLARE
    total_bytes BIGINT;
BEGIN
    SELECT COALESCE(SUM(file_size), 0) INTO total_bytes
    FROM media_files
    WHERE uploaded_by = user_uuid
    AND status IN ('ready', 'processing')
    AND deleted_at IS NULL;

    RETURN total_bytes;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to cleanup expired media files
CREATE OR REPLACE FUNCTION cleanup_expired_media_files()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    UPDATE media_files
    SET status = 'deleted',
        deleted_at = NOW(),
        updated_at = NOW()
    WHERE expires_at < NOW()
    AND status != 'deleted';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- ============================================================================
-- ADDITIONAL ANALYTICS TABLES FOR SOCIAL ANALYTICS MICROSERVICE
-- ============================================================================

-- Analytics User Profiles (Denormalized view for enhanced profiles)
CREATE TABLE analytics_user_profiles (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    display_name VARCHAR(255),
    avatar_url TEXT,
    country_residence VARCHAR(100),
    is_private BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    is_premium BOOLEAN DEFAULT false,

    -- Aggregated counts
    friends_count INTEGER DEFAULT 0,
    contacts_count INTEGER DEFAULT 0,
    bubbles_count INTEGER DEFAULT 0,
    messages_count INTEGER DEFAULT 0,

    -- Profile metrics
    profile_completeness FLOAT DEFAULT 0.0,
    last_active_at TIMESTAMP WITH TIME ZONE,
    joined_at TIMESTAMP WITH TIME ZONE NOT NULL,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT analytics_user_profiles_completeness_range CHECK (profile_completeness >= 0.0 AND profile_completeness <= 1.0),
    CONSTRAINT analytics_user_profiles_counts_non_negative CHECK (
        friends_count >= 0 AND contacts_count >= 0 AND bubbles_count >= 0 AND messages_count >= 0
    )
);

-- Analytics User Activities (Activity tracking and session data)
CREATE TABLE analytics_user_activities (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    total_activities INTEGER DEFAULT 0,
    unique_sessions INTEGER DEFAULT 0,
    avg_session_duration FLOAT DEFAULT 0.0,
    activities_by_type JSONB DEFAULT '{}',
    last_activity_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT analytics_user_activities_non_negative CHECK (
        total_activities >= 0 AND unique_sessions >= 0 AND avg_session_duration >= 0.0
    )
);

-- Analytics User Engagement (Engagement scoring and metrics)
CREATE TABLE analytics_user_engagement (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    engagement_score FLOAT DEFAULT 0.0,
    daily_active_days INTEGER DEFAULT 0,
    weekly_active_days INTEGER DEFAULT 0,
    monthly_active_days INTEGER DEFAULT 0,
    engagement_factors JSONB DEFAULT '{}',
    last_engagement TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT analytics_user_engagement_score_range CHECK (engagement_score >= 0.0 AND engagement_score <= 100.0),
    CONSTRAINT analytics_user_engagement_days_non_negative CHECK (
        daily_active_days >= 0 AND weekly_active_days >= 0 AND monthly_active_days >= 0
    )
);

-- Analytics User Bubbles (User-bubble relationship analytics)
CREATE TABLE analytics_user_bubbles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    bubble_name VARCHAR(255),
    bubble_description TEXT,
    member_count INTEGER DEFAULT 0,
    capacity INTEGER DEFAULT 10,
    status VARCHAR(50) DEFAULT 'active',
    joined_at TIMESTAMP WITH TIME ZONE,
    left_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT analytics_user_bubbles_unique_user_bubble UNIQUE (user_id, bubble_id),
    CONSTRAINT analytics_user_bubbles_member_count_non_negative CHECK (member_count >= 0),
    CONSTRAINT analytics_user_bubbles_capacity_positive CHECK (capacity > 0),
    CONSTRAINT analytics_user_bubbles_valid_dates CHECK (left_at IS NULL OR left_at >= joined_at)
);

-- Friendship Analytics (Friendship relationship tracking)
CREATE TABLE friendship_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL DEFAULT 'friend',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    connection_strength FLOAT DEFAULT 0.0,
    mutual_friends_count INTEGER DEFAULT 0,
    common_bubbles_count INTEGER DEFAULT 0,
    interaction_frequency FLOAT DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT friendship_analytics_unique_relationship UNIQUE (from_user_id, to_user_id),
    CONSTRAINT friendship_analytics_no_self_friendship CHECK (from_user_id != to_user_id),
    CONSTRAINT friendship_analytics_connection_strength_range CHECK (connection_strength >= 0.0 AND connection_strength <= 1.0),
    CONSTRAINT friendship_analytics_counts_non_negative CHECK (
        mutual_friends_count >= 0 AND common_bubbles_count >= 0 AND interaction_frequency >= 0.0
    )
);

-- Contact Analytics (Contact relationship tracking)
CREATE TABLE contact_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL DEFAULT 'contact',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    connection_strength FLOAT DEFAULT 0.0,
    mutual_contacts_count INTEGER DEFAULT 0,
    common_bubbles_count INTEGER DEFAULT 0,
    interaction_frequency FLOAT DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT contact_analytics_unique_relationship UNIQUE (from_user_id, to_user_id),
    CONSTRAINT contact_analytics_no_self_contact CHECK (from_user_id != to_user_id),
    CONSTRAINT contact_analytics_connection_strength_range CHECK (connection_strength >= 0.0 AND connection_strength <= 1.0),
    CONSTRAINT contact_analytics_counts_non_negative CHECK (
        mutual_contacts_count >= 0 AND common_bubbles_count >= 0 AND interaction_frequency >= 0.0
    )
);

-- User Bubble Analytics (Detailed user-bubble interaction tracking)
CREATE TABLE user_bubble_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    messages_sent INTEGER DEFAULT 0,
    reactions_given INTEGER DEFAULT 0,
    reactions_received INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    joined_at TIMESTAMP WITH TIME ZONE,
    left_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT user_bubble_analytics_unique_user_bubble UNIQUE (user_id, bubble_id),
    CONSTRAINT user_bubble_analytics_counts_non_negative CHECK (
        messages_sent >= 0 AND reactions_given >= 0 AND reactions_received >= 0 AND time_spent_minutes >= 0
    ),
    CONSTRAINT user_bubble_analytics_valid_dates CHECK (left_at IS NULL OR left_at >= joined_at)
);

-- Call Analytics (Call tracking and metrics)
CREATE TABLE call_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    call_id UUID UNIQUE NOT NULL,
    bubble_id UUID REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    initiator_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    participant_count INTEGER DEFAULT 0,
    duration_minutes INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active',
    call_type VARCHAR(50) DEFAULT 'voice',
    started_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT call_analytics_participant_count_non_negative CHECK (participant_count >= 0),
    CONSTRAINT call_analytics_duration_non_negative CHECK (duration_minutes >= 0),
    CONSTRAINT call_analytics_valid_dates CHECK (ended_at IS NULL OR ended_at >= started_at)
);

-- Message Analytics (Message tracking and metrics)
-- Note: Message analytics are handled by ScyllaDB realtime service for consistency with messaging
-- This table is kept for potential future PostgreSQL-based analytics aggregation

-- System Downtime Logs (System availability tracking)
CREATE TABLE system_downtime_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_name VARCHAR(100) NOT NULL,
    downtime_type VARCHAR(50) NOT NULL,
    description TEXT,
    severity VARCHAR(20) DEFAULT 'medium',
    affected_users_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT system_downtime_logs_affected_users_non_negative CHECK (affected_users_count >= 0),
    CONSTRAINT system_downtime_logs_valid_dates CHECK (resolved_at IS NULL OR resolved_at >= created_at)
);

-- ============================================================================
-- INDEXES FOR ANALYTICS TABLES
-- ============================================================================

-- Analytics user profiles indexes
CREATE INDEX idx_analytics_user_profiles_last_active ON analytics_user_profiles (last_active_at DESC);
CREATE INDEX idx_analytics_user_profiles_joined_at ON analytics_user_profiles (joined_at DESC);
CREATE INDEX idx_analytics_user_profiles_friends_count ON analytics_user_profiles (friends_count DESC);
CREATE INDEX idx_analytics_user_profiles_engagement ON analytics_user_profiles (profile_completeness DESC);

-- Analytics user activities indexes
CREATE INDEX idx_analytics_user_activities_last_activity ON analytics_user_activities (last_activity_at DESC);
CREATE INDEX idx_analytics_user_activities_total_activities ON analytics_user_activities (total_activities DESC);

-- Analytics user engagement indexes
CREATE INDEX idx_analytics_user_engagement_score ON analytics_user_engagement (engagement_score DESC);
CREATE INDEX idx_analytics_user_engagement_last_engagement ON analytics_user_engagement (last_engagement DESC);

-- Analytics user bubbles indexes
CREATE INDEX idx_analytics_user_bubbles_user_id ON analytics_user_bubbles (user_id);
CREATE INDEX idx_analytics_user_bubbles_bubble_id ON analytics_user_bubbles (bubble_id);
CREATE INDEX idx_analytics_user_bubbles_status ON analytics_user_bubbles (status);
CREATE INDEX idx_analytics_user_bubbles_last_activity ON analytics_user_bubbles (last_activity_at DESC);

-- Friendship analytics indexes
CREATE INDEX idx_friendship_analytics_from_user ON friendship_analytics (from_user_id);
CREATE INDEX idx_friendship_analytics_to_user ON friendship_analytics (to_user_id);
CREATE INDEX idx_friendship_analytics_status ON friendship_analytics (status);
CREATE INDEX idx_friendship_analytics_connection_strength ON friendship_analytics (connection_strength DESC);

-- Contact analytics indexes
CREATE INDEX idx_contact_analytics_from_user ON contact_analytics (from_user_id);
CREATE INDEX idx_contact_analytics_to_user ON contact_analytics (to_user_id);
CREATE INDEX idx_contact_analytics_status ON contact_analytics (status);

-- User bubble analytics indexes
CREATE INDEX idx_user_bubble_analytics_user_id ON user_bubble_analytics (user_id);
CREATE INDEX idx_user_bubble_analytics_bubble_id ON user_bubble_analytics (bubble_id);
CREATE INDEX idx_user_bubble_analytics_last_activity ON user_bubble_analytics (last_activity_at DESC);

-- Call analytics indexes
CREATE INDEX idx_call_analytics_bubble_id ON call_analytics (bubble_id);
CREATE INDEX idx_call_analytics_started_at ON call_analytics (started_at DESC);
CREATE INDEX idx_call_analytics_duration ON call_analytics (duration_minutes DESC);

-- Message analytics indexes
CREATE INDEX idx_message_analytics_bubble_id ON message_analytics (bubble_id);
CREATE INDEX idx_message_analytics_sender ON message_analytics (sender_user_id);
CREATE INDEX idx_message_analytics_sent_at ON message_analytics (sent_at DESC);

-- System downtime logs indexes
CREATE INDEX idx_system_downtime_logs_service ON system_downtime_logs (service_name);
CREATE INDEX idx_system_downtime_logs_created_at ON system_downtime_logs (created_at DESC);
CREATE INDEX idx_system_downtime_logs_severity ON system_downtime_logs (severity);

-- Apply updated_at triggers to all tables
CREATE TRIGGER trigger_media_files_updated_at BEFORE UPDATE ON media_files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_analytics_updated_at BEFORE UPDATE ON user_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_bubble_analytics_updated_at BEFORE UPDATE ON bubble_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_system_analytics_updated_at BEFORE UPDATE ON system_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_search_index_updated_at BEFORE UPDATE ON user_search_index FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_message_search_index_updated_at BEFORE UPDATE ON message_search_index FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Analytics tables triggers
CREATE TRIGGER trigger_analytics_user_profiles_updated_at BEFORE UPDATE ON analytics_user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_analytics_user_activities_updated_at BEFORE UPDATE ON analytics_user_activities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_analytics_user_engagement_updated_at BEFORE UPDATE ON analytics_user_engagement FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_analytics_user_bubbles_updated_at BEFORE UPDATE ON analytics_user_bubbles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_friendship_analytics_updated_at BEFORE UPDATE ON friendship_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_contact_analytics_updated_at BEFORE UPDATE ON contact_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_bubble_analytics_updated_at BEFORE UPDATE ON user_bubble_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_call_analytics_updated_at BEFORE UPDATE ON call_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_message_analytics_updated_at BEFORE UPDATE ON message_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_system_downtime_logs_updated_at BEFORE UPDATE ON system_downtime_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Triggers for search vector updates
CREATE TRIGGER trigger_update_user_search_vector
    BEFORE INSERT OR UPDATE ON user_search_index
    FOR EACH ROW
    EXECUTE FUNCTION update_user_search_vector();

CREATE TRIGGER trigger_update_message_search_vector
    BEFORE INSERT OR UPDATE ON message_search_index
    FOR EACH ROW
    EXECUTE FUNCTION update_message_search_vector();

-- ============================================================================
-- VIEWS FOR COMMON QUERIES
-- ============================================================================

-- View for active user search results
CREATE VIEW user_search_results AS
SELECT
    usi.user_id,
    usi.username,
    usi.first_name,
    usi.last_name,
    usi.display_name,
    usi.avatar_url,
    usi.last_active_at,
    usi.is_active,
    usi.is_private,
    usi.is_verified,
    usi.is_premium,
    usi.search_score
FROM user_search_index usi
WHERE usi.is_active = true;

-- View for media file statistics
CREATE VIEW media_statistics AS
SELECT
    media_type,
    status,
    COUNT(*) as file_count,
    SUM(file_size) as total_size_bytes,
    AVG(file_size) as avg_size_bytes,
    MIN(created_at) as first_upload,
    MAX(created_at) as last_upload
FROM media_files
WHERE deleted_at IS NULL
GROUP BY media_type, status;

-- ============================================================================
-- TABLE COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE media_files IS 'Media file storage with metadata, processing status, and access control';
COMMENT ON TABLE user_analytics IS 'Daily aggregated user activity and engagement metrics';
COMMENT ON TABLE bubble_analytics IS 'Daily aggregated bubble activity and engagement metrics';
COMMENT ON TABLE system_analytics IS 'Daily aggregated system-wide metrics and statistics';
COMMENT ON TABLE user_search_index IS 'Normalized search index for user discovery with full-text search capabilities (relationships queried from source tables)';
COMMENT ON TABLE message_search_index IS 'Optimized search index for chat message history with full-text search';

COMMENT ON COLUMN media_files.content_hash IS 'SHA-256 hash for file deduplication and integrity verification';
COMMENT ON COLUMN media_files.processing_metadata IS 'JSON metadata from media processing pipeline (thumbnails, transcoding, etc.)';
COMMENT ON COLUMN user_search_index.search_vector IS 'Full-text search vector for efficient text search across user fields';
COMMENT ON COLUMN message_search_index.mention_user_ids IS 'Array of user IDs mentioned in the message for notification targeting';

COMMIT;

--- CONTENT END ---

==========================================

=== FILE: postgresql/004_supporting_features_schema.down.sql ===
File Type: sql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/postgresql/004_supporting_features_schema.down.sql
Last Modified: Sep  1 21:05:56 2025
File Size: 4921 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION ROLLBACK: Supporting Features Schema
-- Description: Safely remove notifications, feedback, calls, email, presence, and system tables
-- Version: 1.0 (Consolidated rollback)
-- Date: 2025-01-19
-- ============================================================================

BEGIN;

-- ============================================================================
-- DROP TRIGGERS (in reverse order)
-- ============================================================================

DROP TRIGGER IF EXISTS trigger_saga_states_updated_at ON saga_states;
DROP TRIGGER IF EXISTS trigger_user_presence_updated_at ON user_presence;
DROP TRIGGER IF EXISTS trigger_email_queue_updated_at ON email_queue;
DROP TRIGGER IF EXISTS trigger_email_templates_updated_at ON email_templates;
DROP TRIGGER IF EXISTS trigger_feedback_reports_updated_at ON feedback_reports;
DROP TRIGGER IF EXISTS trigger_call_participants_updated_at ON call_participants;
DROP TRIGGER IF EXISTS trigger_calls_updated_at ON calls;
DROP TRIGGER IF EXISTS trigger_notifications_updated_at ON notifications;

-- ============================================================================
-- DROP FUNCTIONS
-- ============================================================================

DROP FUNCTION IF EXISTS can_see_user_location(UUID, UUID);
DROP FUNCTION IF EXISTS cleanup_old_processed_events(INTEGER);
DROP FUNCTION IF EXISTS update_user_presence(UUID, presence_status, VARCHAR(50), VARCHAR(100));
DROP FUNCTION IF EXISTS create_notification(UUID, notification_type, VARCHAR(255), TEXT, UUID, UUID, JSONB);

-- ============================================================================
-- DROP VIEWS
-- ============================================================================

DROP VIEW IF EXISTS former_bubble_members;

-- ============================================================================
-- DROP INDEXES (explicit cleanup)
-- ============================================================================

-- System tables indexes
DROP INDEX IF EXISTS idx_processed_events_processor;
DROP INDEX IF EXISTS idx_saga_states_running;
DROP INDEX IF EXISTS idx_saga_states_type_status;
DROP INDEX IF EXISTS idx_outbox_events_aggregate;
DROP INDEX IF EXISTS idx_outbox_events_unprocessed;

-- User presence indexes
DROP INDEX IF EXISTS idx_user_presence_last_seen;
DROP INDEX IF EXISTS idx_user_presence_status;

-- Email queue indexes
DROP INDEX IF EXISTS idx_email_queue_pending;
DROP INDEX IF EXISTS idx_email_queue_user;
DROP INDEX IF EXISTS idx_email_queue_recipient;
DROP INDEX IF EXISTS idx_email_queue_status_scheduled;

-- Email templates indexes
DROP INDEX IF EXISTS idx_email_templates_category;
DROP INDEX IF EXISTS idx_email_templates_name;

-- Feedback reports indexes
DROP INDEX IF EXISTS idx_feedback_reports_priority;
DROP INDEX IF EXISTS idx_feedback_reports_assigned_to;
DROP INDEX IF EXISTS idx_feedback_reports_reported_user;
DROP INDEX IF EXISTS idx_feedback_reports_type_status;
DROP INDEX IF EXISTS idx_feedback_reports_user;

-- Call participants indexes
DROP INDEX IF EXISTS idx_call_participants_user;
DROP INDEX IF EXISTS idx_call_participants_call;

-- Calls indexes
DROP INDEX IF EXISTS idx_calls_status;
DROP INDEX IF EXISTS idx_calls_initiator;
DROP INDEX IF EXISTS idx_calls_conversation;
DROP INDEX IF EXISTS idx_calls_bubble;

-- Notifications indexes
DROP INDEX IF EXISTS idx_notifications_expires_at;
DROP INDEX IF EXISTS idx_notifications_related_bubble;
DROP INDEX IF EXISTS idx_notifications_related_user;
DROP INDEX IF EXISTS idx_notifications_unread;
DROP INDEX IF EXISTS idx_notifications_type;
DROP INDEX IF EXISTS idx_notifications_user_status;

-- ============================================================================
-- DROP TABLES (in reverse dependency order)
-- ============================================================================

DROP TABLE IF EXISTS processed_events CASCADE;
DROP TABLE IF EXISTS saga_states CASCADE;
DROP TABLE IF EXISTS outbox_events CASCADE;
DROP TABLE IF EXISTS user_presence CASCADE;
DROP TABLE IF EXISTS email_queue CASCADE;
DROP TABLE IF EXISTS email_templates CASCADE;
DROP TABLE IF EXISTS feedback_reports CASCADE;
DROP TABLE IF EXISTS call_participants CASCADE;
DROP TABLE IF EXISTS calls CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;

-- ============================================================================
-- DROP TYPES (in reverse dependency order)
-- ============================================================================

DROP TYPE IF EXISTS presence_status;
DROP TYPE IF EXISTS email_status;
DROP TYPE IF EXISTS feedback_status;
DROP TYPE IF EXISTS feedback_type;
DROP TYPE IF EXISTS call_type;
DROP TYPE IF EXISTS call_status;
DROP TYPE IF EXISTS notification_status;
DROP TYPE IF EXISTS notification_type;

COMMIT;

--- CONTENT END ---

==========================================

=== FILE: postgresql/004_supporting_features_schema.up.sql ===
File Type: sql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/postgresql/004_supporting_features_schema.up.sql
Last Modified: Sep 14 22:31:47 2025
File Size: 24987 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION: Supporting Features Schema
-- Description: Notifications, feedback, calls, email, presence, and system tables
-- Version: 1.0 (Consolidated from remaining migrations)
-- Date: 2025-01-19
-- ============================================================================

BEGIN;

-- ============================================================================
-- SUPPORTING FEATURE ENUMS
-- ============================================================================

CREATE TYPE notification_type AS ENUM (
    'contactRequestReceived', 'contactRequestAccepted', 'contactRequestDeclined',
    'friendRequestReceived', 'friendRequestAccepted', 'friendRequestDeclined',
    'bubbleInviteReceived', 'bubbleInviteAccepted', 'bubbleInviteDeclined',
    'bubbleJoinRequestReceived', 'bubbleJoinRequestAccepted', 'bubbleJoinRequestDeclined',
    'bubbleKickoutRequestReceived', 'bubbleKickoutRequestAccepted', 'bubbleKickoutRequestDeclined',
    'bubbleStartRequestReceived', 'bubbleStartRequestAccepted', 'bubbleStartRequestDeclined',
    'bubbleExpired', 'bubbleDissolved', 'bubbleCallStarted', 'bubbleCallEnded',
    'bubbleCallMissed', 'birthday', 'systemNotification'
);

CREATE TYPE notification_status AS ENUM ('unread', 'read', 'dismissed', 'archived');
CREATE TYPE call_status AS ENUM ('initiated', 'ringing', 'answered', 'ended', 'missed', 'failed');
CREATE TYPE call_type AS ENUM ('audio', 'video', 'screen_share');
CREATE TYPE feedback_type AS ENUM ('bug_report', 'feature_request', 'general_feedback', 'user_report', 'content_report');
CREATE TYPE feedback_status AS ENUM ('open', 'in_progress', 'resolved', 'closed', 'duplicate');
CREATE TYPE email_status AS ENUM ('pending', 'sent', 'delivered', 'failed', 'bounced');
-- Note: presence_status enum removed - using boolean is_present in user_presence table (migration 001)

-- ============================================================================
-- NOTIFICATIONS TABLE
-- ============================================================================

CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    status notification_status DEFAULT 'unread',
    
    -- Notification content
    title VARCHAR(255) NOT NULL,
    message TEXT,
    action_url TEXT,
    
    -- Related entities
    related_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    related_bubble_id UUID REFERENCES bubbles(bubble_id) ON DELETE SET NULL,
    related_request_id UUID, -- Generic reference to various request tables
    
    -- Notification metadata
    metadata JSONB DEFAULT '{}',
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 5),
    
    -- Delivery tracking
    push_sent BOOLEAN DEFAULT false,
    email_sent BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    dismissed_at TIMESTAMP WITH TIME ZONE,
    
    -- Lifecycle
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT notifications_status_logic CHECK (
        (status = 'unread' AND read_at IS NULL) OR
        (status = 'read' AND read_at IS NOT NULL) OR
        (status = 'dismissed' AND dismissed_at IS NOT NULL)
    )
);

-- ============================================================================
-- CALLS TABLE
-- ============================================================================

CREATE TABLE calls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID REFERENCES bubbles(bubble_id) ON DELETE CASCADE,
    conversation_id UUID, -- For direct calls
    initiator_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- Call details
    call_type call_type DEFAULT 'audio',
    status call_status DEFAULT 'initiated',
    
    -- Call timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    answered_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    
    -- Call metadata
    metadata JSONB DEFAULT '{}',
    recording_url TEXT,
    quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT calls_context_check CHECK (
        (bubble_id IS NOT NULL AND conversation_id IS NULL) OR
        (bubble_id IS NULL AND conversation_id IS NOT NULL)
    ),
    CONSTRAINT calls_timing_logic CHECK (
        (answered_at IS NULL OR answered_at >= started_at) AND
        (ended_at IS NULL OR ended_at >= started_at) AND
        (duration_seconds IS NULL OR duration_seconds >= 0)
    )
);

-- ============================================================================
-- CALL PARTICIPANTS TABLE
-- ============================================================================

CREATE TABLE call_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    call_id UUID NOT NULL REFERENCES calls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- Participation details
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    was_invited BOOLEAN DEFAULT true,
    
    -- Audio/Video status
    audio_enabled BOOLEAN DEFAULT true,
    video_enabled BOOLEAN DEFAULT false,
    screen_sharing BOOLEAN DEFAULT false,
    
    -- Connection quality
    connection_quality VARCHAR(20) DEFAULT 'good' CHECK (connection_quality IN ('excellent', 'good', 'fair', 'poor')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT call_participants_unique_user_call UNIQUE (call_id, user_id),
    CONSTRAINT call_participants_timing_logic CHECK (
        left_at IS NULL OR left_at >= joined_at
    )
);

-- ============================================================================
-- FEEDBACK AND REPORTING TABLE
-- ============================================================================

CREATE TABLE feedback_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    type feedback_type NOT NULL,
    status feedback_status DEFAULT 'open',
    
    -- Report content
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100),
    
    -- Related entities (for user/content reports)
    reported_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    reported_bubble_id UUID REFERENCES bubbles(bubble_id) ON DELETE SET NULL,
    reported_content_id UUID, -- Generic reference to reported content
    reported_content_type VARCHAR(50), -- 'message', 'media', 'profile', etc.
    
    -- Report metadata
    metadata JSONB DEFAULT '{}',
    attachments TEXT[], -- URLs to attached files/screenshots
    
    -- Processing
    assigned_to UUID REFERENCES users(user_id) ON DELETE SET NULL,
    priority INTEGER DEFAULT 2 CHECK (priority >= 1 AND priority <= 5),
    resolution_notes TEXT,
    resolved_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT feedback_reports_resolution_logic CHECK (
        (status IN ('open', 'in_progress') AND resolved_at IS NULL) OR
        (status IN ('resolved', 'closed') AND resolved_at IS NOT NULL)
    )
);

-- ============================================================================
-- EMAIL SYSTEM TABLES
-- ============================================================================

-- Email Templates
CREATE TABLE email_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT,
    
    -- Template metadata
    category VARCHAR(50),
    language VARCHAR(10) DEFAULT 'en',
    is_active BOOLEAN DEFAULT true,
    
    -- Template variables
    required_variables TEXT[] DEFAULT '{}',
    optional_variables TEXT[] DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email Queue
CREATE TABLE email_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_email VARCHAR(255) NOT NULL,
    recipient_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL,
    
    -- Email content
    subject VARCHAR(255) NOT NULL,
    html_content TEXT,
    text_content TEXT,
    template_id UUID REFERENCES email_templates(id) ON DELETE SET NULL,
    template_variables JSONB DEFAULT '{}',
    
    -- Delivery details
    status email_status DEFAULT 'pending',
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    scheduled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    
    -- Error handling
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    last_error TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT email_queue_delivery_logic CHECK (
        (status = 'pending' AND sent_at IS NULL) OR
        (status IN ('sent', 'delivered') AND sent_at IS NOT NULL) OR
        (status IN ('failed', 'bounced'))
    ),
    CONSTRAINT email_queue_attempts_logic CHECK (attempts <= max_attempts)
);

-- ============================================================================
-- PRESENCE AND ACTIVITY TRACKING
-- ============================================================================
-- Note: user_presence table is now defined in migration 001 (core schema)

-- ============================================================================
-- USER LOCATION PRIVACY TABLE
-- ============================================================================

-- Custom location privacy settings for individual user permissions
CREATE TABLE user_location_privacy (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    allowed_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT user_location_privacy_unique_pair UNIQUE (user_id, allowed_user_id),
    CONSTRAINT user_location_privacy_no_self_allow CHECK (user_id != allowed_user_id)
);

-- ============================================================================
-- SYSTEM TABLES (OUTBOX, SAGA, EVENTS)
-- ============================================================================

-- Outbox Pattern for Event Publishing
CREATE TABLE outbox_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    aggregate_id UUID NOT NULL,
    aggregate_type VARCHAR(100) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,

    -- Event metadata
    event_version INTEGER DEFAULT 1,
    correlation_id UUID,
    causation_id UUID,

    -- Processing status
    is_processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT outbox_events_retry_logic CHECK (retry_count <= max_retries)
);

-- Saga State Management
CREATE TABLE saga_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    saga_id VARCHAR(255) UNIQUE NOT NULL,
    saga_type VARCHAR(100) NOT NULL,

    -- Saga data and status
    saga_data JSONB NOT NULL,
    current_step VARCHAR(100),
    status VARCHAR(50) DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'compensated')),

    -- Timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,

    -- Error handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT saga_states_completion_logic CHECK (
        (status = 'running' AND completed_at IS NULL) OR
        (status IN ('completed', 'failed', 'compensated') AND completed_at IS NOT NULL)
    )
);

-- Processed Events (Idempotency)
CREATE TABLE processed_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id VARCHAR(255) UNIQUE NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    processor_name VARCHAR(100) NOT NULL,

    -- Processing metadata
    processing_result JSONB,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- INDEXES FOR OPTIMAL PERFORMANCE
-- ============================================================================

-- Notifications indexes
CREATE INDEX idx_notifications_user_status ON notifications (user_id, status, created_at DESC);
CREATE INDEX idx_notifications_type ON notifications (type, created_at DESC);
CREATE INDEX idx_notifications_unread ON notifications (user_id, status, priority DESC) WHERE status = 'unread';
CREATE INDEX idx_notifications_related_user ON notifications (related_user_id, created_at DESC) WHERE related_user_id IS NOT NULL;
CREATE INDEX idx_notifications_related_bubble ON notifications (related_bubble_id, created_at DESC) WHERE related_bubble_id IS NOT NULL;
CREATE INDEX idx_notifications_expires_at ON notifications (expires_at) WHERE expires_at IS NOT NULL;

-- Calls indexes
CREATE INDEX idx_calls_bubble ON calls (bubble_id, started_at DESC) WHERE bubble_id IS NOT NULL;
CREATE INDEX idx_calls_conversation ON calls (conversation_id, started_at DESC) WHERE conversation_id IS NOT NULL;
CREATE INDEX idx_calls_initiator ON calls (initiator_id, started_at DESC);
CREATE INDEX idx_calls_status ON calls (status, started_at DESC);

-- Unique constraint to prevent multiple active calls per bubble
CREATE UNIQUE INDEX idx_calls_unique_active_bubble
ON calls (bubble_id)
WHERE status IN ('initiated', 'ringing', 'connected') AND bubble_id IS NOT NULL;

-- Unique constraint to prevent multiple active calls per conversation
CREATE UNIQUE INDEX idx_calls_unique_active_conversation
ON calls (conversation_id)
WHERE status IN ('initiated', 'ringing', 'connected') AND conversation_id IS NOT NULL;

-- Call participants indexes
CREATE INDEX idx_call_participants_call ON call_participants (call_id, joined_at);
CREATE INDEX idx_call_participants_user ON call_participants (user_id, joined_at DESC);

-- Feedback reports indexes
CREATE INDEX idx_feedback_reports_user ON feedback_reports (user_id, created_at DESC) WHERE user_id IS NOT NULL;
CREATE INDEX idx_feedback_reports_type_status ON feedback_reports (type, status, created_at DESC);
CREATE INDEX idx_feedback_reports_reported_user ON feedback_reports (reported_user_id, created_at DESC) WHERE reported_user_id IS NOT NULL;
CREATE INDEX idx_feedback_reports_assigned_to ON feedback_reports (assigned_to, status, priority DESC) WHERE assigned_to IS NOT NULL;
CREATE INDEX idx_feedback_reports_priority ON feedback_reports (priority DESC, created_at DESC);

-- Email templates indexes
CREATE INDEX idx_email_templates_name ON email_templates (name) WHERE is_active = true;
CREATE INDEX idx_email_templates_category ON email_templates (category, language) WHERE is_active = true;

-- Email queue indexes
CREATE INDEX idx_email_queue_status_scheduled ON email_queue (status, scheduled_at);
CREATE INDEX idx_email_queue_recipient ON email_queue (recipient_email, created_at DESC);
CREATE INDEX idx_email_queue_user ON email_queue (recipient_user_id, created_at DESC) WHERE recipient_user_id IS NOT NULL;
CREATE INDEX idx_email_queue_pending ON email_queue (priority DESC, scheduled_at) WHERE status = 'pending';

-- User presence indexes
-- Note: user_presence indexes are now defined in migration 001 (core schema)

-- User location privacy indexes
CREATE INDEX idx_user_location_privacy_user ON user_location_privacy (user_id, allowed_user_id);

-- System tables indexes
CREATE INDEX idx_outbox_events_unprocessed ON outbox_events (created_at) WHERE is_processed = false;
CREATE INDEX idx_outbox_events_aggregate ON outbox_events (aggregate_type, aggregate_id, created_at DESC);
CREATE INDEX idx_saga_states_type_status ON saga_states (saga_type, status, created_at DESC);
CREATE INDEX idx_saga_states_running ON saga_states (saga_type, started_at DESC) WHERE status = 'running';
CREATE INDEX idx_processed_events_processor ON processed_events (processor_name, processed_at DESC);

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to create notification
CREATE OR REPLACE FUNCTION create_notification(
    target_user_id UUID,
    notification_type_param notification_type,
    title_param VARCHAR(255),
    message_param TEXT DEFAULT NULL,
    related_user_id_param UUID DEFAULT NULL,
    related_bubble_id_param UUID DEFAULT NULL,
    metadata_param JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO notifications (
        user_id, type, title, message, related_user_id, related_bubble_id, metadata
    ) VALUES (
        target_user_id, notification_type_param, title_param, message_param,
        related_user_id_param, related_bubble_id_param, metadata_param
    ) RETURNING id INTO notification_id;

    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Note: user_presence functions are now defined in migration 001 (core schema)

-- Function to cleanup old processed events
CREATE OR REPLACE FUNCTION cleanup_old_processed_events(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM processed_events
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to check if a user can see another user's location based on privacy settings.
-- This is critical for the GetUserLocation endpoint in the user service.
CREATE OR REPLACE FUNCTION can_see_user_location(requesting_user_id UUID, target_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    privacy_level TEXT;
    are_friends BOOLEAN;
    share_bubble BOOLEAN;
    is_allowed_custom BOOLEAN;
BEGIN
    -- A user can always see their own location
    IF requesting_user_id = target_user_id THEN
        RETURN TRUE;
    END IF;

    -- Get the target user's location privacy setting from the users table
    SELECT location_privacy_level INTO privacy_level FROM users WHERE user_id = target_user_id;

    -- If no setting is found or it's private, deny access
    IF NOT FOUND OR privacy_level = 'private' THEN
        RETURN FALSE;
    END IF;

    -- Public location is visible to everyone
    IF privacy_level = 'public' THEN
        RETURN TRUE;
    END IF;

    -- Check for 'friends' privacy level
    IF privacy_level = 'friends' THEN
        SELECT EXISTS (
            SELECT 1 FROM user_relationships
            WHERE relationship_type = 'friend' AND status = 'accepted'
            AND ((from_user_id = requesting_user_id AND to_user_id = target_user_id) OR
                 (from_user_id = target_user_id AND to_user_id = requesting_user_id))
        ) INTO are_friends;
        RETURN are_friends;
    END IF;

    -- Check for 'bubbles' privacy level (common bubbles)
    IF privacy_level = 'bubbles' THEN
        SELECT EXISTS (
            SELECT 1 FROM bubble_members bm1
            JOIN bubble_members bm2 ON bm1.bubble_id = bm2.bubble_id
            WHERE bm1.user_id = requesting_user_id AND bm1.status = 'active'
            AND bm2.user_id = target_user_id AND bm2.status = 'active'
        ) INTO share_bubble;
        RETURN share_bubble;
    END IF;

    -- Check for 'custom' privacy level
    IF privacy_level = 'custom' THEN
        SELECT EXISTS (
            SELECT 1 FROM user_location_privacy
            WHERE user_id = target_user_id AND allowed_user_id = requesting_user_id
        ) INTO is_allowed_custom;
        RETURN is_allowed_custom;
    END IF;

    RETURN FALSE; -- Default to deny
END;
$$ LANGUAGE plpgsql STABLE;

-- ============================================================================
-- USER & BUBBLE SERVICE VIEWS
-- ============================================================================

-- View to provide a denormalized list of former bubble members.
-- This is required by the GetFormerBubbleMembers endpoint in the user service.
-- Note: Simplified version using existing tables only
CREATE OR REPLACE VIEW former_bubble_members AS
SELECT
    bm.user_id,
    u.username,
    u.first_name,
    u.last_name,
    u.avatar_url AS profile_picture_url,
    bm.bubble_id,
    bm.joined_at,
    bm.left_at,
    CASE
        WHEN bm.status = 'left' THEN 'voluntary'
        WHEN bm.status = 'removed' THEN 'kicked'
        ELSE 'unknown'
    END AS leave_reason
FROM
    bubble_members bm
JOIN
    users u ON bm.user_id = u.user_id
WHERE
    bm.status NOT IN ('active', 'pending');

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Apply updated_at triggers to all tables
CREATE TRIGGER trigger_notifications_updated_at BEFORE UPDATE ON notifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_calls_updated_at BEFORE UPDATE ON calls FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_call_participants_updated_at BEFORE UPDATE ON call_participants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_feedback_reports_updated_at BEFORE UPDATE ON feedback_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_email_templates_updated_at BEFORE UPDATE ON email_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_email_queue_updated_at BEFORE UPDATE ON email_queue FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Note: user_presence triggers are now defined in migration 001 (core schema)
CREATE TRIGGER trigger_user_location_privacy_updated_at BEFORE UPDATE ON user_location_privacy FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_saga_states_updated_at BEFORE UPDATE ON saga_states FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- TABLE COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE notifications IS 'User notifications with delivery tracking and expiration support';
COMMENT ON TABLE calls IS 'Voice and video calls with comprehensive metadata and quality tracking';
COMMENT ON TABLE call_participants IS 'Call participation tracking with audio/video status and connection quality';
COMMENT ON TABLE feedback_reports IS 'User feedback, bug reports, and content moderation reports';
COMMENT ON TABLE email_templates IS 'Reusable email templates with variable substitution support';
COMMENT ON TABLE email_queue IS 'Email delivery queue with retry logic and status tracking';
-- Note: user_presence table comments are now defined in migration 001 (core schema)
COMMENT ON TABLE user_location_privacy IS 'Custom location privacy settings for individual user permissions';
COMMENT ON TABLE outbox_events IS 'Outbox pattern implementation for reliable event publishing';
COMMENT ON TABLE saga_states IS 'Saga orchestration state management for distributed transactions';
COMMENT ON TABLE processed_events IS 'Idempotency tracking for event processing';

COMMENT ON COLUMN notifications.metadata IS 'Flexible JSON metadata for notification-specific data and rendering';
COMMENT ON COLUMN feedback_reports.reported_content_type IS 'Type of reported content: message, media, profile, bubble, etc.';
COMMENT ON COLUMN email_templates.required_variables IS 'Array of variable names that must be provided for template rendering';
COMMENT ON COLUMN saga_states.saga_data IS 'Complete saga state and step data in JSON format';
COMMENT ON COLUMN users.location_privacy_level IS 'User location privacy setting: public, friends, bubbles, custom, or private';
COMMENT ON COLUMN user_location_privacy.allowed_user_id IS 'User ID allowed to see location when privacy is set to custom';

COMMIT;

--- CONTENT END ---

==========================================

=== FILE: scylladb/001_realtime_messaging_schema.cql ===
File Type: cql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/scylladb/001_realtime_messaging_schema.cql
Last Modified: Sep 13 19:54:15 2025
File Size: 24848 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION: Realtime Messaging and Outbox Events Schema (ScyllaDB)
-- Description: Complete realtime messaging schema with normalized design and transactional outbox pattern
-- Version: 5.0 (Consolidated schema incorporating realtime messaging and outbox events)
-- Author: System Consolidation
-- Date: 2025-09-12
-- Rollback Safety: Safe - consolidated schema with proper normalization
-- Performance Impact: Optimized - efficient partition keys, clustering, indexing, and time-based partitioning for outbox
-- Dependencies: Keyspace 'hopen' must exist
-- ============================================================================
--
-- Purpose: Following database guidelines from docs/roadmap/guidelines/how_to_write_databases.md
-- This consolidated migration incorporates all optimizations from previous realtime messaging migrations (001-004)
-- and integrates the native outbox events schema for atomic event publishing.
--
-- Realtime Messaging Components:
-- - Query-driven design for ScyllaDB with proper partition key distribution
-- - Optimized clustering columns for time-series data (created_at DESC)
-- - Counter tables for aggregations (unread counts, reaction counts)
-- - TTL for ephemeral data (presence, typing indicators, device sessions)
-- - Materialized views for different query patterns
-- - Normalized schema removing redundant fields (recipient_id, last_message_content)
-- - Proper indexing strategy following ScyllaDB best practices
--
-- Outbox Events Components:
-- - Transactional outbox pattern implementation for ScyllaDB
-- - Time-based partitioning (hourly buckets) to distribute load evenly
-- - Clustering by event_id (TIMEUUID) for chronological ordering
-- - JSON event data storage for flexibility
-- - Status tracking for processing lifecycle
-- - TTL for automatic cleanup of processed events
--
-- Consolidation Notes:
-- - Removed redundant recipient_id from conversation_messages (can be derived from conversations table)
-- - Removed last_message_content from conversations table (fetched on-demand using last_message_id)
-- - Optimized materialized views for efficient participant-based queries
-- - Added missing tables and indexes from subsequent migrations
-- - Integrated outbox_events, outbox_events_by_status, outbox_metrics, and outbox_processor_state tables
--
-- ============================================================================

-- ============================================================================
-- KEYSPACE USAGE
-- ============================================================================
-- Note: Keyspace 'hopen' is created by the migration script
-- This migration file only creates tables within the existing keyspace

USE hopen;

-- ============================================================================
-- BUBBLE MESSAGES TABLE (GROUP CHATS)
-- ============================================================================
-- Query patterns:
-- 1. Get recent messages for a bubble (partition by bubble_id, order by created_at DESC)
-- 2. Get messages around a specific time (using created_at clustering)
-- 3. Get specific message (using message_id clustering)
-- 4. Real-time message streaming (CDC enabled)

CREATE TABLE IF NOT EXISTS bubble_messages (
    bubble_id UUID,
    created_at TIMESTAMP,
    message_id UUID,

    -- Core message data
    user_id UUID,
    content TEXT,
    content_type TEXT,
    message_type TEXT,

    -- Message state (normalized - no denormalized counters)
    is_edited boolean,
    is_deleted boolean,
    edited_at TIMESTAMP,
    deleted_at TIMESTAMP,

    -- Media attachments (normalized list)
    media_urls LIST<TEXT>,
    media_metadata MAP<TEXT, TEXT>,

    -- Message threading and replies
    reply_to_message_id UUID,
    thread_id UUID,

    -- User mentions (for notification targeting)
    mention_user_ids SET<UUID>,

    -- System and moderation
    is_system_message boolean,
    moderation_status TEXT,

    PRIMARY KEY (bubble_id, created_at, message_id)
) WITH CLUSTERING ORDER BY (created_at DESC, message_id ASC)
AND default_time_to_live = 2592000
AND cdc = {'enabled':true}
AND compaction = {'class':'TimeWindowCompactionStrategy', 'compaction_window_unit':'DAYS', 'compaction_window_size':7}
AND comment = 'Bubble chat messages optimized for chronological retrieval with proper normalization';

-- ============================================================================
-- CONVERSATION MESSAGES TABLE (1-ON-1 CHATS)
-- ============================================================================
-- Optimized schema without redundant recipient_id field
-- Query patterns:
-- 1. Get recent messages for a conversation (partition by conversation_id, order by created_at DESC)
-- 2. Get messages around a specific time (using created_at clustering)
-- 3. Get specific message (using message_id clustering)
-- 4. Real-time message streaming (CDC enabled)
-- Note: recipient_id removed for normalization - can be derived from conversations table

CREATE TABLE IF NOT EXISTS conversation_messages (
    conversation_id UUID,
    created_at TIMESTAMP,
    message_id UUID,
    user_id UUID,
    -- recipient_id REMOVED - normalized out as it can be derived from conversations table
    content TEXT,
    content_type TEXT,

    -- Message status (is_read removed - read status tracked per-user in dedicated read tracking tables)
    is_delivered boolean,
    is_edited boolean,
    is_deleted boolean,
    delivered_at TIMESTAMP,
    edited_at TIMESTAMP,
    deleted_at TIMESTAMP,

    -- Media and attachments
    media_urls LIST<TEXT>,
    media_metadata MAP<TEXT, TEXT>,

    -- Message relationships
    reply_to_message_id UUID,

    -- Encryption and security
    is_encrypted boolean,
    encryption_key_id TEXT,

    PRIMARY KEY (conversation_id, created_at, message_id)
) WITH CLUSTERING ORDER BY (created_at DESC, message_id ASC)
AND default_time_to_live = 2592000
AND cdc = {'enabled':true}
AND compaction = {'class':'TimeWindowCompactionStrategy', 'compaction_window_unit':'DAYS', 'compaction_window_size':7}
AND comment = 'Normalized conversation messages - recipient derived from conversations table';

-- ============================================================================
-- CONVERSATIONS TABLE (METADATA)
-- ============================================================================
-- Query patterns:
-- 1. Get conversation metadata by conversation_id (primary key lookup)
-- 2. Get conversations by participant (via materialized views)
-- 3. Get conversation last message info (last_message_id for on-demand content fetch)

CREATE TABLE IF NOT EXISTS conversations (
    conversation_id UUID PRIMARY KEY,
    user_id_1 UUID,
    user_id_2 UUID,

    -- Conversation metadata
    last_message_id UUID,
    last_message_at TIMESTAMP,
    -- Note: last_message_content removed to prevent inconsistencies
    -- Content should be fetched on-demand using last_message_id

    -- Status and settings
    is_archived boolean,
    is_muted boolean,
    archived_at TIMESTAMP,

    created_at TIMESTAMP,
    updated_at TIMESTAMP
) WITH comment = 'Normalized conversation metadata - message content fetched on-demand using last_message_id';

-- ============================================================================
-- USER CONVERSATIONS TABLE (DENORMALIZED FOR USER QUERIES)
-- ============================================================================
-- Query patterns:
-- 1. Get list of conversations for a specific user (partition by (user_id, bucket))
-- 2. Get recent conversations ordered by last_message_at
-- 3. Avoid hot partitions by bucketing with time-based buckets (e.g., '2024-11')
-- Note: bucket determined by created_at or last_message_at month/year

CREATE TABLE IF NOT EXISTS user_conversations (
    user_id UUID,
    bucket TEXT,  -- Time-based bucket like 'YYYY-MM' to distribute partitions
    conversation_id UUID,
    other_user_id UUID,  -- The other participant in 1:1 conversation
    last_message_at TIMESTAMP,
    last_message_id UUID,
    is_archived BOOLEAN,
    is_muted BOOLEAN,
    created_at TIMESTAMP,

    PRIMARY KEY ((user_id, bucket), last_message_at)
) WITH CLUSTERING ORDER BY (last_message_at DESC)
AND comment = 'Denormalized user conversations with time-based bucketing to prevent hot partitions and enable efficient user-specific queries';

-- ============================================================================
-- MESSAGE REACTIONS TABLE
-- ============================================================================
-- Query patterns:
-- 1. Get all reactions for a message (partition by message_id)
-- 2. Get user's reaction to a message (using user_id clustering)
-- 3. Get reactions by type (using reaction_type clustering)
-- 4. Real-time reaction updates (CDC enabled)

CREATE TABLE IF NOT EXISTS message_reactions (
    message_id UUID,
    user_id UUID,
    reaction_type TEXT,

    -- Context for the reaction (one will be null)
    bubble_id UUID,
    conversation_id UUID,

    created_at TIMESTAMP,

    PRIMARY KEY (message_id, user_id, reaction_type)
) WITH cdc = {'enabled':true}
AND comment = 'Message reactions for both bubble and conversation messages - normalized without denormalized counts';

-- ============================================================================
-- MESSAGE REACTION COUNTS TABLE (COUNTER TABLE)
-- ============================================================================
-- Query patterns:
-- 1. Get reaction counts for a message (partition by message_id)
-- 2. Increment/decrement reaction counts (counter operations)

CREATE TABLE IF NOT EXISTS message_reaction_counts (
    message_id UUID,
    reaction_type TEXT,
    reaction_count COUNTER,

    PRIMARY KEY (message_id, reaction_type)
) WITH comment = 'Aggregated reaction counts per message per reaction type - updated via counter operations';

-- ============================================================================
-- UNREAD COUNTS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS bubble_unread_counts (
    user_id UUID,
    bubble_id UUID,
    unread_count COUNTER,
    
    PRIMARY KEY (user_id, bubble_id)
) WITH comment = 'Unread message counts per user per bubble';

CREATE TABLE IF NOT EXISTS conversation_unread_counts (
    user_id UUID,
    conversation_id UUID,
    unread_count COUNTER,

    PRIMARY KEY (user_id, conversation_id)
) WITH comment = 'Unread message counts per user per conversation';

-- ============================================================================
-- MESSAGE READ TRACKING TABLES
-- ============================================================================

CREATE TABLE IF NOT EXISTS direct_message_reads (
    user_id UUID,
    conversation_id UUID,
    message_id UUID,
    read_at TIMESTAMP,

    PRIMARY KEY (user_id, conversation_id, message_id)
) WITH cdc = {'enabled':true}
AND comment = 'Per-user read status tracking for direct messages';

CREATE TABLE IF NOT EXISTS bubble_message_reads (
    user_id UUID,
    bubble_id UUID,
    message_id UUID,
    read_at TIMESTAMP,

    PRIMARY KEY (user_id, bubble_id, message_id)
) WITH cdc = {'enabled':true}
AND comment = 'Per-user read status tracking for bubble messages';

-- ============================================================================
-- USER PRESENCE TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_presence (
    user_id UUID PRIMARY KEY,
    is_online boolean,
    last_seen_at TIMESTAMP,
    status TEXT,
    status_message TEXT,
    device_type TEXT,
    updated_at TIMESTAMP
) WITH default_time_to_live = 86400
AND comment = 'Real-time user presence and status information';

-- ============================================================================
-- TYPING INDICATORS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS typing_indicators (
    bubble_id UUID,
    user_id UUID,
    is_typing boolean,
    started_typing_at TIMESTAMP,

    PRIMARY KEY (bubble_id, user_id)
) WITH default_time_to_live = 30
AND comment = 'Real-time typing indicators for bubble chats';

CREATE TABLE IF NOT EXISTS conversation_typing_indicators (
    conversation_id UUID,
    user_id UUID,
    is_typing boolean,
    started_typing_at TIMESTAMP,

    PRIMARY KEY (conversation_id, user_id)
) WITH default_time_to_live = 30
AND comment = 'Real-time typing indicators for direct conversations';

-- ============================================================================
-- DEVICE SESSIONS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS device_sessions (
    user_id UUID,
    device_id TEXT,
    session_token TEXT,
    device_type TEXT,
    device_name TEXT,
    push_token TEXT,
    is_active boolean,
    last_active_at TIMESTAMP,
    created_at TIMESTAMP,

    PRIMARY KEY (user_id, device_id)
) WITH default_time_to_live = 2592000
AND comment = 'User device sessions for push notifications and presence';

-- ============================================================================
-- INDEX TABLES FOR EFFICIENT LOOKUPS
-- ============================================================================
-- Following ScyllaDB best practices: use tables instead of secondary indexes

-- Messages by ID index (for cross-bubble/conversation message lookup)
CREATE TABLE IF NOT EXISTS messages_by_id (
    message_id UUID PRIMARY KEY,
    bubble_id UUID,
    conversation_id UUID,
    user_id UUID,
    content TEXT,
    message_type TEXT,
    media_urls LIST<TEXT>,
    reply_to_id UUID,
    is_edited boolean,
    is_deleted boolean,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
) WITH comment = 'Index table for efficient message lookup by message_id across all conversations';

-- User message statistics (counter table for analytics)
CREATE TABLE IF NOT EXISTS user_message_counts (
    user_id UUID PRIMARY KEY,
    messages_sent COUNTER,
    messages_received COUNTER,
    bubbles_created COUNTER,
    bubbles_joined COUNTER
) WITH comment = 'User message and bubble activity counters for analytics';

-- ============================================================================
-- MATERIALIZED VIEWS FOR OPTIMIZED QUERIES
-- ============================================================================

-- Messages by user (for user's message history across all bubbles)
CREATE MATERIALIZED VIEW IF NOT EXISTS messages_by_user AS
SELECT bubble_id, created_at, message_id, user_id, content, content_type, is_deleted
FROM bubble_messages
WHERE user_id IS NOT NULL AND bubble_id IS NOT NULL AND created_at IS NOT NULL AND message_id IS NOT NULL
PRIMARY KEY (user_id, created_at, bubble_id, message_id)
WITH CLUSTERING ORDER BY (created_at DESC, bubble_id ASC, message_id ASC);

-- Recent messages across all bubbles (for global activity feed)
CREATE MATERIALIZED VIEW IF NOT EXISTS recent_bubble_messages AS
SELECT bubble_id, created_at, message_id, user_id, content, content_type, is_deleted
FROM bubble_messages
WHERE bubble_id IS NOT NULL AND created_at IS NOT NULL AND message_id IS NOT NULL
PRIMARY KEY (created_at, bubble_id, message_id)
WITH CLUSTERING ORDER BY (bubble_id ASC, message_id ASC);

-- Note: Conversation materialized views removed due to CQL limitations
-- CQL doesn't allow multiple non-primary key columns in materialized view primary keys
-- Application should query conversations table directly and filter by user_id_1/user_id_2

-- ============================================================================
-- INDEXES FOR SECONDARY QUERIES
-- ============================================================================

-- Index for finding messages by reply_to_message_id
CREATE INDEX IF NOT EXISTS ON bubble_messages (reply_to_message_id);
CREATE INDEX IF NOT EXISTS ON conversation_messages (reply_to_message_id);

-- Index for finding reactions by user
CREATE INDEX IF NOT EXISTS ON message_reactions (user_id);

-- Index for presence queries
CREATE INDEX IF NOT EXISTS ON user_presence (is_online);
CREATE INDEX IF NOT EXISTS ON user_presence (last_seen_at);

-- ============================================================================
-- OUTBOX EVENTS TABLE (TRANSACTIONAL OUTBOX PATTERN)
-- ============================================================================
-- This table implements the transactional outbox pattern natively in ScyllaDB.
-- Events are created atomically with business operations using BATCH statements.
-- The outbox processor reads from this table and publishes to NATS.

CREATE TABLE IF NOT EXISTS outbox_events (
    -- Partition key: time bucket (hourly) to distribute load and avoid hotspots
    time_bucket TEXT,           -- Format: 'YYYY-MM-DD-HH' (e.g., '2025-09-12-14')
    
    -- Clustering columns for ordering and uniqueness
    event_id TIMEUUID,          -- Unique event identifier with timestamp ordering
    
    -- Event metadata
    aggregate_id UUID,          -- ID of the aggregate (message_id, bubble_id, etc.)
    aggregate_type TEXT,        -- Type of aggregate (message, bubble, poll, etc.)
    event_type TEXT,            -- Specific event type (message.created, poll.voted, etc.)
    
    -- Event payload and metadata
    event_data TEXT,            -- JSON string containing event payload
    event_metadata TEXT,        -- JSON string containing metadata (service, action, etc.)
    
    -- Processing status
    status TEXT,                -- 'pending', 'processing', 'published', 'failed'
    retry_count INT,            -- Number of processing attempts
    max_retries INT,            -- Maximum retry attempts before marking as failed
    
    -- Timestamps
    created_at TIMESTAMP,       -- When the event was created
    processed_at TIMESTAMP,     -- When the event was successfully processed
    next_retry_at TIMESTAMP,    -- When to retry failed events
    
    -- Error tracking
    error_message TEXT,         -- Last error message if processing failed
    
    PRIMARY KEY (time_bucket, event_id)
) WITH CLUSTERING ORDER BY (event_id ASC)
AND comment = 'Transactional outbox for reliable event publishing from ScyllaDB'
AND default_time_to_live = 604800  -- 7 days TTL for automatic cleanup
AND gc_grace_seconds = 86400;      -- 1 day grace period for tombstones

-- ============================================================================
-- OUTBOX EVENTS BY STATUS (MATERIALIZED VIEW)
-- ============================================================================
-- Optimized view for the outbox processor to efficiently query pending events
-- across multiple time buckets without scanning all partitions.

CREATE MATERIALIZED VIEW IF NOT EXISTS outbox_events_by_status AS
    SELECT time_bucket, event_id, aggregate_id, aggregate_type, event_type,
           event_data, event_metadata, status, retry_count, max_retries,
           created_at, processed_at, next_retry_at, error_message
    FROM outbox_events
    WHERE time_bucket IS NOT NULL
      AND event_id IS NOT NULL
      AND status IS NOT NULL
    PRIMARY KEY (status, time_bucket, event_id)
    WITH CLUSTERING ORDER BY (time_bucket DESC, event_id ASC)
    AND comment = 'Materialized view for efficient querying of events by status'
    AND gc_grace_seconds = 86400;

-- ============================================================================
-- OUTBOX PROCESSING METRICS (COUNTER TABLE)
-- ============================================================================
-- Track processing metrics for monitoring and alerting

CREATE TABLE IF NOT EXISTS outbox_metrics (
    metric_type TEXT,           -- 'events_processed', 'events_failed', 'events_retried'
    time_bucket TEXT,           -- Hourly bucket for time-series metrics
    count COUNTER,              -- Counter value

    PRIMARY KEY (metric_type, time_bucket)
) WITH CLUSTERING ORDER BY (time_bucket DESC)
AND comment = 'Metrics for outbox event processing'
AND gc_grace_seconds = 86400;

-- ============================================================================
-- OUTBOX PROCESSOR STATE (SINGLETON TABLE)
-- ============================================================================
-- Track the state of outbox processors for coordination and monitoring

CREATE TABLE IF NOT EXISTS outbox_processor_state (
    processor_id TEXT,          -- Unique processor instance ID
    last_processed_bucket TEXT, -- Last time bucket that was fully processed
    last_heartbeat TIMESTAMP,   -- Last heartbeat from this processor
    status TEXT,                -- 'active', 'inactive', 'error'
    processed_count BIGINT,     -- Total events processed by this instance
    error_count BIGINT,         -- Total errors encountered
    
    PRIMARY KEY (processor_id)
) WITH comment = 'State tracking for outbox processor instances'
AND default_time_to_live = 86400   -- 1 day TTL (processors should heartbeat regularly)
AND gc_grace_seconds = 3600;       -- 1 hour grace period

-- ============================================================================
-- INDEXES AND OPTIMIZATIONS
-- ============================================================================
-- Note: ScyllaDB automatically creates indexes for materialized views.
-- Additional secondary indexes are generally not recommended for ScyllaDB
-- due to performance implications. The time-bucket partitioning strategy
-- and materialized view provide efficient query patterns for outbox.

-- ============================================================================
-- MIGRATION COMPLETION
-- ============================================================================

-- ============================================================================
-- CONSOLIDATED SCHEMA SUMMARY
-- ============================================================================
-- This consolidated migration creates the complete realtime messaging and outbox schema with:
--
-- Core Realtime Messaging Tables:
-- - bubble_messages: Group chat messages with proper partitioning by bubble_id (standardized user_id)
-- - conversation_messages: 1-on-1 messages (normalized, standardized user_id)
-- - conversations: Conversation metadata (normalized, standardized user_id_1/user_id_2)
-- - user_conversations: Denormalized user-specific conversations with time-based bucketing to prevent hot partitions
-- - message_reactions: Individual user reactions to messages
-- - message_reaction_counts: Aggregated reaction counts (counter table)
--
-- Realtime Tracking Tables:
-- - bubble_unread_counts: Per-user unread counts for bubbles (counter table)
-- - conversation_unread_counts: Per-user unread counts for conversations (counter table)
-- - direct_message_reads: Per-user read tracking for direct messages
-- - bubble_message_reads: Per-user read tracking for bubble messages
--
-- Presence & Real-time:
-- - user_presence: User online status and last seen (24h TTL)
-- - typing_indicators: Bubble typing indicators (30s TTL)
-- - conversation_typing_indicators: Conversation typing indicators (30s TTL)
-- - device_sessions: User device sessions for push notifications (30d TTL)
--
-- Index Tables:
-- - messages_by_id: Cross-conversation message lookup by message_id
-- - user_message_counts: User activity counters for analytics
--
-- Outbox Events Tables:
-- - outbox_events: Transactional outbox for atomic event publishing (7d TTL)
-- - outbox_events_by_status: Materialized view for status-based queries
-- - outbox_metrics: Processing metrics (counter table)
-- - outbox_processor_state: Processor instance state tracking (1d TTL)
--
-- Materialized Views:
-- - messages_by_user: User's message history across all bubbles
-- - recent_bubble_messages: Global activity feed of recent messages
-- - outbox_events_by_status: Efficient outbox querying by status
--
-- Performance Optimizations:
-- - Proper partition keys for even data distribution
-- - Time-series clustering (created_at DESC) for chronological queries
-- - Counter tables for aggregations to avoid read-modify-write patterns
-- - TTL for ephemeral data (presence, typing, sessions, outbox)
-- - CDC enabled for real-time event streaming
-- - TimeWindowCompactionStrategy for time-series data
-- - Hourly bucketing for outbox to avoid hotspots
--
-- Normalization Benefits:
-- - Standardized user_id naming convention throughout schema
-- - No denormalized last_message_content in conversations (fetched on-demand)
-- - Consistent data without risk of stale denormalized fields
-- - Follows ScyllaDB query-driven design principles
-- - Native outbox enables atomic operations without cross-DB transactions

--- CONTENT END ---

==========================================

=== FILE: scylladb/001_realtime_messaging_schema.down.cql ===
File Type: cql
Full Path: /Users/<USER>/Hopen/hopenbackend/migrations/scylladb/001_realtime_messaging_schema.down.cql
Last Modified: Sep 12 18:31:40 2025
File Size: 5449 bytes

--- CONTENT START ---
-- ============================================================================
-- CONSOLIDATED MIGRATION ROLLBACK: Realtime Messaging and Outbox Events Schema (ScyllaDB)
-- Description: Safely remove all realtime messaging and outbox events tables and views
-- Version: 5.0 (Final consolidated rollback)
-- Author: System Consolidation
-- Date: 2025-09-12
-- Rollback Safety: Safe - removes all tables, views, and indexes in proper order
-- Performance Impact: Complete removal of realtime messaging and outbox schema
-- Dependencies: None (removes all dependencies)
-- ============================================================================
--
-- Purpose: Complete rollback of the consolidated realtime messaging and outbox events schema
-- This rollback removes all tables, materialized views, and indexes created
-- by the consolidated migration 001_realtime_messaging_schema.cql
--
-- Rollback Order:
-- 1. Drop indexes (no dependencies)
-- 2. Drop materialized views (depend on base tables)
-- 3. Drop tables (in reverse dependency order)
--
-- ============================================================================

BEGIN;

USE hopen;

-- ============================================================================
-- DROP INDEXES (in reverse order)
-- ============================================================================
-- Note: ScyllaDB creates indexes with auto-generated names when using CREATE INDEX syntax.
-- These indexes use IF EXISTS protection and can be safely dropped multiple times.

-- ============================================================================
-- DROP MATERIALIZED VIEWS (in reverse dependency order)
-- ============================================================================

DROP MATERIALIZED VIEW IF EXISTS outbox_events_by_status;
DROP MATERIALIZED VIEW IF EXISTS recent_bubble_messages;
DROP MATERIALIZED VIEW IF EXISTS messages_by_user;

-- ============================================================================
-- DROP TABLES (in reverse dependency order)
-- ============================================================================

-- Drop index tables first (no dependencies)
DROP TABLE IF EXISTS user_message_counts;
DROP TABLE IF EXISTS messages_by_id;

-- Drop outbox tables
DROP TABLE IF EXISTS outbox_processor_state;
DROP TABLE IF EXISTS outbox_metrics;
DROP TABLE IF EXISTS outbox_events;

-- Drop tracking and counter tables
DROP TABLE IF EXISTS device_sessions;
DROP TABLE IF EXISTS conversation_typing_indicators;
DROP TABLE IF EXISTS typing_indicators;
DROP TABLE IF EXISTS user_presence;
DROP TABLE IF EXISTS bubble_message_reads;
DROP TABLE IF EXISTS direct_message_reads;
DROP TABLE IF EXISTS conversation_unread_counts;
DROP TABLE IF EXISTS bubble_unread_counts;

-- Drop reaction tables
DROP TABLE IF EXISTS message_reaction_counts;
DROP TABLE IF EXISTS message_reactions;

-- Drop core messaging tables
DROP TABLE IF EXISTS conversations;
DROP TABLE IF EXISTS conversation_messages;
DROP TABLE IF EXISTS bubble_messages;

-- ============================================================================
-- ROLLBACK COMPLETION
-- ============================================================================

COMMIT;

-- ============================================================================
-- ROLLBACK COMPLETE
-- ============================================================================
-- All realtime messaging and outbox schema components have been removed:
-- - All indexes dropped
-- - All materialized views dropped
-- - All tables dropped in proper dependency order
-- - Transaction committed
--
-- The keyspace 'hopen' remains but all realtime messaging and outbox tables are removed.
--
-- KEYSPACE MANAGEMENT OPTIONS:
-- ============================
--
-- Option 1: Keep keyspace (default behavior)
-- The keyspace remains for other services or future migrations
-- This is the safest option for production environments
--
-- Option 2: Drop keyspace completely (uncomment the following lines)
-- WARNING: This will remove ALL data in the keyspace, including data from other services
-- Only use this in development/testing environments or when completely removing the application
--
-- To completely remove the keyspace, uncomment the following lines:
--
-- -- Drop the entire keyspace (WARNING: DESTRUCTIVE)
-- DROP KEYSPACE IF EXISTS hopen;
--
-- Note: If you drop the keyspace, you may also want to remove any associated
-- user permissions and other keyspace-level configurations
--
-- CONSOLIDATED SCHEMA ROLLBACK SUMMARY:
-- ====================================
-- This rollback removes the complete realtime messaging and outbox schema:
--
-- Realtime Messaging Components Removed:
-- - Core tables: bubble_messages, conversation_messages, conversations, user_conversations
-- - Reactions: message_reactions, message_reaction_counts
-- - Tracking: unread counts, read tracking tables
-- - Presence & Real-time: user_presence, typing indicators, device_sessions
-- - Indexes: messages_by_id, user_message_counts
-- - Materialized Views: messages_by_user, recent_bubble_messages
--
-- Outbox Events Components Removed:
-- - outbox_events: Transactional outbox table
-- - outbox_events_by_status: Materialized view for status queries
-- - outbox_metrics: Processing metrics counter table
-- - outbox_processor_state: Processor state tracking table
--
-- All components removed safely without data loss outside this schema.

--- CONTENT END ---

==========================================


# SUMMARY
# =======
Total files processed: 10
File types found:
     2  cql
     8  sql
