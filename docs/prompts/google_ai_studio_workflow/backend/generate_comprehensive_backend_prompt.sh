#!/bin/bash

# Script to generate a comprehensive prompt file containing all backend code
# This will be used for AI review of the entire backend codebase

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# Go up to the Hopen root directory (backend -> google_ai_studio_workflow -> prompts -> docs -> Hopen)
HOPEN_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"

# Configuration
MICROSERVICES_DIR="$HOPEN_ROOT/hopenbackend/microservices"
CMD_DIR="$HOPEN_ROOT/hopenbackend/cmd"
PKG_DIR="$HOPEN_ROOT/hopenbackend/pkg"
MIGRATIONS_DIR="$HOPEN_ROOT/hopenbackend/migrations"
CONFIG_DIR="$HOPEN_ROOT/hopenbackend/config"
PROMPTS_DIR="$SCRIPT_DIR"
OUTPUT_FILE="comprehensive_backend_codebase_review.txt"
TEMP_DIR="/tmp/comprehensive_backend_scan"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Starting Comprehensive Backend Codebase Scan...${NC}"
echo -e "${YELLOW}📁 Script directory: $SCRIPT_DIR${NC}"
echo -e "${YELLOW}📁 Hopen root: $HOPEN_ROOT${NC}"
echo -e "${YELLOW}📁 Microservices directory: $MICROSERVICES_DIR${NC}"
echo -e "${YELLOW}📁 CMD directory: $CMD_DIR${NC}"
echo -e "${YELLOW}📁 PKG directory: $PKG_DIR${NC}"
echo -e "${YELLOW}📁 Migrations directory: $MIGRATIONS_DIR${NC}"
echo -e "${YELLOW}📁 Config directory: $CONFIG_DIR${NC}"

# Create temp directory
mkdir -p "$TEMP_DIR"

# Function to process a file
process_file() {
    local file_path="$1"
    local base_dir="$2"
    local relative_path="${file_path#$base_dir/}"
    
    # Skip certain file types
    if [[ "$file_path" == *.git* ]] || [[ "$file_path" == *.DS_Store* ]] || [[ "$file_path" == *node_modules* ]]; then
        return
    fi
    
    # Get file extension
    local extension="${file_path##*.}"
    
    # Only process text files
    case "$extension" in
        go|txt|md|yaml|yml|json|proto|sql|cql|sh|dockerfile|dockerignore|gitignore|conf|properties|ts)
            echo -e "${GREEN}📄 Processing: $relative_path${NC}"
            
            # Create the labeled content
            echo "=== FILE: $relative_path ===" >> "$TEMP_DIR/content.txt"
            echo "File Type: $extension" >> "$TEMP_DIR/content.txt"
            echo "Full Path: $file_path" >> "$TEMP_DIR/content.txt"
            echo "Last Modified: $(stat -f "%Sm" "$file_path" 2>/dev/null || stat -c "%y" "$file_path" 2>/dev/null || echo "Unknown")" >> "$TEMP_DIR/content.txt"
            echo "File Size: $(wc -c < "$file_path" | tr -d ' ') bytes" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "--- CONTENT START ---" >> "$TEMP_DIR/content.txt"
            cat "$file_path" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "--- CONTENT END ---" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "==========================================" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            ;;
    esac
}

# Function to recursively scan directory
scan_directory() {
    local dir="$1"
    local base_dir="$2"
    
    if [[ ! -d "$dir" ]]; then
        echo -e "${RED}⚠️  Directory not found: $dir${NC}"
        return
    fi
    
    for item in "$dir"/*; do
        if [[ -d "$item" ]]; then
            # It's a directory, recurse
            scan_directory "$item" "$base_dir"
        elif [[ -f "$item" ]]; then
            # It's a file, process it
            process_file "$item" "$base_dir"
        fi
    done
}

# Create the output file with header
cat > "$TEMP_DIR/content.txt" << EOF
# COMPREHENSIVE BACKEND CODEBASE REVIEW PROMPT
# ============================================
# 
# This file contains the complete content of all files in the backend directories.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: $(date)
# Directories scanned: microservices cmd pkg migrations config
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure
# 2. Analyze the overall backend architecture and design patterns
# 3. Identify patterns, dependencies, and relationships across all components
# 4. Provide insights on system design, code quality, and potential improvements
# 5. Note any inconsistencies, missing implementations, or areas for enhancement
# 6. Review the integration between microservices, packages, and commands
# 7. Analyze database migrations and schema evolution
# 8. Assess the overall system architecture and scalability
# 9. Review configuration files, security settings, and deployment configs
# 10. Analyze Ory stack configurations (Kratos, Keto, Oathkeeper) and their integration
#
# FILE CONTENTS:
# =============

EOF

# Start scanning each directory
echo -e "${YELLOW}📁 Scanning directory: $MICROSERVICES_DIR${NC}"
scan_directory "$MICROSERVICES_DIR" "$MICROSERVICES_DIR"

echo -e "${YELLOW}📁 Scanning directory: $CMD_DIR${NC}"
scan_directory "$CMD_DIR" "$CMD_DIR"

echo -e "${YELLOW}📁 Scanning directory: $PKG_DIR${NC}"
scan_directory "$PKG_DIR" "$PKG_DIR"

echo -e "${YELLOW}📁 Scanning directory: $MIGRATIONS_DIR${NC}"
scan_directory "$MIGRATIONS_DIR" "$MIGRATIONS_DIR"

echo -e "${YELLOW}📁 Scanning directory: $CONFIG_DIR${NC}"
scan_directory "$CONFIG_DIR" "$CONFIG_DIR"

# Generate summary
echo "" >> "$TEMP_DIR/content.txt"
echo "# SUMMARY" >> "$TEMP_DIR/content.txt"
echo "# =======" >> "$TEMP_DIR/content.txt"
echo "Total files processed: $(grep -c "=== FILE:" "$TEMP_DIR/content.txt")" >> "$TEMP_DIR/content.txt"
echo "File types found:" >> "$TEMP_DIR/content.txt"
grep "File Type:" "$TEMP_DIR/content.txt" | cut -d: -f2 | sort | uniq -c | sed 's/^/  /' >> "$TEMP_DIR/content.txt"

# Create the final output file
mkdir -p "$PROMPTS_DIR"
cp "$TEMP_DIR/content.txt" "$PROMPTS_DIR/$OUTPUT_FILE"

# Clean up
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ Generated comprehensive backend prompt file: $PROMPTS_DIR/$OUTPUT_FILE${NC}"
echo -e "${BLUE}📊 Summary:${NC}"
echo -e "  - Files processed: $(grep -c "=== FILE:" "$PROMPTS_DIR/$OUTPUT_FILE")"
echo -e "  - Output size: $(du -h "$PROMPTS_DIR/$OUTPUT_FILE" | cut -f1)"
echo -e "  - Location: $PROMPTS_DIR/$OUTPUT_FILE"
echo -e "  - Directories scanned: microservices cmd pkg migrations config"

echo -e "${YELLOW}🎯 Ready for AI review! The file contains all backend code with proper labeling.${NC}" 