# CMD CODEBASE REVIEW PROMPT
# ==========================
# 
# This file contains the complete content of all files in the cmd directory.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: Mon Sep 15 17:52:51 EDT 2025
# Total files to process:       28
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure
# 2. Analyze the command-line applications and their purposes
# 3. Identify patterns, dependencies, and relationships between commands
# 4. Provide insights on application design, entry points, and potential improvements
# 5. Note any inconsistencies, missing implementations, or areas for enhancement
# 6. Review main functions, configuration, and service initialization patterns
#
# FILE CONTENTS:
# =============

=== FILE: auth/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/auth/main.go
Last Modified: Sep 14 17:13:36 2025
File Size: 5990 bytes

--- CONTENT START ---
package main

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/auth"
	"hopenbackend/pkg/jwt"
	"hopenbackend/pkg/monitoring"
	sagapkg "hopenbackend/pkg/saga"
	"hopenbackend/pkg/service"
	authv1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	var globalLogger *zap.Logger
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		globalLogger = deps.Logger
		// FIRST DEBUG: Check if service factory is called at all

		// Create auth repository using standardized pattern
		authRepository := auth.NewRepository(deps.PostgreSQL.Pool)
		deps.Logger.Info("Auth repository initialized successfully")

		// CRITICAL DEBUG: Check if we reach this point

		// Debug message to check if we reach this point
		deps.Logger.Info("DEBUG: About to create saga metrics")

		// Create saga metrics and repository
		sagaMetrics := monitoring.NewSagaMetricsCollector(deps.Logger)
		var sagaRepository sagapkg.Repository

		// Debug logging to understand why saga repository is nil
		deps.Logger.Info("Debug saga repository creation",
			zap.Bool("postgresql_nil", deps.PostgreSQL == nil),
			zap.Bool("pool_nil", deps.PostgreSQL != nil && deps.PostgreSQL.Pool == nil))

		if deps.PostgreSQL != nil && deps.PostgreSQL.Pool != nil {
			sagaRepository = sagapkg.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger)
			deps.Logger.Info("Saga repository created successfully")
		} else {
			deps.Logger.Warn("Saga repository not created - PostgreSQL or Pool is nil")
		}

		// Create alert manager and health checker
		alertManager := monitoring.NewAlertManager(deps.Logger)
		healthChecker := monitoring.NewHealthChecker(deps.Logger, alertManager, deps.PostgreSQL.Pool, deps.NATSConn, "auth")

		// Add HIBP health check
		hibpConfig := auth.HIBPConfig{
			BaseURL:   deps.Config.Security.HIBP.BaseURL,
			UserAgent: "hopen-auth-service",
		}
		hibpHealthCheck := auth.NewHIBPHealthCheck(hibpConfig)
		healthChecker.AddCheck(hibpHealthCheck)

		// Start health checker
		healthChecker.Start(context.Background())

		// Create JWT generator for session tokens with MQTT permissions
		var jwtGenerator *jwt.Generator
		if deps.Config.JWT.Secret != "" {
			jwtGenerator = jwt.NewGenerator("hopen-auth", deps.Config.JWT.Secret)
			deps.Logger.Info("JWT generator initialized for session tokens")
		} else {
			deps.Logger.Warn("JWT secret not configured, session tokens will use legacy format")
		}

		// Create auth service
		authService := auth.New(&auth.Dependencies{
			Logger:         deps.Logger,
			Config:         deps.Config,
			OryClient:      deps.OryClient,
			Repository:     authRepository,
			UserClient:     deps.UserClient,
			EmailClient:    deps.EmailClient,
			BubbleClient:   deps.BubbleClient,
			RetryManager:   deps.RetryManager,
			SagaRepository: sagaRepository,
			SagaMetrics:    sagaMetrics,
			EventPublisher: deps.EventPublisher,
			JWTGenerator:   jwtGenerator,
		})

		// Subscribe to user update events to keep Ory Kratos in sync
		if deps.NATSConn != nil {
			_, err := deps.NATSConn.Subscribe("user.updated", authService.HandleUserUpdatedEvent)
			if err != nil {
				deps.Logger.Error("Failed to subscribe to user.updated events", zap.Error(err))
			} else {
				deps.Logger.Info("Subscribed to user.updated events for Ory Kratos synchronization")
			}
		}

		// Initialize saga worker for background processing
		deps.Logger.Info("Checking saga repository", zap.Bool("saga_repository_nil", sagaRepository == nil))
		if sagaRepository != nil {
			// Use production-optimized saga worker configuration for fast user registration
			workerConfig := sagapkg.ProductionWorkerConfig()
			deps.Logger.Info("Using production saga configuration",
				zap.Duration("scan_interval", workerConfig.ScanInterval),
				zap.Duration("stale_threshold", workerConfig.StaleThreshold),
				zap.Int("max_retries", workerConfig.MaxRetries))
			sagaWorker := sagapkg.NewWorker(sagaRepository, deps.Logger, workerConfig)

			// Register the registration saga handler
			registrationHandler := auth.NewRegistrationSagaHandler(authService)
			sagaWorker.RegisterHandler("user_registration", registrationHandler)

			// Start the saga worker
			sagaWorker.Start(context.Background())

			// Store worker reference for graceful shutdown
			authService.SetSagaWorker(sagaWorker)
		}

		// Start saga cleanup handler if NATS is available
		if deps.NATSConn != nil {
			cleanupHandler := auth.NewSagaCleanupHandler(authService, deps.NATSConn, deps.Logger)
			if err := cleanupHandler.Start(context.Background()); err != nil {
				deps.Logger.Error("Failed to start saga cleanup handler", zap.Error(err))
			} else {
				deps.Logger.Info("Saga cleanup handler started successfully")

				// Store cleanup handler for graceful shutdown
				authService.SetCleanupHandler(cleanupHandler)
			}
		}

		return authService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		authService := service.(*auth.Service)
		authv1.RegisterAuthServiceServer(s, authService)

		// Register ExHook service for EMQX integration
		exhookService := auth.NewExHookService(authService, globalLogger)
		authv1.RegisterHookProviderServer(s, exhookService)
		globalLogger.Info("ExHook service registered for EMQX integration")
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "auth",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequiredClients: []string{"user", "email", "bubble"}, // Auth service needs user, email, and bubble clients
		RetryType:       "external",
	})
	if err != nil {
		// Use a basic logger if runner creation fails
		logger, _ := zap.NewDevelopment()
		logger.Fatal("Failed to create service runner", zap.Error(err))
		return
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: bubble/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/bubble/main.go
Last Modified: Sep 11 13:09:00 2025
File Size: 2665 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/bubble"
	"hopenbackend/pkg/events"
	"hopenbackend/pkg/service"
	bubblev1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize bubble repository using standardized pattern
		bubbleRepository := bubble.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger)
		deps.Logger.Info("Bubble repository initialized successfully")

		// Initialize centralized DLQ manager
		dlqConfig := events.DefaultDLQConfig()
		dlqManager := events.NewDLQManager(deps.NATSConn, deps.Logger, dlqConfig, deps.PostgreSQL.Pool)
		deps.Logger.Info("DLQ manager initialized successfully")

		// Create bubble service using centralized gRPC clients and shared components
		bubbleDeps := &bubble.Dependencies{
			Logger:              deps.Logger,
			DB:                  deps.PostgreSQL,
			Repository:          bubbleRepository,
			OutboxRepository:    deps.OutboxRepository,
			OryClient:           deps.OryClient,
			NATSConn:            deps.NATSConn,
			NotificationService: deps.NotificationClient, // Use centralized client from ServiceDependencies
			UserServiceClient:   deps.UserClient,         // Use user service for friendship creation
			RetryManager:        deps.RetryManager,
			IdempotencyManager:  deps.IdempotencyManager, // Add idempotency manager
			KetoClient:          deps.KetoClient,
			DLQManager:          dlqManager,                                                                                            // Add centralized DLQ manager
			VotingManager:       bubble.NewVotingLifecycleManager(deps.PostgreSQL, bubbleRepository, deps.Logger, deps.EventPublisher), // Add autonomous voting manager
		}
		bubbleService := bubble.NewService(bubbleDeps)

		return bubbleService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		bubbleService := service.(*bubble.Service)
		bubblev1.RegisterBubbleServiceServer(s, bubbleService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "bubble",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequiredClients: []string{"user", "notification"}, // Bubble service needs user and notification clients
		RetryType:       "database",
	})
	if err != nil {
		runner.GetLogger().Fatal("Failed to create service runner", zap.Error(err))
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: call/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/call/main.go
Last Modified: Sep 14 22:39:19 2025
File Size: 3961 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/call"
	"hopenbackend/pkg/distributedlock"
	"hopenbackend/pkg/jwt"
	"hopenbackend/pkg/retry"
	"hopenbackend/pkg/service"
	callv1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize retry manager for external service calls (LiveKit API)
		retryManager := retry.New(retry.ExternalServiceConfig(), deps.Logger)

		// Initialize distributed lock manager for call concurrency control
		var callLockManager *distributedlock.CallLockManager
		if deps.ValkeyClient != nil {
			lockManager := distributedlock.NewLockManager(deps.ValkeyClient, deps.Logger)
			callLockManager = distributedlock.NewCallLockManager(lockManager, deps.Logger)
			deps.Logger.Info("Call lock manager initialized with Valkey")
		} else {
			deps.Logger.Warn("Valkey client not available, call lock manager disabled")
		}

		// Get shared JWT generator from shared components
		var jwtGenerator *jwt.Generator
		if deps.SharedComponents != nil {
			jwtGenerator = deps.SharedComponents.GetLiveKitJWTGenerator()
		}

		// Create CallManager (LiveKit operations)
		var callManager call.CallManager
		if jwtGenerator != nil {
			// Use LiveKit repository with shared JWT generator - jwt generator will be used via SharedComponents
			callManager = call.NewLiveKitRepository(
				deps.Config.LiveKit.ServerURL,
				deps.Logger,
			)
			deps.Logger.Info("LiveKit repository initialized")
		} else {
			deps.Logger.Info("LiveKit JWT generator not available, CallManager will be nil")
		}

		// Create CallHistoryRepository (PostgreSQL operations) using shared JWT generator
		var historyRepo call.CallHistoryRepository
		if jwtGenerator != nil {
			historyRepo = call.NewPostgreSQLRepositoryWithSharedJWT(
				deps.PostgreSQL.Pool,
				deps.Logger,
				jwtGenerator,
			)
			deps.Logger.Info("PostgreSQL repository initialized with shared JWT generator")
		} else {
			// Fallback to repository without JWT generator
			historyRepo = call.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger)
			deps.Logger.Info("PostgreSQL repository initialized without JWT generator")
		}

		// Create call service using centralized gRPC clients
		callDeps := &call.Dependencies{
			Logger:             deps.Logger,
			DB:                 deps.PostgreSQL,
			CallManager:        callManager,
			HistoryRepo:        historyRepo,
			Config:             deps.Config,
			RateLimiter:        deps.RateLimiter,
			OryClient:          deps.OryClient,
			NATSConn:           deps.NATSConn,
			UserService:        deps.UserClient, // Use centralized client from ServiceDependencies
			RetryManager:       retryManager,
			IdempotencyManager: deps.IdempotencyManager, // Add idempotency manager
			KetoClient:         deps.KetoClient,
			SharedComponents:   deps.SharedComponents, // Add shared components for centralized JWT generation
			CallLockManager:    callLockManager,       // Add distributed lock manager for race condition prevention
		}
		callService := call.NewService(callDeps)

		return callService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		callService := service.(*call.Service)
		callv1.RegisterCallServiceServer(s, callService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "call",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequiredClients: []string{"user"}, // Call service only needs user service client
	})
	if err != nil {
		// Use a basic logger if runner creation fails
		logger, _ := zap.NewDevelopment()
		logger.Fatal("Failed to create service runner", zap.Error(err))
		return
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: email/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/email/main.go
Last Modified: Sep  9 23:56:39 2025
File Size: 1944 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/email"
	"hopenbackend/pkg/retry"
	"hopenbackend/pkg/service"
	emailv1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize retry manager for AWS SES operations
		retryManager := retry.New(retry.ExternalServiceConfig(), deps.Logger)

		// Create email repository using standardized pattern
		emailRepository := email.NewRepository(deps.PostgreSQL.Pool, deps.Logger)
		deps.Logger.Info("Email repository initialized successfully")

		// Create email service using centralized gRPC clients and shared components
		emailDeps := &email.Dependencies{
			Logger:             deps.Logger,
			Config:             deps.Config,
			Repository:         emailRepository,
			DB:                 deps.PostgreSQL,
			RateLimiter:        deps.RateLimiter,
			NATSConn:           deps.NATSConn,
			RetryManager:       retryManager,
			IdempotencyManager: deps.IdempotencyManager,
		}
		emailService := email.NewService(emailDeps)

		return emailService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		emailService := service.(*email.Service)
		emailv1.RegisterEmailServiceServer(s, emailService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "email",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequiredClients: []string{}, // Email service doesn't need external gRPC clients
	})
	if err != nil {
		// Use a basic logger if runner creation fails
		logger, _ := zap.NewDevelopment()
		logger.Fatal("Failed to create service runner", zap.Error(err))
		return
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: media/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/media/main.go
Last Modified: Sep  9 23:57:32 2025
File Size: 1744 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/media"
	"hopenbackend/pkg/service"
	mediav1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize media repository using standardized pattern
		mediaRepository := media.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger)
		deps.Logger.Info("Media repository initialized successfully")

		// Create media service using centralized gRPC clients and shared components
		mediaDeps := &media.Dependencies{
			Logger:      deps.Logger,
			DB:          deps.PostgreSQL,
			Repository:  mediaRepository,
			Config:      deps.Config,
			RateLimiter: deps.RateLimiter,
			OryClient:   deps.OryClient,
			NATSConn:    deps.NATSConn,
			KetoClient:  deps.KetoClient,
		}
		mediaService := media.NewService(mediaDeps)

		return mediaService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		mediaService := service.(*media.Service)
		mediav1.RegisterMediaServiceServer(s, mediaService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "media",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequiredClients: []string{}, // Media service doesn't need external gRPC clients
	})
	if err != nil {
		// Use a basic logger if runner creation fails
		logger, _ := zap.NewDevelopment()
		logger.Fatal("Failed to create service runner", zap.Error(err))
		return
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: migrate/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/migrate/main.go
Last Modified: Sep 11 13:30:53 2025
File Size: 6303 bytes

--- CONTENT START ---
package main

import (
	"context"
	"database/sql"
	"errors"
	"flag"
	"fmt"
	"log"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	_ "github.com/lib/pq"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
)

func main() {
	var (
		action       = flag.String("action", "up", "Migration action: up, down, status")
		database     = flag.String("database", "all", "Database to migrate: postgresql, scylladb, all")
		verbose      = flag.Bool("verbose", false, "Enable verbose logging")
		dropKeyspace = flag.Bool("drop-keyspace", false, "Drop ScyllaDB keyspace during down migration (DESTRUCTIVE - dev/test only)")
	)
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Setup logging
	var logger *zap.Logger
	if *verbose {
		logger, _ = zap.NewDevelopment()
	} else {
		logger, _ = zap.NewProduction()
	}
	defer func() { _ = logger.Sync() }()

	// Create context
	ctx := context.Background()

	// Execute migration action based on database type
	switch *database {
	case "postgresql":
		if err := runPostgreSQLMigrations(ctx, cfg, logger, *action); err != nil {
			logger.Fatal("PostgreSQL migration failed", zap.Error(err))
		}
	case "scylladb":
		if err := runScyllaDBMigrations(ctx, cfg, logger, *action, *dropKeyspace); err != nil {
			logger.Fatal("ScyllaDB migration failed", zap.Error(err))
		}
	case "all":
		// Run PostgreSQL migrations first
		logger.Info("Running PostgreSQL migrations...")
		if err := runPostgreSQLMigrations(ctx, cfg, logger, *action); err != nil {
			logger.Fatal("PostgreSQL migration failed", zap.Error(err))
		}

		// Run ScyllaDB migrations
		logger.Info("Running ScyllaDB migrations...")
		if err := runScyllaDBMigrations(ctx, cfg, logger, *action, *dropKeyspace); err != nil {
			logger.Fatal("ScyllaDB migration failed", zap.Error(err))
		}

		logger.Info("All migrations completed successfully")
	default:
		logger.Fatal("Invalid database type", zap.String("database", *database))
	}
}

// runPostgreSQLMigrations handles PostgreSQL migrations using golang-migrate
func runPostgreSQLMigrations(ctx context.Context, cfg *config.Config, logger *zap.Logger, action string) error {
	// Create PostgreSQL client
	postgresql, err := database.NewPostgreSQLClient(&cfg.Databases.PostgreSQL, logger)
	if err != nil {
		return fmt.Errorf("failed to create PostgreSQL client: %w", err)
	}
	defer postgresql.Close()

	// Create a separate SQL connection for migrations using lib/pq driver
	config := postgresql.Pool.Config()
	connConfig := config.ConnConfig

	// Build connection string for lib/pq
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		connConfig.User,
		connConfig.Password,
		connConfig.Host,
		connConfig.Port,
		connConfig.Database,
	)

	// Open SQL connection using lib/pq driver
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return fmt.Errorf("failed to open database connection for migrations: %w", err)
	}
	defer db.Close()

	// Create migration driver using the postgres driver
	driver, err := postgres.WithInstance(db, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Point to the migration files
	m, err := migrate.NewWithDatabaseInstance(
		"file://migrations/postgresql",
		"postgres",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w", err)
	}

	// Execute migration action
	switch action {
	case "up":
		logger.Info("Running PostgreSQL migrations up")
		if err := m.Up(); err != nil {
			// ErrNoChange is ok, it means the database is already up-to-date
			if !errors.Is(err, migrate.ErrNoChange) {
				return fmt.Errorf("failed to run PostgreSQL migrations: %w", err)
			} else {
				logger.Info("PostgreSQL database schema is up-to-date")
			}
		} else {
			logger.Info("PostgreSQL migrations applied successfully")
		}

	case "down":
		logger.Info("Running PostgreSQL migrations down")
		if err := m.Steps(-1); err != nil {
			return fmt.Errorf("failed to rollback PostgreSQL migrations: %w", err)
		}
		logger.Info("PostgreSQL migration rollback completed successfully")

	case "status":
		logger.Info("Checking PostgreSQL migration status")
		version, dirty, err := m.Version()
		if err != nil {
			return fmt.Errorf("failed to check PostgreSQL migration status: %w", err)
		}
		logger.Info("PostgreSQL migration status",
			zap.Int("version", int(version)),
			zap.Bool("dirty", dirty))

	default:
		return fmt.Errorf("invalid action: %s", action)
	}

	return nil
}

// runScyllaDBMigrations handles ScyllaDB migrations using the custom migration runner
func runScyllaDBMigrations(ctx context.Context, cfg *config.Config, logger *zap.Logger, action string, dropKeyspace bool) error {
	// Create ScyllaDB client
	scylladbClient, err := database.NewScyllaDBClient(&cfg.Databases.ScyllaDB, logger)
	if err != nil {
		return fmt.Errorf("failed to create ScyllaDB client: %w", err)
	}
	defer scylladbClient.Close()

	// Create migration runner
	migrationRunner := database.NewMigrationRunner(scylladbClient.Session, cfg.Databases.ScyllaDB.Keyspace, logger)

	// Get migrations path
	migrationsPath := "migrations/scylladb"

	// Execute migration action
	switch action {
	case "up":
		logger.Info("Running ScyllaDB migrations up")
		if err := migrationRunner.Up(ctx, migrationsPath); err != nil {
			return fmt.Errorf("failed to run ScyllaDB migrations: %w", err)
		}
		logger.Info("ScyllaDB migrations completed successfully")

	case "down":
		logger.Info("Running ScyllaDB migrations down")
		options := database.MigrationOptions{DropKeyspaceOnDown: dropKeyspace}
		if err := migrationRunner.DownWithOptions(ctx, migrationsPath, options); err != nil {
			return fmt.Errorf("failed to run ScyllaDB migrations down: %w", err)
		}
		logger.Info("ScyllaDB migration rollback completed successfully")

	case "status":
		logger.Info("Checking ScyllaDB migration status")
		if err := migrationRunner.Status(ctx, migrationsPath); err != nil {
			return fmt.Errorf("failed to check ScyllaDB migration status: %w", err)
		}

	default:
		return fmt.Errorf("invalid action: %s", action)
	}

	return nil
}

--- CONTENT END ---

==========================================

=== FILE: notification/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/notification/main.go
Last Modified: Sep  1 21:05:56 2025
File Size: 1825 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/notification"
	"hopenbackend/pkg/retry"
	"hopenbackend/pkg/service"
	notificationv1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize retry manager for FCM operations
		retryManager := retry.New(retry.ExternalServiceConfig(), deps.Logger)

		// Initialize notification repository
		notificationRepository := notification.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger)

		// Create notification service
		notificationDeps := &notification.Dependencies{
			Logger:       deps.Logger,
			DB:           deps.PostgreSQL,
			Repository:   notificationRepository,
			Config:       deps.Config,
			RateLimiter:  deps.RateLimiter,
			OryClient:    deps.OryClient,
			NATSConn:     deps.NATSConn,
			UserClient:   deps.UserClient,
			BubbleClient: deps.BubbleClient,
			RetryManager: retryManager,
			KetoClient:   deps.KetoClient,
		}
		notificationService := notification.NewService(notificationDeps)

		return notificationService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		notificationService := service.(*notification.Service)
		notificationv1.RegisterNotificationServiceServer(s, notificationService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "notification",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
	})
	if err != nil {
		runner.GetLogger().Fatal("Failed to create service runner", zap.Error(err))
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: outbox_processor/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/outbox_processor/main.go
Last Modified: Sep 13 20:40:52 2025
File Size: 3007 bytes

--- CONTENT START ---
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/outbox"
	"hopenbackend/pkg/service"
)

func main() {
	// Parse command line flags
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	logger, err := service.InitLogger(cfg)
	if err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	logger.Info("Starting Outbox Processor Service (ScyllaDB Native)")

	// Initialize ScyllaDB connection
	scyllaClient, err := database.NewScyllaDBClient(&cfg.Databases.ScyllaDB, logger)
	if err != nil {
		logger.Fatal("Failed to connect to ScyllaDB", zap.Error(err))
	}
	defer scyllaClient.Close()

	// Initialize NATS connection
	natsConn, err := nats.Connect(cfg.NATS.URL)
	if err != nil {
		logger.Fatal("Failed to connect to NATS", zap.Error(err))
	}
	defer natsConn.Close()

	// Initialize ScyllaDB outbox repository
	outboxRepo := outbox.NewScyllaDBRepository(scyllaClient, logger)

	// Create and start outbox processor
	processor := createOutboxProcessor(natsConn, outboxRepo, logger)
	if processor == nil {
		logger.Fatal("Failed to create outbox processor")
	}

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	logger.Info("Received shutdown signal, stopping outbox processor...")
}

// createOutboxProcessor creates and starts the outbox processor
func createOutboxProcessor(natsConn *nats.Conn, outboxRepo outbox.Repository, logger *zap.Logger) interface{} {
	// Try to get JetStream context
	var js nats.JetStreamContext
	var err error
	if js, err = natsConn.JetStream(); err != nil {
		logger.Warn("JetStream not available, using regular NATS", zap.Error(err))
	}

	// Create event publisher
	publisher := outbox.NewNATSEventPublisher(natsConn, js, logger)

	// Create outbox processor
	processor := outbox.NewProcessor(
		outboxRepo,
		publisher,
		logger,
		&outbox.ProcessorConfig{
			BatchSize:        10,
			PollingInterval:  5 * time.Second,
			MaxRetries:       3,
			CleanupInterval:  1 * time.Hour,
			CleanupOlderThan: 24 * time.Hour,
		},
	)

	// Start the processor
	ctx, cancel := context.WithCancel(context.Background())

	// Handle shutdown signals in a separate goroutine
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan
		logger.Info("Received shutdown signal, stopping outbox processor...")
		cancel()
	}()

	// Start processing
	go func() {
		if err := processor.Start(ctx); err != nil {
			logger.Error("Outbox processor failed", zap.Error(err))
		}
	}()

	// Wait for context cancellation
	<-ctx.Done()
	logger.Info("Outbox processor stopped")

	return processor
}

--- CONTENT END ---

==========================================

=== FILE: presence/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/presence/main.go
Last Modified: Sep  6 12:32:40 2025
File Size: 2014 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/presence"
	"hopenbackend/pkg/retry"
	"hopenbackend/pkg/service"
	presencev1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize retry manager for database operations
		retryManager := retry.New(retry.DatabaseConfig(), deps.Logger)

		// Initialize presence repository using standardized pattern
		presenceRepository := presence.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger)
		deps.Logger.Info("Presence repository initialized successfully")

		// Create presence service using centralized gRPC clients and shared components
		presenceDeps := &presence.Dependencies{
			Logger:             deps.Logger,
			DB:                 deps.PostgreSQL,
			Repository:         presenceRepository,
			Config:             deps.Config,
			RateLimiter:        deps.RateLimiter,
			OryClient:          deps.OryClient,
			NATSConn:           deps.NATSConn,
			ValkeyClient:       deps.ValkeyClient,
			RetryManager:       retryManager,
			IdempotencyManager: deps.IdempotencyManager,
		}
		presenceService := presence.NewService(presenceDeps)

		return presenceService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		presenceService := service.(*presence.Service)
		presencev1.RegisterPresenceServiceServer(s, presenceService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "presence",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequiredClients: []string{}, // Presence service doesn't need external gRPC clients
	})
	if err != nil {
		runner.GetLogger().Fatal("Failed to create service runner", zap.Error(err))
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: realtime/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/realtime/main.go
Last Modified: Sep 13 20:11:36 2025
File Size: 2900 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/realtime"
	"hopenbackend/pkg/retry"
	"hopenbackend/pkg/service"
	realtimev1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize retry manager for ScyllaDB operations
		retryManager := retry.New(retry.DatabaseConfig(), deps.Logger)

		// Initialize realtime repository using pure ScyllaDB pattern (no CDC)
		// This follows the pure transactional outbox pattern for event publishing
		realtimeRepository := realtime.NewScyllaDBRepository(
			deps.ScyllaDB,
			deps.Logger,
			deps.PresenceClient, // Pass presence client for last seen data
			deps.UserClient,     // Pass user client for username fetching
			deps.NATSConn,       // Pass NATS connection for real-time subscriptions
		)
		deps.Logger.Info("Realtime repository initialized successfully with pure ScyllaDB outbox pattern")

		// Create realtime service using centralized gRPC clients and shared components
		// Note: No PostgreSQL outbox repository - using pure ScyllaDB outbox pattern
		realtimeDeps := &realtime.Dependencies{
			Logger:             deps.Logger,
			Repository:         realtimeRepository,
			SearchClient:       deps.SearchClient,   // Pass centralized search client
			PresenceClient:     deps.PresenceClient, // Pass presence client for last seen data
			Config:             deps.Config,
			RateLimiter:        deps.RateLimiter,
			OryClient:          deps.OryClient,
			NATSConn:           deps.NATSConn,
			BubbleClient:       deps.BubbleClient,
			RetryManager:       retryManager,
			IdempotencyManager: deps.IdempotencyManager, // Add idempotency manager
			KetoClient:         deps.KetoClient,
			// OutboxRepository removed - using ScyllaDB native outbox pattern
		}
		realtimeService := realtime.NewService(realtimeDeps)

		return realtimeService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		realtimeService := service.(*realtime.Service)
		realtimev1.RegisterRealtimeServiceServer(s, realtimeService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "realtime",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequireScyllaDB: true,                                             // Realtime service requires ScyllaDB
		RequiredClients: []string{"search", "presence", "bubble", "user"}, // Realtime service needs these clients
	})
	if err != nil {
		// Use a basic logger if runner creation fails
		logger, _ := zap.NewDevelopment()
		logger.Fatal("Failed to create service runner", zap.Error(err))
		return
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: search/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/search/main.go
Last Modified: Sep  9 23:57:00 2025
File Size: 2791 bytes

--- CONTENT START ---
package main

import (
	"context"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/search"
	"hopenbackend/pkg/opensearch"
	"hopenbackend/pkg/retry"
	"hopenbackend/pkg/service"
	searchv1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize retry manager for OpenSearch operations
		retryManager := retry.New(retry.ExternalServiceConfig(), deps.Logger)

		// Create enhanced OpenSearch client using factory
		clientFactory := opensearch.NewClientFactory(&deps.Config.OpenSearch, deps.Logger)
		osClient, err := clientFactory.CreateClient()
		if err != nil {
			deps.Logger.Fatal("Failed to create OpenSearch client", zap.Error(err))
		}

		// Create index manager and ensure indices exist
		indexManager := opensearch.NewIndexManager(osClient, deps.Logger)
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := indexManager.EnsureIndicesExist(ctx); err != nil {
			deps.Logger.Error("Failed to ensure OpenSearch indices exist", zap.Error(err))
			// Don't fail startup - indices can be created later
		}

		// Create OpenSearch repository
		opensearchRepository := search.NewOpenSearchRepository(osClient, deps.Logger)

		// Create PostgreSQL repository
		postgresRepository := search.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger)

		// Create hybrid repository with fallback mechanism
		searchRepository := search.NewHybridRepository(opensearchRepository, postgresRepository, deps.Logger)

		// Create search service
		searchService := search.NewService(&search.Dependencies{
			Logger:         deps.Logger,
			Repository:     searchRepository,
			Config:         deps.Config,
			DB:             deps.PostgreSQL,
			NATSConn:       deps.NATSConn,
			UserClient:     deps.UserClient,
			BubbleClient:   deps.BubbleClient,
			RealtimeClient: deps.RealtimeClient,
			RetryManager:   retryManager,
			KetoClient:     deps.KetoClient,
		})

		return searchService
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:    "search",
		ServiceFactory: serviceFactory,
		RegisterService: func(s *grpc.Server, svc interface{}) {
			searchService := svc.(*search.Service)
			searchv1.RegisterSearchServiceServer(s, searchService)
		},
		RequiredClients: []string{}, // Search service doesn't need external gRPC clients
	})
	if err != nil {
		// Use a basic logger if runner creation fails
		logger, _ := zap.NewDevelopment()
		logger.Fatal("Failed to create service runner", zap.Error(err))
		return
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: social_analytics/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/social_analytics/main.go
Last Modified: Sep  6 12:35:46 2025
File Size: 3110 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/social_analytics"
	"hopenbackend/pkg/retry"
	"hopenbackend/pkg/service"
	socialanalyticsv1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize retry manager for database operations
		retryManager := retry.New(retry.DatabaseConfig(), deps.Logger)

		// Initialize social analytics repository with configuration
		socialAnalyticsRepository := social_analytics.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger, &deps.Config.SocialAnalytics)

		// Initialize storage client for analytics exports
		var storageClient social_analytics.StorageClient
		var err error

		// Use MinIO if configured, otherwise fall back to local storage
		if deps.Config.MinIO.Endpoint != "" {
			storageClient, err = social_analytics.NewMinIOStorageClient(deps.Config, deps.Logger)
			if err != nil {
				deps.Logger.Error("Failed to create MinIO storage client, falling back to local storage", zap.Error(err))
			}
		}

		// Fall back to local storage if MinIO is not available
		if storageClient == nil {
			storageClient, err = social_analytics.NewLocalStorageClient("/tmp/hopen_analytics_exports", deps.Logger)
			if err != nil {
				deps.Logger.Fatal("Failed to create local storage client", zap.Error(err))
			}
		}

		// Create social analytics service using centralized gRPC clients and shared components
		socialAnalyticsDeps := &social_analytics.Dependencies{
			Logger:             deps.Logger,
			DB:                 deps.PostgreSQL,
			Repository:         socialAnalyticsRepository,
			Config:             deps.Config,
			RateLimiter:        deps.RateLimiter,
			OryClient:          deps.OryClient,
			NATSConn:           deps.NATSConn,
			UserClient:         deps.UserClient,
			BubbleClient:       deps.BubbleClient,
			PresenceClient:     deps.PresenceClient,
			StorageClient:      storageClient,
			KetoClient:         deps.KetoClient,
			RetryManager:       retryManager,
			OutboxRepository:   deps.OutboxRepository,
			IdempotencyManager: deps.IdempotencyManager,
		}
		socialAnalyticsService := social_analytics.NewService(socialAnalyticsDeps)

		return socialAnalyticsService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		socialAnalyticsService := service.(*social_analytics.Service)
		socialanalyticsv1.RegisterSocialAnalyticsServiceServer(s, socialAnalyticsService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "social_analytics",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequiredClients: []string{"user", "bubble", "presence"}, // Social analytics needs user, bubble, and presence clients
	})
	if err != nil {
		runner.GetLogger().Fatal("Failed to create service runner", zap.Error(err))
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: sync/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/sync/main.go
Last Modified: Sep  9 23:56:22 2025
File Size: 1919 bytes

--- CONTENT START ---
package main

import (
	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/sync"
	"hopenbackend/pkg/retry"
	"hopenbackend/pkg/service"
	syncv1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Initialize retry manager for database operations
		retryManager := retry.New(retry.DatabaseConfig(), deps.Logger)

		// Initialize sync repository
		syncRepository := sync.NewPostgreSQLRepository(deps.PostgreSQL.Pool, deps.Logger)

		// Create sync service
		syncDeps := &sync.Dependencies{
			Logger:             deps.Logger,
			DB:                 deps.PostgreSQL,
			Repository:         syncRepository,
			Config:             deps.Config,
			RateLimiter:        deps.RateLimiter,
			OryClient:          deps.OryClient,
			NATSConn:           deps.NATSConn,
			UserClient:         deps.UserClient,
			BubbleClient:       deps.BubbleClient,
			RealtimeClient:     deps.RealtimeClient,
			NotificationClient: deps.NotificationClient,
			RetryManager:       retryManager,
			IdempotencyManager: deps.IdempotencyManager,
		}
		syncService := sync.NewService(syncDeps)

		return syncService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		syncService := service.(*sync.Service)
		syncv1.RegisterSyncServiceServer(s, syncService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "sync",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
	})
	if err != nil {
		// Use a basic logger if runner creation fails
		logger, _ := zap.NewDevelopment()
		logger.Fatal("Failed to create service runner", zap.Error(err))
		return
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================

=== FILE: user/main.go ===
File Type: go
Full Path: /Users/<USER>/Hopen/hopenbackend/cmd/user/main.go
Last Modified: Sep 11 12:52:31 2025
File Size: 2535 bytes

--- CONTENT START ---
package main

import (
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"

	"hopenbackend/microservices/user"
	"hopenbackend/pkg/cache"
	"hopenbackend/pkg/service"
	userv1 "hopenbackend/protos/gen"
)

func main() {
	// Create service factory
	serviceFactory := func(deps *service.ServiceDependencies) interface{} {
		// Create user repository using standardized pattern
		userRepository := user.NewRepository(deps.PostgreSQL.Pool, deps.Logger)
		deps.Logger.Info("User repository initialized successfully")

		// Create unified cache for caching operations
		var unifiedCache *cache.UnifiedCache
		if deps.ValkeyClient != nil {
			valkeyAdapter := cache.NewValkeyAdapter(deps.ValkeyClient)
			cacheConfig := cache.UnifiedCacheConfig{
				DefaultTTL:    15 * time.Minute,
				KeyPrefix:     "user_service",
				Logger:        deps.Logger,
				EnableMetrics: true,
			}
			unifiedCache = cache.NewUnifiedCache(valkeyAdapter, cacheConfig)
			deps.Logger.Info("Unified cache initialized successfully")
		} else {
			deps.Logger.Warn("Valkey client not available, cache will be disabled")
		}

		// Create user service using centralized gRPC clients and shared components
		userDeps := &user.Dependencies{
			Logger:       deps.Logger,
			DB:           deps.PostgreSQL,
			Repository:   userRepository,
			SearchClient: deps.SearchClient, // Pass centralized search client
			Config:       deps.Config,
			RateLimiter:  deps.RateLimiter,
			OryClient:    deps.OryClient,
			RetryManager: deps.RetryManager,
			KetoClient:   deps.KetoClient,
			NATSConn:     deps.NATSConn, // Pass NATS connection for event publishing
			UnifiedCache: unifiedCache,  // Pass unified cache for caching operations
		}
		userService := user.NewService(userDeps)

		return userService
	}

	// Create service registration function
	registerService := func(s *grpc.Server, service interface{}) {
		userService := service.(*user.Service)
		userv1.RegisterUserServiceServer(s, userService)
	}

	// Create service runner
	runner, err := service.NewServiceRunner(&service.ServiceRunnerOptions{
		ServiceName:     "user",
		ServiceFactory:  serviceFactory,
		RegisterService: registerService,
		RequiredClients: []string{"email", "notification"}, // User service needs email and notification clients
		RetryType:       "database",
	})
	if err != nil {
		runner.GetLogger().Fatal("Failed to create service runner", zap.Error(err))
	}

	// Run the service
	if err := runner.Run(); err != nil {
		runner.GetLogger().Fatal("Failed to run service", zap.Error(err))
	}
}

--- CONTENT END ---

==========================================


# SUMMARY
# =======
Total files processed: 14
File types found:
    14  go
