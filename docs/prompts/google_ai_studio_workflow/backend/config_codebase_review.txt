# CONFIG CODEBASE REVIEW PROMPT
# =============================
# 
# This file contains the complete content of all files in the config directory.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: Mon Sep 15 17:52:54 EDT 2025
# Total files to process:       22
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure
# 2. Analyze the configuration patterns and their purposes
# 3. Identify patterns, dependencies, and relationships between config files
# 4. Provide insights on configuration design, validation, and potential improvements
# 5. Note any inconsistencies, missing configurations, or areas for enhancement
# 6. Review configuration organization, naming conventions, and best practices
# 7. Check for security configurations, environment-specific settings, and deployment configs
#
# FILE CONTENTS:
# =============

=== FILE: acl.conf ===
File Type: conf
Full Path: /Users/<USER>/Hopen/hopenbackend/config/acl.conf
Last Modified: Sep  1 21:05:56 2025
File Size: 1269 bytes

--- CONTENT START ---
# EMQX ACL Configuration for Hopen Backend
# This file defines access control rules for MQTT topics

# Allow users to subscribe to their own notification topics
{allow, {user, "%u"}, subscribe, ["user/%u/notifications"]}

# Allow users to subscribe to realtime messages
{allow, {user, "%u"}, subscribe, ["realtime/messages"]}

# Allow users to subscribe to chat topics for bubbles they're members of
{allow, {user, "%u"}, subscribe, ["hopen/chat/%u"]}

# Allow users to subscribe to request topics for their own requests
{allow, {user, "%u"}, subscribe, ["hopen/requests/%u"]}

# Allow users to subscribe to bubble-specific topics (bubble ID will be validated by backend)
{allow, {user, "%u"}, subscribe, ["hopen/bubbles/+/chat"]}
{allow, {user, "%u"}, subscribe, ["hopen/bubbles/+/notifications"]}
{allow, {user, "%u"}, subscribe, ["bubble/+/messages"]}

# Allow users to publish to their own chat topics
{allow, {user, "%u"}, publish, ["hopen/chat/%u"]}

# Allow users to publish to bubble chat topics (bubble membership will be validated by backend)
{allow, {user, "%u"}, publish, ["hopen/bubbles/+/chat"]}

# Allow users to publish to realtime messages
{allow, {user, "%u"}, publish, ["realtime/messages"]}

# Deny all other topics by default
{deny, all, all, all}

--- CONTENT END ---

==========================================

=== FILE: config.yaml ===
File Type: yaml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/config.yaml
Last Modified: Sep 13 17:33:14 2025
File Size: 12532 bytes

--- CONTENT START ---
# Ory Configuration
ory:
  kratos_public_url: ${ORY_KRATOS_PUBLIC_URL:http://localhost:4433}
  kratos_admin_url: ${ORY_KRATOS_ADMIN_URL:http://localhost:4434}
  hydra_public_url: ${ORY_HYDRA_PUBLIC_URL:http://localhost:4444}
  hydra_admin_url: ${ORY_HYDRA_ADMIN_URL:http://localhost:4445}
  keto_public_url: ${ORY_KETO_PUBLIC_URL:http://localhost:4466}
  keto_admin_url: ${ORY_KETO_ADMIN_URL:http://localhost:4467}
  oathkeeper_url: ${ORY_OATHKEEPER_URL:http://localhost:4455}

# Database Configuration
database:
  host: ${DB_HOST:localhost}
  port: ${DB_PORT:5432}
  name: ${DB_NAME:hopen}
  user: ${DB_USER:postgres}
  password: ${DB_PASSWORD:}
  ssl_mode: ${DB_SSL_MODE:disable}

# Valkey Configuration (NOT Redis)
valkey:
  host: ${VALKEY_HOST:localhost}
  port: ${VALKEY_PORT:6379}
  address: ${VALKEY_ADDRESS:localhost:6379}
  password: ${VALKEY_PASSWORD:}
  database: ${VALKEY_DATABASE:0}
  pool_size: ${VALKEY_POOL_SIZE:10}
  min_idle_connections: ${VALKEY_MIN_IDLE:5}
  max_retries: ${VALKEY_MAX_RETRIES:3}
  dial_timeout: ${VALKEY_DIAL_TIMEOUT:5s}
  read_timeout: ${VALKEY_READ_TIMEOUT:3s}
  write_timeout: ${VALKEY_WRITE_TIMEOUT:3s}

# Server Configuration
server:
  host: ${SERVER_HOST:0.0.0.0}
  port: ${SERVER_PORT:8080}
  mode: ${GIN_MODE:debug}

# Logging
logging:
  level: ${LOG_LEVEL:info}
  format: ${LOG_FORMAT:json}

# Business Logic Configuration
business:
  # Bubble Configuration
  bubble:
    default_expiry_duration: ${BUBBLE_EXPIRY_DURATION:2160h}  # 90 days (90 * 24 hours)
    max_members: ${BUBBLE_MAX_MEMBERS:5}
    min_members_for_active: ${BUBBLE_MIN_MEMBERS:2}
    request_expiry_duration: ${BUBBLE_REQUEST_EXPIRY:168h}  # 7 days (7 * 24 hours)
    auto_archive_delay: ${BUBBLE_AUTO_ARCHIVE_DELAY:48h}
  
  # Relationship Configuration (managed in user microservice)
  relationships:
    contact_request_expiry_duration: ${CONTACT_REQUEST_EXPIRY:7d}
    max_pending_contact_requests: ${CONTACT_MAX_PENDING:50}
    auto_cleanup_delay: ${CONTACT_AUTO_CLEANUP:30d}
    # Friend requests are auto-generated from bubble expiration
    friend_request_expiry_duration: ${FRIENDSHIP_REQUEST_EXPIRY:7d}
    auto_generated_from_bubble: ${FRIENDSHIP_AUTO_GENERATED:true}
    max_friends_per_user: ${FRIENDSHIP_MAX_FRIENDS:1000}
  
  # User Configuration
  user:
    max_username_length: ${USER_MAX_USERNAME:30}
    min_username_length: ${USER_MIN_USERNAME:3}
    max_display_name_length: ${USER_MAX_DISPLAY_NAME:50}
    default_language: ${USER_DEFAULT_LANGUAGE:en}
    max_avatar_size_mb: ${USER_MAX_AVATAR_SIZE:5}
  
  # Message Configuration
  message:
    max_length: ${MESSAGE_MAX_LENGTH:1000}
    max_media_size_mb: ${MESSAGE_MAX_MEDIA_SIZE:10}
    retention_days: ${MESSAGE_RETENTION_DAYS:90}

  # Auth Configuration
  auth:
    default_role_name: ${AUTH_DEFAULT_ROLE_NAME:user}

# Resilience Configuration
resilience:
  # Circuit Breaker Configuration
  circuit_breaker:
    failure_threshold: ${CB_FAILURE_THRESHOLD:5}
    recovery_timeout: ${CB_RECOVERY_TIMEOUT:30s}
    half_open_max_requests: ${CB_HALF_OPEN_MAX:3}

  # Global Retry Configuration (defaults for all services)
  retry:
    max_attempts: ${RETRY_MAX_ATTEMPTS:3}
    initial_backoff: ${RETRY_INITIAL_BACKOFF:100ms}
    max_backoff: ${RETRY_MAX_BACKOFF:30s}
    backoff_multiplier: ${RETRY_BACKOFF_MULTIPLIER:2.0}
    jitter_factor: ${RETRY_JITTER_FACTOR:0.1}
    strategy: ${RETRY_STRATEGY:exponential_jitter}

    # Service-specific retry configurations
    services:
      # Database retry configuration - more aggressive for critical service
      database:
        max_attempts: ${DB_RETRY_MAX_ATTEMPTS:5}
        initial_delay: ${DB_RETRY_INITIAL_DELAY:50ms}
        max_delay: ${DB_RETRY_MAX_DELAY:5s}
        multiplier: ${DB_RETRY_MULTIPLIER:2.0}
        jitter_factor: ${DB_RETRY_JITTER:0.1}
        strategy: ${DB_RETRY_STRATEGY:exponential_jitter}
        enabled: ${DB_RETRY_ENABLED:true}
        retryable_errors:
          - "connection refused"
          - "connection reset"
          - "timeout"
          - "temporary"
          - "deadlock"
          - "lock timeout"
          - "database is locked"
          - "connection lost"

      # MinIO retry configuration
      minio:
        max_attempts: ${MINIO_RETRY_MAX_ATTEMPTS:3}
        initial_delay: ${MINIO_RETRY_INITIAL_DELAY:100ms}
        max_delay: ${MINIO_RETRY_MAX_DELAY:10s}
        multiplier: ${MINIO_RETRY_MULTIPLIER:2.0}
        jitter_factor: ${MINIO_RETRY_JITTER:0.2}
        strategy: ${MINIO_RETRY_STRATEGY:exponential_jitter}
        enabled: ${MINIO_RETRY_ENABLED:true}
        retryable_errors:
          - "connection refused"
          - "timeout"
          - "network"
          - "unavailable"
          - "circuit breaker"
          - "502"
          - "503"
          - "504"

      # LiveKit retry configuration - less aggressive for real-time service
      livekit:
        max_attempts: ${LIVEKIT_RETRY_MAX_ATTEMPTS:2}
        initial_delay: ${LIVEKIT_RETRY_INITIAL_DELAY:200ms}
        max_delay: ${LIVEKIT_RETRY_MAX_DELAY:5s}
        multiplier: ${LIVEKIT_RETRY_MULTIPLIER:2.0}
        jitter_factor: ${LIVEKIT_RETRY_JITTER:0.1}
        strategy: ${LIVEKIT_RETRY_STRATEGY:exponential_jitter}
        enabled: ${LIVEKIT_RETRY_ENABLED:true}
        retryable_errors:
          - "connection refused"
          - "timeout"
          - "network"
          - "unavailable"
          - "502"
          - "503"
          - "504"

      # NATS retry configuration
      nats:
        max_attempts: ${NATS_RETRY_MAX_ATTEMPTS:4}
        initial_delay: ${NATS_RETRY_INITIAL_DELAY:100ms}
        max_delay: ${NATS_RETRY_MAX_DELAY:3s}
        multiplier: ${NATS_RETRY_MULTIPLIER:1.5}
        jitter_factor: ${NATS_RETRY_JITTER:0.1}
        strategy: ${NATS_RETRY_STRATEGY:exponential_jitter}
        enabled: ${NATS_RETRY_ENABLED:true}
        retryable_errors:
          - "connection refused"
          - "timeout"
          - "nats"
          - "publish failed"
          - "no responders"
          - "connection lost"

      # MQTT retry configuration
      mqtt:
        max_attempts: ${MQTT_RETRY_MAX_ATTEMPTS:3}
        initial_delay: ${MQTT_RETRY_INITIAL_DELAY:200ms}
        max_delay: ${MQTT_RETRY_MAX_DELAY:5s}
        multiplier: ${MQTT_RETRY_MULTIPLIER:2.0}
        jitter_factor: ${MQTT_RETRY_JITTER:0.2}
        strategy: ${MQTT_RETRY_STRATEGY:exponential_jitter}
        enabled: ${MQTT_RETRY_ENABLED:true}
        retryable_errors:
          - "connection refused"
          - "timeout"
          - "mqtt"
          - "publish failed"
          - "connection lost"
          - "broker unavailable"

      # Ory retry configuration - less aggressive for auth service
      ory:
        max_attempts: ${ORY_RETRY_MAX_ATTEMPTS:2}
        initial_delay: ${ORY_RETRY_INITIAL_DELAY:300ms}
        max_delay: ${ORY_RETRY_MAX_DELAY:3s}
        multiplier: ${ORY_RETRY_MULTIPLIER:2.0}
        jitter_factor: ${ORY_RETRY_JITTER:0.1}
        strategy: ${ORY_RETRY_STRATEGY:exponential_jitter}
        enabled: ${ORY_RETRY_ENABLED:true}
        retryable_errors:
          - "connection refused"
          - "timeout"
          - "network"
          - "unavailable"
          - "502"
          - "503"
          - "504"

      # FCM retry configuration
      fcm:
        max_attempts: ${FCM_RETRY_MAX_ATTEMPTS:3}
        initial_delay: ${FCM_RETRY_INITIAL_DELAY:500ms}
        max_delay: ${FCM_RETRY_MAX_DELAY:10s}
        multiplier: ${FCM_RETRY_MULTIPLIER:2.0}
        jitter_factor: ${FCM_RETRY_JITTER:0.1}
        strategy: ${FCM_RETRY_STRATEGY:exponential_jitter}
        enabled: ${FCM_RETRY_ENABLED:true}
        retryable_errors:
          - "timeout"
          - "network"
          - "unavailable"
          - "502"
          - "503"
          - "504"
          - "rate limited"
          - "quota exceeded"
  
  # Rate Limiting Configuration
  rate_limit:
    default_requests_per_minute: ${RATE_LIMIT_DEFAULT:60}
    burst_size: ${RATE_LIMIT_BURST:10}
    window_size: ${RATE_LIMIT_WINDOW:1m}
    
    # Service-specific rate limits
    auth:
      requests_per_minute: ${RATE_LIMIT_AUTH:30}
    user:
      requests_per_minute: ${RATE_LIMIT_USER:100}
    bubble:
      requests_per_minute: ${RATE_LIMIT_BUBBLE:50}
    # Contact and friendship handled in user microservice
    relationships:
      requests_per_minute: ${RATE_LIMIT_RELATIONSHIPS:40}
    social_analytics:
      requests_per_minute: ${RATE_LIMIT_SOCIAL_ANALYTICS:20}

# Cache Configuration
cache:
  # TTL Configuration (in seconds)
  ttl:
    user_profile: ${CACHE_TTL_USER_PROFILE:300}
    bubble_info: ${CACHE_TTL_BUBBLE_INFO:180}
    contact_list: ${CACHE_TTL_CONTACT_LIST:600}
    friendship_list: ${CACHE_TTL_FRIENDSHIP_LIST:600}
    social_analytics: ${CACHE_TTL_SOCIAL_ANALYTICS:900}
  
  # Cache Size Limits
  max_size:
    user_profiles: ${CACHE_MAX_USER_PROFILES:10000}
    bubble_info: ${CACHE_MAX_BUBBLE_INFO:5000}
    contact_lists: ${CACHE_MAX_CONTACT_LISTS:5000}
    social_analytics: ${CACHE_MAX_SOCIAL_ANALYTICS:1000}

# Monitoring Configuration
monitoring:
  # Metrics Configuration
  metrics:
    enabled: ${METRICS_ENABLED:true}
    port: ${METRICS_PORT:9090}
    path: ${METRICS_PATH:/metrics}
  
  # Health Check Configuration
  health:
    enabled: ${HEALTH_CHECK_ENABLED:true}
    port: ${HEALTH_CHECK_PORT:8081}
    path: ${HEALTH_CHECK_PATH:/health}
    readiness_path: ${READINESS_PATH:/ready}
  
  # Tracing Configuration
  tracing:
    enabled: ${TRACING_ENABLED:false}
    jaeger_endpoint: ${JAEGER_ENDPOINT:http://localhost:14268/api/traces}

# Security Configuration
security:
  # JWT Configuration
  jwt:
    secret: ${JWT_SECRET:your-secret-key}
    expiry_hours: ${JWT_EXPIRY_HOURS:24}
    refresh_expiry_hours: ${JWT_REFRESH_EXPIRY_HOURS:168}

  # CORS Configuration
  cors:
    allowed_origins: ${CORS_ALLOWED_ORIGINS:*}
    allowed_methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
    allowed_headers: ${CORS_ALLOWED_HEADERS:*}
    allow_credentials: ${CORS_ALLOW_CREDENTIALS:true}

  # Have I Been Pwned (HIBP) Configuration
  hibp:
    base_url: ${HIBP_BASE_URL:https://api.pwnedpasswords.com}
    api_key: ${HIBP_API_KEY:} # Optional: Premium API key for higher rate limits
    user_agent: ${HIBP_USER_AGENT:HopenBackend/1.0 (Security Check)}
    timeout_seconds: ${HIBP_TIMEOUT:15}
    max_response_size_mb: ${HIBP_MAX_RESPONSE_SIZE:10}
    enable_caching: ${HIBP_ENABLE_CACHING:true}
    cache_ttl_minutes: ${HIBP_CACHE_TTL:60}

  # Rate Limiting for Security
  security_rate_limit:
    login_attempts_per_hour: ${SECURITY_LOGIN_ATTEMPTS:5}
    password_reset_attempts_per_hour: ${SECURITY_PASSWORD_RESET_ATTEMPTS:3}
    hibp_requests_per_minute: ${SECURITY_HIBP_REQUESTS:30}
  
  # Authentication Configuration
  auth:
    # Public methods that don't require authentication
    public_methods:
      - "/auth.v1.AuthService/RegisterUser"
      - "/auth.v1.AuthService/LoginUser"
      - "/grpc.health.v1.Health/Check"
      - "/grpc.health.v1.Health/Watch"
      - "/grpc.reflection.v1.ServerReflection/ServerReflectionInfo"
      # User service methods for service-to-service calls
      - "/hopen.user.v1.UserService/CreateUser"
      - "/hopen.user.v1.UserService/GetUserByUsername"
      - "/hopen.user.v1.UserService/GetUserByEmail"
    
    # Health check methods (subset of public methods)
    health_checks:
      - "/grpc.health.v1.Health/Check"
      - "/grpc.health.v1.Health/Watch"

# Service Discovery Configuration
discovery:
  # NATS Configuration
  nats:
    url: ${NATS_URL:nats://localhost:4222}
    cluster_id: ${NATS_CLUSTER_ID:test-cluster}
    client_id: ${NATS_CLIENT_ID:hopen-backend}
    connect_timeout: ${NATS_CONNECT_TIMEOUT:10s}
    reconnect_timeout: ${NATS_RECONNECT_TIMEOUT:5s}
    max_reconnect_attempts: ${NATS_MAX_RECONNECT:10}
  
  # Service Registry Configuration
  registry:
    heartbeat_interval: ${REGISTRY_HEARTBEAT:30s}
    service_ttl: ${REGISTRY_SERVICE_TTL:90s}
    cleanup_interval: ${REGISTRY_CLEANUP:60s}

# Environment-specific overrides
environments:
  development:
    logging:
      level: debug
    server:
      mode: debug
    business:
      bubble:
        default_expiry_duration: 1h
        max_members: 5
  
  staging:
    logging:
      level: info
    server:
      mode: release
    resilience:
      circuit_breaker:
        failure_threshold: 3
  
  production:
    logging:
      level: warn
    server:
      mode: release
    security:
      jwt:
        secret: ${PROD_JWT_SECRET}
    monitoring:
      tracing:
        enabled: true
--- CONTENT END ---

==========================================

=== FILE: emqx.conf ===
File Type: conf
Full Path: /Users/<USER>/Hopen/hopenbackend/config/emqx.conf
Last Modified: Sep 14 17:24:37 2025
File Size: 338 bytes

--- CONTENT START ---
# EMQX v5.8 Static Configuration for Hopen Backend
# This file contains static settings that cannot be overridden at runtime

# Node configuration (static)
node {
  name = "emqx@emqx"
  cookie = "hopen-emqx-secure-cookie-2024"
}

# Basic logging
log {
  console_handler {
    enable = true
    level = "info"
    formatter = "text"
  }
}

--- CONTENT END ---

==========================================

=== FILE: firebase-service-account.json ===
File Type: json
Full Path: /Users/<USER>/Hopen/hopenbackend/config/firebase-service-account.json
Last Modified: Sep  1 21:05:56 2025
File Size: 616 bytes

--- CONTENT START ---
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

--- CONTENT END ---

==========================================

=== FILE: keto/keto.yml ===
File Type: yml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/keto/keto.yml
Last Modified: Sep  1 21:05:56 2025
File Size: 399 bytes

--- CONTENT START ---
# Ory Keto Configuration for Hopen Backend v0.11.0
# This configuration sets up Keto for centralized authorization

# Database configuration
dsn: *****************************************/hopen_keto?sslmode=disable

# Logging configuration
log:
  level: info
  format: text

# Serve configuration
serve:
  read:
    port: 4466
  write:
    port: 4467
  opl:
    port: 4469
  metrics:
    port: 4468

--- CONTENT END ---

==========================================

=== FILE: keto/policies/bubble-policies.yml ===
File Type: yml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/keto/policies/bubble-policies.yml
Last Modified: Sep  1 21:05:56 2025
File Size: 3083 bytes

--- CONTENT START ---
# Ory Keto Authorization Policies for Hopen Backend
# This file defines the authorization model for bubbles and related resources

# Bubble Creator Policy
# Users can only perform creator actions on bubbles they created
- namespace: bubbles
  relation: creator
  subject_set:
    namespace: users
    relation: member

# Bubble Admin Policy  
# Users can perform admin actions if they are creators or explicitly granted admin role
- namespace: bubbles
  relation: admin
  subject_set:
    namespace: users
    relation: member

# Bubble Member Policy
# Users can access bubble content if they are active members
- namespace: bubbles
  relation: member
  subject_set:
    namespace: users
    relation: member

# Bubble Invite Policy
# Users can invite others if they are admins or creators
- namespace: bubbles
  relation: invite
  subject_set:
    namespace: bubbles
    relation: admin

# Bubble Kick Policy
# Users can kick members if they are admins or creators
- namespace: bubbles
  relation: kick
  subject_set:
    namespace: bubbles
    relation: admin

# Bubble Delete Policy
# Only creators can delete bubbles
- namespace: bubbles
  relation: delete
  subject_set:
    namespace: bubbles
    relation: creator

# Bubble View Policy
# Users can view bubble information if they are members
- namespace: bubbles
  relation: view
  subject_set:
    namespace: bubbles
    relation: member

# Bubble Edit Policy
# Users can edit bubble settings if they are admins or creators
- namespace: bubbles
  relation: edit
  subject_set:
    namespace: bubbles
    relation: admin

# Message Send Policy
# Users can send messages if they are bubble members
- namespace: messages
  relation: send
  subject_set:
    namespace: bubbles
    relation: member

# Message View Policy
# Users can view messages if they are bubble members
- namespace: messages
  relation: view
  subject_set:
    namespace: bubbles
    relation: member

# Message Edit Policy
# Users can edit messages they sent
- namespace: messages
  relation: edit
  subject_set:
    namespace: users
    relation: member

# Message Delete Policy
# Users can delete messages they sent
- namespace: messages
  relation: delete
  subject_set:
    namespace: users
    relation: member

# Media Upload Policy
# Users can upload media if they are bubble members
- namespace: media
  relation: upload
  subject_set:
    namespace: bubbles
    relation: member

# Media View Policy
# Users can view media if they are bubble members
- namespace: media
  relation: view
  subject_set:
    namespace: bubbles
    relation: member

# Media Delete Policy
# Users can delete media they uploaded
- namespace: media
  relation: delete
  subject_set:
    namespace: users
    relation: member

# Call Join Policy
# Users can join calls if they are bubble members
- namespace: calls
  relation: join
  subject_set:
    namespace: bubbles
    relation: member

# Call Manage Policy
# Users can manage calls if they are bubble admins or creators
- namespace: calls
  relation: manage
  subject_set:
    namespace: bubbles
    relation: admin

--- CONTENT END ---

==========================================

=== FILE: keto/policies/mqtt-policies.yml ===
File Type: yml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/keto/policies/mqtt-policies.yml
Last Modified: Sep  1 21:05:56 2025
File Size: 3638 bytes

--- CONTENT START ---
# Ory Keto Authorization Policies for MQTT Topics
# This file defines the authorization model for MQTT topic access and operations

# Personal Chat Topic Publish Policy
# Users can publish to their own personal chat topic
- namespace: mqtt_topics
  relation: publish_personal_chat
  subject_set:
    namespace: users
    relation: member

# Personal Chat Topic Subscribe Policy
# Users can subscribe to their own personal chat topic
- namespace: mqtt_topics
  relation: subscribe_personal_chat
  subject_set:
    namespace: users
    relation: member

# Personal Requests Topic Subscribe Policy
# Users can subscribe to their own requests/notifications topic
- namespace: mqtt_topics
  relation: subscribe_personal_requests
  subject_set:
    namespace: users
    relation: member

# Personal Notifications Topic Subscribe Policy
# Users can subscribe to their own notifications topic
- namespace: mqtt_topics
  relation: subscribe_personal_notifications
  subject_set:
    namespace: users
    relation: member

# Bubble Chat Topic Publish Policy
# Users can publish to bubble chat topics if they are members
- namespace: mqtt_topics
  relation: publish_bubble_chat
  subject_set:
    namespace: bubbles
    relation: member

# Bubble Chat Topic Subscribe Policy
# Users can subscribe to bubble chat topics if they are members
- namespace: mqtt_topics
  relation: subscribe_bubble_chat
  subject_set:
    namespace: bubbles
    relation: member

# Bubble Notifications Topic Subscribe Policy
# Users can subscribe to bubble notification topics if they are members
- namespace: mqtt_topics
  relation: subscribe_bubble_notifications
  subject_set:
    namespace: bubbles
    relation: member

# Bubble Messages Topic Subscribe Policy
# Users can subscribe to bubble message topics if they are members
- namespace: mqtt_topics
  relation: subscribe_bubble_messages
  subject_set:
    namespace: bubbles
    relation: member

# Realtime Messages Topic Subscribe Policy
# All authenticated users can subscribe to general realtime messages
- namespace: mqtt_topics
  relation: subscribe_realtime_messages
  subject_set:
    namespace: users
    relation: member

# Call Signaling Topic Publish Policy
# Users can publish to call signaling topics if they are call participants
- namespace: mqtt_topics
  relation: publish_call_signaling
  subject_set:
    namespace: calls
    relation: participant

# Call Signaling Topic Subscribe Policy
# Users can subscribe to call signaling topics if they are call participants
- namespace: mqtt_topics
  relation: subscribe_call_signaling
  subject_set:
    namespace: calls
    relation: participant

# Presence Topic Publish Policy
# Users can publish their presence status
- namespace: mqtt_topics
  relation: publish_presence
  subject_set:
    namespace: users
    relation: member

# Presence Topic Subscribe Policy
# Users can subscribe to presence updates of their contacts and bubble members
- namespace: mqtt_topics
  relation: subscribe_presence
  subject_set:
    namespace: users
    relation: member

# System Broadcast Topic Subscribe Policy
# All authenticated users can subscribe to system broadcasts
- namespace: mqtt_topics
  relation: subscribe_system_broadcast
  subject_set:
    namespace: users
    relation: member

# Admin Topic Publish Policy
# Only admins can publish to admin topics
- namespace: mqtt_topics
  relation: publish_admin
  subject_set:
    namespace: roles
    relation: admin

# Admin Topic Subscribe Policy
# Only admins can subscribe to admin topics
- namespace: mqtt_topics
  relation: subscribe_admin
  subject_set:
    namespace: roles
    relation: admin

--- CONTENT END ---

==========================================

=== FILE: keto/policies/user-policies.yml ===
File Type: yml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/keto/policies/user-policies.yml
Last Modified: Sep  1 21:05:56 2025
File Size: 2854 bytes

--- CONTENT START ---
# Ory Keto Authorization Policies for User Management
# This file defines the authorization model for user-related operations

# User Profile View Policy
# Users can view their own profile
- namespace: users
  relation: view_own
  subject_set:
    namespace: users
    relation: member

# User Profile Edit Policy
# Users can edit their own profile
- namespace: users
  relation: edit_own
  subject_set:
    namespace: users
    relation: member

# User Profile Delete Policy
# Users can delete their own profile
- namespace: users
  relation: delete_own
  subject_set:
    namespace: users
    relation: member

# User Privacy Settings Policy
# Users can manage their own privacy settings
- namespace: users
  relation: manage_privacy
  subject_set:
    namespace: users
    relation: member

# User Contact Request Policy
# Users can send contact requests to other users
- namespace: users
  relation: send_contact_request
  subject_set:
    namespace: users
    relation: member

# User Contact Response Policy
# Users can respond to contact requests sent to them
- namespace: users
  relation: respond_contact_request
  subject_set:
    namespace: users
    relation: member

# User Friendship Policy
# Users can manage friendships with other users
- namespace: users
  relation: manage_friendship
  subject_set:
    namespace: users
    relation: member

# User Block Policy
# Users can block other users
- namespace: users
  relation: block_user
  subject_set:
    namespace: users
    relation: member

# User Unblock Policy
# Users can unblock users they previously blocked
- namespace: users
  relation: unblock_user
  subject_set:
    namespace: users
    relation: member

# User Search Policy
# Users can search for other users
- namespace: users
  relation: search
  subject_set:
    namespace: users
    relation: member

# User Analytics Policy
# Users can view their own analytics
- namespace: users
  relation: view_analytics
  subject_set:
    namespace: users
    relation: member

# User Notification Policy
# Users can manage their own notifications
- namespace: users
  relation: manage_notifications
  subject_set:
    namespace: users
    relation: member

# User Session Policy
# Users can manage their own sessions
- namespace: users
  relation: manage_sessions
  subject_set:
    namespace: users
    relation: member

# User Credentials Policy
# Users can update their own credentials
- namespace: users
  relation: update_credentials
  subject_set:
    namespace: users
    relation: member

# User Recovery Policy
# Users can initiate account recovery
- namespace: users
  relation: initiate_recovery
  subject_set:
    namespace: users
    relation: member

# User Verification Policy
# Users can verify their identity
- namespace: users
  relation: verify_identity
  subject_set:
    namespace: users
    relation: member

--- CONTENT END ---

==========================================

=== FILE: kong/kong.yaml ===
File Type: yaml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/kong/kong.yaml
Last Modified: Sep 14 17:11:52 2025
File Size: 9204 bytes

--- CONTENT START ---
_format_version: "3.0"
_transform: true

# Kong API Gateway Configuration for Hopen Backend
# Architecture: Client → Kong (Ingress) → gRPC Microservices
# Full gRPC routing with Kong Gateway for maximum performance

# Services and Routes Configuration
services:
  # Auth Service - gRPC
  - name: auth-grpc-service
    protocol: grpc
    host: auth
    port: 4000

  # User Service - gRPC
  - name: user-grpc-service
    protocol: grpc
    host: user
    port: 4001

  # Bubble Service - gRPC
  - name: bubble-grpc-service
    protocol: grpc
    host: bubble
    port: 4002

  # Email Service - gRPC
  - name: email-grpc-service
    protocol: grpc
    host: email
    port: 4010

  # Media Service - gRPC
  - name: media-grpc-service
    protocol: grpc
    host: media
    port: 4017

  # Search Service - gRPC
  - name: search-grpc-service
    protocol: grpc
    host: search
    port: 4016

  # Call Service - gRPC
  - name: call-grpc-service
    protocol: grpc
    host: call
    port: 4006

  # Realtime Service - gRPC
  - name: realtime-grpc-service
    protocol: grpc
    host: realtime
    port: 4007

  # Presence Service - gRPC
  - name: presence-grpc-service
    protocol: grpc
    host: presence
    port: 4013

  # Notification Service - gRPC
  - name: notification-grpc-service
    protocol: grpc
    host: notification
    port: 4012

  # Social Analytics Service - gRPC
  - name: social-analytics-grpc-service
    protocol: grpc
    host: social_analytics
    port: 4014

  # Sync Service - gRPC
  - name: sync-grpc-service
    protocol: grpc
    host: sync
    port: 4015

  # HTTP API Service - Proxy through Oathkeeper for full auth flow
  - name: http-api-service
    protocol: http
    host: oathkeeper
    port: 80

  # Identity Management Service - Direct to Kratos
  - name: identity-service
    protocol: http
    host: kratos
    port: 4433

  # OAuth2 Service - Direct to Hydra
  - name: oauth2-service
    protocol: http
    host: hydra
    port: 4444

  # Health Check Service - Simple static response
  - name: health-check-service
    protocol: http
    host: kong
    port: 8001

  # Default Service - Simple static response
  - name: default-service
    protocol: http
    host: kong
    port: 8001

# Routes Configuration
routes:
  # gRPC Routes for all microservices - using prefix matching for proper gRPC routing
  - name: auth-grpc-routes
    service: auth-grpc-service
    paths: ["/hopen.auth.v1.AuthService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: user-grpc-routes
    service: user-grpc-service
    paths: ["/hopen.user.v1.UserService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: bubble-grpc-routes
    service: bubble-grpc-service
    paths: ["/hopen.bubble.v1.BubbleService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: email-grpc-routes
    service: email-grpc-service
    paths: ["/hopen.email.v1.EmailService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: media-grpc-routes
    service: media-grpc-service
    paths: ["/hopen.media.v1.MediaService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: search-grpc-routes
    service: search-grpc-service
    paths: ["/hopen.search.v1.SearchService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: call-grpc-routes
    service: call-grpc-service
    paths: ["/hopen.call.v1.CallService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: realtime-grpc-routes
    service: realtime-grpc-service
    paths: ["/hopen.realtime.v1.RealtimeService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: presence-grpc-routes
    service: presence-grpc-service
    paths: ["/hopen.presence.v1.PresenceService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: notification-grpc-routes
    service: notification-grpc-service
    paths: ["/hopen.notification.v1.NotificationService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: social-analytics-grpc-routes
    service: social-analytics-grpc-service
    paths: ["/hopen.social_analytics.v1.SocialAnalyticsService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  - name: sync-grpc-routes
    service: sync-grpc-service
    paths: ["/hopen.sync.v1.SyncService"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  # gRPC Reflection Routes - route to auth service for reflection
  - name: grpc-reflection-routes
    service: auth-grpc-service
    paths: ["/grpc.reflection.v1.ServerReflection", "/grpc.reflection.v1alpha.ServerReflection"]
    protocols: ["grpc", "grpcs"]
    strip_path: false

  # gRPC-Web Routes for Flutter Web (browser compatibility)
  - name: auth-grpc-web-routes
    service: auth-grpc-service
    paths: ["/web/hopen.auth.v1.AuthService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: user-grpc-web-routes
    service: user-grpc-service
    paths: ["/web/hopen.user.v1.UserService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: bubble-grpc-web-routes
    service: bubble-grpc-service
    paths: ["/web/hopen.bubble.v1.BubbleService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: email-grpc-web-routes
    service: email-grpc-service
    paths: ["/web/hopen.email.v1.EmailService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: media-grpc-web-routes
    service: media-grpc-service
    paths: ["/web/hopen.media.v1.MediaService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: search-grpc-web-routes
    service: search-grpc-service
    paths: ["/web/hopen.search.v1.SearchService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: call-grpc-web-routes
    service: call-grpc-service
    paths: ["/web/hopen.call.v1.CallService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: realtime-grpc-web-routes
    service: realtime-grpc-service
    paths: ["/web/hopen.realtime.v1.RealtimeService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: presence-grpc-web-routes
    service: presence-grpc-service
    paths: ["/web/hopen.presence.v1.PresenceService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: notification-grpc-web-routes
    service: notification-grpc-service
    paths: ["/web/hopen.notification.v1.NotificationService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: social-analytics-grpc-web-routes
    service: social-analytics-grpc-service
    paths: ["/web/hopen.social_analytics.v1.SocialAnalyticsService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  - name: sync-grpc-web-routes
    service: sync-grpc-service
    paths: ["/web/hopen.sync.v1.SyncService"]
    protocols: ["http", "https"]
    strip_path: true
    plugins:
      - name: grpc-web
        config:
          pass_stripped_path: true

  # HTTP Routes
  - name: http-api-routes
    service: http-api-service
    paths: ["/api"]
    protocols: ["http", "https"]
    strip_path: false

  - name: identity-routes
    service: identity-service
    paths: ["/self-service"]
    protocols: ["http", "https"]
    strip_path: false

  - name: oauth2-routes
    service: oauth2-service
    paths: ["/oauth2", "/.well-known", "/userinfo"]
    protocols: ["http", "https"]
    strip_path: false

  - name: health-check-routes
    service: health-check-service
    paths: ["/health", "/ready", "/metrics"]
    protocols: ["http", "https"]
    strip_path: false

  - name: default-route
    service: default-service
    paths: ["/"]
    protocols: ["http", "https"]
    strip_path: false

# Global Plugins for all services
plugins:
  - name: cors
    config:
      origins: ["*"]
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
      headers: ["Content-Type", "Authorization", "X-Requested-With", "X-Grpc-Web", "grpc-timeout", "grpc-status", "grpc-message"]
      exposed_headers: ["X-Total-Count", "Grpc-Status", "Grpc-Message", "grpc-status", "grpc-message"]
      credentials: true
      max_age: 3600
  - name: correlation-id
    config:
      header_name: "X-Correlation-ID"
      generator: "uuid"
      echo_downstream: true
  - name: rate-limiting
    config:
      minute: 1000
      hour: 10000

--- CONTENT END ---

==========================================

=== FILE: kratos/identity.default.schema.json ===
File Type: json
Full Path: /Users/<USER>/Hopen/hopenbackend/config/kratos/identity.default.schema.json
Last Modified: Sep 14 10:09:44 2025
File Size: 2205 bytes

--- CONTENT START ---
{
  "$id": "https://schemas.hopen.app/identity.enhanced.schema.json",
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Hopen Enhanced Identity Schema",
  "type": "object",
  "properties": {
    "traits": {
      "type": "object",
      "properties": {
        "email": {
          "type": "string",
          "format": "email",
          "title": "Email",
          "description": "User's email address",
          "ory.sh/kratos": {
            "credentials": {
              "password": {
                "identifier": true
              }
            },
            "verification": {
              "via": "email"
            }
          }
        },
        "username": {
          "type": "string",
          "minLength": 3,
          "maxLength": 64,
          "pattern": "^[a-zA-Z0-9_]+$",
          "title": "Username",
          "description": "Unique username for the user",
          "ory.sh/kratos": {
            "credentials": {
              "password": {
                "identifier": true
              }
            }
          }
        },
        "first_name": {
          "type": "string",
          "minLength": 1,
          "maxLength": 100,
          "title": "First Name",
          "description": "User's first name"
        },
        "last_name": {
          "type": "string",
          "minLength": 1,
          "maxLength": 100,
          "title": "Last Name",
          "description": "User's last name"
        },
        "profile_picture": {
          "type": "string",
          "format": "uri",
          "title": "Profile Picture",
          "description": "URL to user's profile picture"
        },
        "birthday": {
          "type": "string",
          "format": "date",
          "title": "Birthday",
          "description": "User's birthday in YYYY-MM-DD format"
        },
        "oauth_provider": {
          "type": "string",
          "enum": ["google", "apple", "password"],
          "title": "OAuth Provider",
          "description": "The OAuth provider used for registration"
        }
      },
      "required": ["email"],
      "additionalProperties": false
    }
  },
  "required": ["traits"],
  "additionalProperties": false
}

--- CONTENT END ---

==========================================

=== FILE: kratos/identity.enhanced.schema.json ===
File Type: json
Full Path: /Users/<USER>/Hopen/hopenbackend/config/kratos/identity.enhanced.schema.json
Last Modified: Sep 14 10:24:31 2025
File Size: 2205 bytes

--- CONTENT START ---
{
  "$id": "https://schemas.hopen.app/identity.enhanced.schema.json",
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Hopen Enhanced Identity Schema",
  "type": "object",
  "properties": {
    "traits": {
      "type": "object",
      "properties": {
        "email": {
          "type": "string",
          "format": "email",
          "title": "Email",
          "description": "User's email address",
          "ory.sh/kratos": {
            "credentials": {
              "password": {
                "identifier": true
              }
            },
            "verification": {
              "via": "email"
            }
          }
        },
        "username": {
          "type": "string",
          "minLength": 3,
          "maxLength": 64,
          "pattern": "^[a-zA-Z0-9_]+$",
          "title": "Username",
          "description": "Unique username for the user",
          "ory.sh/kratos": {
            "credentials": {
              "password": {
                "identifier": true
              }
            }
          }
        },
        "first_name": {
          "type": "string",
          "minLength": 1,
          "maxLength": 100,
          "title": "First Name",
          "description": "User's first name"
        },
        "last_name": {
          "type": "string",
          "minLength": 1,
          "maxLength": 100,
          "title": "Last Name",
          "description": "User's last name"
        },
        "profile_picture": {
          "type": "string",
          "format": "uri",
          "title": "Profile Picture",
          "description": "URL to user's profile picture"
        },
        "birthday": {
          "type": "string",
          "format": "date",
          "title": "Birthday",
          "description": "User's birthday in YYYY-MM-DD format"
        },
        "oauth_provider": {
          "type": "string",
          "enum": ["google", "apple", "password"],
          "title": "OAuth Provider",
          "description": "The OAuth provider used for registration"
        }
      },
      "required": ["email"],
      "additionalProperties": false
    }
  },
  "required": ["traits"],
  "additionalProperties": false
}

--- CONTENT END ---

==========================================

=== FILE: kratos/kratos.yml ===
File Type: yml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/kratos/kratos.yml
Last Modified: Sep 14 10:10:56 2025
File Size: 2004 bytes

--- CONTENT START ---
version: v0.10.0
dsn: *****************************************/hopen_db?sslmode=disable
log:
  level: info
  format: text
serve:
  public:
    base_url: http://localhost:4433/
    cors:
      enabled: true
  admin:
    base_url: http://localhost:4434/
selfservice:
  default_browser_return_url: http://localhost:3000/
  methods:
    password:
      enabled: true
    oidc:
      enabled: true
      config:
        providers:
          - id: google
            provider: google
            client_id: ************-8555p6h5grihi9qmgrts661sro96kfd7.apps.googleusercontent.com
            client_secret: ${GOOGLE_OAUTH_CLIENT_SECRET}
            issuer_url: https://accounts.google.com
            mapper_url: file:///etc/config/oidc.google.jsonnet
            scope:
              - openid
              - profile
              - email
              - https://www.googleapis.com/auth/user.birthday.read
          - id: apple
            provider: apple
            client_id: ${APPLE_OAUTH_CLIENT_ID}
            apple_team_id: ${APPLE_OAUTH_TEAM_ID}
            apple_private_key_id: ${APPLE_OAUTH_KEY_ID}
            apple_private_key: ${APPLE_OAUTH_PRIVATE_KEY}
            issuer_url: https://appleid.apple.com
            mapper_url: file:///etc/config/oidc.apple.jsonnet
            scope:
              - email
              - name
  flows:
    error:
      ui_url: http://localhost:3000/error
    settings:
      ui_url: http://localhost:3000/settings
    verification:
      ui_url: http://localhost:3000/verify
      enabled: false
    login:
      ui_url: http://localhost:3000/login
    registration:
      ui_url: http://localhost:3000/register
    recovery:
      ui_url: http://localhost:3000/recovery
      enabled: false
identity:
  default_schema_id: enhanced
  schemas:
    - id: default
      url: file:///etc/config/identity.default.schema.json
    - id: enhanced
      url: file:///etc/config/identity.enhanced.schema.json

courier:
  smtp:
    connection_uri: smtp://localhost:1025

--- CONTENT END ---

==========================================

=== FILE: nginx-http2.conf ===
File Type: conf
Full Path: /Users/<USER>/Hopen/hopenbackend/config/nginx-http2.conf
Last Modified: Sep  1 21:05:56 2025
File Size: 572 bytes

--- CONTENT START ---
# Custom nginx configuration for Kong HTTP/2 support
# This file is included by Kong to enable proper HTTP/2 handling

# HTTP/2 specific settings for Kong
http2_max_concurrent_streams 128;
http2_recv_buffer_size 256k;

# gRPC specific settings for HTTP/2
grpc_read_timeout 1h;
grpc_send_timeout 1h;
grpc_socket_keepalive on;

# HTTP/2 connection settings
keepalive_timeout 65;

# gRPC HTTP/2 specific settings
grpc_set_header TE $http_te;
grpc_set_header X-Real-IP $remote_addr;
grpc_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
grpc_set_header Host $http_host; 
--- CONTENT END ---

==========================================

=== FILE: oathkeeper/access-rules.yml ===
File Type: yml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/oathkeeper/access-rules.yml
Last Modified: Sep 11 19:38:45 2025
File Size: 2604 bytes

--- CONTENT START ---
# Health check rules - no authentication required
- id: "health-check"
  upstream:
    url: "http://kong:8000"
  match:
    url: "^/health"
    methods: ["GET"]
  authenticators:
    - handler: "anonymous"
  authorizer:
    handler: "allow"
  mutators:
    - handler: "header"

# Health ready check rule - for Docker health checks
- id: "health-ready-check"
  upstream:
    url: "http://kong:8000"
  match:
    url: "^/health/ready"
    methods: ["GET"]
  authenticators:
    - handler: "anonymous"
  authorizer:
    handler: "allow"
  mutators:
    - handler: "header"

# Health alive check rule - for Docker health checks
- id: "health-alive-check"
  upstream:
    url: "http://kong:8000"
  match:
    url: "^/health/alive"
    methods: ["GET"]
  authenticators:
    - handler: "anonymous"
  authorizer:
    handler: "allow"
  mutators:
    - handler: "header"

# Rule for HTTP API endpoints (through Oathkeeper)
- id: "http-api-access"
  description: "Handle HTTP API requests with authentication"
  upstream:
    url: "http://kong:8000"
  match:
    url: "^/api"
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
  authenticators:
    - handler: "cookie_session"
  authorizer:
    handler: "allow"
  mutators:
    - handler: "header"

# Rule for Kratos identity endpoints
- id: "identity-endpoints"
  description: "Handle Kratos identity management endpoints"
  upstream:
    url: "http://kratos:4433"
  match:
    url: "^/self-service"
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
  authenticators:
    - handler: "anonymous"
  authorizer:
    handler: "allow"
  mutators:
    - handler: "header"

# Rule for Hydra OAuth2 endpoints
- id: "oauth2-endpoints"
  description: "Handle Hydra OAuth2 endpoints"
  upstream:
    url: "http://hydra:4444"
  match:
    url: "^/oauth2"
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
  authenticators:
    - handler: "anonymous"
  authorizer:
    handler: "allow"
  mutators:
    - handler: "header"

# Rule for OAuth2 well-known endpoints
- id: "oauth2-well-known"
  description: "Handle OAuth2 well-known endpoints"
  upstream:
    url: "http://hydra:4444"
  match:
    url: "^/\\.well-known"
    methods: ["GET"]
  authenticators:
    - handler: "anonymous"
  authorizer:
    handler: "allow"
  mutators:
    - handler: "header"

# Rule for userinfo endpoint
- id: "userinfo-endpoint"
  description: "Handle userinfo endpoint"
  upstream:
    url: "http://hydra:4444"
  match:
    url: "^/userinfo"
    methods: ["GET"]
  authenticators:
    - handler: "anonymous"
  authorizer:
    handler: "allow"
  mutators:
    - handler: "header"

--- CONTENT END ---

==========================================

=== FILE: oathkeeper/jwks.json ===
File Type: json
Full Path: /Users/<USER>/Hopen/hopenbackend/config/oathkeeper/jwks.json
Last Modified: Sep 11 19:39:33 2025
File Size: 1812 bytes

--- CONTENT START ---
{
  "keys": [
    {
      "use": "sig",
      "kty": "RSA",
      "kid": "4bf94d1c-caae-4989-9d8b-89792c937ef1",
      "alg": "RS256",
      "n": "s1v9VOZljJJQTDd2lJqGD1K7qBXUySECbiYzCaorp8V1K-seG0odiQrFGBfwfBvYUFmRROrRVQA8uSoNZdWIN9SFzTnGt7Xzs1sew8WmnW97MZwf3o9xeLWDVD4ysGtGf5H2w8BM9NdYGbHECLsBzMlB9tzUMoRSvdWT3n1TvFZI1UokcU3f8pDc2tUG8F2wDEi-KAg06edgDRo99AREQp7y_lQs2-gVqQxF9PpICcNaM-KVzcMOfEmE3mGlJdjqCz2Ue6ibGPBGAUzKNsZiPhSZjSnu-JvMOlilKxz2Bl8KiDQkZ9WJI10BU_JGmfZaGFAhuILQjZPlhQKS3vgyNw",
      "e": "AQAB",
      "d": "i_xJ9o9zxokTnsjTBhyscR0zVXbkgq3-xZWO1AY62FCmqMZy0v2_m4wKVEmq6lKZO-0MEmvrQZpTynjlyAsmq9x1Kb3kONIMCHofwtBqwAmYbxnZTc1K1NlmTMPWsBaqCTsVC6b06UuBeVxjEiVSvFB_6XplK6BXK86cAFR0nFK1O5p-18bggHlm90SrD80sbvpJVs8O-SWhT5-tp1MqI7BZtvRYq8yUua9b9FzAYnl5lexoIYzzt8CC2TPxHU6Uu87sMyTppyv2D0sMSf4kHYCmYX-LDr89im2OdPJyHVtsu5IbKJFZHepRizZDS8eWySrkkrAAarqT3lANtaubQQ",
      "p": "16XLyyi-h8ObQptLyUIGRkUeUPHQL94v57CsZECiknEnw5T_h256BGITj5gpjT107bouDjkDMcFf7AkEg2xUriUrDVN_Y8spxDKb_GMucAqRBrafOvbKTBIyfR2GvrinlqN0zsGtvUTpRRndFUY7VyDbu5h1RqTcIiqL_qTjL5E",
      "q": "1OvebiwnUk0KC5h0a0DQNcGlkLYC-p_ja7jxyvinucWPvf6BirsMq3TbHYEyhX1o9Zeia_G8H0qm1rw-zO-FUyjUTBGfmP-w63lr_ShVimvx-2Owpb3hairBkFUXk7mN4bSvr4YAY7Nu0rswmM7TiF-ElgYqK9BCS587Eh8HcUc",
      "dp": "tdMrg_60t5AsZvq96dHrrHA24ZUiLlovFjGXCWieKs-R8Yy975ks9GkliRuJXXeGEbO7hW54zQj5MfFghtdehv77z96pOKFic_UgJKwTyZB_O6PbmkHfzEhDnc65gDH9zDvCD1qqfT2PWh42lOuEuTARVZl0uL3khkJNi8XHcKE",
      "dq": "zAJ8C0APJRJfz6Bx29osJXW396IN1KmiNDLuqjX6L0uWYMscwBpdF2NOF0UVbUYubTKvE5J5V9olZZbU7XOKYZW7ZG92ZhiIIfcje1tMKucm4mIwfX4kH597-5kskAS4Z0zDEQDqGp1wUEW_WIrDr-UlmsGpYnWXL_IPnVtRvD0",
      "qi": "boJbl3VTa0WB_2ycbMClTdvI7lHzASztgMGYHQleCOAUcEuNG-_ewzeodC4JbYme7tug1Rr3mpYXTL9KDPmMjg8PlluyciEulD9_hZQYXlBHB61WC79aNlN-c04Jo_uoKKONKz49mQMzUfXrW4lfxmntzfB7DTa0rE7TwnrBQuY"
    }
  ]
}

--- CONTENT END ---

==========================================

=== FILE: oathkeeper/oathkeeper.yml ===
File Type: yml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/oathkeeper/oathkeeper.yml
Last Modified: Sep 12 19:59:34 2025
File Size: 2876 bytes

--- CONTENT START ---
# Ory Oathkeeper Configuration for Hopen Backend
# Version: v0.40.0+

version: v0.40.0

# Defines the ports Oathkeeper listens on.
serve:
  # The public port that receives incoming traffic from clients.
  proxy:
    port: 80
    cors:
      enabled: true
      allowed_origins:
        - http://localhost:3000 # Your frontend development URL
        - https://hopenapp.com   # Your production frontend URL
      allowed_methods:
        - GET
        - POST
        - PUT
        - DELETE
        - PATCH
      allowed_headers:
        - Authorization
        - Content-Type
      allow_credentials: true

  # The internal port for Oathkeeper's API (e.g., for health checks).
  api:
    port: 4456

# How Oathkeeper authenticates incoming requests.
authenticators:
  cookie_session:
    enabled: true
    config:
      # The URL of your Ory Kratos instance to validate the session.
      check_session_url: http://kratos:4433/sessions/whoami
      # Only act on the session cookie set by Kratos.
      only:
        - ory_kratos_session
  
  anonymous:
    enabled: true
    config:
      subject: "anonymous"

# How Oathkeeper authorizes requests after authentication.
authorizers:
  # This authorizer simply allows any authenticated request to pass.
  # Fine-grained authorization will be handled by Ory Keto inside each microservice.
  allow:
    enabled: true

# How Oathkeeper mutates requests before forwarding them.
mutators:
  id_token:
    enabled: true
    config:
      # The issuer URL for the JWTs Oathkeeper will create.
      issuer_url: https://api.hopen.com
      
      # IMPORTANT: Path to the JWKS file containing the private key for signing.
      # This file must be mounted into the Oathkeeper container.
      jwks_url: file:///etc/secrets/jwks.json
      
      # Defines the claims to be included in the internal JWT.
      # This extracts the user ID and traits from the Kratos session.
      claims: |
        {
          "iss": "https://api.hopen.com",
          "aud": ["hopen-internal"],
          "user_id": "{{ .Subject }}",
          "email": "{{ .Extra.identity.traits.email }}",
          "jti": "{{ .ID }}",
          "session": {
            "id": "{{ .ID }}"
          }
        }
  
  header:
    enabled: true
    config:
      headers:
        X-User-ID: "{{ .Subject }}"
        X-Authenticated: "true"

# Defines how incoming requests are routed to your microservices.
access_rules:
  repositories:
    # Use a file-based repository for access rules.
    - file:///etc/oathkeeper/access-rules.yml

# How Oathkeeper handles errors (e.g., authentication failure).
errors:
  fallback:
    - json
  
  handlers:
    json:
      enabled: true
      config:
        verbose: true # Set to false in production for security

# Logging configuration.
log:
  level: info # Use debug in development, warn or error in production
  format: text

--- CONTENT END ---

==========================================

=== FILE: prometheus/prometheus.yml ===
File Type: yml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/prometheus/prometheus.yml
Last Modified: Sep  1 21:05:56 2025
File Size: 158 bytes

--- CONTENT START ---
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090'] 
--- CONTENT END ---

==========================================

=== FILE: scylla/scylla-rackdc.properties ===
File Type: properties
Full Path: /Users/<USER>/Hopen/hopenbackend/config/scylla/scylla-rackdc.properties
Last Modified: Sep  1 21:05:56 2025
File Size: 216 bytes

--- CONTENT START ---
# ScyllaDB Rack and Datacenter Configuration
# This file is required for GossipingPropertyFileSnitch

# Datacenter name
dc=hopen_dc

# Rack name
rack=hopen_rack

# Prefer local datacenter for reads
prefer_local=true 
--- CONTENT END ---

==========================================

=== FILE: tls.yaml ===
File Type: yaml
Full Path: /Users/<USER>/Hopen/hopenbackend/config/tls.yaml
Last Modified: Sep  1 21:05:56 2025
File Size: 775 bytes

--- CONTENT START ---
# TLS Configuration for Development
tls:
  enabled: true
  cert_file: "/app/certs/server.crt"
  key_file: "/app/certs/server.key"
  domain: "hopen.local"
  
  # HTTP/3 (QUIC) Configuration
  http3:
    enabled: true
    port: 8443
    
  # HTTP/2 Configuration  
  http2:
    enabled: true
    port: 8443
    
  # Security settings
  min_version: "1.2"
  max_version: "1.3"
  
  # Cipher suites optimized for HTTP/3
  cipher_suites:
    - "TLS_AES_128_GCM_SHA256"
    - "TLS_AES_256_GCM_SHA384"
    - "TLS_CHACHA20_POLY1305_SHA256"
    
  # QUIC specific settings
  quic:
    max_idle_timeout: "30s"
    max_receive_stream_flow_control_window: 6291456
    max_receive_connection_flow_control_window: 15728640
    allow_connection_migration: true
    keep_alive_period: "10s"

--- CONTENT END ---

==========================================


# SUMMARY
# =======
Total files processed: 19
File types found:
     3  conf
     4  json
     1  properties
     3  yaml
     8  yml
