#!/bin/bash

# Master script to run all codebase review prompt generation scripts
# Location: /Users/<USER>/Hopen/docs/prompts/google_ai_studio_workflow/

set -e  # Exit on any error

cd "$(dirname "$0")"

echo "🚀 Starting all prompt generation scripts..."
echo "📁 Working directory: $(pwd)"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to run a script and handle errors
run_script() {
    local script_path="$1"
    local script_name=$(basename "$script_path")

    echo -e "${YELLOW}🔍 Running ${script_name}...${NC}"

    if [ -f "$script_path" ]; then
        if [ -x "$script_path" ]; then
            "$script_path"
            echo -e "${GREEN}✅ ${script_name} completed successfully${NC}"
            echo
        else
            echo -e "${RED}❌ Error: ${script_name} is not executable${NC}"
            echo -e "${YELLOW}🔧 Making ${script_name} executable...${NC}"
            chmod +x "$script_path"
            "$script_path"
            echo -e "${GREEN}✅ ${script_name} completed successfully${NC}"
            echo
        fi
    else
        echo -e "${RED}❌ Error: ${script_name} not found at ${script_path}${NC}"
        echo
        return 1
    fi
}

echo "📊 Starting Backend Scripts..."
echo "================================="

# Backend scripts
run_script "backend/generate_cmd_prompt.sh"
run_script "backend/generate_comprehensive_backend_prompt.sh"
run_script "backend/generate_config_prompt.sh"
run_script "backend/generate_microservices_prompt.sh"
run_script "backend/generate_migrations_prompt.sh"
run_script "backend/generate_pkg_prompt.sh"
run_script "backend/generate_protos_prompt.sh"

echo "📱 Starting Frontend Scripts..."
echo "=================================="

# Frontend scripts
run_script "frontend/generate_backend_logic_prompt.sh"
run_script "frontend/generate_presentation_prompt.sh"
run_script "frontend/generate_data_prompt.sh"
run_script "frontend/generate_repositories_prompt.sh"
run_script "frontend/generate_statefulbusinesslogic_prompt.sh"
run_script "frontend/generate_riverpod_prompt.sh"

echo "👻 Starting Frontend No Comments Scripts..."
echo "=========================================="

# Frontend No Comments scripts
run_script "frontend_no_comments/generate_backend_logic_prompt.sh"
run_script "frontend_no_comments/generate_core_prompt.sh"
run_script "frontend_no_comments/generate_data_prompt.sh"
run_script "frontend_no_comments/generate_presentation_prompt.sh"
run_script "frontend_no_comments/generate_repositories_prompt.sh"
run_script "frontend_no_comments/generate_riverpod_prompt.sh"
run_script "frontend_no_comments/generate_statefulbusinesslogic_prompt.sh"

echo "🔄 Starting Mixed Scripts..."
echo "=============================="

# Mixed scripts
run_script "mixed/generate_microservices_prompt.sh"

echo -e "${GREEN}🎉 All prompt generation scripts completed successfully!${NC}"
echo
echo "📋 Generated files are ready for AI review:"
echo "  - Backend: 7 prompt files"
echo "  - Frontend: 6 prompt files"
echo "  - Frontend No Comments: 7 prompt files"
echo "  - Mixed: 1 prompt file"
echo "  - Total: 21 prompt files"
echo
echo "📁 Files location: $(pwd)"