import os
import re

# --- Configuration ---
# Define the layers and their corresponding directories.
# The order defines the dependency flow: Presentation -> SBL -> Repository -> Data
LAYER_HIERARCHY = {
    "presentation": [],
    "statefulbusinesslogic": ["presentation"],
    "repositories": ["presentation", "statefulbusinesslogic"],
    "data": ["presentation", "statefulbusinesslogic", "repositories"]
}

# Directories to be processed within the 'lib/' folder.
# We add 'main' to catch main.dart and main_dev.dart
DIRECTORIES_TO_PROCESS = list(LAYER_HIERARCHY.keys()) + ['di', 'core', 'main']

# Name of the output file for the code abstract.
script_dir = os.path.dirname(os.path.abspath(__file__))
OUTPUT_FILENAME = os.path.join(script_dir, "code_abstract.txt")

# --- Helper Functions ---

def get_file_layer(path):
    """Determines the architectural layer of a file based on its path."""
    # Normalize path separators for cross-platform compatibility
    path = path.replace(os.path.sep, '/')
    for layer in LAYER_HIERARCHY:
        # Check for both root layer folders and SBL subfolders
        if f'lib/{layer}/' in path or \
           f'lib/statefulbusinesslogic/{layer}/' in path:
            return layer
    # Check for main files
    if 'lib/main' in path:
        return 'presentation' # Treat main files as part of the presentation layer
    return None

def analyze_dependencies(lib_path):
    """
    Scans all .dart files and validates their imports against the LAYER_HIERARCHY.
    """
    print("="*80)
    print("🔎 Starting Dependency Rule Analysis...")
    print("="*80)

    violations = []
    project_package_name = get_project_package_name('pubspec.yaml')

    for root, _, files in os.walk(lib_path):
        for file in files:
            if not file.endswith('.dart'):
                continue

            file_path = os.path.join(root, file)
            current_layer = get_file_layer(file_path)

            if not current_layer:
                continue

            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip().startswith('import '):
                        match = re.search(r"import\s+['\"](.*?)['\"];", line)
                        if not match:
                            continue

                        import_path = match.group(1)
                        # Resolve project-absolute imports
                        if import_path.startswith(f'package:{project_package_name}/'):
                            resolved_path = import_path.replace(f'package:{project_package_name}/', 'lib/')
                            imported_layer = get_file_layer(resolved_path)
                        # For now, we skip relative paths and other packages as they are more complex to resolve reliably
                        # A more robust solution would require analyzing .packages file
                        else:
                            continue

                        if not imported_layer or imported_layer == current_layer:
                            continue

                        # Check for violations: A layer cannot import from a layer above it.
                        # The valid imports for a layer are defined in the hierarchy.
                        if imported_layer not in LAYER_HIERARCHY.get(current_layer, []):
                             violations.append(
                                f"Violation in: {os.path.relpath(file_path)}\n"
                                f" -> Layer '{current_layer}' illegally imports from layer '{imported_layer}' (L{line_num}):\n"
                                f"    {line.strip()}\n"
                            )

    if violations:
        print("❌ Found Dependency Rule Violations:\n")
        for v in violations:
            print(v)
    else:
        print("✅ No dependency rule violations found. Your architecture looks clean!")
    print("="*80 + "\n")
    return not violations

def get_project_package_name(pubspec_path):
    """Extracts the project name from pubspec.yaml."""
    try:
        with open(pubspec_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip().startswith('name:'):
                    return line.split(':')[1].strip()
    except FileNotFoundError:
        return "your_project" # Fallback
    return "your_project"

def create_code_abstract(file_path):
    """
    Generates a condensed, token-friendly "abstract" of a Dart file.
    It keeps signatures, class/enum definitions, and doc comments.
    """
    abstract_lines = []
    brace_level = 0
    in_multiline_comment = False

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                stripped_line = line.strip()

                # Handle multiline comments
                if '/*' in stripped_line:
                    in_multiline_comment = True
                if '*/' in stripped_line:
                    in_multiline_comment = False
                    continue
                if in_multiline_comment or stripped_line.startswith('//') and not stripped_line.startswith('///'):
                    continue

                # Keep imports and doc comments
                if stripped_line.startswith('import ') or \
                   stripped_line.startswith('export ') or \
                   stripped_line.startswith('part ') or \
                   stripped_line.startswith('///'):
                    abstract_lines.append(line)
                    continue

                # Heuristics for keeping definition lines
                is_definition = (
                    'class ' in stripped_line or
                    'enum ' in stripped_line or
                    'mixin ' in stripped_line or
                    'extends ' in stripped_line or
                    stripped_line.endswith(';') # Keep variable declarations and abstract methods
                )

                if brace_level == 0 and is_definition:
                    # For classes and methods with bodies, just show the signature
                    if '{' in stripped_line:
                        abstract_lines.append(line[:line.find('{') + 1] + ' ... }\n')
                    else:
                        abstract_lines.append(line)

                # Track brace levels to ignore implementation details
                brace_level += line.count('{')
                brace_level -= line.count('}')
                brace_level = max(0, brace_level) # Reset if it goes negative

    except Exception as e:
        return [f"// Error processing file: {e}\n"]

    return "".join(abstract_lines)

def generate_full_project_abstract(lib_path, pubspec_path):
    """
    Generates the complete code abstract file for the entire project.
    """
    print(f"✍️  Generating code abstract file: {OUTPUT_FILENAME}...")
    with open(OUTPUT_FILENAME, 'w', encoding='utf-8') as f:
        # 1. Write the prompt header
        f.write(
            "I need a complete code review of my Flutter application. Due to token limits, "
            "I am providing a 'code abstract' of the entire project.\n\n"
            "Please analyze this structure for architectural issues, inconsistencies, and "
            "adherence to best practices. After you confirm your understanding, "
            "I will provide full code for specific files for a detailed review.\n\n"
        )

        # 2. Generate and write the project file tree
        f.write("--- PROJECT STRUCTURE ---\n")
        for root, dirs, files in os.walk(lib_path):
            level = root.replace(lib_path, '').count(os.sep)
            indent = ' ' * 4 * level
            f.write(f"{indent}{os.path.basename(root)}/\n")
            sub_indent = ' ' * 4 * (level + 1)
            for file in sorted(files):
                if file.endswith('.dart'):
                     f.write(f"{sub_indent}{file}\n")
        f.write("\n")


        # 3. Write the pubspec.yaml content
        f.write("--- DEPENDENCIES (pubspec.yaml) ---\n")
        try:
            with open(pubspec_path, 'r', encoding='utf-8') as pubspec_file:
                f.write(pubspec_file.read())
            f.write("\n\n")
        except FileNotFoundError:
            f.write("!!! pubspec.yaml not found !!!\n\n")


        # 4. Generate and write code abstracts for all .dart files
        f.write("--- CODE ABSTRACTS ---\n\n")
        for root, _, files in os.walk(lib_path):
            # Process files in specified directories only
            if not any(dir_name in root for dir_name in DIRECTORIES_TO_PROCESS):
                continue

            for file in sorted(files):
                if not file.endswith('.dart'):
                    continue
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, os.path.dirname(lib_path))
                f.write(f"// path: {rel_path.replace(os.path.sep, '/')}\n")
                abstract = create_code_abstract(file_path)
                f.write(abstract)
                f.write("\n\n")

    print(f"✅ Success! Project abstract saved to '{OUTPUT_FILENAME}'.")
    print("You can now copy its content into Google AI Studio.")

# --- Main Execution ---

def main():
    """Main function to run the script."""
    # Change to the Flutter project root directory
    flutter_project_root = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))), 'hopen')
    os.chdir(flutter_project_root)
    
    # Find the project root by looking for pubspec.yaml
    current_dir = os.getcwd()
    pubspec_path = os.path.join(current_dir, 'pubspec.yaml')
    lib_path = os.path.join(current_dir, 'lib')

    if not os.path.exists(pubspec_path) or not os.path.exists(lib_path):
        print("Error: This script must be run from the root directory of your Flutter project.")
        print("(The directory containing 'pubspec.yaml' and 'lib/')")
        return

    # 1. Perform dependency analysis first
    is_clean = analyze_dependencies(lib_path)

    if not is_clean:
        print("Dependency issues found. Please review them before proceeding with a full AI review for best results.\n")

    # 2. Generate the full project abstract
    generate_full_project_abstract(lib_path, pubspec_path)


if __name__ == "__main__":
    main()
