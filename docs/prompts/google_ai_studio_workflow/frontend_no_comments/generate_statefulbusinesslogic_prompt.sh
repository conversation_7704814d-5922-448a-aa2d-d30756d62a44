#!/bin/bash

# Script to generate a comprehensive prompt file containing all statefulbusinesslogic code
# This will be used for AI review of the entire statefulbusinesslogic codebase

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# Go up to the Hopen root directory (frontend -> google_ai_studio_workflow -> prompts -> docs -> Hopen)
HOPEN_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"

# Configuration
STATEFUL_BUSINESS_LOGIC_DIR="$HOPEN_ROOT/hopen/lib/statefulbusinesslogic"
PROMPTS_DIR="$SCRIPT_DIR"
OUTPUT_FILE="statefulbusinesslogic_codebase_review.txt"
TEMP_DIR="/tmp/statefulbusinesslogic_scan"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Starting Stateful Business Logic Codebase Scan...${NC}"
echo -e "${YELLOW}📁 Script directory: $SCRIPT_DIR${NC}"
echo -e "${YELLOW}📁 Hopen root: $HOPEN_ROOT${NC}"
echo -e "${YELLOW}📁 Stateful Business Logic directory: $STATEFUL_BUSINESS_LOGIC_DIR${NC}"

# Check if directory exists
if [[ ! -d "$STATEFUL_BUSINESS_LOGIC_DIR" ]]; then
    echo -e "${RED}❌ Error: Stateful Business Logic directory not found: $STATEFUL_BUSINESS_LOGIC_DIR${NC}"
    exit 1
fi

# Create temp directory
mkdir -p "$TEMP_DIR"

# Function to strip comments and LoggingService calls from Dart files while preserving string literals
strip_dart_comments() {
    python3 -c "
import re
import sys

content = sys.stdin.read()
lines = content.split('\n')
result = []
in_multiline_comment = False
in_multiline_doc = False

for line in lines:
    if in_multiline_comment:
        if '*/' in line:
            line = line.split('*/', 1)[1]
            in_multiline_comment = False
        else:
            continue

    if in_multiline_doc:
        if '*/' in line:
            line = line.split('*/', 1)[1]
            in_multiline_doc = False
        else:
            continue

    if not in_multiline_comment and not in_multiline_doc:
        # Check for LoggingService calls and other logging patterns and skip them
        stripped_line = line.strip()
        if (stripped_line.startswith('LoggingService.') or
            'LoggingService.' in stripped_line or
            '.info(' in stripped_line or
            '.error(' in stripped_line or
            '.warning(' in stripped_line or
            '.success(' in stripped_line or
            '.debug(' in stripped_line or
            '.log(' in stripped_line or
            stripped_line.startswith('print(') or
            'debugPrint(' in stripped_line or
            'developer.log(' in stripped_line or
            (stripped_line.startswith(\"'\") and ('🔍' in stripped_line or '⚠️' in stripped_line or '✅' in stripped_line or '❌' in stripped_line or '🚀' in stripped_line or '💡' in stripped_line or '🔧' in stripped_line)) or
            (stripped_line.startswith('\"') and ('🔍' in stripped_line or '⚠️' in stripped_line or '✅' in stripped_line or '❌' in stripped_line or '🚀' in stripped_line or '💡' in stripped_line or '🔧' in stripped_line)) or
            'data: {' in stripped_line):
            continue

        # Track quotes to avoid stripping comments inside strings
        quote_count = 0
        double_quote_count = 0
        new_line = ''
        i = 0

        while i < len(line):
            char = line[i]

            # Handle escape sequences
            if char == '\\\\' and i + 1 < len(line):
                new_line += char + line[i + 1]
                i += 2
                continue

            # Track quotes
            if char == '\"':
                double_quote_count += 1
            elif char == \"'\":
                quote_count += 1

            # Check for comments when not inside strings
            is_inside_string = (double_quote_count % 2 == 1) or (quote_count % 2 == 1)

            if not is_inside_string and i + 1 < len(line):
                if line[i:i+2] == '//' and not line[i:i+3].startswith('///'):
                    break
                elif line[i:i+2] == '/*' and not line[i:i+3].startswith('/**'):
                    end_pos = line.find('*/', i)
                    if end_pos >= 0:
                        i = end_pos + 1
                        continue
                    else:
                        in_multiline_comment = True
                        break
                elif line[i:i+3] == '/**':
                    end_pos = line.find('*/', i)
                    if end_pos >= 0:
                        i = end_pos + 1
                        continue
                    else:
                        in_multiline_doc = True
                        break
                elif line[i:i+3] == '///':
                    break

            new_line += char
            i += 1

        result.append(new_line)

print('\n'.join(result))
" < "$1"
}

# Function to process a file
process_file() {
    local file_path="$1"
    local relative_path="${file_path#$STATEFUL_BUSINESS_LOGIC_DIR/}"
    
    # Skip certain file types
    if [[ "$file_path" == *.git* ]] || [[ "$file_path" == *.DS_Store* ]] || [[ "$file_path" == *node_modules* ]]; then
        return
    fi
    
    # Get file extension
    local extension="${file_path##*.}"
    
    # Only process text files
    case "$extension" in
        dart|txt|md|yaml|yml|json|proto|sql|cql|sh|dockerfile|dockerignore|gitignore)
            echo -e "${GREEN}📄 Processing: $relative_path${NC}"
            
            # Create the labeled content
            echo "=== FILE: $relative_path ===" >> "$TEMP_DIR/content.txt"
            echo "File Type: $extension" >> "$TEMP_DIR/content.txt"
            echo "Full Path: $file_path" >> "$TEMP_DIR/content.txt"
            echo "Last Modified: $(stat -f "%Sm" "$file_path" 2>/dev/null || stat -c "%y" "$file_path" 2>/dev/null || echo "Unknown")" >> "$TEMP_DIR/content.txt"
            echo "File Size: $(wc -c < "$file_path" | tr -d ' ') bytes" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "--- CONTENT START ---" >> "$TEMP_DIR/content.txt"
            
            # Strip comments from Dart files, otherwise include as-is
            if [[ "$extension" == "dart" ]]; then
                strip_dart_comments "$file_path" >> "$TEMP_DIR/content.txt"
            else
                cat "$file_path" >> "$TEMP_DIR/content.txt"
            fi
            
            echo "" >> "$TEMP_DIR/content.txt"
            echo "--- CONTENT END ---" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            echo "==========================================" >> "$TEMP_DIR/content.txt"
            echo "" >> "$TEMP_DIR/content.txt"
            ;;
    esac
}

# Function to recursively scan directory
scan_directory() {
    local dir="$1"
    
    for item in "$dir"/*; do
        if [[ -d "$item" ]]; then
            # It's a directory, recurse
            scan_directory "$item"
        elif [[ -f "$item" ]]; then
            # It's a file, process it
            process_file "$item"
        fi
    done
}

# Create the output file with header
cat > "$TEMP_DIR/content.txt" << EOF
# STATEFUL BUSINESS LOGIC CODEBASE REVIEW PROMPT
# ===================================
# 
# This file contains the complete content of all files in the statefulbusinesslogic directory.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: $(date)
# Total files to process: $(find "$STATEFUL_BUSINESS_LOGIC_DIR" -type f | wc -l)
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure.
# 2. Analyze the BLoC pattern implementations and their purposes.
# 3. Identify patterns, dependencies, and relationships between BLoCs.
# 4. Provide insights on state management, event handling, and potential improvements.
# 5. Note any inconsistencies, missing implementations, or areas for enhancement.
# 6. Review the overall stateful business logic layer architecture and abstraction patterns.
# 7. Analyze how BLoCs interact with repositories and the presentation layer.
#
# FILE CONTENTS:
# =============

EOF

# Start scanning
echo -e "${YELLOW}📁 Scanning directory: $STATEFUL_BUSINESS_LOGIC_DIR${NC}"
scan_directory "$STATEFUL_BUSINESS_LOGIC_DIR"

# Generate summary
echo "" >> "$TEMP_DIR/content.txt"
echo "# SUMMARY" >> "$TEMP_DIR/content.txt"
echo "# =======" >> "$TEMP_DIR/content.txt"
echo "Total files processed: $(grep -c "=== FILE:" "$TEMP_DIR/content.txt")" >> "$TEMP_DIR/content.txt"
echo "File types found:" >> "$TEMP_DIR/content.txt"
grep "File Type:" "$TEMP_DIR/content.txt" | cut -d: -f2 | sort | uniq -c | sed 's/^/  /' >> "$TEMP_DIR/content.txt"

# Create the final output file
mkdir -p "$PROMPTS_DIR"
cp "$TEMP_DIR/content.txt" "$PROMPTS_DIR/$OUTPUT_FILE"

# Clean up
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ Generated stateful business logic prompt file: $PROMPTS_DIR/$OUTPUT_FILE${NC}"
echo -e "${BLUE}📊 Summary:${NC}"
echo -e "  - Files processed: $(grep -c "=== FILE:" "$PROMPTS_DIR/$OUTPUT_FILE")"
echo -e "  - Output size: $(du -h "$PROMPTS_DIR/$OUTPUT_FILE" | cut -f1)"
echo -e "  - Location: $PROMPTS_DIR/$OUTPUT_FILE"

echo -e "${YELLOW}🎯 Ready for AI review! The file contains all stateful business logic code with proper labeling.${NC}"
