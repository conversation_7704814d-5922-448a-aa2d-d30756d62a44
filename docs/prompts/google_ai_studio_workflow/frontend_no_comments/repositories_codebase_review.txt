# REPOSITORIES CODEBASE REVIEW PROMPT
# ===================================
# 
# This file contains the complete content of all files in the repositories directory.
# Each file is labeled with its path, type, and metadata for AI review.
#
# Generated on: Mon Sep 15 17:53:43 EDT 2025
# Total files to process:       49
#
# INSTRUCTIONS FOR AI:
# ===================
# 1. Review each file's content and structure
# 2. Analyze the repository pattern implementations and their purposes
# 3. Identify patterns, dependencies, and relationships between repositories
# 4. Provide insights on data access patterns, caching, and potential improvements
# 5. Note any inconsistencies, missing implementations, or areas for enhancement
# 6. Review thedata layer architecture and abstraction patterns
# 7. Analyze how repositories interact with the backend services
#
# FILE CONTENTS:
# =============

=== FILE: activity_status/activity_status_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/activity_status/activity_status_repository.dart
Last Modified: Sep 15 12:54:31 2025
File Size: 1413 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/activity_status_model.dart';






abstract class ActivityStatusRepository {
  
  
  Future<Result<ActivityStatusModel?>> getActivityStatus(String userId);

  
  
  Future<Result<void>> saveActivityStatus(ActivityStatusModel activityStatus);

  
  
  Future<Result<void>> updateActivityStatus(ActivityStatusModel activityStatus);

  
  
  Future<Result<void>> deleteActivityStatus(String userId);

  
  
  Future<Result<bool>> hasActivityStatus(String userId);
}


--- CONTENT END ---

==========================================

=== FILE: analytics/analytics_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/analytics/analytics_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1244 bytes

--- CONTENT START ---
abstract class AnalyticsRepository {
  
  Future<void> identify({required String userId, Map<String, dynamic>? traits});

  
  Future<void> track({
    required String eventName,
    required String userId,
    Map<String, dynamic>? properties,
  });

  
  Future<void> page({
    required String pageName,
    required String userId,
    Map<String, dynamic>? properties,
  });

  
  Future<void> screen({
    required String screenName,
    required String userId,
    Map<String, dynamic>? properties,
  });

  
  Future<void> group({
    required String groupType,
    required String groupId,
    required String userId,
    Map<String, dynamic>? traits,
  });

  
  Future<void> alias({required String userId, required String previousId});

  
  Future<void> reset();

  
  Future<void> flush();

  
  Future<void> setOptOut({required bool optOut});

  
  Future<bool> isOptedOut();
}


--- CONTENT END ---

==========================================

=== FILE: audio_recording/audio_recording_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/audio_recording/audio_recording_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 554 bytes

--- CONTENT START ---
import 'dart:async';


abstract class AudioRecordingRepository {
  
  Future<bool> checkPermission();

  
  Future<void> initialize();

  
  Future<void> startRecording();

  
  Future<String?> stopRecording();

  
  Future<bool> isRecording();

  
  Future<void> dispose();
} 

--- CONTENT END ---

==========================================

=== FILE: auth/auth_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/auth/auth_repository.dart
Last Modified: Sep 13 18:08:42 2025
File Size: 3691 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/errors.dart' show AppError;
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

abstract class AuthRepository {
  
  
  Future<Result<UserModel>> login({
    required String identifier,
    required String password,
  });

  
  
  Future<Result<UserModel>> loginWithGoogle();

  
  
  Future<Result<UserModel>> loginWithApple();

  
  
  Future<Result<UserModel>> signUp({
    required String email,
    required String password,
    required String username,
    required String firstName,
    required String lastName,
    DateTime? birthday,
    String? profilePictureUrl,
    bool notificationsEnabled = false,
  });

  
  
  Future<Result<UserModel>> getCurrentUser();

  
  
  Future<Result<bool>> logout();

  
  
  Future<Result<bool>> updateOnboardingStatus({required bool hasCompletedOnboarding});

  
  
  
  Future<Result<bool>> initiatePasswordReset(String email);

  
  
  
  Future<Result<bool>> verifyResetCode(String email, String code);

  
  
  
  Future<Result<bool>> resetPassword({
    required String token,
    required String newPassword,
    required String email,
  });

  
  UserModel? get authenticatedUser;

  
  bool get isAuthenticated;

  
  Stream<AuthState> get authStateStream;

  
  
  Future<Result<bool>> resendVerificationEmail([String? email]);

  
  
  Future<Result<bool>> verifyEmail(String token);

  
  
  Future<Result<bool>> updatePassword({
    required String currentPassword,
    required String newPassword,
  });

  
  
  Future<Result<bool>> signOut();

  
  
  Future<Result<bool>> deleteAccount({
    required String password,
    required String confirmationText,
    String? reason,
  });
}


enum AuthState {
  authenticated,
  unauthenticated,
  loading,
}


--- CONTENT END ---

==========================================

=== FILE: availability/availability_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/availability/availability_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 966 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/models/availability_result.dart';



abstract class AvailabilityRepository {
  
  
  Future<AvailabilityResult> checkEmailAvailability(String email);

  
  
  Future<AvailabilityResult> checkUsernameAvailability(String username);

  
  
  Future<Map<String, AvailabilityResult>> checkBothAvailability({
    required String email,
    required String username,
  });
}


--- CONTENT END ---

==========================================

=== FILE: background_processing/background_processing_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/background_processing/background_processing_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 839 bytes

--- CONTENT START ---


abstract class BackgroundProcessingRepository {
  
  Future<void> processPendingRequests();

  
  Future<void> scheduleBackgroundTask(String taskId, Map<String, dynamic> data);

  
  Future<void> cancelBackgroundTask(String taskId);

  
  Future<String?> getTaskStatus(String taskId);

  
  Future<bool> isBackgroundProcessingAvailable();

  
  Future<void> initialize();

  
  Future<void> cleanup();
}


--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_invite_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_invite_request_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2826 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';


enum InviteRequestStatus { pending, accepted, declined, expired }


class BubbleInviteRequest {
  const BubbleInviteRequest({
    required this.id,
    required this.bubbleId,
    required this.bubbleName,
    required this.inviterId,
    required this.inviterName,
    required this.inviteeId,
    required this.inviteeName,
    required this.invitedAt,
    required this.status,
    this.message,
    this.respondedAt,
    this.expiresAt,
  });
  final String id;
  final String bubbleId;
  final String bubbleName;
  final String inviterId;
  final String inviterName;
  final String inviteeId;
  final String inviteeName;
  final DateTime invitedAt;
  final InviteRequestStatus status;
  final String? message;
  final DateTime? respondedAt;
  final DateTime? expiresAt;
}


abstract class BubbleInviteRequestRepository {
  
  Future<Result<BubbleInviteRequest>> sendInvite(
    String bubbleId,
    String userId,
    String message,
  );

  
  Future<Result<List<BubbleInviteRequest>>> getPendingInvites(String userId);

  
  Future<Result<List<BubbleInviteRequest>>> getSentInvites(String userId);

  
  Future<Result<List<BubbleInviteRequest>>> getBubbleInvites(String bubbleId);

  
  Future<Result<void>> acceptInvite(String inviteId);

  
  Future<Result<void>> declineInvite(String inviteId);

  
  Future<Result<void>> cancelInvite(String inviteId);

  
  Future<Result<BubbleInviteRequest>> getInvite(String inviteId);

  
  Future<Result<bool>> hasPendingInvite(String bubbleId, String userId);

  
  Future<Result<BubbleInviteRequest>> resendInvite(String inviteId);

  
  Future<Result<List<BubbleInviteRequest>>> getInviteHistory(String bubbleId);

  
  Future<Result<void>> expireOldInvites();

  
  Future<Result<Map<String, int>>> getInviteStatistics(String bubbleId);

  
  Future<Result<void>> acceptInviteRequest(String requestId);

  
  Future<Result<void>> declineInviteRequest(String requestId);

  
  Future<Result<BubbleInviteRequest>> getBubbleDetails(String bubbleId);

  
  Future<Result<void>> sendInviteRequest(
    String bubbleId,
    String inviteeId,
    String? message,
  );
}


--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_join_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_join_request_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2375 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';


enum JoinRequestStatus { pending, approved, rejected, expired }


class BubbleJoinRequest {
  const BubbleJoinRequest({
    required this.id,
    required this.bubbleId,
    required this.userId,
    required this.userName,
    required this.requestedAt,
    required this.status,
    this.userAvatarUrl,
    this.message,
    this.respondedAt,
    this.respondedBy,
  });
  final String id;
  final String bubbleId;
  final String userId;
  final String userName;
  final String? userAvatarUrl;
  final DateTime requestedAt;
  final JoinRequestStatus status;
  final String? message;
  final DateTime? respondedAt;
  final String? respondedBy;
}


abstract class BubbleJoinRequestRepository {
  
  Future<Result<BubbleJoinRequest>> sendJoinRequest(
    String bubbleId,
    String message,
  );

  
  Future<Result<List<BubbleJoinRequest>>> getPendingJoinRequests(
    String bubbleId,
  );

  
  Future<Result<List<BubbleJoinRequest>>> getUserJoinRequests(String userId);

  
  Future<Result<void>> approveJoinRequest(String requestId);

  
  Future<Result<void>> rejectJoinRequest(String requestId, String reason);

  
  Future<Result<void>> cancelJoinRequest(String requestId);

  
  Future<Result<BubbleJoinRequest>> getJoinRequest(String requestId);

  
  Future<Result<bool>> hasPendingJoinRequest(String bubbleId, String userId);

  
  Future<Result<List<BubbleJoinRequest>>> getJoinRequestHistory(
    String bubbleId,
  );

  
  Future<Result<void>> expireOldJoinRequests();

  
  Future<Result<BubbleJoinRequest>> getBubbleDetails(String bubbleId);

  
  Future<Result<void>> acceptJoinRequest(String bubbleId, String requestId);

  
  Future<Result<void>> declineJoinRequest(String bubbleId, String requestId);
}


--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_kickout_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_kickout_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1474 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/bubble_kickout_record.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';


abstract class BubbleKickoutRepository {
  
  Future<Result<void>> kickoutMember({
    required String bubbleId,
    required String memberId,
    String? reason,
  });

  
  Future<Result<List<BubbleKickoutRecord>>> getKickoutHistory(String bubbleId);

  
  Future<Result<List<BubbleKickoutRecord>>> getUserKickoutHistory(String userId);

  
  Future<Result<bool>> isUserKickedOut({
    required String bubbleId,
    required String userId,
  });

  
  Future<Result<List<UserModel>>> getKickedOutUsers(String bubbleId);

  
  Future<Result<void>> allowUserToRejoin({
    required String bubbleId,
    required String userId,
  });

  
  
  
  
  
  Future<Result<Map<String, dynamic>>> getKickoutStatistics(String bubbleId);
}


--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_propose_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_propose_request_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 686 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';


abstract class BubbleProposeRequestRepository {
  
  
  
  
  Future<Result<bool>> acceptProposeRequest({
    required String requestId,
    required String proposedMemberId,
  });

  
  Future<Result<void>> declineProposeRequest({
    required String requestId,
    required String proposedMemberId,
  });
}


--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_repository.dart
Last Modified: Sep  7 17:28:35 2025
File Size: 6254 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../statefulbusinesslogic/core/models/bubble_member.dart';
import '../../statefulbusinesslogic/core/models/bubble_request_model.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/models/value_objects.dart';



abstract class BubbleRepository {
  
  Future<Result<BubbleEntity>> createBubble({
    required BubbleName name,
    required String description,
    String? locationName,
    double? locationLat,
    double? locationLng,
    int? locationRadius,
    String? customImageUrl,
    String? colorTheme,
    bool? allowInvites,
    bool? requireApproval,
  });

  Future<Result<BubbleEntity>> getBubble(BubbleId bubbleId);

  Future<Result<BubbleEntity?>> getBubbleById(String bubbleId);

  Future<Result<BubbleEntity>> getBubbleDetailsById(BubbleId bubbleId);

  
  Future<Result<BubbleEntity?>> getCurrentBubble();

  Future<Result<void>> updateBubble(
    BubbleId bubbleId,
    Map<String, dynamic> updates,
  );

  Future<Result<void>> deleteBubble(BubbleId bubbleId);

  
  Future<Result<BubbleEntity>> joinBubble({
    required BubbleId bubbleId,
    UserId? userId,
  });

  Future<Result<void>> leaveBubble({required BubbleId bubbleId, UserId? userId});

  Future<Result<List<UserModel>>> getBubbleMembers(BubbleId bubbleId);

  
  Future<Result<List<BubbleMember>>> getBubbleMembersWithRoles(BubbleId bubbleId);

  Future<Result<List<BubbleEntity>>> getUserBubbles(UserId userId);

  Future<Result<List<BubbleEntity>>> getBubbles(UserId userId);

  

  Future<Result<BubbleEntity>> updateMemberStatus({
    required BubbleId bubbleId,
    required UserId memberId,
    bool? isOnline,
    int? unreadMessageCount,
  });

  
  Future<Result<void>> inviteToBubble({
    required BubbleId bubbleId,
    required UserId inviterId,
    required List<UserId> inviteeIds,
  });

  Future<Result<BubbleEntity>> voteToRemoveMember({
    required BubbleId bubbleId,
    required UserId voterId,
    required UserId targetMemberId,
  });

  Future<Result<void>> kickMemberFromBubble({
    required BubbleId bubbleId,
    required UserId memberId,
  });

  Future<Result<BubbleEntity>> updateBubbleInfo({
    required BubbleId bubbleId,
    BubbleName? name,
    DateTime? endDate,
  });

  Future<Result<void>> markAllMessagesRead({
    required BubbleId bubbleId,
    required UserId memberId,
  });

  Future<Result<void>> proposeMember({
    required BubbleId bubbleId,
    required String memberName,
    required String memberEmail,
  });

  

  
  Future<Result<BubbleRequestModel>> createRequest({
    required String bubbleId,
    required String targetId,
    required String type, 
    String? message,
  });

  Future<Result<BubbleRequestModel>> respondToRequest({
    required String requestId,
    required String status, 
  });

  Future<Result<List<BubbleRequestModel>>> getPendingRequests();

  
  Future<void> selectFriends({
    required String bubbleId,
    required List<String> selectedUserIds,
  });

  

  
  Future<Result<void>> sendBubbleStartRequest({
    required String recipientId,
    required String bubbleName,
    String? message,
  });

  
  Future<Result<void>> sendBubbleInviteRequest({
    required String bubbleId,
    required String inviteeId,
    String? message,
  });

  
  Future<Result<void>> sendBubbleJoinRequest({
    required String bubbleId,
    String? message,
  });

  Future<Result<void>> acceptBubbleRequest({
    required String requestId,
    required String bubbleId,
    required String userId,
  });

  Future<Result<void>> declineBubbleRequest({
    required String requestId,
    required String bubbleId,
    required String userId,
  });

  
  Future<Result<BubbleRequestModel?>> getBubbleRequestById(String requestId);

  
  Future<Result<void>> acceptBubbleKickoutRequest({
    required String requestId,
    required String bubbleId,
    required String targetMemberId,
  });

  Future<Result<void>> declineBubbleKickoutRequest({
    required String requestId,
    required String bubbleId,
    required String targetMemberId,
  });

  
  
  Future<Result<void>> voteOnJoinRequest({
    required String requestId,
    required bool approve,
    String? reason,
  });

  
  Future<Result<void>> voteOnInviteRequest({
    required String requestId,
    required bool approve,
    String? reason,
  });

  
  Future<Result<void>> voteOnKickoutRequest({
    required String bubbleId,
    required String targetUserId,
    required bool approve,
    String? reason,
  });
}


class BubbleStatistics {
  const BubbleStatistics({
    required this.totalMessages,
    required this.totalMembers,
    required this.activeMembers,
    required this.averageSessionDuration,
    required this.lastActivity,
    required this.totalCalls,
    required this.totalCallTime,
  });
  final int totalMessages;
  final int totalMembers;
  final int activeMembers;
  final Duration averageSessionDuration;
  final DateTime lastActivity;
  final int totalCalls;
  final Duration totalCallTime;
}


--- CONTENT END ---

==========================================

=== FILE: bubble/bubble_start_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble/bubble_start_request_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 534 bytes

--- CONTENT START ---
abstract class BubbleStartRequestRepository {
  
  
  Future<bool> acceptStartRequest({
    required String bubbleId,
    required String requesterId,
  });

  
  Future<void> declineStartRequest({
    required String bubbleId,
    required String requesterId,
  });

  
  Future<void> createStartRequest({required String bubbleId});
}


--- CONTENT END ---

==========================================

=== FILE: bubble_history/bubble_history_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/bubble_history/bubble_history_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 3313 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../statefulbusinesslogic/core/models/bubble_history_events.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';


enum BubbleSortBy { dateCreated, dateUpdated, name, memberCount, activity }


abstract class BubbleHistoryRepository {
  
  Future<Result<List<BubbleEntity>>> getBubbleHistory(String userId);

  
  Future<Result<List<BubbleEntity>>> getActiveBubbles(String userId);

  
  Future<Result<List<BubbleEntity>>> getCompletedBubbles(String userId);

  
  Future<Result<List<UserModel>>> getBubbleMembers(String bubbleId);

  
  Future<Result<void>> archiveBubble(String bubbleId);

  
  Future<Result<void>> unarchiveBubble(String bubbleId);

  
  Future<Result<void>> favoriteBubble(String bubbleId);

  
  Future<Result<void>> unfavoriteBubble(String bubbleId);

  
  Future<Result<List<BubbleEntity>>> getFavoriteBubbles(String userId);

  
  Future<Result<List<BubbleEntity>>> searchBubbleHistory(
    String userId,
    String query,
  );

  
  Future<Result<List<BubbleEntity>>> filterBubblesByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  );

  
  Future<Result<List<BubbleEntity>>> sortBubbleHistory(
    String userId,
    BubbleSortBy sortBy, {
    bool ascending = true,
  });

  
  Future<Result<Map<String, dynamic>>> getBubbleStatistics(String userId);

  
  Future<Result<void>> clearCache();

  
  Future<Result<void>> joinBubble(String bubbleId);

  
  Future<Result<void>> leaveBubble(String bubbleId);

  
  Future<Result<void>> voteToRemoveMember(String bubbleId, String memberId);

  
  Future<Result<List<BubbleEntity>>> getUserBubbleHistory(String userId) =>
      getBubbleHistory(userId);

  Future<Result<BubbleEntity>> getBubbleById(String bubbleId);
  Future<Result<void>> deleteBubbleFromHistory(String bubbleId, String userId);
  Future<Result<void>> clearBubbleHistory(String userId);
  Future<Result<List<BubbleEntity>>> getRecentBubbles(
    String userId, {
    int limit = 10,
  });

  
  
  Future<Result<List<MemberHistoryEvent>>> getBubbleMemberHistory(
    String bubbleId,
  );

  
  
  Future<Result<List<ActivityEvent>>> getBubbleActivityHistory(
    String bubbleId,
  );
}


--- CONTENT END ---

==========================================

=== FILE: call/call_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/call/call_repository.dart
Last Modified: Sep 15 09:13:37 2025
File Size: 6602 bytes

--- CONTENT START ---
import 'dart:async';

import 'package:flutter_webrtc/flutter_webrtc.dart';

import '../../generated/grpc/call.pb.dart' as call_proto;
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/call_management_models.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';



class RemoteStreamAdded {
  RemoteStreamAdded({
    required this.callId,
    required this.participantId,
    required this.stream,
  });
  final String callId;
  final String participantId;
  final MediaStream stream;
}

class IceConnectionStateChanged {
  IceConnectionStateChanged({
    required this.callId,
    required this.participantId,
    required this.state,
  });
  final String callId;
  final String participantId;
  final RTCIceConnectionState state;
}

class CallErrorOccurred {
  CallErrorOccurred({
    required this.callId,
    required this.error,
    required this.stackTrace,
  });
  final String callId;
  final dynamic error;
  final StackTrace stackTrace;
}

class CallOutcomeOccurred {
  CallOutcomeOccurred({required this.callId, required this.outcome});
  final String callId;
  final String outcome;
}

class JoinCallAcknowledged {
  JoinCallAcknowledged({required this.callId, required this.participants});
  final String callId;
  final List<UserModel> participants;
}

class JoinCallFailed {
  JoinCallFailed({required this.callId, required this.reason});
  final String callId;
  final String reason;
}

class IncomingOfferOccurred {
  IncomingOfferOccurred({
    required this.callId,
    required this.callerId,
    required this.callerName,
    required this.isGroup,
    required this.isVideoOffered,
    required this.isAudioOffered,
    required this.isScreenShareOffered,
    required this.remoteOfferSdp,
    this.callerAvatar,
    this.groupId,
    this.groupName,
    this.groupAvatarUrl,
  });
  final String callId;
  final String callerId;
  final String callerName;
  final String? callerAvatar;
  final bool isGroup;
  final bool isVideoOffered;
  final bool isAudioOffered;
  final bool isScreenShareOffered;
  final String? groupId;
  final String? groupName;
  final String? groupAvatarUrl;
  final String remoteOfferSdp;
}

class CallAccepted {
  CallAccepted({
    required this.callId,
    required this.participantId,
    required this.localStream,
  });
  final String callId;
  final String participantId;
  final MediaStream? localStream;
}


abstract class CallRepository {
  
  Future<Map<String, dynamic>> initiateCall({
    required String targetId,
    required bool isGroup,
    required bool withVideo,
    required bool withScreenShare,
  });

  Future<void> acceptCall({
    required String callId,
    required bool withVideo,
    required Map<String, dynamic> remoteOfferSdp,
    required String callerId,
    required bool isGroupCall,
    String? groupId,
  });

  Future<void> rejectCall({required String callId, String? targetUserId});
  Future<void> endCall({required String callId, String? targetUserId});
  Future<Map<String, dynamic>> joinCall({
    required String bubbleId,
    required String callId,
    required bool withVideo,
    required bool withScreenShare,
  });

  
  Future<void> setMuted(String callId, {required bool muted});
  Future<void> setVideoEnabled(String callId, {required bool enabled});
  Future<void> switchCamera(String callId);
  Future<void> startScreenShare(String callId);
  Future<void> stopScreenShare(String callId);

  
  Stream<MediaStream?> getLocalStream(String callId);
  Stream<Map<String, MediaStream>> getRemoteStreams(String callId);

  
  Future<bool> isCallActive(String callId);
  Future<List<UserModel>> getCallParticipants(String callId);

  
  Future<Result<call_proto.CallInfo>> getCallInfo(String callId);

  
  Future<void> addParticipant(String callId, String participantId);
  Future<void> removeParticipant(String callId, String participantId);
  Future<void> updateParticipantMuteStatus({
    required String callId,
    required String participantId,
    required bool isMuted,
  });

  

  
  Future<Result<void>> setCallLayout(String callId, CallLayoutType layoutType, Map<String, String> settings);
  Future<Result<CallLayoutInfo>> getCallLayout(String callId);

  
  Future<Result<void>> setActiveSpeaker(String callId, String activeSpeakerId);
  Future<Result<ActiveSpeakerInfo>> getActiveSpeaker(String callId);

  
  Future<Result<void>> setCallQualityProfile(String callId, CallQualityProfileInfo qualityProfile);
  Future<Result<CallQualityProfileInfo>> getCallQualityProfile(String callId);

  
  Future<Result<void>> updateParticipantQuality(String callId, String participantId, ParticipantQualitySettings qualitySettings);

  
  Future<CallStateValidationResult> validateCallState(String bubbleId, String userId);

  
  Future<void> leaveCall({required String callId});

  
  Future<void> updateCallClientState({
    required String callId,
    required bool isBackground,
    required DateTime timestamp,
  });

  
  UserModel? getCachedUser(String userId);
}


abstract class CallRepositoryExtended extends CallRepository {
  
  Stream<RemoteStreamAdded> get onRemoteStreamAdded;
  Stream<IceConnectionStateChanged> get onIceConnectionStateChanged;
  Stream<CallErrorOccurred> get onCallErrorOccurred;
  Stream<CallOutcomeOccurred> get onCallOutcomeOccurred;
  Stream<JoinCallAcknowledged> get onJoinCallAcknowledged;
  Stream<JoinCallFailed> get onJoinCallFailed;
  Stream<IncomingOfferOccurred> get onIncomingOfferOccurred;
  Stream<CallAccepted> get onCallAccepted;

  
  void dispose();
}


class CallStateValidationResult {
  const CallStateValidationResult({
    required this.hasActiveCall,
    this.callInfo,
    this.error,
  });

  final bool hasActiveCall;
  final call_proto.CallInfo? callInfo;
  final String? error;
}


--- CONTENT END ---

==========================================

=== FILE: chat/chat_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/chat/chat_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 3812 bytes

--- CONTENT START ---
import 'dart:io';

import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/chat_message.dart';

abstract class ChatRepository {
  
  
  
  
  
  
  Future<Result<Map<String, dynamic>>> getChatInfo(String chatId, {
    String chatType = 'bubble',
  });

  
  Future<Result<List<ChatMessage>>> getMessages(
    String chatId, {
    int limit = 50,
    String? before,
  });

  
  Future<Result<ChatMessage?>> getMessageById(String messageId);

  
  Future<Result<ChatMessage>> sendMessage(
    String chatId,
    String content,
    String senderId,
  );

  
  Future<Result<ChatMessage>> sendMediaMessage({
    required String chatId,
    required String senderId,
    required File mediaFile,
    required String mediaType,
    String? caption,
    String? replyToMessageId,
  });

  
  
  Future<Result<void>> markMessageAsRead({
    required String messageId,
    required String userId,
    required DateTime createdAt,
    String? conversationId,
  });

  
  
  Future<Result<void>> deleteMessage({
    required String messageId,
    required String userId,
    required DateTime createdAt,
    String? bubbleId,
  });

  
  Future<Result<List<ChatMessage>>> searchMessages(String chatId, String query);

  
  Future<void> connectToChat(String chatId);

  
  Future<void> disconnectFromChat(String chatId);

  
  Stream<ChatMessage> messageStream(String chatId);

  
  Stream<Map<String, dynamic>> typingStatusStream(String chatId);

  
  Stream<Map<String, dynamic>> chatEventsStream(String chatId);

  
  Future<void> sendTypingStatus(String chatId, String userId, {required bool isTyping});

  
  Future<Result<void>> markAllMessagesAsRead(String chatId, String userId);

  
  
  Future<Result<ChatMessage>> editMessage({
    required String messageId,
    required String newContent,
    required String userId,
    required DateTime createdAt,
    String? bubbleId,
  });

  
  Future<Result<int>> getUnreadCount(String chatId, String userId);

  
  Stream<int> unreadCountStream(String chatId, String userId);

  
  Future<Result<String>> uploadMedia(File mediaFile, String mediaType);

  
  Future<Result<bool>> startVoiceRecording();

  
  Future<Result<File>> stopVoiceRecording();

  
  Future<Result<void>> addReaction(String messageId, String reactionType, String userId);

  
  Future<Result<void>> removeReaction(String messageId, String reactionType, String userId);

  
  Future<void> dispose();
}


--- CONTENT END ---

==========================================

=== FILE: contacts/contact_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/contacts/contact_request_repository.dart
Last Modified: Sep  5 13:59:15 2025
File Size: 7689 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';





abstract class ContactRequestRepository {
  
  Future<Result<void>> sendContactRequest({
    required String fromUserId,
    required String toUserId,
    String? message,
  });
  
  
  Future<Result<void>> acceptContactRequest({
    required String requestId,
    required String fromUserId,
    required String toUserId,
  });
  
  
  Future<Result<void>> declineContactRequest({
    required String requestId,
    required String fromUserId,
    required String toUserId,
    String? reason,
  });
  
  
  Future<Result<List<ContactRequest>>> getSentContactRequests();
  
  
  Future<Result<List<ContactRequest>>> getReceivedContactRequests();
  
  
  Future<Result<List<ContactRequest>>> getAllContactRequests();
  
  
  Future<Result<ContactRequest?>> getContactRequest(String requestId);
  
  
  Future<Result<void>> cancelContactRequest(String requestId);
  
  
  Future<Result<List<ContactRequest>>> getContactRequestHistory({
    int page = 0,
    int pageSize = 20,
    String direction = 'both', 
    List<String> statuses = const ['pending', 'accepted', 'declined', 'cancelled'],
  });
  
  
  Future<Result<ContactRequest?>> getPendingRequestBetweenUsers({
    required String userId1,
    required String userId2,
  });
  
  
  Future<Result<ContactRequestStats>> getContactRequestStats();
  
  
  Future<Result<void>> clearExpiredRequests();
  
  
  Future<Result<void>> blockUser(String userId);
  
  
  Future<Result<void>> unblockUser(String userId);
  
  
  Future<Result<List<UserModel>>> getBlockedUsers();
  
  
  Future<Result<bool>> isUserBlocked(String userId);
}


class ContactRequest {
  const ContactRequest({
    required this.id,
    required this.fromUserId,
    required this.toUserId,
    required this.status,
    required this.createdAt,
    this.message,
    this.respondedAt,
    this.expiresAt,
    this.fromUser,
    this.toUser,
  });

  
  factory ContactRequest.fromJson(Map<String, dynamic> json) => ContactRequest(
      id: json['id'] as String,
      fromUserId: json['fromUserId'] as String,
      toUserId: json['toUserId'] as String,
      status: ContactRequestStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ContactRequestStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      message: json['message'] as String?,
      respondedAt: json['respondedAt'] != null
          ? DateTime.parse(json['respondedAt'] as String)
          : null,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      fromUser: json['fromUser'] != null
          ? UserModel.fromJson(json['fromUser'] as Map<String, dynamic>)
          : null,
      toUser: json['toUser'] != null
          ? UserModel.fromJson(json['toUser'] as Map<String, dynamic>)
          : null,
    );

  final String id;
  final String fromUserId;
  final String toUserId;
  final ContactRequestStatus status;
  final DateTime createdAt;
  final String? message;
  final DateTime? respondedAt;
  final DateTime? expiresAt;
  final UserModel? fromUser;
  final UserModel? toUser;



  
  Map<String, dynamic> toJson() => {
      'id': id,
      'fromUserId': fromUserId,
      'toUserId': toUserId,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'message': message,
      'respondedAt': respondedAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'fromUser': fromUser?.toJson(),
      'toUser': toUser?.toJson(),
    };

  
  ContactRequest copyWith({
    String? id,
    String? fromUserId,
    String? toUserId,
    ContactRequestStatus? status,
    DateTime? createdAt,
    String? message,
    DateTime? respondedAt,
    DateTime? expiresAt,
    UserModel? fromUser,
    UserModel? toUser,
  }) => ContactRequest(
      id: id ?? this.id,
      fromUserId: fromUserId ?? this.fromUserId,
      toUserId: toUserId ?? this.toUserId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      message: message ?? this.message,
      respondedAt: respondedAt ?? this.respondedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      fromUser: fromUser ?? this.fromUser,
      toUser: toUser ?? this.toUser,
    );

  
  bool get isExpired {
    if (expiresAt == null) {
      return false;
    }
    return DateTime.now().isAfter(expiresAt!);
  }

  
  bool get isPending => status == ContactRequestStatus.pending && !isExpired;

  @override
  String toString() => 'ContactRequest(id: $id, fromUserId: $fromUserId, toUserId: $toUserId, status: $status, createdAt: $createdAt)';
}


enum ContactRequestStatus {
  pending,
  accepted,
  declined,
  cancelled,
  expired,
}


class ContactRequestStats {
  const ContactRequestStats({
    required this.totalSent,
    required this.totalReceived,
    required this.pendingSent,
    required this.pendingReceived,
    required this.acceptedSent,
    required this.acceptedReceived,
    required this.declinedSent,
    required this.declinedReceived,
  });

  
  factory ContactRequestStats.fromJson(Map<String, dynamic> json) => ContactRequestStats(
      totalSent: json['totalSent'] as int,
      totalReceived: json['totalReceived'] as int,
      pendingSent: json['pendingSent'] as int,
      pendingReceived: json['pendingReceived'] as int,
      acceptedSent: json['acceptedSent'] as int,
      acceptedReceived: json['acceptedReceived'] as int,
      declinedSent: json['declinedSent'] as int,
      declinedReceived: json['declinedReceived'] as int,
    );

  final int totalSent;
  final int totalReceived;
  final int pendingSent;
  final int pendingReceived;
  final int acceptedSent;
  final int acceptedReceived;
  final int declinedSent;
  final int declinedReceived;

  
  int get totalPending => pendingSent + pendingReceived;

  
  int get totalAccepted => acceptedSent + acceptedReceived;

  
  int get totalDeclined => declinedSent + declinedReceived;



  
  Map<String, dynamic> toJson() => {
      'totalSent': totalSent,
      'totalReceived': totalReceived,
      'pendingSent': pendingSent,
      'pendingReceived': pendingReceived,
      'acceptedSent': acceptedSent,
      'acceptedReceived': acceptedReceived,
      'declinedSent': declinedSent,
      'declinedReceived': declinedReceived,
    };
}


--- CONTENT END ---

==========================================

=== FILE: contacts/contacts_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/contacts/contacts_repository.dart
Last Modified: Sep  4 20:47:09 2025
File Size: 7348 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../statefulbusinesslogic/core/models/contact_request.dart';
import '../../statefulbusinesslogic/core/models/relationship_type.dart';
import '../../statefulbusinesslogic/core/models/user_contact.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';











abstract class ContactsRepository {
  
  
  

  
  Future<Result<List<UserContact>>> getContacts();

  
  Future<Result<void>> addContact(
    String userId, {
    RelationshipType relationshipType = RelationshipType.contact,
  });

  
  Future<Result<void>> removeContact(String contactId);

  
  Future<Result<void>> blockContact(String contactId);

  
  Future<Result<void>> updateContactRelationship({
    required String contactId,
    required RelationshipType relationshipType,
  });

  
  Future<Result<bool>> isContact(String userId);

  
  
  

  
  Future<Result<ContactRequestModel?>> sendContactRequest({
    required String recipientId,
    String? message,
  });

  
  Future<Result<List<ContactRequestModel>>> getPendingReceivedRequests();

  
  Future<Result<List<ContactRequestModel>>> getPendingSentRequests();

  
  Future<Result<void>> acceptContactRequest(String requestId);

  
  Future<Result<void>> declineContactRequest(String requestId);

  
  Future<Result<void>> cancelContactRequest(String requestId);

  
  Future<Result<ContactRequestModel?>> getContactRequest(String requestId);

  
  Future<Result<RelationshipType>> getRelationshipStatus(String userId);

  
  Future<Result<List<UserModel>>> getReceivedContactRequests();

  
  Future<Result<List<UserModel>>> getSentContactRequests();

  
  Future<Result<bool>> hasPendingRequest(String userId1, String userId2);

  
  Future<Result<List<ContactRequestModel>>> getContactRequestHistory();

  
  
  

  
  Future<Result<List<UserContact>>> searchUsers(String query);

  
  Future<Result<List<UserContact>>> searchContacts(String query);

  
  Future<Result<List<UserModel>>> searchUsersForContactRequests(String query);

  
  Future<Result<List<UserModel>>> getMutualContacts(String userId);

  
  Future<Result<List<UserModel>>> getSuggestedContacts();

  
  
  

  
  Future<Result<void>> sendBubbleStartRequest({
    required String contactId,
    String? message,
  });

  
  Future<Result<void>> sendBubbleInviteRequest({
    required String bubbleId,
    required String contactId,
    String? message,
  });

  
  Future<Result<void>> sendBubbleJoinRequest({
    required String bubbleId,
    String? message,
  });

  
  
  

  
  Stream<List<ContactRequestModel>> get pendingReceivedRequestsStream;

  
  Stream<List<ContactRequestModel>> get pendingSentRequestsStream;

  
  Stream<List<UserContact>> get contactsStream;

  
  
  

  
  Future<Result<void>> expireOldRequests();

  
  
  void setCurrentUserId(String userId);

  
  Future<void> refreshFromRemote();

  
  void dispose();

  
  
  

  
  Future<void> initialize();

  
  Future<void> clearCache();

  
  Future<bool> get hasCachedData;

  
  DateTime? get lastRemoteRefresh;

  
  
  

  
  Future<void> handleMqttEvent(MqttEvent event);

  
  Stream<MqttEvent> get eventStream;

  
  Future<void> processRealTimeEvent(MqttEvent event);
}


--- CONTENT END ---

==========================================

=== FILE: content_report/content_report_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/content_report/content_report_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 3651 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/content_report_models.dart';


abstract class ContentReportRepository {
  
  
  
  
  
  
  
  
  
  
  
  
  Future<Result<String>> reportContent({
    required String reporterId,
    required String contentId,
    required ContentType contentType,
    required ReportCategory category,
    required String reason,
    String? description,
    List<String>? evidenceUrls,
    Map<String, String>? metadata,
  });

  
  
  
  
  
  Future<Result<ContentReportModel>> getContentReport(String reportId);

  
  
  
  
  
  
  
  
  
  
  
  
  Future<Result<ContentReportListResponse>> listContentReports({
    required int page,
    required int pageSize,
    ReportStatus? status,
    ContentType? contentType,
    ReportCategory? category,
    String? reporterId,
    DateTime? fromDate,
    DateTime? toDate,
  });

  
  
  
  
  
  
  
  
  Future<Result<void>> updateContentReportStatus({
    required String reportId,
    required String adminId,
    required ReportStatus status,
    String? resolutionNotes,
  });

  
  
  
  
  
  
  Future<Result<void>> deleteContentReport({
    required String reportId,
    required String adminId,
  });
}


class ContentReportListResponse {
  const ContentReportListResponse({
    required this.reports,
    required this.totalCount,
    required this.page,
    required this.pageSize,
  });

  final List<ContentReportModel> reports;
  final int totalCount;
  final int page;
  final int pageSize;

  
  bool get hasNextPage => page * pageSize < totalCount;

  
  bool get hasPreviousPage => page > 1;

  
  int get totalPages => (totalCount / pageSize).ceil();
}


--- CONTENT END ---

==========================================

=== FILE: dialog_state/dialog_state_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/dialog_state/dialog_state_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1858 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/models/pending_dialog_model.dart';



abstract class DialogStateRepository {
  
  Future<void> savePendingDialog(PendingDialog dialog);

  
  Future<List<PendingDialog>> loadPendingDialogs();

  
  Future<void> removePendingDialog(String dialogId);

  
  Future<void> clearAllPendingDialogs();

  
  Future<bool> hasDialog(String dialogId);

  
  Future<void> updateDialog(PendingDialog dialog);

  
  Future<List<PendingDialog>> getDialogsByType(String type);

  
  Future<List<PendingDialog>> getDialogsByPriority(int minPriority);

  
  Future<void> markDialogShown(String requestId, String requestType);

  
  Future<bool> hasDialogBeenShown(String requestId, String requestType);

  
  Future<void> incrementDialogRetry(String requestId, String requestType);

  
  Future<void> addPendingDialog(
    String requestId,
    String requestType,
    Map<String, dynamic> requestData, {
    DialogPriority priority = DialogPriority.normal,
    DateTime? expiresAt,
  });

  
  Future<Map<String, dynamic>> getDialogMetrics();

  
  Future<void> clearAllDialogState();

  
  Future<void> cleanupExpiredDialogs();
}


--- CONTENT END ---

==========================================

=== FILE: friendship/friend_request_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/friendship/friend_request_repository.dart
Last Modified: Sep  3 15:39:59 2025
File Size: 3913 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';








abstract class FriendRequestRepository {
  
  
  

  
  Future<Result<List<Map<String, dynamic>>>> getPendingFriendRequests();

  
  Future<Result<void>> acceptFriendRequest(String requestId);

  
  Future<Result<void>> declineFriendRequest(String requestId);

  
  Future<Result<Map<String, dynamic>>> getFriendRequest(String requestId);

  
  Future<Result<List<String>>> getFriends();

  
  Future<Result<Map<String, dynamic>>> getRequestStats();

  
  Future<Result<void>> clearOldRequests();

  
  
  
  
  
  
  
  Future<Result<List<Map<String, dynamic>>>> getRequestHistory({
    int page = 0,
    int pageSize = 20,
    String direction = 'both',
    List<String> statuses = const ['pending', 'accepted', 'declined', 'expired'],
  });

  
  
  

  
  Stream<List<Map<String, dynamic>>> get pendingFriendRequestsStream;

  
  Stream<Map<String, dynamic>> get requestStatsStream;

  
  
  

  
  Future<void> handleMqttEvent(MqttEvent event);

  
  Future<void> processFriendRequestEvent(MqttEvent event);

  
  
  

  
  Future<void> initialize();

  
  Future<void> clearCache();

  
  Future<bool> get hasCachedData;

  
  DateTime? get lastRemoteRefresh;

  
  Future<void> refreshFromRemote();

  
  
  

  
  Stream<MqttEvent> get eventStream;

  
  Future<void> processRealTimeEvent(MqttEvent event);
}


--- CONTENT END ---

==========================================

=== FILE: friendship/friendship_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/friendship/friendship_repository.dart
Last Modified: Sep  9 16:07:17 2025
File Size: 7136 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';











abstract class FriendshipRepository {
  
  
  

  
  Future<Result<List<UserModel>>> getFriendList();

  
  Future<Result<UserModel>> getFriend(String friendId);

  
  Future<Result<List<UserModel>>> getFriends();

  
  Future<Result<void>> addFriend(String friendId);

  
  
  
  Future<Result<void>> addMaybeFriend(String userId);

  
  Future<Result<void>> removeFriend(String friendId);

  
  Future<Result<bool>> isFriend(String userId);

  
  Future<Result<bool>> isUserFriend(String userId);

  
  
  

  
  Future<Result<int>> getFriendCount();

  
  Future<Result<int>> getOnlineFriendsCount();

  
  
  

  
  Future<Result<List<UserModel>>> searchFriends(String query);

  
  Future<Result<List<UserModel>>> getFriendsPaginated({
    required int page,
    required int pageSize,
  });

  
  Future<Result<List<UserModel>>> getOnlineFriends();

  
  Future<Result<List<UserModel>>> getFriendsInBubble();

  
  Future<Result<List<UserModel>>> getMutualFriends(String userId);

  
  Future<Result<List<UserModel>>> getRecentlyActiveFriends({
    required int limit,
  });

  
  

  
  Future<Result<List<UserModel>>> getFriendsByStatus(String status);

  
  
  

  
  Future<Result<void>> syncFriendList();

  
  Future<Result<void>> refreshFriends();

  
  Stream<List<UserModel>> get friendListStream;

  
  Stream<int> get onlineFriendsCountStream;

  
  
  

  
  Future<void> handleMqttEvent(MqttEvent event);

  
  Future<void> processFriendshipEvent(MqttEvent event);

  
  
  

  
  
  
  
  
  
  Future<Result<List<UserModel>>> getFormerMembers(String bubbleId, String userId, {
    int page = 0,
    int pageSize = 20,
    DateTime? since,
  });

  
  
  

  
  Future<Result<void>> acceptFriendRequest(String requestId);

  
  Future<Result<void>> declineFriendRequest(String requestId);

  
  Future<Result<List<UserModel>>> getReceivedFriendRequests();

  
  Future<Result<List<UserModel>>> getMaybeFriends();

  
  Future<Result<void>> acceptFriendRequestRelationship(String currentUserId, String otherUserId);

  
  Future<Result<void>> declineFriendRequestRelationship(
    String currentUserId,
    String otherUserId,
    String reason,
  );

  
  
  

  
  Future<void> initialize();

  
  Future<void> clearCache();

  
  Future<bool> get hasCachedData;

  
  DateTime? get lastRemoteRefresh;

  
  Future<void> refreshFromRemote();

  
  
  

  
  Stream<MqttEvent> get eventStream;

  
  Future<void> processRealTimeEvent(MqttEvent event);
}

--- CONTENT END ---

==========================================

=== FILE: image_processing/image_processing_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/image_processing/image_processing_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1764 bytes

--- CONTENT START ---
import 'dart:typed_data';

import 'package:image_picker/image_picker.dart';

import '../../statefulbusinesslogic/core/models/image_processing_result.dart';



abstract class ImageProcessingRepository {
  
  Future<XFile?> pickFromGallery();

  
  Future<XFile?> takePhoto();

  
  Future<Uint8List> processImageFromFile(XFile imageFile);

  
  Future<Uint8List> processImageFromBytes(Uint8List imageBytes);

  
  Future<void> validateImageFile(XFile imageFile);

  
  Future<ImageInfo> getImageInfo(String imagePath);

  
  Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  });
}


class ImageInfo {

  const ImageInfo({
    required this.width,
    required this.height,
    required this.fileSizeBytes,
    required this.format,
  });
  final int width;
  final int height;
  final int fileSizeBytes;
  final String format;

  double get fileSizeMB => fileSizeBytes / (1024 * 1024);

  bool get isSquare => width == height;

  bool get isValidDimensions =>
      width >= 640 &&
      height >= 640 &&
      width <= 8192 &&
      height <= 8192;
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_chat_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_chat_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 779 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/models/chat_message.dart'
    as stateful_models; 

abstract class AbstractChatMessageRepository {
  Future<void> saveMessage(stateful_models.ChatMessage message, String chatId);
  Future<List<stateful_models.ChatMessage>> getMessages(String chatId);
  Stream<List<stateful_models.ChatMessage>> watchMessages(String chatId);
  Future<void> deleteMessage(String messageId, String chatId);
  Future<void> deleteAllMessagesForChat(String chatId);
  Future<void> saveMessages(String chatId, List<stateful_models.ChatMessage> messages);
  Future<List<stateful_models.ChatMessage>> searchMessages(String chatId, String query);
  
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_chat_metadata_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_chat_metadata_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 243 bytes

--- CONTENT START ---
abstract class AbstractChatMetadataRepository {
  Future<void> saveChatMetadata(String chatId, Map<String, dynamic> metadata);
  Future<Map<String, dynamic>?> getChatMetadata(String chatId);
  Future<void> deleteChatMetadata(String chatId);
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_draft_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_draft_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 216 bytes

--- CONTENT START ---
abstract class AbstractDraftMessageRepository {
  Future<void> saveDraftMessage(String chatId, String draftText);
  Future<String?> getDraftMessage(String chatId);
  Future<void> deleteDraftMessage(String chatId);
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_fcm_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_fcm_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 649 bytes

--- CONTENT START ---

class DomainFcmMessage {
  DomainFcmMessage({
    required this.data,
    required this.timestamp,
    this.id,
    this.notificationTitle,
    this.notificationBody,
  });
  final String? id; 
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? notificationTitle;
  final String? notificationBody;
}

abstract class AbstractFcmMessageRepository {
  Future<void> saveFcmMessage(DomainFcmMessage message);
  Future<List<DomainFcmMessage>> getFcmMessages();
  Future<void> deleteAllFcmMessages();
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_profile_picture_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_profile_picture_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1224 bytes

--- CONTENT START ---










abstract class AbstractProfilePictureCacheRepository {
  
  
  Future<String?> getProfilePicturePath(String userId, String imageUrl);
  
  
  Future<void> clearOldCache(Duration maxAge);
  
  
  Future<Map<String, dynamic>> getCacheStats();
  
  
  Future<void> clearAllCache();
  
  
  Future<void> updateLastAccessed(String userId);
  
  
  Future<bool> isProfilePictureCached(String userId);
} 

--- CONTENT END ---

==========================================

=== FILE: local_storage/abstract_unread_count_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/abstract_unread_count_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 372 bytes

--- CONTENT START ---
abstract class AbstractUnreadCountRepository {
  Future<void> saveUnreadCount(String chatId, int count);
  Future<int> getUnreadCount(String chatId);
  Future<void> incrementUnreadCount(String chatId);
  Future<void> clearUnreadCount(String chatId);
  
  Stream<int> watchUnreadCount(String chatId);
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_chat_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_chat_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 5622 bytes

--- CONTENT START ---
import 'package:drift/drift.dart';

import '../../statefulbusinesslogic/core/db/app_database.dart';
import '../../statefulbusinesslogic/core/models/chat_message.dart';
import '../../statefulbusinesslogic/core/models/media_type.dart';
import '../../statefulbusinesslogic/core/models/message_delivery_status.dart';
import 'abstract_chat_message_repository.dart';

class DriftChatMessageRepository extends AbstractChatMessageRepository {
  DriftChatMessageRepository(this._database);
  final AppDatabase _database;

  MessageType _mapMediaTypeToMessageType(MediaType mediaType) {
    switch (mediaType) {
      case MediaType.image:
        return MessageType.image;
      case MediaType.video:
        return MessageType.video;
      case MediaType.audio:
        return MessageType.audio;
      case MediaType.text:
      default:
        return MessageType.text;
    }
  }

  MessageStatus _mapDeliveryStatusToMessageStatus(MessageDeliveryStatus status) {
    switch (status) {
      case MessageDeliveryStatus.sending:
        return MessageStatus.sending;
      case MessageDeliveryStatus.sent:
        return MessageStatus.sent;
      case MessageDeliveryStatus.delivered:
        return MessageStatus.delivered;
      case MessageDeliveryStatus.read:
        return MessageStatus.read;
      case MessageDeliveryStatus.failed:
        return MessageStatus.failed;
    }
  }

  MessageDeliveryStatus _mapMessageStatusToDeliveryStatus(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return MessageDeliveryStatus.sending;
      case MessageStatus.sent:
        return MessageDeliveryStatus.sent;
      case MessageStatus.delivered:
        return MessageDeliveryStatus.delivered;
      case MessageStatus.read:
        return MessageDeliveryStatus.read;
      case MessageStatus.failed:
        return MessageDeliveryStatus.failed;
    }
  }

  ChatMessage _toDomain(DriftChatMessage driftMessage) => ChatMessage(
      id: driftMessage.id,
      content: driftMessage.textContent,
      senderId: driftMessage.senderId,
      timestamp: driftMessage.timestamp, 
      bubbleId: driftMessage.bubbleId,
      type: _mapMediaTypeToMessageType(driftMessage.mediaType),
      status: _mapDeliveryStatusToMessageStatus(driftMessage.deliveryStatus),
      mediaType: driftMessage.mediaType,
      mediaUrl: driftMessage.mediaUrl,
      replyToMessageId: driftMessage.replyToMessageId,
      isEdited: driftMessage.isEdited,
      isDeleted: driftMessage.isDeleted,
    );

  DriftChatMessage _fromDomain(
    ChatMessage domainMessage,
    String chatId,
  ) => DriftChatMessage(
    id: domainMessage.id,
    chatId: chatId,
    textContent: domainMessage.content,
    senderId: domainMessage.senderId,
    timestamp: domainMessage.timestamp, 
    bubbleId: domainMessage.bubbleId,
    conversationId: domainMessage.bubbleId, 
    mediaType: domainMessage.mediaType ?? MediaType.text,
    mediaUrl: domainMessage.mediaUrl,
    replyToMessageId: domainMessage.replyToMessageId,
    isEdited: domainMessage.isEdited,
    isDeleted: domainMessage.isDeleted,
    updatedAt: domainMessage.isEdited ? DateTime.now() : null,
    deliveryStatus: _mapMessageStatusToDeliveryStatus(domainMessage.status),
    isForwarded: false, 
  );



  @override
  Future<void> saveMessage(
    ChatMessage message,
    String chatId,
  ) async {
    final driftMessage = _fromDomain(message, chatId);
    await _database.into(_database.chatMessages).insert(driftMessage);
  }

  @override
  Future<List<ChatMessage>> getMessages(String chatId) async {
    final query =
        _database.select(_database.chatMessages)
          ..where((tbl) => tbl.chatId.equals(chatId))
          ..orderBy([(tbl) => OrderingTerm.asc(tbl.timestamp)]);

    final driftMessages = await query.get();
    return driftMessages.map(_toDomain).toList();
  }

  @override
  Stream<List<ChatMessage>> watchMessages(String chatId) {
    final query =
        _database.select(_database.chatMessages)
          ..where((tbl) => tbl.chatId.equals(chatId))
          ..orderBy([(tbl) => OrderingTerm.asc(tbl.timestamp)]);

    return query.watch().map(
      (driftMessages) => driftMessages.map(_toDomain).toList(),
    );
  }

  @override
  Future<void> deleteMessage(String messageId, String chatId) async {
    await (_database.delete(_database.chatMessages)
      ..where((tbl) => tbl.id.equals(messageId))).go();
  }

  @override
  Future<void> deleteAllMessagesForChat(String chatId) async {
    await (_database.delete(_database.chatMessages)
      ..where((tbl) => tbl.chatId.equals(chatId))).go();
  }

  @override
  Future<void> saveMessages(String chatId, List<ChatMessage> messages) async {
    if (messages.isEmpty) {
      return;
    }
    final driftMessages = messages.map((m) => _fromDomain(m, chatId)).toList();
    
    await _database.batch((batch) {
      batch.insertAllOnConflictUpdate(_database.chatMessages, driftMessages);
    });
  }

  @override
  Future<List<ChatMessage>> searchMessages(String chatId, String query) async {
    final likeQuery = '%${query.toLowerCase()}%';
    final table = _database.chatMessages;
    final driftMessages = await (_database.select(table)
          ..where((tbl) => tbl.chatId.equals(chatId) & tbl.textContent.lower().like(likeQuery))
          ..orderBy([(tbl) => OrderingTerm.desc(tbl.timestamp)]))
        .get();
    return driftMessages.map(_toDomain).toList();
  }
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_chat_metadata_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_chat_metadata_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1186 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/db/app_database.dart';
import 'abstract_chat_metadata_repository.dart';

class DriftChatMetadataRepository implements AbstractChatMetadataRepository {
  DriftChatMetadataRepository({required ChatMetadataDao chatMetadataDao})
    : _chatMetadataDao = chatMetadataDao;
  final ChatMetadataDao _chatMetadataDao;

  @override
  Future<void> saveChatMetadata(
    String chatId,
    Map<String, dynamic> metadata,
  ) async {
    
    
    final driftMetadata = DriftChatMetadata(chatId: chatId, metadata: metadata);
    await _chatMetadataDao.insertOrUpdateChatMetadata(driftMetadata);
  }

  @override
  Future<Map<String, dynamic>?> getChatMetadata(String chatId) async {
    final driftMetadata = await _chatMetadataDao.getChatMetadata(chatId);
    return driftMetadata?.metadata;
  }

  @override
  Future<void> deleteChatMetadata(String chatId) async {
    
    await _chatMetadataDao.deleteChatMetadata(chatId);
  }
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_draft_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_draft_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1032 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/db/app_database.dart';
import 'abstract_draft_message_repository.dart';

class DriftDraftMessageRepository implements AbstractDraftMessageRepository {
  DriftDraftMessageRepository({required DraftMessageDao draftMessageDao})
    : _draftMessageDao = draftMessageDao;
  final DraftMessageDao _draftMessageDao;

  @override
  Future<void> saveDraftMessage(String chatId, String draftText) async {
    if (draftText.isEmpty) {
      
      await _draftMessageDao.deleteDraftMessage(chatId);
    } else {
      await _draftMessageDao.insertOrUpdateDraftMessage(
        DriftDraftMessage(chatId: chatId, draftText: draftText),
      );
    }
  }

  @override
  Future<String?> getDraftMessage(String chatId) async {
    final draft = await _draftMessageDao.getDraftMessage(chatId);
    return draft?.draftText;
  }

  @override
  Future<void> deleteDraftMessage(String chatId) async {
    await _draftMessageDao.deleteDraftMessage(chatId);
  }
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_fcm_message_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_fcm_message_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2501 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/db/app_database.dart';
import 'abstract_fcm_message_repository.dart';

class DriftFcmMessageRepository implements AbstractFcmMessageRepository {
  DriftFcmMessageRepository({required FcmMessageDao fcmMessageDao})
    : _fcmMessageDao = fcmMessageDao;
  final FcmMessageDao _fcmMessageDao;

  
  DomainFcmMessage _toDomain(DriftFcmMessage driftMessage) => DomainFcmMessage(
    
    
    
    id:
        driftMessage.id
            .toString(), 
    data:
        driftMessage
            .data, 
    timestamp: driftMessage.receivedTimestamp,
    notificationTitle: driftMessage.notificationTitle,
    notificationBody: driftMessage.notificationBody,
  );

  
  
  
  DriftFcmMessage _fromDomain(
    DomainFcmMessage domainMessage,
  ) => DriftFcmMessage(
    
    id: 0, 
    messageId:
        domainMessage
            .id, 
    data: domainMessage.data,
    receivedTimestamp: domainMessage.timestamp,
    notificationTitle: domainMessage.notificationTitle,
    notificationBody: domainMessage.notificationBody,
  );
  

  @override
  Future<void> saveFcmMessage(DomainFcmMessage message) async {
    final driftMessage = _fromDomain(message);
    await _fcmMessageDao.insertFcmMessage(driftMessage);
  }

  @override
  Future<List<DomainFcmMessage>> getFcmMessages() async {
    final driftMessages = await _fcmMessageDao.getAllFcmMessages();
    return driftMessages.map(_toDomain).toList();
  }

  @override
  Future<void> deleteAllFcmMessages() async {
    await _fcmMessageDao.deleteAllFcmMessages();
  }
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/drift_unread_count_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/drift_unread_count_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1428 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/db/app_database.dart';
import 'abstract_unread_count_repository.dart';

class DriftUnreadCountRepository implements AbstractUnreadCountRepository {
  DriftUnreadCountRepository({required UnreadCountDao unreadCountDao})
    : _unreadCountDao = unreadCountDao;
  final UnreadCountDao _unreadCountDao;

  @override
  Future<void> saveUnreadCount(String chatId, int count) async {
    await _unreadCountDao.insertOrUpdateUnreadCount(
      DriftUnreadCount(chatId: chatId, count: count),
    );
  }

  @override
  Future<int> getUnreadCount(String chatId) async {
    final unread = await _unreadCountDao.getUnreadCount(chatId);
    return unread?.count ?? 0;
  }

  @override
  Future<void> incrementUnreadCount(String chatId) async {
    final currentCount = await getUnreadCount(chatId);
    await saveUnreadCount(chatId, currentCount + 1);
  }

  @override
  Future<void> clearUnreadCount(String chatId) async {
    await saveUnreadCount(chatId, 0);
  }

  @override
  Stream<int> watchUnreadCount(String chatId) =>
      
      
      
      
      _unreadCountDao
          .watchUnreadCountStream(chatId)
          .map((driftCount) => driftCount?.count ?? 0);
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/local_storage_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/local_storage_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 545 bytes

--- CONTENT START ---
abstract class LocalStorageRepository {
  Future<void> saveString(String key, String value);
  Future<String?> getString(String key);
  Future<void> saveBool(String key, {required bool value});
  Future<bool?> getBool(String key);
  Future<void> saveInt(String key, int value);
  Future<int?> getInt(String key);
  Future<void> saveDouble(String key, double value);
  Future<double?> getDouble(String key);
  Future<void> remove(String key);
  Future<void> clear();
  Future<bool> containsKey(String key);
  Future<List<String>> getAllKeys();
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/secure_storage_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/secure_storage_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2352 bytes

--- CONTENT START ---







abstract class SecureStorageRepository {
  
  Future<void> saveString(String key, String value);

  
  Future<String?> getString(String key);

  
  Future<void> saveBool(String key, {required bool value});

  
  Future<bool?> getBool(String key);

  
  Future<void> saveInt(String key, int value);

  
  Future<int?> getInt(String key);

  
  Future<void> saveDouble(String key, double value);

  
  Future<double?> getDouble(String key);

  
  Future<void> remove(String key);

  
  Future<void> clear();

  
  Future<bool> containsKey(String key);

  
  Future<List<String>> getAllKeys();

  
  Future<void> saveAuthToken(String token);

  
  Future<String?> getAuthToken();

  
  Future<void> removeAuthToken();

  
  Future<void> saveRefreshToken(String token);

  
  Future<String?> getRefreshToken();

  
  Future<void> removeRefreshToken();

  
  Future<void> saveUserSession(Map<String, dynamic> sessionData);

  
  Future<Map<String, dynamic>?> getUserSession();

  
  Future<void> removeUserSession();

  
  Future<void> saveEncryptionKey(String keyId, String key);

  
  Future<String?> getEncryptionKey(String keyId);

  
  Future<void> removeEncryptionKey(String keyId);
}


--- CONTENT END ---

==========================================

=== FILE: local_storage/user_settings_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/local_storage/user_settings_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1044 bytes

--- CONTENT START ---







abstract class UserSettingsRepository {
  
  
  Future<T?> getSetting<T>(String key);

  
  
  Future<bool> saveSetting<T>(String key, T value);

  
  
  Future<bool> deleteSetting(String key);

  
  Future<bool> hasSetting(String key);

  
  
  Future<bool> clearAllSettings();

  
  Future<List<String>> getAllKeys();
}


--- CONTENT END ---

==========================================

=== FILE: notification/notification_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/notification/notification_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1359 bytes

--- CONTENT START ---
import 'dart:async';
import '../../statefulbusinesslogic/core/models/notification_model.dart';


abstract class NotificationRepository {
  
  Future<void> saveNotification(Notification notification);
  
  
  Future<List<Notification>> getNotifications();
  
  
  Future<int> getUnreadCount();
  
  
  Future<void> markAsRead(String notificationId);
  
  
  Future<void> markAllAsRead();
  
  
  Future<void> deleteNotification(String notificationId);
  
  
  Future<void> clearAll();
  
  
  Future<List<Notification>> getNotificationsByCategory(NotificationCategory category);
  
  
  Future<List<Notification>> getNotificationsByType(String type);
  
  
  Stream<List<Notification>> notificationsStream();
  
  
  Stream<int> unreadCountStream();
  
  
  Future<void> markAsUnread(String notificationId);
  
  
  Future<void> deleteAllNotifications();
}


--- CONTENT END ---

==========================================

=== FILE: notification/toast_service.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/notification/toast_service.dart
Last Modified: Sep 14 23:33:48 2025
File Size: 587 bytes

--- CONTENT START ---


abstract class ToastService {
  
  void showErrorToast(String message);

  
  void showWarningToast(String message);

  
  void showInfoToast(String message);

  
  void showSuccessToast(String message);

  
  void showInfo(String title, {String? description});
}


--- CONTENT END ---

==========================================

=== FILE: notification_permission/notification_permission_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/notification_permission/notification_permission_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 208 bytes

--- CONTENT START ---

abstract class NotificationPermissionRepository {
  
  Future<bool> requestSystemPermission();
} 

--- CONTENT END ---

==========================================

=== FILE: presence/presence_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/presence/presence_repository.dart
Last Modified: Sep 11 17:56:48 2025
File Size: 11481 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/typing_indicator.dart';
import '../../statefulbusinesslogic/core/models/user_with_presence.dart';



@Deprecated('Use bool isPresent instead. true = online, false = offline')
enum PresenceStatus {
  online,
  offline,
}

class UserPresenceInfo {

  const UserPresenceInfo({
    required this.userId,
    required this.isPresent,
    required this.lastSeen,
    required this.updatedAt,
    this.statusMessage,
    this.currentActivity,
    this.currentBubbleId,
    this.deviceType = 'unknown',
    this.isTyping = false,
    this.activeSessions = const [],
  });
  final String userId;
  final bool isPresent; 
  final DateTime lastSeen;
  final DateTime updatedAt;
  final String? statusMessage;
  final String? currentActivity;
  final String? currentBubbleId;
  final String deviceType;
  final bool isTyping;
  final List<String> activeSessions;

  
  bool get isOnline => isPresent;
  bool get isOffline => !isPresent;

  @Deprecated('Use isPresent instead')
  PresenceStatus get status => PresenceStatus.online;

  UserPresenceInfo copyWith({
    String? userId,
    bool? isPresent,
    DateTime? lastSeen,
    DateTime? updatedAt,
    String? statusMessage,
    String? currentActivity,
    String? currentBubbleId,
    String? deviceType,
    bool? isTyping,
    List<String>? activeSessions,
  }) => UserPresenceInfo(
      userId: userId ?? this.userId,
      isPresent: isPresent ?? this.isPresent,
      lastSeen: lastSeen ?? this.lastSeen,
      updatedAt: updatedAt ?? this.updatedAt,
      statusMessage: statusMessage ?? this.statusMessage,
      currentActivity: currentActivity ?? this.currentActivity,
      currentBubbleId: currentBubbleId ?? this.currentBubbleId,
      deviceType: deviceType ?? this.deviceType,
      isTyping: isTyping ?? this.isTyping,
      activeSessions: activeSessions ?? this.activeSessions,
    );
}


class TypingStatus {

  const TypingStatus({
    required this.userId,
    required this.bubbleId,
    required this.isTyping, this.channelId,
    this.startedTypingAt,
    this.expiresAt,
  });
  final String userId;
  final String bubbleId;
  final String? channelId;
  final bool isTyping;
  final DateTime? startedTypingAt;
  final DateTime? expiresAt;
}


class PresenceStats {

  const PresenceStats({
    required this.totalUsers,
    required this.onlineUsers,
    required this.awayUsers,
    required this.busyUsers,
    required this.offlineUsers,
    required this.onlinePercentage,
    required this.statusDistribution,
    required this.mostActiveUsers,
    required this.lastUpdated,
  });
  final int totalUsers;
  final int onlineUsers;
  final int awayUsers;
  final int busyUsers;
  final int offlineUsers;
  final double onlinePercentage;
  final Map<String, int> statusDistribution;
  final List<String> mostActiveUsers;
  final DateTime lastUpdated;
}


class PresenceHistoryEntry {

  const PresenceHistoryEntry({
    required this.userId,
    required this.isPresent,
    required this.timestamp,
    required this.deviceType, this.statusMessage,
    this.activity,
  });
  final String userId;
  final bool isPresent;
  final DateTime timestamp;
  final String? statusMessage;
  final String? activity;
  final String deviceType;
}







abstract class PresenceRepository {
  
  
  

  
  Future<Result<UserPresenceInfo?>> getUserPresence(String userId);

  
  Future<Result<UserPresenceInfo>> updateUserPresence({
    required String userId,
    required bool isPresent,
    String? statusMessage,
    String? currentActivity,
    String? currentBubbleId,
    String? deviceType,
  });

  
  Future<Result<Map<String, UserPresenceInfo>>> getBatchPresence(
    List<String> userIds,
  );

  
  Future<Result<UserPresenceInfo>> setUserOnline(
    String userId, {
    String? statusMessage,
  });

  
  Future<Result<UserPresenceInfo>> setUserAway(
    String userId, {
    String? awayMessage,
  });

  
  Future<Result<void>> setUserOffline(String userId);

  
  Future<Result<void>> updateUserActivity(String userId, String activity);

  
  Future<Result<void>> updateTypingStatus(String userId, String conversationId, {required bool isTyping});



  
  
  

  
  Stream<Map<String, UserPresenceInfo>> watchPresenceUpdates(
    List<String> userIds,
  );

  
  Stream<TypingIndicator> watchTypingIndicators(String conversationId);

  
  Stream<Map<String, UserPresenceInfo>> watchFriendsPresence(String userId);

  
  
  Stream<UserPresenceInfo> watchUserPresence(String userId);

  

  
  Future<Result<void>> subscribeToPresenceUpdates({
    required String userId,
    required List<String> targetUserIds,
  });

  
  Future<Result<void>> unsubscribeFromPresenceUpdates({
    required String userId,
    required List<String> targetUserIds,
  });



  
  
  

  
  Future<Result<void>> setTypingStatus({
    required String userId,
    required String bubbleId,
    required bool isTyping, String? channelId,
  });

  
  Future<Result<List<TypingStatus>>> getTypingUsersInBubble(String bubbleId);

  
  Stream<List<TypingStatus>> watchTypingStatus(String bubbleId);

  
  Future<Result<void>> clearTypingStatus(String userId, String bubbleId);

  
  
  

  
  Future<Result<List<UserPresenceInfo>>> getOnlineUsers({
    String? bubbleId,
    int limit = 50,
    int offset = 0,
  });

  
  
  
  
  Future<Result<List<UserWithPresence>>> getOnlineFriends(String userId, {
    int page = 0,
    int pageSize = 20,
  });

  
  
  
  
  Future<Result<List<UserWithPresence>>> getOnlineContacts(String userId, {
    int page = 0,
    int pageSize = 20,
  });

  
  
  
  
  Future<Result<List<UserWithPresence>>> getOnlineBubblers(String bubbleId, {
    int page = 0,
    int pageSize = 20,
  });

  
  Future<Result<bool>> isUserOnline(String userId);

  
  Future<Result<Map<String, bool>>> getMultipleUserOnlineStatus(
    List<String> userIds,
  );

  
  Future<Result<int>> getOnlineUsersCount(String bubbleId);

  
  
  

  
  Future<Result<List<PresenceHistoryEntry>>> getPresenceHistory({
    required String userId,
    DateTime? startTime,
    DateTime? endTime,
    int limit = 100,
  });

  
  Future<Result<PresenceStats>> getPresenceStats({
    String? bubbleId,
    String period = 'daily', 
  });

  
  Future<Result<List<String>>> getMostActiveUsers({
    Duration timeWindow = const Duration(hours: 24),
    int limit = 10,
  });

  
  
  

  
  Future<Result<void>> sendHeartbeat(String userId);

  
  Future<Result<void>> updateLastSeen(String userId);

  
  Future<Result<void>> registerDeviceSession({
    required String userId,
    required String deviceId,
    required String deviceType,
    String? deviceName,
  });

  
  Future<Result<void>> unregisterDeviceSession({
    required String userId,
    required String deviceId,
  });

  
  Future<Result<void>> endDeviceSession(String userId, String deviceId);

  
  Future<Result<List<String>>> getUserActiveSessions(String userId);

  

  
  
  

  
  Future<Result<UserWithPresence?>> getUserWithPresence(String userId);

  
  Future<Result<List<UserWithPresence>>> getUsersWithPresence(
    List<String> userIds,
  );

  
  Future<Result<void>> initializePresence(String userId);

  
  Future<Result<void>> cleanupPresence(String userId);

  
  Future<Result<void>> refreshPresence(String userId);
}


--- CONTENT END ---

==========================================

=== FILE: profile_picture/profile_picture_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/profile_picture/profile_picture_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1162 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/models/profile_picture_result.dart' as result_model;



abstract class ProfilePictureRepository {
  
  
  Future<result_model.ProfilePictureResult> pickFromGallery();

  
  
  Future<result_model.ProfilePictureResult> takePhoto();

  
  
  Future<bool> removeProfilePicture(String imageUrl);

  
  
  Future<result_model.ProfilePictureResult> validateImage(String imagePath);

  
  
  Future<String?> uploadLocalProfilePicture(String localPath);
}


--- CONTENT END ---

==========================================

=== FILE: report/report_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/report/report_repository.dart
Last Modified: Sep 15 12:55:29 2025
File Size: 1985 bytes

--- CONTENT START ---
import '../../../statefulbusinesslogic/core/error/result.dart';







abstract class ReportRepository {
  
  
  Future<Result<String>> submitBugReport({
    required String title,
    required String description,
    String? userEmail,
  });

  
  
  Future<Result<String>> submitFeatureRequest({
    required String title,
    required String description,
    String? userEmail,
  });

  
  
  Future<Result<String>> submitFeedback({
    required String title,
    required String description,
    String? userEmail,
  });

  
  
  Future<Result<String>> submitUserReport({
    required String reporterId,
    required String reportedUserId,
    required String reason,
    required String description,
    required String category,
    Map<String, String>? evidence,
  });

  
  
  Future<Result<String>> reportUser({
    required String reporterId,
    required String reportedUserId,
    required String reason,
    required String description,
    required String category,
  });

  
  
  Future<Result<void>> removeContact({
    required String userId,
    required String contactId,
  });
}


--- CONTENT END ---

==========================================

=== FILE: resilience/resilience_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/resilience/resilience_repository.dart
Last Modified: Sep  5 18:20:17 2025
File Size: 6366 bytes

--- CONTENT START ---





abstract class ResilienceRepository {
  
  Future<T> executeWithResilience<T>(
    Future<T> Function() operation, {
    required String operationName,
    ResilienceConfig? config,
    Map<String, dynamic>? metadata,
  });

  
  Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    required String operationName,
    RetryConfig? config,
  });

  
  Future<T> executeWithCircuitBreaker<T>(
    Future<T> Function() operation, {
    required String operationName,
    CircuitBreakerConfig? config,
  });

  
  Future<T> executeWithIdempotency<T>(
    Future<T> Function() operation, {
    required String operationName,
    required String idempotencyKey,
    Duration? cacheDuration,
  });

  
  Future<T> executeGrpcOperation<T>(
    Future<T> Function() operation, {
    required String operationName,
    GrpcResilienceConfig? config,
  });

  
  Future<T> executeNetworkOperation<T>(
    Future<T> Function() operation, {
    required String operationName,
    NetworkResilienceConfig? config,
  });

  
  Future<T> executeStorageOperation<T>(
    Future<T> Function() operation, {
    required String operationName,
    StorageResilienceConfig? config,
  });

  
  CircuitBreakerState getCircuitBreakerState(String operationName);

  
  void resetCircuitBreaker(String operationName);

  
  Map<String, dynamic> getResilienceStats();

  
  void resetAll();
}


class ResilienceConfig {
  const ResilienceConfig({
    this.retry = const RetryConfig(),
    this.circuitBreaker = const CircuitBreakerConfig(),
    this.enableIdempotency = false,
    this.idempotencyCacheDuration = const Duration(minutes: 5),
    this.enableMetrics = true,
  });

  final RetryConfig retry;
  final CircuitBreakerConfig circuitBreaker;
  final bool enableIdempotency;
  final Duration idempotencyCacheDuration;
  final bool enableMetrics;
}


class RetryConfig {
  const RetryConfig({
    this.maxAttempts = 3,
    this.initialDelay = const Duration(milliseconds: 100),
    this.maxDelay = const Duration(seconds: 30),
    this.backoffMultiplier = 2.0,
    this.jitterFactor = 0.1,
    this.shouldRetry,
  });

  final int maxAttempts;
  final Duration initialDelay;
  final Duration maxDelay;
  final double backoffMultiplier;
  final double jitterFactor;
  final bool Function(Object error)? shouldRetry;

  
  Duration calculateDelay(int attempt) {
    if (attempt <= 0) {
      return initialDelay;
    }

    final exponentialDelay = initialDelay.inMilliseconds *
        (backoffMultiplier * attempt);
    final clampedDelay = exponentialDelay.clamp(
      initialDelay.inMilliseconds.toDouble(),
      maxDelay.inMilliseconds.toDouble(),
    );

    
    final jitter = clampedDelay * jitterFactor * (0.5 - (DateTime.now().millisecondsSinceEpoch % 1000) / 1000);

    return Duration(milliseconds: (clampedDelay + jitter).round());
  }
}


class CircuitBreakerConfig {
  const CircuitBreakerConfig({
    this.failureThreshold = 5,
    this.successThreshold = 3,
    this.timeout = const Duration(seconds: 60),
    this.minimumThroughput = 10,
  });

  final int failureThreshold;
  final int successThreshold;
  final Duration timeout;
  final int minimumThroughput;
}


class GrpcResilienceConfig extends ResilienceConfig {
  const GrpcResilienceConfig({
    super.retry = const RetryConfig(),
    super.circuitBreaker = const CircuitBreakerConfig(),
    super.enableIdempotency = true,
    this.enableDeadlineHandling = true,
    this.deadlineTimeout = const Duration(seconds: 30),
  });

  final bool enableDeadlineHandling;
  final Duration deadlineTimeout;
}


class NetworkResilienceConfig extends ResilienceConfig {
  const NetworkResilienceConfig({
    super.retry = const RetryConfig(
      initialDelay: Duration(milliseconds: 200),
    ),
    super.circuitBreaker = const CircuitBreakerConfig(
      failureThreshold: 3,
      timeout: Duration(seconds: 30),
    ),
    this.enableConnectivityCheck = true,
  });

  final bool enableConnectivityCheck;
}


class StorageResilienceConfig extends ResilienceConfig {
  const StorageResilienceConfig({
    super.retry = const RetryConfig(
      maxAttempts: 2,
      initialDelay: Duration(milliseconds: 500),
    ),
    super.circuitBreaker = const CircuitBreakerConfig(
      failureThreshold: 3,
      timeout: Duration(seconds: 15),
    ),
    this.enableStorageHealthCheck = true,
  });

  final bool enableStorageHealthCheck;
}


enum CircuitBreakerState {
  closed,
  open,
  halfOpen,
}


class ResilienceResult<T> {
  const ResilienceResult({
    required this.data,
    required this.metadata,
  });

  final T data;
  final ResilienceMetadata metadata;
}


class ResilienceMetadata {
  const ResilienceMetadata({
    required this.operationName,
    required this.attempts,
    required this.totalDuration,
    required this.circuitBreakerState,
    this.fromCache = false,
    this.retryDelays = const [],
  });

  final String operationName;
  final int attempts;
  final Duration totalDuration;
  final CircuitBreakerState circuitBreakerState;
  final bool fromCache;
  final List<Duration> retryDelays;
}


--- CONTENT END ---

==========================================

=== FILE: security/security_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/security/security_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 246 bytes

--- CONTENT START ---

abstract class SecurityRepository {
  
  
  Future<bool> checkPasswordBreach(String password);
}




--- CONTENT END ---

==========================================

=== FILE: social_analytics/social_analytics_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/social_analytics/social_analytics_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2110 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';





abstract class SocialAnalyticsRepository {
  
  Future<Result<Map<String, dynamic>>> getEnhancedProfile({
    required String userId,
  });

  
  Future<Result<Map<String, dynamic>>> getProfileAnalytics({
    required String targetUserId,
  });

  
  
  Future<Result<List<UserModel>>> getMutualFriends(String targetUserId);

  
  
  Future<Result<List<UserModel>>> getMutualContacts(String targetUserId);

  
  
  Future<Result<List<String>>> getMutualFriendsIds({
    required String targetUserId,
  });

  
  
  Future<Result<List<String>>> getMutualContactsIds({
    required String targetUserId,
  });

  
  Future<Result<List<Map<String, dynamic>>>> getCommonBubbles({
    required String targetUserId,
  });

  
  Future<Result<Map<String, dynamic>>> getConnectionStrength({
    required String targetUserId,
  });

  
  Future<Result<Map<String, dynamic>>> getSocialGraph();

  
  Future<Result<Map<String, dynamic>>> getEngagementMetrics();
} 

--- CONTENT END ---

==========================================

=== FILE: storage/storage_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/storage/storage_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 1633 bytes

--- CONTENT START ---
import 'dart:typed_data';

abstract class StorageRepository {
  
  Future<String> uploadFile({
    required String bucketName,
    required String fileName,
    required Uint8List fileData,
    required String contentType,
    Map<String, String>? metadata,
  });

  
  Future<Uint8List> downloadFile({
    required String bucketName,
    required String fileName,
  });

  
  Future<void> deleteFile({
    required String bucketName,
    required String fileName,
  });

  
  Future<String> getPresignedUrl({
    required String bucketName,
    required String fileName,
    Duration expiry = const Duration(hours: 1),
  });

  
  Future<bool> fileExists({
    required String bucketName,
    required String fileName,
  });

  
  Future<List<String>> listFiles({required String bucketName, String? prefix});

  
  Future<Map<String, String>> getFileMetadata({
    required String bucketName,
    required String fileName,
  });

  
  Future<void> updateFileMetadata({
    required String bucketName,
    required String fileName,
    required Map<String, String> metadata,
  });

  
  Future<void> copyFile({
    required String sourceBucketName,
    required String sourceFileName,
    required String destinationBucketName,
    required String destinationFileName,
  });
}


--- CONTENT END ---

==========================================

=== FILE: sync/sync_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/sync/sync_repository.dart
Last Modified: Sep 15 13:30:21 2025
File Size: 6240 bytes

--- CONTENT START ---





library;

import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/sync_models.dart';











abstract class SyncRepository {
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  Future<Result<SyncResult>> performInitialSync([SyncConfig? config]);

  
  
  
  
  SyncStatus get currentStatus;

  
  
  
  
  Stream<SyncStatus> get statusStream;

  
  
  
  
  SyncMetrics? get lastSyncMetrics;

  
  
  
  
  Future<Result<bool>> cancelSync();

  
  bool get isSyncing;

  
  
  
  
  DateTime? get lastSuccessfulSync;

  
  
  
  
  
  
  
  
  
  
  
  Future<Result<SyncResult>> getSyncData(
    String userId, {
    DateTime? lastSyncTimestamp,
    List<String>? dataTypes,
    String? deviceId,
  });

  
  
  
  
  
  
  
  
  
  
  Future<Result<List<SyncConflict>>> pushSyncData(
    String userId,
    Map<String, dynamic> syncData, {
    String? deviceId,
  });

  
  
  
  
  
  
  
  
  Future<Result<SyncStatusInfo>> getSyncStatus(String userId);

  
  
  
  
  
  
  
  
  
  Future<Result<SyncResult>> forceSyncRefresh(String userId, {String? deviceId});

  
  
  
  
  
  
  
  
  
  
  
  
  
  Future<Result<void>> queueLocalMutation({
    required String userId,
    required String dataType,
    required String entityId,
    required String operation,
    required Map<String, dynamic> data,
    Map<String, dynamic>? metadata,
  });

  
  
  
  
  
  
  
  Future<Result<List<dynamic>>> getPendingMutations(String userId);
}


--- CONTENT END ---

==========================================

=== FILE: user/user_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/user/user_repository.dart
Last Modified: Sep  7 20:15:25 2025
File Size: 2936 bytes

--- CONTENT START ---
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/relationship_status.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';

abstract class UserRepository {
  
  Future<UserModel?> getUserById(String userId);

  
  
  Future<UserModel?> getCurrentUser();

  
  Future<Result<UserModel>> getCurrentUserSafe();

  String? get currentUserId;

  Future<UserModel?> getUser(String userId);
  Future<UserModel?> getEnhancedUser(String userId);
  Future<Map<String, dynamic>> getUserInfo(String userId);
  Future<List<UserModel>> getUsers(List<String> userIds);
  Future<List<UserModel>> getAllUsers();
  Future<List<UserModel>> getFriends(String userId);

  Future<void> createUser(UserModel user);
  Future<void> updateUser(UserModel user);

  Future<List<UserModel>> findUsers({
    String? firstName,
    String? lastName,
    String? email,
    
  });

  
  Future<Map<String, dynamic>> getGroupInfo(String groupId);

  Future<List<UserModel>> getMutualFriends(String userId1, String userId2);
  Future<List<UserModel>> getMutualContacts(String userId1, String userId2);

  
  
  
  
  
  
  
  
  Future<Result<RelationshipStatus>> getUserRelationshipStatus(
    String currentUserId,
    String targetUserId,
  );

  Future<void> addFriend(String userId, String friendId);

  
  Future<bool> sendContactRequest({required String fromUserId, required String toUserId});
  Future<bool> acceptContactRequest({required String fromUserId, required String toUserId});
  Future<bool> rejectContactRequest({required String fromUserId, required String toUserId});

  
  Future<bool> removeContact({required String userId, required String contactId});
  Future<Result<void>> blockUser(String targetUserId);
  Future<bool> unblockUser({required String userId, required String targetUserId});
  Future<Result<void>> reportUser(String targetUserId, String reason);

  
  Future<bool> deleteAccount({required String userId});
  Future<bool> deactivateAccount({required String userId, required String reason});
}


--- CONTENT END ---

==========================================

=== FILE: webrtc/webrtc_repository.dart ===
File Type: dart
Full Path: /Users/<USER>/Hopen/hopen/lib/repositories/webrtc/webrtc_repository.dart
Last Modified: Sep  1 21:05:56 2025
File Size: 2720 bytes

--- CONTENT START ---
import 'package:flutter_webrtc/flutter_webrtc.dart';

abstract class WebRTCRepository {
  
  Future<MediaStream> initializeUserMedia({
    bool audio = true,
    bool video = true,
  });

  
  Future<RTCPeerConnection> createPeerConnection({
    required Map<String, dynamic> configuration,
    Map<String, dynamic>? constraints,
  });

  
  Future<RTCSessionDescription> createOffer({
    required RTCPeerConnection peerConnection,
    Map<String, dynamic>? constraints,
  });

  
  Future<RTCSessionDescription> createAnswer({
    required RTCPeerConnection peerConnection,
    Map<String, dynamic>? constraints,
  });

  
  Future<void> setLocalDescription({
    required RTCPeerConnection peerConnection,
    required RTCSessionDescription description,
  });

  
  Future<void> setRemoteDescription({
    required RTCPeerConnection peerConnection,
    required RTCSessionDescription description,
  });

  
  Future<void> addIceCandidate({
    required RTCPeerConnection peerConnection,
    required RTCIceCandidate candidate,
  });

  
  Future<List<StatsReport>> getStats({
    required RTCPeerConnection peerConnection,
    MediaStreamTrack? track,
  });

  
  Future<bool> switchCamera({required MediaStreamTrack videoTrack});

  
  Future<void> setTrackEnabled({
    required MediaStreamTrack track,
    required bool enabled,
  });

  
  Future<MediaStream> startScreenShare();

  
  Future<void> stopScreenShare({required MediaStream screenStream});

  
  Future<void> dispose({
    required RTCPeerConnection peerConnection,
    required MediaStream localStream,
  });

  
  Future<List<MediaDeviceInfo>> enumerateDevices();

  
  Future<void> setAudioOutput({required String deviceId});

  
  Stream<RTCPeerConnectionState> onConnectionStateChange({
    required RTCPeerConnection peerConnection,
  });

  
  Stream<RTCIceCandidate> onIceCandidate({
    required RTCPeerConnection peerConnection,
  });

  
  Stream<void> onNegotiationNeeded({required RTCPeerConnection peerConnection});

  
  Stream<MediaStream> onTrack({required RTCPeerConnection peerConnection});

  
  void disposeAll();
}


--- CONTENT END ---

==========================================


# SUMMARY
# =======
Total files processed: 49
File types found:
    49  dart
