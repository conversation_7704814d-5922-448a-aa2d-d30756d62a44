# Hopen Flutter Application Architecture
*Production-Ready Clean Architecture with Riverpod 3.0 State Management*

**Last Updated:** December 2024
**Version:** 4.0.0
**Status:** ✅ **Production-Ready**

---

## Table of Contents

1. [Overview](#overview)
2. [Clean Architecture Layers](#clean-architecture-layers)
3. [Directory Structure](#directory-structure)
4. [State Management with Riverpod 3.0](#state-management-with-riverpod-30)
5. [Dependency Injection](#dependency-injection)
6. [Request Dialog System](#request-dialog-system)
7. [Real-time Communication](#real-time-communication)
8. [Data Flow](#data-flow)
9. [Error Handling](#error-handling)
10. [Authentication & Authorization](#authentication--authorization)
11. [Performance Optimizations](#performance-optimizations)
12. [Security Implementation](#security-implementation)
13. [Backend Integration](#backend-integration)
14. [Testing Strategy](#testing-strategy)

---

## Overview

The Hopen Flutter application follows **Clean Architecture** principles with **Riverpod 3.0** for state management and dependency injection. The architecture ensures separation of concerns, testability, and maintainability while providing a robust foundation for a production social networking application with real-time features.

### Core Principles

- **Clean Architecture**: Clear separation between presentation, business logic, and data layers
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Single Responsibility**: Each class has one reason to change
- **Open/Closed Principle**: Open for extension, closed for modification
- **Interface Segregation**: Clients shouldn't depend on interfaces they don't use
- **Dependency Injection**: Dependencies are injected rather than created internally
- **Event-Driven Architecture**: Real-time communication through MQTT and WebSocket
- **Offline-First Design**: Local caching with automatic synchronization

### Key Features

- **Real-time Communication**: MQTT integration for instant messaging, presence, and notifications
- **Request Dialog System**: Comprehensive handling of contact, friend, and bubble requests
- **Offline-First**: Local caching with automatic synchronization and conflict resolution
- **Type Safety**: Comprehensive error handling with sealed classes and Result patterns
- **Performance**: Optimized rendering with custom shaders and efficient state management
- **Security**: End-to-end encryption, secure authentication flows, and data protection
- **Accessibility**: Full accessibility support with semantic widgets
- **Scalability**: Modular architecture supporting microservices backend

## System Architecture Overview

```mermaid
graph TD
    subgraph "Flutter App Architecture"
        subgraph "Presentation Layer"
            A[Pages] --> B[Widgets]
            A --> C[Router]
            A --> D[Theme]
        end

        subgraph "Business Logic Layer (Riverpod 3.0)"
            E[AsyncNotifier Controllers] --> F[Domain Models]
            E --> G[Use Cases]
            E --> H[Error Handling]
            E --> I[State Management]
        end

        subgraph "Repository Layer (Interfaces)"
            J[Auth Repository] --> K[Bubble Repository]
            J --> L[Chat Repository]
            J --> M[Contact Repository]
            J --> N[Other Repositories]
        end

        subgraph "Data Layer (Implementations)"
            O[Repository Implementations] --> P[Data Sources]
            O --> Q[Services]
            O --> R[Local Storage]
            O --> S[Mappers]
        end

        subgraph "Dependency Injection"
            T[Core Module] --> U[Auth Module]
            T --> V[Services Module]
            T --> W[User Module]
        end
    end

    A -.->|uses| E
    E -.->|depends on| J
    J -.->|implemented by| O
    O -.->|injected via| T
```

---

## Clean Architecture Layers

### 1. Presentation Layer (`lib/presentation/`)

The presentation layer handles UI components and user interactions:

- **Pages**: Screen-level widgets that represent complete views
- **Widgets**: Reusable UI components and custom widgets
- **Router**: Navigation management with GoRouter
- **Theme**: Consistent styling and theming
- **Constants**: UI-related constants and configurations

### 2. Business Logic Layer (`lib/statefulbusinesslogic/`)

The business logic layer manages application state and business rules:

- **Riverpod Controllers**: AsyncNotifier-based state management
- **Core Models**: Domain entities and value objects
- **Use Cases**: Business logic operations
- **Error Handling**: Comprehensive error management with Result patterns
- **Core Services**: Business logic services (logging, validation, etc.)

### 3. Repository Layer (`lib/repositories/`)

The repository layer defines contracts for data access:

- **Abstract Interfaces**: Clean contracts for data operations
- **Domain-Specific Repositories**: Auth, Bubble, Chat, Contact, etc.
- **Result Patterns**: Type-safe error handling
- **Dependency Inversion**: High-level modules depend on abstractions

### 4. Data Layer (`lib/data/`)

The data layer implements repository interfaces and handles external data:

- **Repository Implementations**: Concrete implementations of repository interfaces
- **Data Sources**: Remote (gRPC) and local (SQLite) data sources
- **Services**: External service integrations (MQTT, Firebase, etc.)
- **Mappers**: Data transformation between layers
- **Models**: Data transfer objects and API models

---

## State Management with Riverpod

The application uses Riverpod's AsyncNotifier pattern for comprehensive state management with type safety and performance optimizations.

### Core State Management Patterns

#### AsyncNotifier Controllers
```dart
@Riverpod(keepAlive: true)
class AuthController extends _$AuthController {
  @override
  FutureOr<AuthState> build() async => await _checkAuthStatus();

  Future<void> login(String email, String password) async {
    state = const AsyncLoading();
    try {
      final result = await ref.read(authRepositoryModuleProvider).login(
        email: email,
        password: password
      );

      if (result.isSuccess) {
        state = AsyncData(AuthAuthenticated(user: result.data));
        await _initializePresenceService(result.data.id);
      } else {
        state = AsyncData(AuthError(error: result.error));
      }
    } catch (e, stackTrace) {
      state = AsyncError(e, stackTrace);
    }
  }
}
```

#### State Classes with Sealed Classes
```dart
sealed class AuthState {
  const AuthState();
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthAuthenticated extends AuthState {
  const AuthAuthenticated({required this.user});
  final User user;
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthError extends AuthState {
  const AuthError({required this.error});
  final Failure error;
}
```

### Key State Management Features

#### 1. Type-Safe State Management
- **Sealed Classes**: Exhaustive pattern matching for state handling
- **AsyncValue**: Built-in loading, error, and data states
- **Code Generation**: Automatic provider generation with riverpod_generator

#### 2. Dependency Management
- **Provider Composition**: Complex dependencies through provider composition
- **Scoped Providers**: Different scopes for different lifecycle needs
- **Auto-Dispose**: Automatic cleanup of unused providers

#### 3. Real-time State Updates
- **Stream Providers**: Real-time data streams from MQTT and WebSocket
- **State Synchronization**: Automatic synchronization across app instances
- **Optimistic Updates**: Immediate UI updates with rollback on failure

#### 4. Performance Optimizations
- **Selective Rebuilds**: Only rebuild widgets that depend on changed state
- **State Caching**: Intelligent caching of expensive computations
- **Memory Management**: Automatic disposal of unused state

---

## Dependency Injection

The application uses a modular dependency injection system with Riverpod providers organized into domain-specific modules.

### Module Structure

```dart
// Core Module - Base services and utilities
@riverpod
LoggingService loggingServiceModule(Ref ref) => LoggingService();

@riverpod
CentralizedConfigService centralizedConfigServiceModule(Ref ref) =>
    CentralizedConfigService();

// Auth Module - Authentication services
@riverpod
AuthRepository authRepositoryModule(Ref ref) =>
    CachedAuthRepositoryImpl(
      remoteDataSource: ref.read(authRemoteDataSourceModuleProvider),
      localDataSource: ref.read(authLocalDataSourceModuleProvider),
    );

// Services Module - External service integrations
@riverpod
RealTimeServiceManager realTimeServiceManagerModule(Ref ref) =>
    RealTimeServiceManager(
      kratosAuthService: ref.read(kratosAuthServiceModuleProvider),
      authRemoteDataSource: ref.read(authRemoteDataSourceModuleProvider),
    );
```

### Module Organization

#### Core Module (`core_module.dart`)
- **Logging Service**: Centralized logging with different levels
- **Config Service**: Environment-specific configuration management
- **Validation Service**: Input validation and business rule enforcement

#### Services Module (`services_module.dart`)
- **Real-time Service Manager**: MQTT and WebSocket management
- **Firebase Service**: Push notifications and analytics
- **Storage Service**: File upload and download operations

#### Auth Module (`auth_module.dart`)
- **Auth Repository**: Authentication operations
- **Kratos Service**: Ory Kratos integration
- **Token Service**: JWT token management

#### User Module (`user_module.dart`)
- **User Repository**: User profile management
- **Profile Picture Service**: Avatar handling
- **Presence Service**: User activity tracking

## Technology Stack with HTTP/3

### HTTP/3 & Protocol Layer
- **Primary Protocol**: HTTP/3 with QUIC transport for 50-70% faster connections
- **Fallback Protocol**: HTTP/2 for compatibility with older clients
- **Connection Features**: 0-RTT connection establishment, connection migration
- **Security**: Mandatory TLS 1.3 with modern cipher suites
- **Edge Computing**: Global HTTP/3 edge locations for optimal performance

### Backend & API Communication
- **Backend**: Go 1.23.0 microservices (Gin + NATS JetStream)
- **API Client**: HTTP/3-optimized type-safe client with protocol detection
- **Authentication**: Ory Stack with TLS 1.3 integration for identity management
- **Database**: PostgreSQL with Kubernetes-managed storage & sqlc
- **File Storage**: MinIO with HTTP/3 CDN integration (S3-compatible)

### Real-time Communication with Enhanced Reliability
- **Messaging Protocol**: MQTT 5 with enhanced reliability (QoS 1, auto-reconnect, 60s keep-alive)
- **MQTT Broker**: EMQX with QUIC transport support and JWT authentication
- **WebRTC**: Enhanced with QUIC optimization and shared UDP stack
- **MQTT Client**: mqtt5_client with comprehensive error handling and connection recovery
- **Connection Multiplexing**: Shared UDP stack for improved bandwidth utilization
- **Message Persistence**: Unprocessed message storage with retry mechanisms
- **Background Processing**: WorkManager integration for reliable background task handling

### Local Storage & Caching
- **Local Database**: Drift (SQLite wrapper for Flutter)
- **Cache Management**: Valkey for backend caching and session management
- **CDN Integration**: MinIO-based CDN 

### Mobile Development
- **Framework**: Flutter 3.x with null safety and HTTP/3 client
- **State Management**: BLoC pattern with flutter_bloc (enhanced CallBloc)
- **Dependency Injection**: Custom ServiceLocator (lightweight replacement for GetIt)
- **Navigation**: GoRouter for type-safe routing
- **Push Notifications**: Firebase Cloud Messaging (FCM)

### Development & Deployment
- **CI/CD**: GitHub Actions
- **Deployment**: Docker containers
- **Hosting**: OVHcloud VPS
- **Load Balancing**: Load balancing with geo-proximity routing
- **Monitoring**: Prometheus and PostHog analytics
- **Logging**: Structured logging with dart:developer (LoggingService)

## Clean Architecture Implementation

### Domain-Driven Design

The application follows Domain-Driven Design principles with rich domain models:

#### Domain Entities
- **BubbleEntity**: Core bubble domain entity with comprehensive business logic
- **BubbleMemberEntity**: Member management with status tracking
- **UserModel**: User domain model with profile management

#### Value Objects
- **BubbleId**: Type-safe bubble identifier
- **UserId**: Type-safe user identifier  
- **BubbleName**: Validated bubble name
- **MemberCapacity**: Validated member capacity

#### Domain Services
- **BubbleUseCases**: Complex business operations
- **ValidationServices**: Business rule validation
- **LoggingService**: Centralized structured logging using dart:developer

### Layer Details

#### Directory Structure

The application is organized into four distinct layers, each with its own directory and responsibilities:

```
lib/
├── presentation/              # UI Layer
│   ├── pages/                 # Screen implementations
│   │   ├── auth/              # Authentication flows
│   │   ├── bubble/            # Bubble management
│   │   ├── call/              # WebRTC call interface
│   │   ├── chat/              # Real-time messaging
│   │   ├── contacts/          # Contact management
│   │   └── profile/           # User profiles
│   ├── widgets/               # Reusable UI components
│   │   ├── common/            # Common widgets
│   │   ├── requests/          # Request dialog widgets
│   │   └── forms/             # Form components
│   └── router/                # Navigation configuration
│
├── statefulbusinesslogic/     # Business Logic Layer
│   ├── bloc/                  # BLoC implementations
│   │   ├── auth/              # Authentication BLoCs
│   │   ├── bubble/            # Bubble-related BLoCs
│   │   ├── call/              # Call state management
│   │   ├── chat/              # Chat BLoCs
│   │   ├── contact/           # Contact management
│   │   ├── notification/      # Notification handling
│   │   ├── theme/             # Theme management
│   │   └── user_profile/      # User profile BLoCs
│   └── core/                  # Shared business logic
│       ├── models/            # Domain models + PendingDialog model
│       ├── services/          # Core business services + persistence services
│       │   ├── request_dialog_state_manager.dart      # Dialog state persistence
│       │   ├── mqtt_message_persistence.dart          # MQTT reliability layer
│       │   ├── background_request_processor.dart      # Background task handling
│       │   ├── request_state_restoration_service.dart # State restoration
│       │   ├── enhanced_dialog_manager.dart           # Unified dialog coordination
│       │   ├── notification_orchestrator.dart         # Enhanced notification handling
│       │   └── logging_service.dart                   # Structured logging
│       ├── error/             # Error handling with Result<T> pattern
│       └── usecases/          # Domain use cases
│
├── repositories/              # Repository Interfaces
│   ├── auth/                  # Authentication contracts
│   ├── bubble/                # Bubble domain contracts
│   ├── call/                  # Call management contracts
│   ├── chat/                  # Chat contracts
│   ├── contact/               # Contact management contracts
│   ├── notification/          # Notification contracts
│   ├── storage/               # File storage contracts
│   └── user/                  # User management contracts
│
├── data/                  # Implementation Layer
│   ├── repositories/          # Repository implementations
│   ├── datasources/           # External data sources
│   ├── services/              # External service integrations
│   │   ├── api/               # Go REST service layer
│   │   ├── storage/           # MinIO storage service
│   │   ├── mqtt_only_real_time_service.dart  # Enhanced MQTT service (QoS 1, reliability)
│   │   ├── app_context_manager.dart          # App lifecycle + request recovery
│   │   ├── notification_service_fcm.dart     # FCM push notifications
│   │   ├── webrtc/            # WebRTC service
│   │   ├── assets/            # Asset optimization service
│   │   └── performance/       # Performance monitoring
│   ├── mappers/               # DTO-Entity mapping
│   └── local_storage/         # Drift database access
│   ├── utils/               # Shared provider utilities
│   ├── websocket/           # WebSocket & MQTT abstractions
│   ├── exceptions/          # Provider-level exceptions
│   ├── theme/               # Design tokens & theme extensions
│   └── config/              # Provider-specific configuration
│
├── di/                        # Dependency Injection
│   ├── modules/               # Modular DI configuration
│   │   ├── core_module.dart   # Core services
│   │   ├── auth_module.dart   # Authentication services
│   │   ├── chat_module.dart   # Chat services
│   │   ├── database_module.dart # Database services
│   │   └── performance_module.dart # Performance services
│   ├── services/              # DI utilities
│   └── injection_container_refactored.dart # Main DI setup
│
├── config/                    # Configuration
    ├── environments/          # Environment-specific configs
    └── app_config.dart        # Main app configuration

```

### Dependency Rules

The architecture enforces strict unidirectional dependencies:

╭────────────────────────────────────────────────────────╮
│                 Dependency Flow                        │
│                                                        │
│  Presentation                                          │
│       │                                                │
│       │ can import                                     │
│       ▼                                                │
│  Statefulbusinesslogic (Riverpod)                      │
│       │                                                │
│       │ can import                                     │
│       ▼                                                │
│  Repository (Interfaces)                               │
│       │                                                │
│       │ can import                                     │
│       ▼                                                │
│   Provider (Implementations)                           │
│       │                                                │
│       │ can import                                     │
│       ▼                                                │
│  External Packages                                     │
╰────────────────────────────────────────────────────────╯

### Layer Responsibilities

1. **Presentation Layer** (`lib/presentation/`)
   - UI components and screens
   - Event handling and user interaction
   - Navigation with GoRouter
   - UI-specific state management
   - Error boundaries and user-friendly error displays
   - **Dependencies**: Only Statefulbusinesslogic layer

2. **Statefulbusinesslogic Layer** (`lib/statefulbusinesslogic/`)
   - Business logic implementation with BLoC pattern
   - Domain models with rich business logic (BubbleEntity, UserModel, PendingDialog)
   - State management and event handling
   - Domain use cases for complex business operations
   - Drift database configuration
   - Comprehensive error handling with Result<T> pattern
   - **Enhanced Services**: Request dialog persistence, MQTT message reliability, background processing, state restoration
   - **Dependencies**: Only Repository layer

3. **Repository Layer** (`lib/repositories/`)
   - Abstract interfaces for data operations
   - Domain-specific contracts
   - Local storage abstractions
   - Clean separation from implementation details
   - **Dependencies**: Only Data layer

4. **Provider Layer** (`lib/data/`)
   - Concrete implementations of repositories
   - Clean Architecture mappers for DTO-to-Entity conversion
   - Go REST API client integration
   - MQTT messaging service
   - MinIO storage service
   - Drift-based local storage implementations
   - External service integrations
   - Low-level error handling and retry mechanisms
   - **Dependencies**: External services and packages only

## Backend Architecture (Go Microservices)

The backend follows a microservices architecture built with Gin, employing PostgreSQL, ArangoDB, Cassandra, NATS JetStream, and EMQX.

### Core Services

- **🔐 auth**: JWT authentication, OAuth, MQTT auth
- **👤 user**: User management, profiles, settings
- **🫧 bubble**: Bubble creation, invitations, voting, lifecycle management
- **💬 chat**: Real-time messaging with MQTT integration
- **📞 call**: WebRTC video/audio calls
- **👥 friendship**: Social connections, friend requests
- **📱 media**: File upload/download, MinIO integration
- **🔔 notification**: Push notifications, in-app notifications

### Key Features

- **Type-safe APIs**: Automatic request/response validation
- **Built-in Database**: PostgreSQL with automatic migrations
- **Real-time Support**: WebSocket and MQTT integration
- **Authentication**: Ory provider integration
- **Monitoring**: Built-in observability and tracing
- **Microservices**: Independent, scalable services

## Data Flow Architecture

```mermaid
graph TD
    A[Flutter App] --> B[API Gateway (Kong/Nginx)]
    B --> C[Auth Service]
    B --> D[User Service]
    B --> E[Bubble Service]
    B --> F[Chat Service]
    B --> G[Call Service]
    B --> H[Friendship Service]
    B --> I[Media Service]
    B --> J[Notification Service]
    
    C --> K[Ory Auth]
    D --> L[PostgreSQL]
    E --> L
    F --> M[EMQX MQTT]
    G --> N[WebRTC]
    H --> L
    I --> O[MinIO Storage]
    J --> P[Firebase FCM]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#ccf,stroke:#333,stroke-width:2px
    style K fill:#9fc,stroke:#333,stroke-width:2px
    style L fill:#9fc,stroke:#333,stroke-width:2px
    style M fill:#9fc,stroke:#333,stroke-width:2px
    style N fill:#9fc,stroke:#333,stroke-width:2px
    style O fill:#9fc,stroke:#333,stroke-width:2px
    style P fill:#9fc,stroke:#333,stroke-width:2px
```

## Recent Architectural Improvements

### Request Dialog Persistence & Recovery System (January 2025)

#### **Comprehensive Request Dialog Reliability**
The Hopen Flutter app now implements a production-ready request dialog persistence and recovery system that ensures **zero request dialog loss** across all failure scenarios.

#### **Core Architecture Components**

**🔧 Persistence Services:**
- **RequestDialogStateManager**: Persistent storage using SharedPreferences with priority-based ordering, expiration handling, and comprehensive metrics
- **MqttMessagePersistence**: MQTT message reliability layer with unprocessed message storage, retry mechanisms, and automatic cleanup
- **RequestStateRestorationService**: Flutter state restoration with RestorationBucket integration for cross-app-restart recovery
- **EnhancedDialogManager**: Unified dialog coordination with duplicate prevention, priority ordering, and comprehensive error handling

**⚙️ Background Processing:**
- **BackgroundRequestProcessor**: WorkManager integration with periodic checks (15min/30min/daily cycles)
- **Background Task Types**: `check_pending_requests`, `process_unprocessed_messages`, `cleanup_expired_data`
- **Local Notification Scheduling**: For missed requests when app is backgrounded

**🔄 Integration Enhancements:**
- **AppContextManager**: Enhanced app lifecycle handling with automatic request recovery on app resume
- **MqttOnlyRealTimeService**: Improved reliability settings (QoS 1, auto-reconnect, 60s keep-alive)
- **NotificationOrchestrator**: Enhanced with persistent state handling and request-type identification
- **MyApp**: State restoration integration with RestorationMixin

#### **Critical Edge Cases Solved**
| **Scenario** | **Solution** | **Implementation** |
|--------------|--------------|-------------------|
| **App crash during dialog display** | State restoration + persistent storage | RequestStateRestorationService + RequestDialogStateManager |
| **Network issues during MQTT event** | QoS 1 + message persistence | MqttMessagePersistence + enhanced MQTT config |
| **App killed while dialog is open** | Background tasks + app lifecycle recovery | BackgroundRequestProcessor + AppContextManager |
| **MQTT connection failure** | Auto-reconnect + persistent sessions | Enhanced MqttOnlyRealTimeService |
| **Background app termination** | WorkManager + notification scheduling | BackgroundRequestProcessor |
| **System-level app recovery** | RestorationManager integration | RequestStateRestorationService |
| **Duplicate dialog prevention** | Active dialog tracking + state checking | EnhancedDialogManager |
| **Request state synchronization** | Database-driven UI state | RequestDialogStateManager |

#### **Production-Ready Features**
- ✅ **Comprehensive Testing**: Integration test suite covering all scenarios
- ✅ **Performance Optimized**: Efficient caching and background processing
- ✅ **Memory Management**: Proper cleanup and resource management
- ✅ **Error Handling**: Comprehensive logging and error recovery
- ✅ **Metrics & Monitoring**: Dialog state metrics and restoration analytics
- ✅ **Four-Layer Compliance**: Strict adherence to clean architecture principles

### Bubble Model Refactoring (January 2025)

#### Before: Legacy BubbleModel
- Simple data class with basic properties
- Limited business logic
- Manual validation
- Inconsistent error handling

#### After: Enhanced BubbleEntity
- Rich domain entity with comprehensive business logic
- Value objects for type safety (BubbleId, BubbleName, MemberCapacity)
- Comprehensive validation using Result<T> pattern
- Enhanced error handling with specific error types
- Clean architecture mappers for DTO-to-Entity conversion

#### Key Improvements
- **Domain-Driven Design**: Rich business logic in domain entities
- **Type Safety**: Value objects prevent invalid states
- **Error Handling**: Consistent Result<T> pattern throughout
- **Clean Architecture**: Proper separation of concerns
- **Validation**: Comprehensive business rule validation

### Clean Architecture Implementation

#### Repository Pattern
```dart
// Repository Interface (Domain Layer)
abstract class BubbleRepository {
  Future<Result<BubbleEntity>> getBubbleById(BubbleId bubbleId);
  Future<Result<BubbleEntity>> createBubble({
    required BubbleName name,
    required MemberCapacity capacity,
  });
}

// Repository Implementation (Infrastructure Layer)
class BubbleRepositoryImpl implements BubbleRepository {
  final HopenApiDataSource _remoteDataSource;
  
  @override
  Future<Result<BubbleEntity>> getBubbleById(BubbleId bubbleId) async {
    try {
      final apiBubble = await _remoteDataSource.getBubble(bubbleId.value);
      return apiBubble.toDomain(); // Using mapper
    } on Exception catch (e) {
      return Result.failure(UnexpectedError(message: e.toString()));
    }
  }
}
```

#### Clean Architecture Mappers
```dart
// DTO-to-Entity Mapping
extension ApiBubbleMapper on ApiBubble {
  Result<BubbleEntity> toDomain() {
    return BubbleEntity.create(
      id: id,
      name: name,
      capacity: maxMembers ?? 5,
      members: const [],
      createdAt: createdAt ?? DateTime.now(),
      endDate: expiresAt,
      status: _mapLifecycleStatus(status),
    );
  }
}
```

## Error Handling Strategy

### Result<T> Pattern
```dart
// Consistent error handling throughout the application
sealed class Result<T> {
  const Result();
  
  bool get isSuccess => this is Success<T>;
  bool get isFailure => this is Failure<T>;
  
  T get data => (this as Success<T>).data;
  AppError get error => (this as Failure<T>).error;
}

class Success<T> extends Result<T> {
  final T data;
  const Success(this.data);
}

class Failure<T> extends Result<T> {
  final AppError error;
  const Failure(this.error);
}
```

### Error Types
- **ValidationError**: Business rule violations
- **NetworkError**: API communication issues
- **AuthenticationError**: Authentication failures
- **BubbleAccessDeniedError**: Bubble-specific access errors
- **UnexpectedError**: Unexpected system errors

## Testing Strategy

### Unit Tests
- Domain entity business logic
- Value object validation
- Use case implementations
- Mapper functionality

### Integration Tests
- Repository implementations
- API client integration
- Database operations
- MQTT messaging

### Widget Tests
- UI component behavior
- BLoC state management
- Navigation flows
- Error handling

## Performance Considerations

### Frontend Optimization
- **BLoC Pattern**: Efficient state management
- **Drift Database**: Fast local storage with type safety
- **Image Caching**: Optimized image loading
- **Lazy Loading**: On-demand data loading

### Backend Optimization
- **Go (Gin + NATS)**: High-performance microservices
- **PostgreSQL**: Optimized database queries
- **MQTT**: Efficient real-time messaging
- **MinIO**: Scalable file storage

## Security Implementation

### Authentication & Authorization
- **Ory Auth**: Secure authentication
- **JWT Tokens**: Stateless authentication
- **Role-based Access**: Granular permissions

### Data Protection
- **Input Validation**: Comprehensive validation at all layers
- **Encrypted Storage**: Secure local data storage
- **HTTPS/TLS**: Encrypted communication
- **File Security**: Secure file upload/download

## Deployment Architecture

### Containerization
```yaml
# docker-compose.yml structure
services:
  hopen-backend:
    build: .
    ports:
      - "4000:4000"
    environment:
      - DATABASE_URL=postgresql://...
    
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=hopen
    
  emqx:
    image: emqx/emqx:latest
    ports:
      - "1883:1883"
      - "8083:8083"
    
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
```

### CI/CD Pipeline
1. **Code Quality**: Linting, formatting, static analysis
2. **Testing**: Unit tests, integration tests, widget tests
3. **Build**: Flutter build for multiple platforms
4. **Deploy**: Docker container deployment to OVHcloud VPS

This architecture ensures the Hopen application is maintainable, scalable, testable, and follows industry best practices for mobile application development with a robust backend infrastructure.
