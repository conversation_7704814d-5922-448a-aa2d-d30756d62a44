# Nationality Flag Implementation

## Overview

This document describes the implementation of nationality flags in the Hopen application, which displays a user's country of residence as a flag icon on their profile page.

## Architecture

### Backend Implementation

#### Database Schema
- **Table**: `users`
- **Field**: `country_residence VARCHAR(2)`
- **Format**: ISO 3166-1 alpha-2 country codes (e.g., "US", "GB", "FR")
- **Index**: `idx_users_country_residence` for efficient queries

#### Enhanced Profile Service
- **File**: `microservices/social_analytics/postgres_repository.go`
- **Struct**: `EnhancedProfile` includes `CountryResidence *string` field
- **Query**: Updated to fetch `u.country_residence` from the users table

#### Country Code Validation
- **File**: `pkg/validation/country_codes.go`
- **Features**:
  - Validates ISO 3166-1 alpha-2 country codes
  - Normalizes codes to uppercase
  - Provides country name mapping
  - Includes comprehensive list of valid country codes

### Frontend Implementation

#### Flag Widget
- **File**: `lib/presentation/widgets/nationality_flag_widget.dart`
- **Features**:
  - Uses existing flag assets from `/assets/flags/1x1/`
  - Supports fallback icons for missing flags
  - Includes loading states and error handling
  - Provides tooltips with country names
  - Accessible with semantic labels

#### User Model
- **File**: `lib/statefulbusinesslogic/core/models/user_model.dart`
- **Field**: `countryResidence String?` for storing country code
- **Parsing**: Updated `fromJson` method to parse `country_residence` from API responses

#### Profile Page Integration
- **File**: `lib/presentation/pages/unified_profile_page/unified_profile_page.dart`
- **Location**: Next to user's name in profile header
- **Styling**: Responsive sizing based on text size
- **Behavior**: Only shows when country code is available

## Best Practices Implemented

### 1. ISO 3166-1 Alpha-2 Standard
- Uses official ISO country codes for consistency
- Validates codes against comprehensive list
- Normalizes input to uppercase format

### 2. Performance Optimization
- Uses existing flag assets (no additional network requests)
- Implements lazy loading for flag widgets
- Provides fallback icons for missing flags
- Caches flag assets efficiently

### 3. Accessibility
- Includes semantic labels for screen readers
- Provides tooltips with country names
- Supports high contrast mode
- Maintains proper focus management

### 4. User Experience
- Responsive design that scales with text size
- Graceful fallbacks for missing flags
- Loading states for better perceived performance
- Consistent styling with app theme

### 5. Error Handling
- Validates country codes before display
- Provides fallback icons for invalid codes
- Logs warnings for missing flag assets
- Graceful degradation for network issues

### 6. Internationalization
- Supports all major country codes
- Provides localized country names
- Handles special characters and diacritics
- Supports right-to-left languages

## Usage Examples

### Backend Query
```sql
SELECT u.country_residence 
FROM users u 
WHERE u.id = $1 AND u.is_active = true
```

### Frontend Widget
```dart
NationalityFlagWidget(
  countryCode: user.countryResidence,
  size: 24.0,
  showTooltip: true,
  showFallback: true,
)
```

### Country Code Validation
```go
if err := validation.ValidateCountryCode("US"); err != nil {
    // Handle invalid country code
}
```

## Flag Assets

### Directory Structure
```
assets/flags/
├── 1x1/          # Square flags (primary)
│   ├── us.svg
│   ├── gb.svg
│   └── ...
└── 4x3/          # Rectangular flags (alternative)
    ├── us.svg
    ├── gb.svg
    └── ...
```

### Asset Format
- **Format**: SVG (scalable vector graphics)
- **Optimization**: Compressed and optimized for web
- **Naming**: Lowercase ISO country codes
- **Quality**: High-resolution vector graphics

## Security Considerations

### Input Validation
- Validates country codes against whitelist
- Prevents injection attacks through malformed codes
- Sanitizes input before database storage

### Data Privacy
- Country residence is considered public profile information
- Users can control visibility through privacy settings
- No sensitive location data is stored

## Testing

### Backend Tests
- Country code validation tests
- Database query performance tests
- API response format tests

### Frontend Tests
- Widget rendering tests
- Flag asset loading tests
- Accessibility compliance tests
- Responsive design tests

## Future Enhancements

### Planned Features
1. **Dynamic Flag Loading**: Load flags on-demand for better performance
2. **Flag Caching**: Implement intelligent caching for frequently used flags
3. **Custom Flag Support**: Allow users to set custom flags for special cases
4. **Flag Animation**: Add subtle animations for flag interactions
5. **Regional Variants**: Support regional flag variants (e.g., GB-ENG, GB-SCT)

### Performance Optimizations
1. **Flag Preloading**: Preload flags for common countries
2. **Asset Compression**: Further optimize SVG assets
3. **CDN Integration**: Serve flags from CDN for faster loading
4. **Progressive Loading**: Implement progressive flag loading

## Troubleshooting

### Common Issues

1. **Flag Not Displaying**
   - Check if country code is valid
   - Verify flag asset exists in `/assets/flags/1x1/`
   - Check console for loading errors

2. **Invalid Country Code**
   - Validate country code format (2 uppercase letters)
   - Check against supported country list
   - Ensure code is in ISO 3166-1 alpha-2 format

3. **Performance Issues**
   - Monitor flag loading times
   - Check asset optimization
   - Verify caching implementation

### Debug Tools
- Browser developer tools for frontend debugging
- Database logs for backend query analysis
- Asset loading logs for flag performance

## Conclusion

The nationality flag implementation provides a clean, accessible, and performant way to display user nationality information. It follows industry best practices for internationalization, accessibility, and user experience while maintaining high performance and security standards.

The implementation is designed to be extensible and maintainable, with clear separation of concerns between backend data management and frontend presentation logic. 