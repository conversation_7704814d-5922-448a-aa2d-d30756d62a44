# Dialog Widgets Technical Documentation

## Overview

Hopen uses a consistent set of dialog widgets across the application to handle various user interactions, particularly for requests and notifications. These dialogs share a common design language, animation patterns, and interaction models to provide a cohesive user experience.

This document describes the implementation details, UI components, and interaction patterns across all dialog widgets, with a focus on the request-related dialogs.

## Shared Components and Utilities

### ColorTransitionController

- **Location**: `lib/presentation/utils/color_transition_controller.dart`
- **Purpose**: Centralizes the color animation logic used across multiple dialog widgets, providing consistent behavior and appearance throughout the app.
- **Key Features**:
  - Manages a timer for cycling through colors
  - Provides the current active color
  - Generates glow effects for text
  - Handles proper cleanup when no longer needed
  - Provides utility methods for responsive font sizing and spacing

- **Implementation**:
  ```dart
  class ColorTransitionController extends ChangeNotifier {
    // Standard Hopen gradient colors
    static const List<Color> hopenGradientColors = [...];

    // Standard gradient stops
    static const List<double> hopenGradientStops = [...];

    // Current color index and timer
    Timer? _colorChangeTimer;
    int _currentColorIndex = 0;

    // Methods for color animation and utilities
    Color get currentColor => ...;
    void startColorLoop() { ... }
    List<Shadow> getGlowEffect() { ... }
    static double getTitleSize(BuildContext context) { ... }
    static double getSubtitleSize(BuildContext context) { ... }
    static double getDialogVerticalSpacing(BuildContext context) { ... }
  }
  ```

## Request Dialog Widgets

All request dialogs follow a consistent pattern:
- Non-dismissible (require explicit user action)
- Animated color transition for the title
- Profile picture display
- Accept/Decline buttons
- Confirmation dialogs for declining
- Success messages for accepting

### 1. Contact Request Dialog

- **Location**: `lib/presentation/widgets/requests/contact_request_dialog.dart`
- **Purpose**: Appears when another user sends a request to become a contact with the current user.
- **Key Components**:
  - Title: "Contact request"
  - Message: "[RequesterName] wants to connect with you"
  - Timestamp display
  - Accept/Decline buttons
  - Success message: "New contact added!"

- **Usage**:
  ```dart
  import 'package:hopen/presentation/widgets/requests/contact_request_dialog.dart';

  // Then in your code:
  ContactRequestDialog.show(
    context,
    requesterId: 'user-123',
    requesterName: 'John Smith',
    requesterUsername: 'johnsmith',
    requesterProfilePicUrl: 'https://example.com/profile.jpg',
    requestTimestamp: DateTime.now(),
  );
  ```

- **Return Value**: `Future<bool?>` - `true` if accepted, `false` if declined, `null` if dismissed.

### 2. Bubble Start Request Dialog

- **Location**: `lib/presentation/widgets/requests/bubble_start_request_dialog.dart`
- **Purpose**: Appears when another contact sends a request to the current user, asking them to start a new bubble together.
- **Key Components**:
  - Title: "New bubble request"
  - Message: "[RequesterName] wants to start a bubble with you"
  - Timestamp display
  - Accept/Decline buttons
  - Success message: "New bubble created!"

- **Usage**:
  ```dart
  import 'package:hopen/presentation/widgets/requests/bubble_start_request_dialog.dart';

  // Then in your code:
  BubbleStartRequestDialog.show(
    context,
    requesterId: 'user-456',
    requesterName: 'Alex Johnson',
    requesterUsername: 'alexj',
    requesterProfilePicUrl: 'https://example.com/profile.jpg',
    requestTimestamp: DateTime.now(),
  );
  ```

- **Return Value**: `Future<bool?>` - `true` if accepted, `false` if declined, `null` if dismissed.

### 3. Bubble Join Request Dialog

- **Location**: `lib/presentation/widgets/requests/bubble_join_request_dialog.dart`
- **Purpose**: Appears when another user requests to join a bubble that the current user is a member of.
- **Key Components**:
  - Title: "Bubble request"
  - Message: "[RequesterName] wants to join "[BubbleName]""
  - Timestamp display
  - Accept/Decline buttons
  - Success message: "Request accepted" or "Member added"

- **Usage**:
  ```dart
  import 'package:hopen/presentation/widgets/requests/bubble_join_request_dialog.dart';

  // Then in your code:
  BubbleJoinRequestDialog.show(
    context,
    requesterId: 'user-123',
    requesterName: 'Michael Brown',
    requesterUsername: 'michaelb',
    requesterProfilePicUrl: 'https://example.com/profile.jpg',
    requestTimestamp: DateTime.now(),
    bubbleId: 'bubble-456',
    bubbleName: 'Weekend Hiking Group',
  );
  ```

- **Return Value**: `Future<bool?>` - `true` if accepted, `false` if declined, `null` if dismissed.

### 4. Bubble Invite Request Dialog

- **Location**: `lib/presentation/widgets/requests/bubble_invite_request_dialog.dart`
- **Purpose**: Appears when another contact invites the current user to join their bubble.
- **Key Components**:
  - Title: "Bubble invitation"
  - Message: "[InviterName] invites you to join "[BubbleName]""
  - Timestamp display
  - Accept/Decline buttons
  - Success message: "You have joined "[BubbleName]"!" or "Invitation accepted"

- **Usage**:
  ```dart
  import 'package:hopen/presentation/widgets/requests/bubble_invite_request_dialog.dart';

  // Then in your code:
  BubbleInviteRequestDialog.show(
    context,
    inviterId: 'user-789',
    inviterName: 'Sarah Miller',
    inviterUsername: 'sarahm',
    inviterProfilePicUrl: 'https://example.com/profile.jpg',
    inviteTimestamp: DateTime.now(),
    bubbleId: 'bubble-123',
    bubbleName: 'Movie Night Group',
  );
  ```

- **Return Value**: `Future<bool?>` - `true` if accepted, `false` if declined, `null` if dismissed.

### 5. Friends Choice Dialog

- **Location**: `lib/presentation/widgets/requests/friends_choice_dialog.dart`
- **Purpose**: Appears when a bubble has popped (ended), allowing the user to select which former bubble members they want to become friends with.
- **Key Components**:
  - Title: "Your bubble has popped!"
  - Heart image display
  - Message: "It's time to choose your new friends."
  - Scrollable list of former bubble members with selection capability
  - Confirmation button
  - Confirmation dialog for partial selections
  - Integration with `FriendSelectionBloc` for state management

- **Usage**:
  ```dart
  import 'package:hopen/presentation/widgets/requests/friends_choice_dialog.dart';

  // Then in your code:
  FriendsChoiceDialog.show(
    context,
    bubbleId: 'bubble-456',
    bubbleName: 'Weekend Hiking Group',
    formerMembers: [/* List of BubbleMember objects */],
  );
  ```

- **Return Value**: `Future<void>` - No return value as selections are handled through the BLoC.

- **Unique Features**:
  - Uses a list of selectable member cards instead of simple Accept/Decline buttons
  - Each member card has a selection animation with glow effect
  - Integrates with `FriendSelectionBloc` for managing the selection state
  - Shows a confirmation dialog if not all members are selected
  - Includes explanatory text about what happens after selection

## Implementation Details

### Common Structure

Most dialog widgets follow this common structure:
1. A `StatefulWidget` class with required parameters
2. A static `show` method that handles displaying the dialog
3. A state class that manages the UI and interactions
4. Methods for building the UI components
5. Methods for handling accept/decline actions
6. Methods for showing success messages and confirmation dialogs

The `FriendsChoiceDialog` follows a similar structure but with some key differences:
1. It integrates with `FriendSelectionBloc` for state management
2. It uses a `BlocProvider.value` in its `show` method to provide the BLoC
3. It implements a list of selectable items instead of simple accept/decline buttons
4. It includes a confirmation dialog for partial selections
5. It doesn't return a value directly but instead dispatches events to the BLoC

### Shared UI Elements

- **Profile Picture Display**: All dialogs use the same profile picture display logic, with fallback to initials when no image is available.
- **Button Styling**: Consistent button styling across all dialogs, with blue for accept and gray for decline.
- **Animation Effects**: All dialogs use the `ColorTransitionController` for title animations.
- **Success Messages**: All success messages use the same styling and auto-close after 3 seconds.
- **Selection UI**: The `FriendsChoiceDialog` implements a unique selection UI with animated cards that glow when selected, while still using the same `ColorTransitionController` for the title animation.

### Integration with Notification System

These dialog widgets are designed to be triggered by the notification system. When a notification of the appropriate type is received, the corresponding dialog can be shown to the user.

## Usage Guidelines

1. **When to Use**:
   - Use these dialogs when a user action requires explicit confirmation
   - Show them in response to real-time events (e.g., WebSocket notifications)

2. **Customization**:
   - Avoid modifying the core UI structure to maintain consistency
   - Customize text content to match the specific use case

3. **Error Handling**:
   - All dialogs include error message display capability
   - In production, connect to appropriate BLoCs or services for error handling

## Related Documentation

- For more information about the notification system, see [Notifications Documentation](notifications.md)
- For details about the bubble system, see [Bubble System Documentation](bubble.md)
- For information about contacts management, see [Contacts Documentation](contacts.md)
