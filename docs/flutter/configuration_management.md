# Configuration Management

This document describes the enhanced configuration management system for the Hopen Flutter app, which replaces the previous `String.fromEnvironment` approach with environment-specific configuration files.

## Overview

The new configuration system provides:

- **Environment-specific configuration files** (YAML format)
- **Type-safe configuration models** with validation
- **Build-time configuration loading** with proper error handling
- **Environment variable substitution** for sensitive values
- **Consistent caching strategies** across all repositories

## Configuration Files

Configuration files are located in the `config/` directory:

- `development.yaml` - Development environment settings
- `staging.yaml` - Staging environment settings  
- `production.yaml` - Production environment settings
- `test.yaml` - Test environment settings

### Configuration Structure

Each configuration file contains the following sections:

```yaml
environment:
  name: "development"
  flavor: "dev"

network:
  host_ip:
    default: "localhost"
    emulator_host: "********"
    docker_host: "*********"

grpc:
  port: 50051
  use_tls: false
  timeout_seconds: 30
  file_upload_timeout_minutes: 5

minio:
  endpoint: ""
  port: 9000
  access_key: "minioadmin"
  secret_key: "minioadmin"
  use_ssl: false
  default_bucket: "hopen-dev"
  region: "us-east-1"
  connection_timeout_seconds: 30

mqtt:
  port: 1883
  use_ssl: false
  username: "dev_user"
  password: "dev_password"
  client_id_prefix: "hopen_dev"
  keep_alive_seconds: 60
  connection_timeout_seconds: 30
  auto_reconnect: true

cache:
  default_ttl_minutes: 30
  stale_period_minutes: 5
  max_cache_objects: 1000
  profile_pictures:
    max_objects: 200
    stale_period_days: 7

logging:
  level: "debug"
  enable_console: true
  enable_file: true
  max_file_size_mb: 10
  max_files: 5

features:
  enable_analytics: false
  enable_crash_reporting: false
  enable_performance_monitoring: false
  enable_debug_tools: true
  enable_mock_data: true
```

## Environment Variable Substitution

Sensitive values can be provided via environment variables using the `${VAR_NAME}` syntax:

```yaml
minio:
  access_key: "${PRODUCTION_MINIO_ACCESS_KEY}"
  secret_key: "${PRODUCTION_MINIO_SECRET_KEY}"
```

## Usage

### Initialization

Initialize the configuration service before using any configuration values:

```dart
import 'package:hopen/data/services/config/centralized_config_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize configuration service
  await CentralizedConfigService().initialize();
  
  runApp(MyApp());
}
```

### Accessing Configuration

```dart
final configService = CentralizedConfigService();

// Check environment
if (configService.isDevelopment) {
  // Development-specific logic
}

// Access gRPC configuration
final grpcConfig = configService.grpcConfig;
print('gRPC host: ${grpcConfig.host}:${grpcConfig.port}');

// Access cache configuration
final cacheConfig = configService.cacheConfig;
final ttl = cacheConfig.defaultTtl;
```

## Build System Integration

### Using Makefile

The project includes a Makefile with convenient commands:

```bash
# Setup development configuration
make config-dev

# Build for different environments
make build-dev
make build-staging
make build-prod

# Validate configuration
make validate-config ENV=production

# Run app with specific environment
make run-dev
make run-staging
```

### Manual Configuration Setup

```bash
# Copy configuration to assets
dart run scripts/build_config.dart development --copy-config

# Validate configuration
dart run scripts/build_config.dart production --validate

# Clean generated files
dart run scripts/build_config.dart development --clean
```

### Flutter Build Commands

```bash
# Development build
flutter build apk --debug --dart-define=ENVIRONMENT=development

# Production build
flutter build apk --release --dart-define=ENVIRONMENT=production
```

## Caching Strategy Standardization

All cached repositories now extend `BaseCachedRepository` and use consistent caching patterns:

### Cache Configuration

Cache settings are centralized in configuration files:

```yaml
cache:
  default_ttl_minutes: 30        # Cache time-to-live
  stale_period_minutes: 5        # Stale-while-revalidate threshold
  max_cache_objects: 1000        # Maximum cached objects
```

### Repository Implementation

Cached repositories implement the following pattern:

```dart
class CachedExampleRepositoryImpl extends BaseCachedRepository
    implements ExampleRepository {
  
  @override
  String get repositoryTag => 'CachedExampleRepository';

  @override
  Duration get cacheTtl => configService.cacheConfig.defaultTtl;

  @override
  Duration get stalePeriod => configService.cacheConfig.stalePeriod;

  @override
  Future<bool> checkCachedData() async {
    // Check if repository has cached data
  }

  @override
  Future<void> onRefreshFromRemote() async {
    // Refresh data from remote source
  }

  @override
  Future<void> onCacheInvalidation(String entityId, {String? entityType}) async {
    // Handle cache invalidation
  }
}
```

### Cache-First Pattern

Use the `executeCacheFirst` method for consistent caching behavior:

```dart
Future<Result<List<Item>>> getItems() async {
  return await executeCacheFirst<Result<List<Item>>>(
    cacheKey: 'items_list',
    cacheOperation: () async {
      // Try to get from cache
      final cached = await dao.getItems();
      return cached.isNotEmpty ? Result.success(cached) : null;
    },
    remoteOperation: () async {
      // Fetch from remote
      return await remoteDataSource.getItems();
    },
    cacheUpdate: (result) async {
      // Update cache with fresh data
      if (result.isSuccess) {
        await dao.insertItems(result.data);
      }
    },
  );
}
```

## Environment-Specific Requirements

### Development
- Relaxed validation
- Debug logging enabled
- Mock services available
- Hot reload support

### Staging
- Requires staging-specific credentials
- TLS enabled for external services
- Analytics and crash reporting enabled
- Test features available

### Production
- Strict validation of all required environment variables
- TLS/SSL required for all external connections
- Performance optimizations enabled
- Debug tools disabled

### Test
- Shorter timeouts for faster test execution
- Mock services enabled
- Minimal logging
- Animations disabled

## Migration from String.fromEnvironment

The old `String.fromEnvironment` approach has been replaced with the new configuration system:

### Before
```dart
static const String host = String.fromEnvironment('GRPC_HOST', defaultValue: 'localhost');
static const int port = int.fromEnvironment('GRPC_PORT', defaultValue: 50051);
```

### After
```dart
final config = CentralizedConfigService().grpcConfig;
final host = config.host;
final port = config.port;
```

## Validation

Configuration validation occurs at multiple levels:

1. **Build-time validation** - Checks configuration file syntax and required values
2. **Runtime validation** - Validates configuration when service initializes
3. **Environment-specific validation** - Ensures environment requirements are met

## Best Practices

1. **Always initialize** the configuration service before accessing any configuration values
2. **Use environment variables** for sensitive values in staging/production
3. **Validate configuration** in CI/CD pipelines before deployment
4. **Keep configuration files** in version control (except sensitive values)
5. **Use the Makefile** for consistent build processes across environments
6. **Extend BaseCachedRepository** for all cached repositories
7. **Use consistent cache TTL** values across similar data types

## Troubleshooting

### Configuration Not Found
- Ensure configuration files exist in the `config/` directory
- Check that the correct environment is specified
- Verify assets are properly included in `pubspec.yaml`

### Environment Variables Not Substituted
- Ensure environment variables are set before running the app
- Check variable names match exactly (case-sensitive)
- Verify the `${VAR_NAME}` syntax is correct

### Cache Issues
- Check that repositories extend `BaseCachedRepository`
- Verify cache configuration values are reasonable
- Use repository health monitoring to debug cache behavior

### Build Failures
- Run `make validate-config ENV=<environment>` to check configuration
- Ensure all required environment variables are set for production builds
- Check that configuration files are valid YAML
