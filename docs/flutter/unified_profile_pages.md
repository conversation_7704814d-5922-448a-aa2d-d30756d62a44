# Unified Profile Pages Technical Documentation

## Overview

Hopen provides detailed profile pages for different types of connections: Friends, Contacts, and Bubble Members (Bubblers). These pages share a common design language while offering context-specific functionality appropriate to the relationship type. This document describes the implementation details, UI components, and interaction patterns across all profile page variants.

## Common Components Across Profile Pages

### Core Elements

All profile pages share these core UI elements:

1. **Gradient Background**: Uses the standard `GradientBackground` widget for the `ContactProfilePage`, and `BubblerProfilePage`. `FriendProfilePage` uses the same gradient as the `BubbleStatusCard`.
2. **App Bar**: Standard app bar with a back button and optional actions (like <PERSON><PERSON>, Block, Report).
    * Title displays `@username` if available, otherwise the user's name. Font size is responsive.
    * The `more_vert` icon opens a `PopupMenuButton` with context-specific actions (e.g., Unfriend only appears on `FriendProfilePage`).
3. **Profile Picture Area**: A large `Image.network` widget occupying the top 45% of screen height. Includes a vertical gradient fade (`<PERSON>rMask` with `BlendMode.dstIn`) at the bottom for a smooth transition.
    * Displays a placeholder icon (`Icons.person` or `Icons.broken_image`) during loading or on error.
4. **Content Block**: Positioned starting at 40% screen height (overlapping the profile picture area slightly) via `Transform.translate`. Contains:
    * **Name Row**: Displays the user's name prominently. Font size is responsive.
        * An `OnlineStatusIndicator` appears next to the name if the user is online.
        * The `BubbleStatusBadge` widget is displayed inline after the name and online status. The badge uses standardized sizing that matches the filter chips in the Contacts page.
    * **Relationship Date**: Specific details like 'Friends since [Date]', 'Contact since [Date]', or 'Bubbler since [Date]' are shown directly below the name/status row with minimal vertical spacing. Font size is responsive and smaller than the name.
    * **Mutual Connections Area**: Displays horizontally scrolling lists of mutual friends and contacts. This section appears below the relationship date with minimal top padding.
        * Each item consists of a `CircleAvatar` (radius 14) and the connection's first name below it (small, responsive font size).
        * Each avatar+name combination is wrapped in a `SizedBox` with a fixed `width` (currently 40.0) to ensure consistent horizontal spacing regardless of name length.
        * The parent `Row` for each list uses a `spacing` property (currently 2.0) to define the gap *between* these fixed-width containers.
    * **Bubble Members Area (New)**: Displays a horizontally scrolling list of the *viewed user's* bubble members.
        * **Visibility**:
            * On `FriendProfilePage` and `ContactProfilePage`, this section only appears if the viewed user is part of *a* bubble (i.e., their `bubbleStatus` is not `BubbleStatus.noBubble`) AND the list of their bubble members is not empty.
            * On `BubblerProfilePage`, this section appears if the list of the bubbler's members is not empty.
        * **Content**: Shows the count of members in the title (e.g., "4 bubblers"). **Note:** This displays the members of the *profiled user's* bubble, not members *in common* with the viewing user.
        * **UI**: Matches the style and structure of the "Mutual Connections Area" (title, horizontal `Row` of avatars+names within fixed-width `SizedBox` containers, spacing of 2.0).
        * **Interaction**: Tapping an avatar navigates to that member's profile page (using the logic in `_buildMutualAvatar`).
        * **Data**: Currently implemented using mock data (`_bubbleMembers` list). **TODO:** Integrate with backend to fetch the actual bubble members list for the viewed user.
5. **Bottom Action Button Area**: Contains the main action button(s) relevant to the relationship.

### Navigation Flow

Navigation to a profile page hides the bottom navigation bar.
Navigation back from a profile page restores the bottom navigation bar.
Mutual connection avatars provide deep-linking to other profiles.
Each profile type has its dedicated route pattern.

### Styling Guidelines

**Typography**:
    Name: Responsive size (e.g., 22-30px), Bold, White (or themed color for Bubblers).
    App Bar Title: Responsive size (e.g., 14-18px), Bold, White.
    Section Titles ("X friends in common"): Responsive size (e.g., 12-16px), Bold, White.
    Relationship Date Text: Responsive size (e.g., 10-14px), Regular, White70.
    Mutual Avatar Names: Responsive size (e.g., 8-12px), Semi-bold, White.
    Bubble Status Badge Text: Standardized size matching filter chips, Regular, White.
**Responsiveness**: Font sizes for most text elements scale based on screen width using helper methods (e.g., `_getHeadingSize`, `_getSubheadingSize`). Avatar sizes and spacing are fixed but chosen to work well across typical mobile screen sizes.
**Animations**:
    `OnlineStatusIndicator` uses animated glow effect.
    Standard platform transitions for page navigation.

## Relationship Type-Specific Implementation

### When the users have no relations (relationshipType == none)

**Unique Features**:
    1. **No Relationship History**: No "Friends since [Date]" or "Contact since [Date]" text is displayed.
    2. **No Call Buttons**: No audio call, video call, or screen share buttons are displayed.
    3. **More Options**: Excludes "Unfriend".
    4. **Bottom Button Logic**:
        **Active State**: Button shows "Send contact request" and is enabled
        **Action**: When clicked, sends a contact request to the target user
        **Post-Action**: Button immediately updates to "Contact request sent" and becomes disabled
        **Note**: Request acceptance happens through ContactRequestDialog when the recipient receives the notification

### When the users are friends

**Unique Features**:
    1. **Relationship History**: Displays "Friends since [Date]" text.
    2. **Call Action Buttons**: Audio Call, Video Call, Screen Share buttons are displayed using `_buildGlassButton`. Labels below these buttons have been restored.
    3. **More Options**: Includes "Unfriend" in the `PopupMenuButton`.
    4. **Bottom Button Logic**:
        **Active State**: Button shows "Message" and is enabled
        **Action**: When clicked, navigates to the chat page with the friend
        **Note**: Friends can always message each other directly
    5. **Bubble Status Badge**: Uses the shared `BubbleStatusBadge` widget with standardized sizing.

### When the users are maybe_friends

**Unique Features**:
    1. **Bubble Status Badge**: Uses the shared `BubbleStatusBadge` widget with standardized sizing.
    2. **Relationship History**: Displays relationship history based on the source bubble that created the maybe_friend status.
    3. **Bottom Button Logic**:
        **Disabled State**: Button shows "Friend request sent" and is disabled
        **Context**: This state occurs when viewing a maybe_friend user where you have accepted the friend request but they haven't responded yet
        **Note**: Friend request acceptance happens through FriendsChoiceDialog when users receive notifications
        **Auto-Generation**: Friend requests are automatically created when bubbles expire, creating bidirectional requests for all former bubble members

### When the users are contacts

**Unique Features**:
    1. **No Call Buttons**: Contact profiles do not offer direct calling options.
    2. **More Options**: Excludes "Unfriend".
    3. **Bottom Button Logic**: Context-dependent based on bubble statuses and pending requests:

        **Active Button States:**
        * **Both No Bubble**: Button shows "Start bubble together" - initiates bubble start request
        * **Contact No Bubble, User In Non-Full Bubble**: Button shows "Invite to bubble" - initiates bubble invite request
        * **Contact In Non-Full Bubble, User No Bubble**: Button shows "Request to join bubble" - initiates bubble join request
        * **Contact In Bubble, User In Same Bubble**: Impossible scenario (would be bubblers, not contacts)

        **Disabled Button States:**
        * **Contact In Full Bubble**: Button shows "Bubble full" (disabled)
        * **User In Full Bubble**: Button shows "Bubble full" (disabled)
        * **Both In Different Bubbles**: Button shows "Contact" (disabled)

        **Request Sent States (Button Disabled):**
        * **"Bubble start request sent"**: When you sent a bubble start request (pending)
        * **"Bubble invite request sent"**: When you sent a bubble invite request (pending)
        * **"Bubble join request sent"**: When you sent a bubble join request (pending)

        **Request Received States (Button Disabled):**
        * **"Bubble start request received"**: When contact sent you a bubble start request
        * **"Bubble invite request received"**: When contact sent you a bubble invite request
        * **"Bubble join request received"**: When contact sent you a bubble join request

        **Note**: All request acceptance/rejection happens through dedicated dialog widgets (BubbleStartRequestDialog, BubbleInviteRequestDialog, BubbleJoinRequestDialog) when notifications are received.

    4. **Bubble Status Badge**: Uses the shared `BubbleStatusBadge` widget with standardized sizing.
    5. **Relationship History**: Displays "Contact since [Date]" text if available.

### When the users are bubblers (same bubble members)

**Unique Features**:
    1. **Colored Name**: Name displays using the color assigned to this bubbler in the bubble.
    2. **Bottom Button Logic**:
        **Active State**: Button shows "Message" and is enabled
        **Action**: When clicked, navigates to the chat page with the bubbler
        **Note**: Bubblers can always message each other directly within the bubble context
    3. **More Options**: Includes "Kickout from bubble" option (requires unanimous approval from other bubble members)
    4. **Bubble Status Badge**: Displays the `BubbleStatusBadge` widget.
    5. **Relationship History**: Displays "Bubbler since [Date]" text if available.
    6. **Call Action Buttons**: Audio Call, Video Call, Screen Share buttons are available for bubblers.

## Unified Profile Page Button States

The button at the bottom of the unified profile page reflects the current request status and relationship state, following the comprehensive request system documented in `contact, bubble and friends requests system.md`.

### Request Sent States (Button Deactivated)

When you have sent a request that is pending:
  **"Contact request sent"**: When viewing a user you sent a contact request to (pending)
  **"Friend request sent"**: When viewing a maybe_friend user you accepted the friend request for (pending from their side)
  **"Bubble join request sent"**: When viewing a user you sent a bubble join request to (pending)
  **"Bubble invite request sent"**: When viewing a user you sent a bubble invite request to (pending)
  **"Bubble start request sent"**: When viewing a user you sent a bubble start request to (pending)

### Request Received States (Button Deactivated)

When you have received a request that is pending:
  **"Contact request received"**: When viewing a user who sent you a contact request (acceptance happens through ContactRequestDialog)
  **"Friend request received"**: When viewing a maybe_friend user who accepted the friend request (pending from your side, acceptance happens through FriendsChoiceDialog)
  **"Bubble join request received"**: When viewing a user who sent you a bubble join request (acceptance happens through BubbleJoinRequestDialog)
  **"Bubble invite request received"**: When viewing a user who sent you a bubble invite request (acceptance happens through BubbleInviteRequestDialog)
  **"Bubble start request received"**: When viewing a user who sent you a bubble start request (acceptance happens through BubbleStartRequestDialog)

### Active Relationship States (Button Activated)

When no requests are pending and action is available:
  **"Send contact request"**: When viewing a user with no relationship
  **"Message"**: When viewing a contact, friend, or bubbler
  **"Start bubble together"**: When viewing a contact and both users have no bubble
  **"Invite to bubble"**: When viewing a contact, you're in a non-full bubble, and they have no bubble
  **"Request to join bubble"**: When viewing a contact, they're in a non-full bubble, and you have no bubble
  **"Unblock"**: When viewing a blocked user

### Disabled States (Button Deactivated, No Action)

When action is not possible due to constraints:
  **"Bubble full"**: When bubble capacity constraints prevent action
  **"Contact"**: When both users are in different bubbles (cannot perform bubble actions)

### Important Implementation Notes

  **No Direct Request Handling**: The unified profile page never directly handles request acceptance/rejection
  **Dialog-Based Acceptance**: All request handling happens through dedicated dialog widgets that appear via notifications
  **Real-Time Updates**: Button states update immediately when requests are sent/received
  **State Persistence**: Button states persist across app sessions until requests are resolved

## Interactive Features

### Mutual Connection Interaction

On all types of unified profile pages, "friends in common" and "contacts in common" sections display interactive avatars. When a user taps on any mutual connection:

1. The application determines the appropriate profile type (friend or contact).
2. Navigates to the corresponding profile page using `MaterialPageRoute`.
3. Passes mock data for development (in production, would fetch actual user data).
4. Maintains navigation stack for proper back-button functionality.

Implementation details:

```dart
// Inside the .map() function for the avatar Row in each profile page
// Wrap avatar in SizedBox for consistent spacing
return SizedBox(
  width: 40.0, // Fixed width
  child: _buildMutualAvatar(
    connection.imageUrl,
    connection.name,
  ),
);

// Inside _buildMutualAvatar method
GestureDetector(
  onTap: () {
    // Determine if friend or contact
    final bool isMutualFriend = name.startsWith('Friend'); // Example logic

    if (isMutualFriend) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FriendProfilePage(
            // ... pass required data ...
          ),
        ),
      );
    } else {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ContactProfilePage(
            // ... pass required data ...
          ),
        ),
      );
    }
  },
  child: Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      CircleAvatar(radius: 14, ...), // Small radius
      const SizedBox(height: 3),
      Flexible(
        child: Text(
          name.split(' ')[0], // First name only
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: _getBodyTextSize(context) - 4, // Small, responsive
            // ... other style props ...
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    ],
  ),
)


## Navigation Patterns

### Path-Based Navigation
- `FriendProfilePage`: Accessed via `/friends/:friendId`
- `ContactProfilePage`: Accessed via `/contact/:contactId`
- `BubblerProfilePage`: Accessed via `/bubble/member/:memberId`


### Data Passing
Profile pages receive data through:
1. **Path Parameters**: IDs extracted from route paths.
2. **Extra Parameters**: Additional data passed through routers `extra` parameter (e.g., `memberColor` for `BubblerProfilePage`, `UserContact` object for `ContactProfilePage`).

### Navigation Bar Management
All profile pages use `NavBarVisibilityNotifier` to control the visibility of the bottom navigation bar:
```dart
final navNotifier = Provider.of<NavBarVisibilityNotifier>(context, listen: false);
navNotifier.hideNavBar();
// Navigation code...
if (context.mounted) {
  navNotifier.showNavBar();
}


## Request System Integration

### BLoC Architecture
The unified profile pages integrate with the comprehensive request management system:

1. **UnifiedProfileBloc**: Manages profile state and determines button states based on:
   - Current relationship type between users
   - Pending request status (sent/received)
   - Bubble membership status and constraints
   - Real-time updates from request notifications

2. **RequestManagementBloc**: Handles all request operations:
   - Contact requests (send/accept/decline)
   - Friend requests (accept/decline - auto-generated only)
   - Bubble requests (start/invite/join/kickout)

3. **Real-Time Updates**: Profile pages respond to:
   - MQTT notifications for incoming requests
   - Request status changes (accepted/declined/expired)
   - Relationship status updates

### Request Dialog Integration
Profile pages trigger appropriate dialogs based on user actions:

- **ContactRequestDialog**: For contact request acceptance/rejection
- **FriendsChoiceDialog**: For friend request acceptance/rejection
- **BubbleStartRequestDialog**: For bubble start request acceptance/rejection
- **BubbleInviteRequestDialog**: For bubble invite request acceptance/rejection
- **BubbleJoinRequestDialog**: For bubble join request acceptance/rejection
- **BubbleProposeRequestDialog**: For bubble member voting on new additions
- **BubbleKickoutRequestDialog**: For bubble member voting on removals

### State Synchronization
Profile pages maintain consistency with the request system through:

1. **Optimistic Updates**: Immediate UI feedback when requests are sent
2. **Server Confirmation**: Backend validation and state persistence
3. **Error Handling**: Rollback on failure with user notification
4. **Cache Invalidation**: Refresh profile data after successful operations

## Implementation Considerations

### State Management
Profile pages use comprehensive state management with:
- **Real-time data**: Fetched from API endpoints with live updates
- **BLoC pattern**: Proper separation of concerns and reactive state management
- **Caching strategy**: Intelligent caching to reduce network requests while maintaining freshness
- **Error handling**: Graceful degradation and user feedback for network issues

### Performance Optimization
- **Lazy loading**: Secondary data (mutual connections, bubble members) loaded after primary profile data
- **Image caching**: Profile pictures cached for offline viewing
- **Request debouncing**: Prevent duplicate requests during rapid user interactions

### User Stories
For a complete list of user stories related to profiles and user interactions, see the [User Stories](../technical/user_stories.md) documentation, particularly the "Profile Viewing" section.

