# Password Breach Checking Security Implementation

## Overview
The Hopen platform implements a secure password breach checking system using the HaveIBeenPwned (HIBP) API with k-anonymity to protect user privacy. This document outlines the security measures in place to ensure that plaintext passwords are never transmitted to the server.

## Security Principles

### 1. Client-Side Hashing Only
- **Requirement**: All password hashing MUST occur on the client side before transmission
- **Implementation**: Clients hash passwords locally using SHA-1 before sending to the server
- **Enforcement**: Server strictly rejects any plaintext passwords

### 2. K-Anonymity Protection
The system uses the k-anonymity approach to protect password privacy:
- Only the first 5 characters of the SHA-1 hash are sent to HIBP
- The remaining 35 characters are never sent to external services
- This ensures that even HIBP cannot determine the actual password being checked

### 3. No Server-Side Password Processing
- The server NEVER accepts plaintext passwords for breach checking
- The deprecated `password` field in the protobuf is maintained only for backward compatibility
- Any attempt to use the plaintext password field results in immediate rejection with a security error

## Implementation Details

### Backend (Go) Implementation

#### Auth Service (`microservices/auth/service.go`)

```go
// CheckPasswordBreach validates passwords against known breaches
// SECURITY: Only accepts SHA-1 hashes, never plaintext passwords
func (s *Service) CheckPasswordBreach(ctx context.Context, req *authv1.CheckPasswordBreachRequest) (*authv1.CheckPasswordBreachResponse, error)
```

Key security features:
1. **Strict Input Validation**: The `validatePasswordBreachRequest` method ensures:
   - Plaintext passwords are immediately rejected
   - Only valid SHA-1 hashes or k-anonymity data are accepted
   - All hash formats are validated for correctness

2. **Security Logging**: 
   - Attempts to use plaintext passwords are logged as security violations
   - Compromised passwords are logged with only the hash prefix for safety
   - User IDs are tracked for audit purposes

3. **Removed Dangerous Methods**:
   - The `hashPassword` method has been completely removed
   - No fallback to server-side hashing exists

### Frontend (Flutter) Implementation

#### Auth Remote Data Source (`data/datasources/auth_remote_data_source.dart`)

```dart
@override
Future<bool> checkPasswordBreach(String password) async =>
    _safeApiCall(() async {
      // SECURITY: Always hash passwords locally before sending to server
      final bytes = utf8.encode(password);
      final digest = sha1.convert(bytes);
      final sha1Hash = digest.toString().toUpperCase();
      
      // Use k-anonymity approach for enhanced privacy
      final hashPrefix = sha1Hash.substring(0, 5);
      final hashSuffix = sha1Hash.substring(5);
      
      final request = auth_proto.CheckPasswordBreachRequest()
        ..hashPrefix = hashPrefix
        ..hashSuffix = hashSuffix;
        // Note: The deprecated 'password' field is NOT set
```

Key security features:
1. **Local Hashing**: Passwords are always hashed on the client before transmission
2. **K-Anonymity**: Uses hash prefix/suffix separation for privacy
3. **No Plaintext Transmission**: The deprecated password field is never used

### Protocol Buffer Definition

```protobuf
message CheckPasswordBreachRequest {
  // DEPRECATED: Plaintext password - DO NOT USE IN PRODUCTION
  string password = 1 [deprecated = true];
  
  // SHA-1 hash of the password (40 characters, uppercase)
  string sha1_hash = 2;
  
  // K-anonymity approach: hash prefix (first 5 characters)
  string hash_prefix = 3;
  
  // K-anonymity approach: hash suffix (remaining 35 characters)
  string hash_suffix = 4;
}
```

## Security Validation

### Server-Side Validation
The server performs multiple layers of validation:

1. **Request Validation**:
   ```go
   func (s *Service) validatePasswordBreachRequest(req *authv1.CheckPasswordBreachRequest) error {
     // Strictly reject any plaintext passwords
     if req.Password != "" {
       return fmt.Errorf("plaintext passwords are strictly forbidden")
     }
     // Validate SHA-1 hash or k-anonymity data
     // ...
   }
   ```

2. **Hash Format Validation**:
   ```go
   func (s *Service) validateSHA1Hash(hash string) error {
     // Validate 40-character hexadecimal format
     // Convert to uppercase for consistency
     // Return detailed error messages
   }
   ```

## Error Handling

### Security Errors
When security violations are detected:
1. A `SECURITY_ERROR` response is returned
2. The violation is logged with appropriate context
3. No sensitive information is included in error messages

### Example Error Response
```json
{
  "apiResponse": {
    "success": false,
    "errorCode": "SECURITY_ERROR",
    "message": "Plaintext passwords are strictly forbidden. Client must hash passwords locally using SHA-1"
  }
}
```

## Migration Path

For systems upgrading from the old implementation:

1. **Phase 1**: Update all clients to use SHA-1 hashing
2. **Phase 2**: Monitor logs for any plaintext password attempts
3. **Phase 3**: Remove support for the deprecated password field entirely

## Testing

### Security Test Cases
1. **Reject Plaintext Passwords**: Verify server rejects any request with the password field set
2. **Accept Valid Hashes**: Verify server accepts properly formatted SHA-1 hashes
3. **K-Anonymity Support**: Verify hash prefix/suffix approach works correctly
4. **Invalid Hash Rejection**: Verify malformed hashes are rejected

### Example Test
```go
// Test that plaintext passwords are rejected
func TestCheckPasswordBreach_RejectsPlaintext(t *testing.T) {
    req := &authv1.CheckPasswordBreachRequest{
        Password: "plaintext_password", // This should be rejected
    }
    _, err := service.CheckPasswordBreach(ctx, req)
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "plaintext passwords are strictly forbidden")
}
```

## Compliance

This implementation complies with:
- **OWASP Password Storage Guidelines**: Passwords are never stored or transmitted in plaintext
- **Privacy by Design**: K-anonymity ensures user privacy even from the breach checking service
- **GDPR Requirements**: No unnecessary personal data processing occurs

## Monitoring and Alerts

### Security Metrics to Track
1. **Plaintext Password Attempts**: Count and alert on any attempts to use plaintext passwords
2. **Compromised Password Detections**: Track how many compromised passwords are detected
3. **API Response Times**: Monitor HIBP API performance

### Alert Conditions
- Immediate alert if plaintext password attempts exceed threshold
- Daily report of compromised password statistics
- Performance degradation alerts for HIBP API

## Future Improvements

1. **Additional Hash Algorithms**: Consider supporting SHA-256 or SHA-3 for future-proofing
2. **Local Breach Database**: Implement a local cache of common breached passwords
3. **Rate Limiting**: Add rate limiting to prevent abuse of the breach checking endpoint
4. **Enhanced Privacy**: Implement additional privacy-preserving techniques like homomorphic encryption

## References

- [HaveIBeenPwned API Documentation](https://haveibeenpwned.com/API/v3)
- [K-Anonymity in Practice](https://blog.cloudflare.com/validating-leaked-passwords-with-k-anonymity/)
- [OWASP Password Storage Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Password_Storage_Cheat_Sheet.html)
