# Repository Caching Strategy Guidelines

## Overview

This document establishes consistent caching strategies across all repositories in the Hopen application, ensuring uniform data access patterns, offline support, and optimal performance.

## Caching Strategy Decision Matrix

### When to Use Cached Repositories

**ALWAYS use cached repositories for:**
- User data (profiles, friends, contacts)
- Chat messages and conversation history
- Bubble history and member information
- Presence information
- Request data (friend requests, bubble requests)
- Any data that users expect to access offline

**CONSIDER network-only for:**
- Real-time operations requiring immediate consistency
- One-time actions (create, delete, update operations)
- Security-sensitive operations
- Operations that must reflect the latest server state

### Repository Categories

#### 1. Fully Cached Repositories ✅
These repositories implement complete cache-first patterns with Drift persistence:

- `CachedBubbleHistoryRepositoryImpl` - Bubble history with stale-while-revalidate
- `CachedChatRepositoryImpl` - Chat messages with persistent storage
- `CachedFriendshipRepositoryImpl` - Friends list with presence integration
- `CachedPresenceRepositoryImpl` - User presence with real-time updates
- `CachedFriendRequestRepositoryImpl` - Friend requests from bubble expiry
- `CachedContactRequestRepositoryImpl` - Contact management
- `CachedBubbleStartRequestRepositoryImpl` - Bubble start requests
- `CachedBubbleInviteRequestRepositoryImpl` - Bubble invite requests
- `CachedBubbleProposeRequestRepositoryImpl` - Bubble propose requests
- `CachedBubbleJoinRequestRepositoryImpl` - Bubble join requests
- `CachedBubbleRepositoryImpl` - Main bubble operations
- `CachedUserRepositoryImpl` - User profiles and relationships

#### 2. Network-Only Repositories (Intentional) ⚠️
These repositories remain network-only by design:

- `AuthRepositoryImpl` - Authentication must be real-time
- `StorageRepositoryImpl` - File operations require server state
- `SecurityRepositoryImpl` - Security operations must be fresh
- `BackgroundProcessingRepositoryImpl` - System-level operations
- `ActiveBubbleRepositoryImpl` - Real-time bubble state
- `CallRepositoryImpl` - Real-time call operations
- `SyncRepositoryImpl` - Initial sync operations

#### 3. Hybrid Repositories 🔄
These repositories use both cached and network operations strategically:

- `ChatRepositoryImpl` (network) + `CachedChatRepositoryImpl` (cached)
- `BubbleRepositoryImpl` (network) + `CachedBubbleRepositoryImpl` (cached)

## Implementation Standards

### Cache-First Repository Pattern

All cached repositories must:

1. **Extend CacheFirstRepositoryBase**
   ```dart
   class CachedXRepositoryImpl extends CacheFirstRepositoryBase implements XRepository
   ```

2. **Use Constructor Injection**
   ```dart
   CachedXRepositoryImpl({
     required XRepositoryImpl networkRepository,
     required AppDatabase database,
     required AuthRepository authRepository,
   })
   ```

3. **Implement Required Methods**
   ```dart
   @override
   Future<bool> get hasCachedData;
   
   @override
   Future<void> initializeCache();
   
   @override
   Future<void> fetchFromRemote();
   
   @override
   Future<void> clearCache();
   
   @override
   Future<void> handleMqttEvent(MqttEvent event);
   ```

4. **Follow Stale-While-Revalidate Pattern**
   - Return cached data immediately
   - Refresh from network in background
   - Update cache with fresh data
   - Handle offline scenarios gracefully

### Dependency Injection Pattern

#### Correct Pattern ✅
```dart
// Register network implementation first
..registerLazySingleton<XRepositoryImpl>(
  () => XRepositoryImpl(
    remoteDataSource: sl<XRemoteDataSource>(),
    authRepository: sl<AuthRepository>(),
  ),
)
// Register cached implementation as main interface
..registerLazySingleton<XRepository>(
  () => CachedXRepositoryImpl(
    networkRepository: sl<XRepositoryImpl>(),
    database: sl<AppDatabase>(),
    authRepository: sl<AuthRepository>(),
  ),
)
```

#### Incorrect Pattern ❌
```dart
// Direct network-only registration
..registerLazySingleton<XRepository>(
  () => XRepositoryImpl(remoteDataSource: sl<XRemoteDataSource>()),
)
```

## Architectural Principles

### Four-Layer Dependency Rule

1. **Domain Layer** - Business logic and use cases
2. **Repository Layer** - Abstract interfaces
3. **Provider Layer** - Implementation details
4. **External Layer** - Framework and infrastructure

### Constructor Injection vs Service Locator

**Always use constructor injection** ✅
```dart
class RepositoryImpl {
  RepositoryImpl({
    required AuthRepository authRepository,
    required DataSource dataSource,
  });
}
```

**Never use direct service locator calls** ❌
```dart
class RepositoryImpl {
  void method() {
    final auth = sl<AuthRepository>(); // BAD
  }
}
```

### Mapper Pattern Standardization

**Use dedicated mapper classes** ✅
```dart
class BubbleMapper {
  static BubbleEntity fromProto(BubbleProto proto) {
    return _statusMapper.mapFromBackendStatus(proto.status);
  }
}
```

**Avoid inline mapping** ❌
```dart
// Don't do this in repository files
final entity = BubbleEntity(
  status: proto.status == 'active' ? Status.active : Status.inactive,
);
```

## Cache Configuration

### Default Cache Settings
```dart
static const Duration _cacheValidity = Duration(minutes: 15);
static const Duration _presenceCacheExpiry = Duration(seconds: 30);
static const Duration _chatCacheExpiry = Duration(hours: 24);
```

### Cache Invalidation Rules
- **User actions**: Invalidate related caches immediately
- **MQTT events**: Update specific cache entries
- **Network errors**: Extend cache validity temporarily
- **App restart**: Validate cache freshness

## Testing Requirements

Each cached repository must include:

1. **Unit tests** for cache-first behavior
2. **Integration tests** for network fallback
3. **Offline scenario tests**
4. **Cache invalidation tests**
5. **Performance benchmarks**

## Monitoring and Metrics

Track these metrics for each cached repository:
- Cache hit/miss ratios
- Network call frequency
- Cache refresh success rates
- Offline operation success rates
- Memory usage patterns

## Migration Checklist

When converting a network-only repository to cached:

- [ ] Create cached implementation extending CacheFirstRepositoryBase
- [ ] Add constructor injection for all dependencies
- [ ] Implement cache-first methods with stale-while-revalidate
- [ ] Add MQTT event handling for real-time updates
- [ ] Update DI (riverpod) registration to use cached implementation
- [ ] Create comprehensive unit and integration tests
- [ ] Update documentation and architectural diagrams

## Future Considerations

- **Cache size limits**: Implement LRU eviction policies
- **Selective sync**: Allow users to control what data is cached
- **Background refresh**: Optimize refresh timing based on usage patterns
- **Cross-device sync**: Ensure cache consistency across user devices
