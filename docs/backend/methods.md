# Hopen Backend Methods Documentation

## Overview

This document provides a comprehensive description of all methods available in the Hopen backend microservices architecture. Each method is documented with its purpose, parameters, return values, usage scenarios, and **critical authorization rules**.

**Total Services**: 12
**Total Methods**: 290
**Architecture**: Pure gRPC Microservices
**Protocol**: gRPC with HTTP/2 support
**Status**: Production-Ready with Advanced Features
**Last Updated**: September 15, 2025
**Version**: 3.3.1

---

## Table of Contents

1. [Authentication Service (AuthService)](#authentication-service-authservice) - Advanced authentication with Ory integration
2. [User Management Service (UserService)](#user-management-service-userservice) - User profiles and relationship management
3. [Bubble Management Service (BubbleService)](#bubble-management-service-bubbleservice) - Social bubble creation and management
4. [Call Management Service (CallService)](#call-management-service-callservice) - WebRTC call management with LiveKit
5. [Email Service (EmailService)](#email-service-emailservice) - Email delivery with AWS SES integration
6. [Search Service (SearchService)](#search-service-searchservice) - OpenSearch-powered user search with real-time indexing
7. [Social Analytics Service (SocialAnalyticsService)](#social-analytics-service-socialanalyticsservice) - Comprehensive analytics and insights
8. [Sync Service (SyncService)](#sync-service-syncservice) - Cross-device data synchronization
9. [Presence Service (PresenceService)](#presence-service-presenceservice) - Real-time user presence and status
10. [Media Management Service (MediaService)](#media-management-service-mediaservice) - File storage with MinIO integration
11. [Notification Service (NotificationService)](#notification-service-notificationservice) - Push notifications with FCM
12. [Realtime Communication Service (RealtimeService)](#realtime-communication-service-realtimeservice) - Real-time messaging with ScyllaDB

---

## Authentication Service (AuthService)

**Service Package**: `hopen.auth.v1.AuthService`
**Total Methods**: 27
**Role**: Handles user authentication, authorization, session management, and Ory Kratos OAuth flows

### 1. RegisterUser
**Purpose**: Register a new user account in the system
**Request**: `RegisterUserRequest`
**Response**: `RegisterUserResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Creates new user accounts with email verification and password hashing

### 2. LoginUser
**Purpose**: Authenticates a user with email and password credentials.
**Request**: `LoginUserRequest`
**Response**: `LoginUserResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Handles user login and returns a session token.

### 3. ValidateSession
**Purpose**: Validate an existing session token and return session information
**Request**: `ValidateSessionRequest`
**Response**: `ValidateSessionResponse`
**Authentication**: Session token required
**Authorization**: Valid session token required (no additional authorization)
**Role**: Verifies session validity and returns user permissions

### 4. RefreshToken
**Purpose**: Refresh an expired access token using a refresh token
**Request**: `RefreshTokenRequest`
**Response**: `RefreshTokenResponse`
**Authentication**: Refresh token required
**Authorization**: Valid refresh token required (no additional authorization)
**Role**: Generates new access tokens without re-authentication

### 5. Logout
**Purpose**: Logout user and invalidate session
**Request**: `LogoutRequest`
**Response**: `LogoutResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Ends user session and clears server-side session data

### 6. GetUserProfile
**Purpose**: Retrieve user profile information
**Request**: `GetUserProfileRequest`
**Response**: `GetUserProfileResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Returns user profile data for display

### 7. GetUserPermissions
**Purpose**: Get all permissions for a specific user
**Request**: `GetUserPermissionsRequest`
**Response**: `GetUserPermissionsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Returns list of user permissions for authorization checks

### 8. HasPermission
**Purpose**: Check if user has a specific permission
**Request**: `HasPermissionRequest`
**Response**: `HasPermissionResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Authorization check for specific actions

### 9. GetUserRoles
**Purpose**: Get all roles assigned to a user
**Request**: `GetUserRolesRequest`
**Response**: `GetUserRolesResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Returns user roles for role-based access control

### 10. GetDefaultRole
**Purpose**: Retrieves the current default role from the database.
**Request**: `GetDefaultRoleRequest`
**Response**: `GetDefaultRoleResponse`
**Authentication**: Admin session required
**Authorization**: Admin role required
**Role**: Returns the default role assigned to new users.

### 11. SetDefaultRole
**Purpose**: Sets a new default role for new user registrations.
**Request**: `SetDefaultRoleRequest`
**Response**: `SetDefaultRoleResponse`
**Authentication**: Admin session required
**Authorization**: Admin role required
**Role**: Sets the default role for new users.

### 12. ValidateMqttConnection
**Purpose**: Validate MQTT connection credentials and return allowed topics
**Request**: `ValidateMqttConnectionRequest`
**Response**: `ValidateMqttConnectionResponse`
**Authentication**: MQTT credentials required
**Authorization**: Valid MQTT credentials required (no additional authorization)
**Role**: MQTT authentication and topic authorization

### 13. CheckMqttTopicPermission
**Purpose**: Validates if a user can perform an action on an MQTT topic.
**Request**: `CheckMqttTopicPermissionRequest`
**Response**: `CheckMqttTopicPermissionResponse`
**Authentication**: MQTT credentials required
**Authorization**: Valid MQTT credentials required
**Role**: MQTT topic authorization.

### 14. GetAuthStatus
**Purpose**: Get current authentication status for a user
**Request**: `GetAuthStatusRequest`
**Response**: `GetAuthStatusResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Returns comprehensive authentication status

### 15. CheckPasswordBreach
**Purpose**: Check if a password has been compromised in data breaches
**Request**: `CheckPasswordBreachRequest`
**Response**: `CheckPasswordBreachResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Password security validation using k-anonymity

### 16. ForgotPassword
**Purpose**: Initiate password reset process for users who have forgotten their password
**Request**: `ForgotPasswordRequest`
**Response**: `ForgotPasswordResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Sends password reset email with secure token

### 17. ResetPassword
**Purpose**: Reset user password using a valid reset token
**Request**: `ResetPasswordRequest`
**Response**: `ResetPasswordResponse`
**Authentication**: Reset token required
**Authorization**: Valid reset token required (no additional authorization)
**Role**: Allows users to set new password after verification

### 18. ChangePassword
**Purpose**: Change user password with current password verification
**Request**: `ChangePasswordRequest`
**Response**: `ChangePasswordResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Allows authenticated users to change their password securely

### 19. EnableTwoFactorAuth
**Purpose**: Enable two-factor authentication for enhanced account security
**Request**: `EnableTwoFactorAuthRequest`
**Response**: `EnableTwoFactorAuthResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Sets up TOTP-based 2FA with QR code and backup codes

### 20. VerifyTwoFactorAuth
**Purpose**: Verify TOTP code during 2FA setup or login
**Request**: `VerifyTwoFactorAuthRequest`
**Response**: `VerifyTwoFactorAuthResponse`
**Authentication**: User session required (for setup) or TOTP code (for login)
**Authorization**: Valid TOTP code required
**Role**: Validates 6-digit TOTP codes from authenticator apps

### 21. DisableTwoFactorAuth
**Purpose**: Disable two-factor authentication for user account
**Request**: `DisableTwoFactorAuthRequest`
**Response**: `DisableTwoFactorAuthResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, password and TOTP verification required
**Role**: Removes 2FA requirement and invalidates backup codes

### 22. InitiateKratosLoginFlow
**Purpose**: Initiate a Kratos login flow for OAuth providers (Google, Apple)
**Request**: `InitiateLoginFlowRequest`
**Response**: `InitiateLoginFlowResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Creates Kratos OAuth login flows with automatic username generation and conflict resolution

### 23. InitiateKratosRegistrationFlow
**Purpose**: Initiate a Kratos registration flow for OAuth providers (Google, Apple)
**Request**: `InitiateRegistrationFlowRequest`
**Response**: `InitiateRegistrationFlowResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Creates Kratos OAuth registration flows with profile data mapping

### 24. CompleteKratosFlow
**Purpose**: Complete a Kratos OAuth flow and return session information with resolved username conflicts
**Request**: `CompleteKratosFlowRequest`
**Response**: `CompleteKratosFlowResponse`
**Authentication**: Kratos session token required
**Authorization**: Valid Kratos session token required
**Role**: Finalizes OAuth authentication with automatic username conflict resolution (firstnamelastname, firstnamelastname1, etc.)

### 25. DeleteAccount
**Purpose**: Delete user account and identity completely from the system
**Request**: `DeleteAccountRequest`
**Response**: `DeleteAccountResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Complete account deletion with identity cleanup and data removal

### 26. Health
**Purpose**: Check auth service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

---

## User Management Service (UserService)

**Service Package**: `hopen.user.v1.UserService`
**Total Methods**: 50
**Role**: Manages user profiles, privacy settings, and user-related operations

### 1. CreateUser
**Purpose**: Create user profile (called by auth service during registration)
**Request**: `CreateUserRequest`
**Response**: `CreateUserResponse`
**Authentication**: Service-level authentication required
**Authorization**: Valid service credentials required
**Role**: Creates user profile during registration

### 2. GetUser
**Purpose**: Retrieve user profile by ID
**Request**: `GetUserRequest`
**Response**: `GetUserResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role, OR the target user's profile must be public
**Role**: Returns user profile with privacy controls

### 3. GetUserByEmail
**Purpose**: Get user profile by email address
**Request**: `GetUserByEmailRequest`
**Response**: `GetUserByEmailResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Email-based user lookup

### 4. GetUsersBatch
**Purpose**: Get multiple user profiles in batch
**Request**: `GetUsersBatchRequest`
**Response**: `GetUsersBatchResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: Efficient batch user retrieval

### 5. UpdateUser
**Purpose**: Update user profile information
**Request**: `UpdateUserRequest`
**Response**: `UpdateUserResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Updates user profile with validation

### 6. SearchUsers
**Purpose**: Search for users with privacy controls
**Request**: `SearchUsersRequest`
**Response**: `SearchUsersResponse`
**Authentication**: User session required
**Authorization**: Valid user session required (no additional authorization)
**Role**: User discovery with privacy respect

### 7. CheckUsernameAvailability
**Purpose**: Check if a username is available for registration
**Request**: `CheckUsernameAvailabilityRequest`
**Response**: `CheckUsernameAvailabilityResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Username validation during registration

### 8. CheckEmailAvailability
**Purpose**: Check if an email is available for registration
**Request**: `CheckEmailAvailabilityRequest`
**Response**: `CheckEmailAvailabilityResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Email validation during registration

### 9. GetUserPrivacySettings
**Purpose**: Get user privacy settings
**Request**: `GetUserPrivacySettingsRequest`
**Response**: `GetUserPrivacySettingsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Returns privacy configuration

### 10. UpdateUserPrivacySettings
**Purpose**: Update user privacy settings
**Request**: `UpdateUserPrivacySettingsRequest`
**Response**: `UpdateUserPrivacySettingsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Updates privacy preferences

### 11. DeleteUser
**Purpose**: Delete user account with confirmation
**Request**: `DeleteUserRequest`
**Response**: `DeleteUserResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Account deletion with data cleanup

### 12. GetUserStats
**Purpose**: Get user statistics and activity metrics
**Request**: `GetUserStatsRequest`
**Response**: `GetUserStatsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Returns user activity statistics

### 13. BlockUser
**Purpose**: Block a user from contacting or interacting
**Request**: `BlockUserRequest`
**Response**: `BlockUserResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: User blocking functionality

### 14. UnblockUser
**Purpose**: Unblock a previously blocked user
**Request**: `UnblockUserRequest`
**Response**: `UnblockUserResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: User unblocking functionality

### 15. GetUserBlockedUsers
**Purpose**: Get list of users blocked by current user
**Request**: `GetUserBlockedUsersRequest`
**Response**: `GetUserBlockedUsersResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns blocked users list

### 16. ReportContent
**Purpose**: Report content for review.
**Request**: `ReportContentRequest`
**Response**: `ReportContentResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: Content reporting system.

### 17. GetContentReport
**Purpose**: Get a specific content report.
**Request**: `GetContentReportRequest`
**Response**: `GetContentReportResponse`
**Authentication**: Admin session required
**Authorization**: Admin role required
**Role**: Retrieves a content report.

### 18. ListContentReports
**Purpose**: List content reports.
**Request**: `ListContentReportsRequest`
**Response**: `ListContentReportsResponse`
**Authentication**: Admin session required
**Authorization**: Admin role required
**Role**: Lists content reports.

### 19. UpdateContentReportStatus
**Purpose**: Update the status of a content report.
**Request**: `UpdateContentReportStatusRequest`
**Response**: `UpdateContentReportStatusResponse`
**Authentication**: Admin session required
**Authorization**: Admin role required
**Role**: Updates a content report.

### 20. DeleteContentReport
**Purpose**: Delete a content report.
**Request**: `DeleteContentReportRequest`
**Response**: `DeleteContentReportResponse`
**Authentication**: Admin session required
**Authorization**: Admin role required
**Role**: Deletes a content report.

### 21. SendFriendRequest
**Purpose**: Send a friend request to another user
**Request**: `SendFriendRequestRequest`
**Response**: `SendFriendRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Friend request management

### 22. AcceptFriendRequest
**Purpose**: Accept a friend request
**Request**: `AcceptFriendRequestRequest`
**Response**: `AcceptFriendRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Friend request acceptance

### 23. DeclineFriendRequest
**Purpose**: Decline a friend request
**Request**: `DeclineFriendRequestRequest`
**Response**: `DeclineFriendRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Friend request rejection

### 24. RemoveFriend
**Purpose**: Remove a friend relationship
**Request**: `RemoveFriendRequest`
**Response**: `RemoveFriendResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Friend removal

### 25. GetFriends
**Purpose**: Get list of user's friends
**Request**: `GetFriendsRequest`
**Response**: `GetFriendsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns user's friend list

### 26. GetFriendsWithRelationshipInfo
**Purpose**: Get list of user's friends with relationship info.
**Request**: `GetFriendsWithRelationshipInfoRequest`
**Response**: `GetFriendsWithRelationshipInfoResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns user's friend list with extra relationship info.

### 27. GetPendingFriendRequests
**Purpose**: Get pending friend requests.
**Request**: `GetPendingFriendRequestsRequest`
**Response**: `GetPendingFriendRequestsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns pending friend requests.

### 28. GetRecentlyActiveFriends
**Purpose**: Get recently active friends.
**Request**: `GetRecentlyActiveFriendsRequest`
**Response**: `GetRecentlyActiveFriendsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns recently active friends.

### 29. GetSortedContacts
**Purpose**: Get sorted contacts.
**Request**: `GetSortedContactsRequest`
**Response**: `GetSortedContactsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns sorted contacts.

### 30. AcceptFriendRequestRelationship
**Purpose**: Accept a friend request relationship.
**Request**: `AcceptFriendRequestRelationshipRequest`
**Response**: `AcceptFriendRequestRelationshipResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Accepts a friend request relationship.

### 31. DeclineFriendRequestRelationship
**Purpose**: Decline a friend request relationship.
**Request**: `DeclineFriendRequestRelationshipRequest`
**Response**: `DeclineFriendRequestRelationshipResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Declines a friend request relationship.

### 32. SendContactRequest
**Purpose**: Send a contact request to another user
**Request**: `SendContactRequestRequest`
**Response**: `SendContactRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Contact request management

### 33. AcceptContactRequest
**Purpose**: Accept a contact request
**Request**: `AcceptContactRequestRequest`
**Response**: `AcceptContactRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Contact request acceptance

### 34. DeclineContactRequest
**Purpose**: Decline a contact request.
**Request**: `DeclineContactRequestRequest`
**Response**: `DeclineContactRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Declines a contact request.

### 35. RemoveContact
**Purpose**: Remove a contact relationship
**Request**: `RemoveContactRequest`
**Response**: `RemoveContactResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Contact removal

### 36. GetContacts
**Purpose**: Get list of user's contacts
**Request**: `GetContactsRequest`
**Response**: `GetContactsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns user's contact list

### 37. GetPendingContactRequests
**Purpose**: Get pending contact requests.
**Request**: `GetPendingContactRequestsRequest`
**Response**: `GetPendingContactRequestsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns pending contact requests.

### 38. GetContactRequest
**Purpose**: Get a specific contact request.
**Request**: `GetContactRequestRequest`
**Response**: `GetContactRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns a contact request.

### 39. GetUserRelationships
**Purpose**: Get all relationships for a user
**Request**: `GetUserRelationshipsRequest`
**Response**: `GetUserRelationshipsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Comprehensive relationship overview

### 40. GetUserRelationshipStatus
**Purpose**: Get the relationship status between two users.
**Request**: `GetUserRelationshipStatusRequest`
**Response**: `GetUserRelationshipStatusResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns the relationship status between two users.

### 41. GetFriendsByLocation
**Purpose**: Get friends by location.
**Request**: `GetFriendsByLocationRequest`
**Response**: `GetFriendsByLocationResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns friends by location.

### 42. GetFriendsByInterests
**Purpose**: Get friends by interests.
**Request**: `GetFriendsByInterestsRequest`
**Response**: `GetFriendsByInterestsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns friends by interests.

### 43. GetFriendsByActivityLevel
**Purpose**: Get friends by activity level.
**Request**: `GetFriendsByActivityLevelRequest`
**Response**: `GetFriendsByActivityLevelResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns friends by activity level.

### 44. GetFriendsByEngagementLevel
**Purpose**: Get friends by engagement level.
**Request**: `GetFriendsByEngagementLevelRequest`
**Response**: `GetFriendsByEngagementLevelResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns friends by engagement level.

### 45. GetFriendsByCompatibilityScore
**Purpose**: Get friends by compatibility score.
**Request**: `GetFriendsByCompatibilityScoreRequest`
**Response**: `GetFriendsByCompatibilityScoreResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns friends by compatibility score.

### 46. GetFriendsBySocialDistance
**Purpose**: Get friends by social distance.
**Request**: `GetFriendsBySocialDistanceRequest`
**Response**: `GetFriendsBySocialDistanceResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns friends by social distance.

### 47. CreateFriendshipsFromBubble
**Purpose**: Create friendships between bubble members
**Request**: `CreateFriendshipsFromBubbleRequest`
**Response**: `CreateFriendshipsFromBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble
**Role**: Automatic friendship creation from bubble membership

### 48. CreateContactsFromBubble
**Purpose**: Create contacts from bubble members.
**Request**: `CreateContactsFromBubbleRequest`
**Response**: `CreateContactsFromBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble
**Role**: Automatic contact creation from bubble membership.

### 49. GetUserActivityFeed
**Purpose**: Get user's activity feed showing recent activities and social interactions
**Request**: `GetUserActivityFeedRequest`
**Response**: `GetUserActivityFeedResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Returns a chronological feed of user activities like joining bubbles, making friends, profile updates, and other social interactions for building user profiles and social features

### 50. Health
**Purpose**: Check user service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

### 51. SubmitFeedback
**Purpose**: Submit user feedback for the platform
**Request**: `SubmitFeedbackRequest`
**Response**: `SubmitFeedbackResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: Collects user feedback for product improvement

### 52. SubmitBugReport
**Purpose**: Submit a bug report with optional attachments
**Request**: `SubmitBugReportRequest`
**Response**: `SubmitBugReportResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: Handles bug reporting with logging and triage

### 53. ReportUser
**Purpose**: Report a user for inappropriate behavior
**Request**: `ReportUserRequest`
**Response**: `ReportUserResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: User reporting system for moderation

### 54. GetUserLanguagePreference
**Purpose**: Get user's preferred language setting
**Request**: `GetUserLanguagePreferenceRequest`
**Response**: `GetUserLanguagePreferenceResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Retrieves language preferences for localization

### 55. UpdateUserLanguagePreference
**Purpose**: Update user's language preference
**Request**: `UpdateUserLanguagePreferenceRequest`
**Response**: `UpdateUserLanguagePreferenceResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Updates language settings for user experience

### 56. DeactivateUser
**Purpose**: Deactivate user account (GDPR compliant)
**Request**: `DeactivateUserRequest`
**Response**: `DeactivateUserResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Temporarily deactivates account with data preservation

### 57. ReactivateUser
**Purpose**: Reactivate a previously deactivated user account
**Request**: `ReactivateUserRequest`
**Response**: `ReactivateUserResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Restores deactivated account access

### 58. UpdateUserLocation
**Purpose**: Update user's current location
**Request**: `UpdateUserLocationRequest`
**Response**: `UpdateUserLocationResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Updates location for proximity-based features

### 59. GetUserLocation
**Purpose**: Get user's current location (respects privacy)
**Request**: `GetUserLocationRequest`
**Response**: `GetUserLocationResponse`
**Authentication**: User session required
**Authorization**: Location sharing must be enabled and user must be connected
**Role**: Retrieves location with privacy controls

### 60. UpdateLocationPrivacy
**Purpose**: Update user's location privacy settings
**Request**: `UpdateLocationPrivacyRequest`
**Response**: `UpdateLocationPrivacyResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Manages location visibility preferences

### 61. GetFriendRequestHistory
**Purpose**: Get friend request history with pagination
**Request**: `GetFriendRequestHistoryRequest`
**Response**: `GetFriendRequestHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Historical view of friend requests

### 62. GetFormerBubbleMembers
**Purpose**: Get former bubble members for friend suggestions
**Request**: `GetFormerBubbleMembersRequest`
**Response**: `GetFormerBubbleMembersResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Suggests connections from past bubbles

### 63. GetMaybeFriends
**Purpose**: Get users with 'maybe_friend' relationship status
**Request**: `GetMaybeFriendsRequest`
**Response**: `GetMaybeFriendsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Potential friend suggestions from interactions

---

## Bubble Management Service (BubbleService)

**Service Package**: `hopen.bubble.v1.BubbleService`
**Total Methods**: 43
**Role**: Manages temporary social groups (bubbles), membership, unanimous voting system, and bubble-related operations

### 1. CreateBubble
**Purpose**: Create a new bubble (temporary social group)
**Request**: `CreateBubbleRequest`
**Response**: `CreateBubbleResponse`
**Authentication**: User session required
**Authorization**: Valid user session required (no additional authorization)
**Role**: Creates new bubbles with expiration and member management

### 2. GetBubble
**Purpose**: Retrieve bubble information by ID
**Request**: `GetBubbleRequest`
**Response**: `GetBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble, OR the user must have an `admin` role
**Role**: Returns bubble details and membership status

### 3. UpdateBubble
**Purpose**: Update bubble information (creator only)
**Request**: `UpdateBubbleRequest`
**Response**: `UpdateBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role
**Role**: Updates bubble details with creator validation

### 4. DeleteBubble
**Purpose**: Delete a bubble (creator only)
**Request**: `DeleteBubbleRequest`
**Response**: `DeleteBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role
**Role**: Removes bubble and all associated data

### 5. JoinBubble
**Purpose**: Join a bubble as a member
**Request**: `JoinBubbleRequest`
**Response**: `JoinBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Adds user to bubble membership

### 6. LeaveBubble
**Purpose**: Leave a bubble (remove membership)
**Request**: `LeaveBubbleRequest`
**Response**: `LeaveBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Removes user from bubble membership

### 7. KickMember
**Purpose**: Initiate a kickout request with unanimous voting system
**Request**: `KickMemberRequest`
**Response**: `KickMemberResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role (admins cannot kick other admins)
**Role**: Creates kickout request that requires unanimous approval from all active members

### 8. GetBubbleMembers
**Purpose**: Get list of bubble members
**Request**: `GetBubbleMembersRequest`
**Response**: `GetBubbleMembersResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble, OR the user must have an `admin` role
**Role**: Returns bubble membership list

### 9. GetUserBubbles
**Purpose**: Get bubbles that a user is a member of
**Request**: `GetUserBubblesRequest`
**Response**: `GetUserBubblesResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, OR the user must have an `admin` role
**Role**: Returns user's bubble memberships

### 10. GetNearbyBubbles
**Purpose**: Get nearby bubbles.
**Request**: `GetNearbyBubblesRequest`
**Response**: `GetNearbyBubblesResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: Returns nearby bubbles.

### 11. CheckBubbleMembership
**Purpose**: Check if a user is a member of a specific bubble
**Request**: `CheckBubbleMembershipRequest`
**Response**: `CheckBubbleMembershipResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble, OR the user must have an `admin` role
**Role**: Membership validation

### 12. BatchCheckBubbleMembership
**Purpose**: Batch check bubble membership.
**Request**: `BatchCheckBubbleMembershipRequest`
**Response**: `BatchCheckBubbleMembershipResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: Batch checks bubble membership.

### 13. GetUserBubblePermissions
**Purpose**: Get user bubble permissions.
**Request**: `GetUserBubblePermissionsRequest`
**Response**: `GetUserBubblePermissionsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble, OR the user must have an `admin` role
**Role**: Returns user bubble permissions.

### 14. GetBubbleRequests
**Purpose**: Get pending join/invite requests for a bubble
**Request**: `GetBubbleRequestsRequest`
**Response**: `GetBubbleRequestsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role
**Role**: Returns pending bubble requests

### 15. GetBubbleRequestById
**Purpose**: Get a bubble request by ID.
**Request**: `GetBubbleRequestByIdRequest`
**Response**: `GetBubbleRequestByIdResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role
**Role**: Returns a bubble request.

### 16. AcceptBubbleRequest
**Purpose**: Accept a join/invite request for a bubble
**Request**: `AcceptBubbleRequestRequest`
**Response**: `AcceptBubbleRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role
**Role**: Approves bubble membership requests

### 17. AcceptBubbleInvite
**Purpose**: Accept a bubble invite.
**Request**: `AcceptBubbleInviteRequest`
**Response**: `AcceptBubbleInviteResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the invitee
**Role**: Accepts a bubble invite.

### 18. AcceptJoinRequest
**Purpose**: Accept a join request.
**Request**: `AcceptJoinRequestRequest`
**Response**: `AcceptJoinRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble
**Role**: Accepts a join request.

### 19. RejectBubbleRequest
**Purpose**: Reject a join/invite request for a bubble
**Request**: `RejectBubbleRequestRequest`
**Response**: `RejectBubbleRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role
**Role**: Denies bubble membership requests

### 20. VoteOnKickoutRequest
**Purpose**: Vote on kickout requests (unanimous voting system)
**Request**: `VoteOnKickoutRequestRequest`
**Response**: `VoteOnKickoutRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be an active member of the bubble (excluding the target)
**Role**: Records votes for kickout requests with unanimous approval requirement

### 21. VoteOnJoinRequest
**Purpose**: Vote on a user's request to join a bubble (unanimous voting system)
**Request**: `VoteOnJoinRequestRequest`
**Response**: `VoteOnJoinRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be an active member of the bubble
**Role**: Records votes for join requests

### 22. VoteOnInviteRequest
**Purpose**: Vote on an invitation request (unanimous voting system)
**Request**: `VoteOnInviteRequestRequest`
**Response**: `VoteOnInviteRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be an active member of the bubble
**Role**: Records votes for invitation requests

### 23. GetKickoutHistory
**Purpose**: Get kickout history for a bubble.
**Request**: `GetKickoutHistoryRequest`
**Response**: `GetKickoutHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble
**Role**: Returns kickout history.

### 24. GetKickedOutMembers
**Purpose**: Get kicked out members from a bubble.
**Request**: `GetKickedOutMembersRequest`
**Response**: `GetKickedOutMembersResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble
**Role**: Returns kicked out members.

### 25. GetUserKickoutHistory
**Purpose**: Get user kickout history.
**Request**: `GetUserKickoutHistoryRequest`
**Response**: `GetUserKickoutHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns user kickout history.

### 26. GetUserJoinRequests
**Purpose**: Get user join requests.
**Request**: `GetUserJoinRequestsRequest`
**Response**: `GetUserJoinRequestsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns user join requests.

### 27. SortBubbleHistory
**Purpose**: Sort user's bubble history by criteria like duration or activity
**Request**: `SortBubbleHistoryRequest`
**Response**: `SortBubbleHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Custom sorting of bubble history

### 28. GetUserPendingRequests
**Purpose**: Get user pending requests.
**Request**: `GetUserPendingRequestsRequest`
**Response**: `GetUserPendingRequestsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns user pending requests.

### 29. UnbanUser
**Purpose**: Unban a user from a bubble.
**Request**: `UnbanUserRequest`
**Response**: `UnbanUserResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be an admin of the bubble
**Role**: Unbans a user from a bubble.

### 30. GetBubbleLocationHistory
**Purpose**: Get bubble location history.
**Request**: `GetBubbleLocationHistoryRequest`
**Response**: `GetBubbleLocationHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble
**Role**: Returns bubble location history.

### 31. SendBubbleInvite
**Purpose**: Send an invitation to join a bubble
**Request**: `SendBubbleInviteRequest`
**Response**: `SendBubbleInviteResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble
**Role**: Invites users to join bubbles

### 32. CreateBubbleStartRequest
**Purpose**: Create a request to start a bubble with a specific user
**Request**: `CreateBubbleStartRequestRequest`
**Response**: `CreateBubbleStartRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator or an admin of the bubble
**Role**: Initiates bubble start requests

### 33. AcceptBubbleStartRequest
**Purpose**: Accept a bubble start request
**Request**: `AcceptBubbleStartRequestRequest`
**Response**: `AcceptBubbleStartRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the target of the start request
**Role**: Approves bubble start requests

### 34. DeclineBubbleStartRequest
**Purpose**: Decline a bubble start request
**Request**: `DeclineBubbleStartRequestRequest`
**Response**: `DeclineBubbleStartRequestResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the target of the start request
**Role**: Rejects bubble start requests

### 35. GetBubbleAnalytics
**Purpose**: Get analytics and statistics for a bubble
**Request**: `GetBubbleAnalyticsRequest`
**Response**: `GetBubbleAnalyticsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role
**Role**: Returns bubble activity metrics

### 36. UpdateBubbleStatus
**Purpose**: Update the status of a bubble.
**Request**: `UpdateBubbleStatusRequest`
**Response**: `UpdateBubbleStatusResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble, OR the user must have an `admin` role
**Role**: Updates the status of a bubble.

### 37. UpdateMemberRole
**Purpose**: Update a member's role in a bubble.
**Request**: `UpdateMemberRoleRequest`
**Response**: `UpdateMemberRoleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator of the bubble
**Role**: Updates a member's role in a bubble.

### 38. GetBubbleHistory
**Purpose**: Get bubble history for a user.
**Request**: `GetBubbleHistoryRequest`
**Response**: `GetBubbleHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns bubble history.

### 39. GetActiveBubbles
**Purpose**: Get active bubbles for a user.
**Request**: `GetActiveBubblesRequest`
**Response**: `GetActiveBubblesResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns active bubbles.

### 40. GetCompletedBubbles
**Purpose**: Get completed bubbles for a user.
**Request**: `GetCompletedBubblesRequest`
**Response**: `GetCompletedBubblesResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns completed bubbles.

### 41. GetArchivedBubbles
**Purpose**: Get archived bubbles for a user.
**Request**: `GetArchivedBubblesRequest`
**Response**: `GetArchivedBubblesResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Returns archived bubbles.

### 42. SearchBubbleHistory
**Purpose**: Search bubble history for a user.
**Request**: `SearchBubbleHistoryRequest`
**Response**: `SearchBubbleHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Searches bubble history.

### 43. Health
**Purpose**: Check bubble service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

### 44. FilterBubblesByDateRange
**Purpose**: Filter bubbles by creation or activity date range
**Request**: `FilterBubblesByDateRangeRequest`
**Response**: `FilterBubblesByDateRangeResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Time-based bubble filtering for history review

### 45. SortBubbleHistory
**Purpose**: Sort user's bubble history by criteria like duration or activity
**Request**: `SortBubbleHistoryRequest`
**Response**: `SortBubbleHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Custom sorting of bubble history

### 46. ArchiveBubble
**Purpose**: Archive a completed bubble to hide from active lists
**Request**: `ArchiveBubbleRequest`
**Response**: `ArchiveBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator or admin of the bubble
**Role**: Organizes completed bubbles out of main views

### 47. UnarchiveBubble
**Purpose**: Unarchive a previously archived bubble
**Request**: `UnarchiveBubbleRequest`
**Response**: `UnarchiveBubbleResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the creator or admin of the bubble
**Role**: Restores archived bubbles to active history

### 48. GetBubbleMemberHistory
**Purpose**: Get historical membership changes for a bubble
**Request**: `GetBubbleMemberHistoryRequest`
**Response**: `GetBubbleMemberHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member or admin of the bubble
**Role**: Tracks membership over time for auditing

### 49. GetBubbleActivityHistory
**Purpose**: Get comprehensive activity log for a bubble
**Request**: `GetBubbleActivityHistoryRequest`
**Response**: `GetBubbleActivityHistoryResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member or admin of the bubble
**Role**: Full audit trail of bubble events

### 50. GetBubbleActivity
**Purpose**: Get recent activity feed for a bubble
**Request**: `GetBubbleActivityRequest`
**Response**: `GetBubbleActivityResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the bubble
**Role**: Real-time activity display in bubble UI

### 51. GetBubbleStatistics
**Purpose**: Get aggregated statistics for a bubble (members, messages, etc.)
**Request**: `GetBubbleStatisticsRequest`
**Response**: `GetBubbleStatisticsResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member or admin of the bubble
**Role**: Quick stats overview without full history load

---

## Call Management Service (CallService)

**Service Package**: `hopen.call.v1.CallService`
**Total Methods**: 24
**Role**: Manages voice and video calls, call history, and call-related operations

### 1. StartCall
**Purpose**: Initiate a new call
**Request**: `StartCallRequest`
**Response**: `StartCallResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, AND the user must be an active member of the target `bubble_id`
**Role**: Creates new call sessions

### 2. JoinCall
**Purpose**: Join an existing call
**Request**: `JoinCallRequest`
**Response**: `JoinCallResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request, AND the user must be an active member of the call's bubble
**Role**: Adds user to active call

### 3. LeaveCall
**Purpose**: Leave an active call
**Request**: `LeaveCallRequest`
**Response**: `LeaveCallResponse`
**Authentication**: User session required
**Role**: Removes user from call

### 4. EndCall
**Purpose**: End an active call (caller only)
**Request**: `EndCallRequest`
**Response**: `EndCallResponse`
**Authentication**: User session required
**Role**: Terminates call session

### 5. GetCall
**Purpose**: Get call information by ID
**Request**: `GetCallRequest`
**Response**: `GetCallResponse`
**Authentication**: User session required
**Role**: Returns call details

### 6. UpdateCallMedia
**Purpose**: Update call media settings (mute, camera, etc.)
**Request**: `UpdateCallMediaRequest`
**Response**: `UpdateCallMediaResponse`
**Authentication**: User session required
**Role**: Updates call media settings

### 7. GetCallParticipants
**Purpose**: Get list of call participants
**Request**: `GetCallParticipantsRequest`
**Response**: `GetCallParticipantsResponse`
**Authentication**: User session required
**Role**: Returns call participant list

### 8. UpdateParticipantMute
**Purpose**: Update participant mute status
**Request**: `UpdateParticipantMuteRequest`
**Response**: `UpdateParticipantMuteResponse`
**Authentication**: User session required
**Role**: Controls participant audio/video

### 9. GetCallHistory
**Purpose**: Get user's call history
**Request**: `GetCallHistoryRequest`
**Response**: `GetCallHistoryResponse`
**Authentication**: User session required
**Role**: Returns call history

### 10. GetCallStats
**Purpose**: Get call statistics for a user
**Request**: `GetCallStatsRequest`
**Response**: `GetCallStatsResponse`
**Authentication**: User session required
**Role**: Returns call metrics

### 11. StartCallRecording
**Purpose**: Start recording an active call
**Request**: `StartCallRecordingRequest`
**Response**: `StartCallRecordingResponse`
**Authentication**: User session required
**Role**: Initiates call recording

### 12. StopCallRecording
**Purpose**: Stop recording an active call
**Request**: `StopCallRecordingRequest`
**Response**: `StopCallRecordingResponse`
**Authentication**: User session required
**Role**: Stops call recording

### 13. PauseCallRecording
**Purpose**: Pause call recording
**Request**: `PauseCallRecordingRequest`
**Response**: `PauseCallRecordingResponse`
**Authentication**: User session required
**Role**: Pauses active recording

### 14. ResumeCallRecording
**Purpose**: Resume paused call recording
**Request**: `ResumeCallRecordingRequest`
**Response**: `ResumeCallRecordingResponse`
**Authentication**: User session required
**Role**: Resumes paused recording

### 15. GetCallRecordingStatus
**Purpose**: Get recording status for a call
**Request**: `GetCallRecordingStatusRequest`
**Response**: `GetCallRecordingStatusResponse`
**Authentication**: User session required
**Role**: Returns recording status

### 16. SetCallLayout
**Purpose**: Set the call layout for all participants.
**Request**: `SetCallLayoutRequest`
**Response**: `SetCallLayoutResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the call initiator or an admin
**Role**: Sets the call layout.

### 17. GetCallLayout
**Purpose**: Get the current call layout.
**Request**: `GetCallLayoutRequest`
**Response**: `GetCallLayoutResponse`
**Authentication**: User session required
**Role**: Returns the current call layout.

### 18. SetActiveSpeaker
**Purpose**: Set the active speaker in a call.
**Request**: `SetActiveSpeakerRequest`
**Response**: `SetActiveSpeakerResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the call initiator or an admin
**Role**: Sets the active speaker.

### 19. GetActiveSpeaker
**Purpose**: Get the active speaker in a call.
**Request**: `GetActiveSpeakerRequest`
**Response**: `GetActiveSpeakerResponse`
**Authentication**: User session required
**Role**: Returns the active speaker.

### 20. SetCallQualityProfile
**Purpose**: Set the quality profile for a call.
**Request**: `SetCallQualityProfileRequest`
**Response**: `SetCallQualityProfileResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be the call initiator or an admin
**Role**: Sets the call quality profile.

### 21. GetCallQualityProfile
**Purpose**: Get the quality profile for a call.
**Request**: `GetCallQualityProfileRequest`
**Response**: `GetCallQualityProfileResponse`
**Authentication**: User session required
**Role**: Returns the call quality profile.

### 22. ValidateCallState
**Purpose**: Proactively validate if a call is still active for UI synchronization
**Request**: `ValidateCallStateRequest`
**Response**: `ValidateCallStateResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the call's bubble
**Role**: Prevents navigation to non-existent calls and synchronizes UI state

### 23. UpdateParticipantQuality
**Purpose**: Update quality settings for a specific participant in a call
**Request**: `UpdateParticipantQualityRequest`
**Response**: `UpdateParticipantQualityResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a participant in the call
**Role**: Allows dynamic quality adjustment for individual participants

### 24. Health
**Purpose**: Check call service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

---

## Email Service (EmailService)

**Service Package**: `hopen.email.v1.EmailService`
**Total Methods**: 10
**Role**: Manages email delivery, templates, and analytics using AWS SES

### 1. SendEmail
**Purpose**: Send a single email
**Request**: `SendEmailRequest`
**Response**: `SendEmailResponse`
**Authentication**: Service-level authentication required
**Authorization**: Valid service credentials required
**Role**: Sends individual emails via AWS SES

### 2. SendTemplatedEmail
**Purpose**: Send email using a template
**Request**: `SendTemplatedEmailRequest`
**Response**: `SendEmailResponse`
**Authentication**: Service-level authentication required
**Authorization**: Valid service credentials required
**Role**: Sends templated emails with variable substitution

### 3. SendBulkEmail
**Purpose**: Send bulk emails efficiently
**Request**: `SendBulkEmailRequest`
**Response**: `SendBulkEmailResponse`
**Authentication**: Service-level authentication required
**Authorization**: Valid service credentials required
**Role**: Handles bulk email sending with batching

### 4. GetEmailStatus
**Purpose**: Get email delivery status
**Request**: `GetEmailStatusRequest`
**Response**: `GetEmailStatusResponse`
**Authentication**: Service-level authentication required
**Authorization**: Valid service credentials required
**Role**: Tracks email delivery status

### 5. GetEmailAnalytics
**Purpose**: Get email analytics and metrics
**Request**: `GetEmailAnalyticsRequest`
**Response**: `GetEmailAnalyticsResponse`
**Authentication**: Service-level authentication required
**Authorization**: Valid service credentials required
**Role**: Provides email performance analytics

### 6. HandleEmailBounce
**Purpose**: Handle email bounce notifications
**Request**: `HandleEmailBounceRequest`
**Response**: `HandleEmailBounceResponse`
**Authentication**: AWS SNS webhook authentication
**Authorization**: Valid SNS signature required
**Role**: Processes bounce notifications from AWS SES

### 7. HandleEmailComplaint
**Purpose**: Handle email complaint notifications
**Request**: `HandleEmailComplaintRequest`
**Response**: `HandleEmailComplaintResponse`
**Authentication**: AWS SNS webhook authentication
**Authorization**: Valid SNS signature required
**Role**: Processes complaint notifications from AWS SES

### 8. VerifyEmailAddress
**Purpose**: Verify email address with AWS SES
**Request**: `VerifyEmailAddressRequest`
**Response**: `VerifyEmailAddressResponse`
**Authentication**: Service-level authentication required
**Authorization**: Valid service credentials required
**Role**: Verifies email addresses for sending

### 9. GetEmailTemplates
**Purpose**: Get available email templates
**Request**: `GetEmailTemplatesRequest`
**Response**: `GetEmailTemplatesResponse`
**Authentication**: Service-level authentication required
**Authorization**: Valid service credentials required
**Role**: Lists available email templates

### 10. HealthCheck
**Purpose**: Check email service health
**Request**: `HealthCheckRequest`
**Response**: `HealthCheckResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

---

## Search Service (SearchService)

**Service Package**: `hopen.search.v1.SearchService`
**Total Methods**: 6
**Role**: Provides OpenSearch-powered user search with real-time indexing

### 1. SearchUsers
**Purpose**: Search for users with advanced filtering
**Request**: `SearchServiceSearchUsersRequest`
**Response**: `SearchServiceSearchUsersResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: OpenSearch-powered user search with relationship filtering

### 2. GetUserSuggestions
**Purpose**: Get user suggestions based on relationships
**Request**: `GetUserSuggestionsRequest`
**Response**: `GetUserSuggestionsResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: Provides intelligent user suggestions

### 3. SearchMessages
**Purpose**: Search for messages across conversations and bubbles
**Request**: `SearchMessagesRequest`
**Response**: `SearchMessagesResponse`
**Authentication**: User session required
**Authorization**: Valid user session required
**Role**: Full-text search across user's accessible messages

### 4. SearchBubbles
**Purpose**: Search for public bubbles by name, description, or other criteria
**Request**: `SearchBubblesRequest`
**Response**: `SearchBubblesResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Enables users to discover and join public bubbles based on their interests, improving bubble discoverability

### 5. GlobalSearch
**Purpose**: Perform comprehensive search across users, bubbles, and messages
**Request**: `GlobalSearchRequest`
**Response**: `GlobalSearchResponse`
**Authentication**: User session required
**Authorization**: The authenticated user's ID must match the `user_id` in the request
**Role**: Provides a unified search experience for all content types in the platform, offering powerful cross-content search capabilities

### 6. HealthCheck
**Purpose**: Check search service health
**Request**: `SearchServiceHealthCheckRequest`
**Response**: `SearchServiceHealthCheckResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

---

## Media Management Service (MediaService)

**Service Package**: `hopen.media.v1.MediaService`
**Total Methods**: 13
**Role**: Manages file uploads, media storage, and media-related operations

### 1. UploadFile
**Purpose**: Upload a file to the media storage
**Request**: `UploadFileRequest`
**Response**: `UploadFileResponse`
**Authentication**: User session required
**Role**: Handles file uploads with validation

### 2. GetFileInfo
**Purpose**: Get file information by ID
**Request**: `GetFileInfoRequest`
**Response**: `GetFileInfoResponse`
**Authentication**: User session required
**Role**: Returns file details

### 3. DeleteFile
**Purpose**: Delete a file from media storage
**Request**: `DeleteFileRequest`
**Response**: `DeleteFileResponse`
**Authentication**: User session required
**Role**: Removes files with authorization

### 4. GetFileUrl
**Purpose**: Get a signed URL for file access
**Request**: `GetFileUrlRequest`
**Response**: `GetFileUrlResponse`
**Authentication**: User session required
**Role**: Generates secure file access URLs

### 5. UploadProfilePicture
**Purpose**: Upload a profile picture for a user
**Request**: `UploadProfilePictureRequest`
**Response**: `UploadProfilePictureResponse`
**Authentication**: User session required
**Role**: Handles profile picture uploads

### 6. GetUserMedia
**Purpose**: Get media files uploaded by a user
**Request**: `GetUserMediaRequest`
**Response**: `GetUserMediaResponse`
**Authentication**: User session required
**Role**: Returns user's media files

### 7. GenerateThumbnail
**Purpose**: Generate thumbnail for a media file
**Request**: `GenerateThumbnailRequest`
**Response**: `GenerateThumbnailResponse`
**Authentication**: User session required
**Role**: Creates thumbnails for media files

### 8. GetMediaStats
**Purpose**: Get media statistics for a user
**Request**: `GetMediaStatsRequest`
**Response**: `GetMediaStatsResponse`
**Authentication**: User session required
**Role**: Returns media usage statistics

### 9. StreamFileUpload
**Purpose**: Stream file upload for large files
**Request**: `stream FileChunk`
**Response**: `UploadFileResponse`
**Authentication**: User session required
**Role**: Handles large file uploads via streaming

### 10. GetFileMetadata
**Purpose**: Get detailed metadata for a file
**Request**: `GetFileMetadataRequest`
**Response**: `GetFileMetadataResponse`
**Authentication**: User session required
**Role**: Returns comprehensive file metadata

### 11. UpdateFileMetadata
**Purpose**: Update file metadata atomically without re-uploading
**Request**: `UpdateFileMetadataRequest`
**Response**: `UpdateFileMetadataResponse`
**Authentication**: User session required
**Authorization**: File owner or admin permissions required
**Role**: Allows atomic metadata updates for existing files

### 12. CopyFile
**Purpose**: Copy file server-side without download/upload
**Request**: `CopyFileRequest`
**Response**: `CopyFileResponse`
**Authentication**: User session required
**Authorization**: Read permission on source file required
**Role**: Efficiently copies files within the storage system

### 13. Health
**Purpose**: Check media service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

---

## Notification Service (NotificationService)

**Service Package**: `hopen.notification.v1.NotificationService`
**Total Methods**: 13
**Role**: Manages push notifications, notification settings, and notification-related operations

### 1. RegisterFCMToken
**Purpose**: Register FCM token for push notifications
**Request**: `RegisterFCMTokenRequest`
**Response**: `RegisterFCMTokenResponse`
**Authentication**: User session required
**Role**: Registers device for push notifications

### 2. UnregisterFCMToken
**Purpose**: Unregister FCM token
**Request**: `UnregisterFCMTokenRequest`
**Response**: `UnregisterFCMTokenResponse`
**Authentication**: User session required
**Role**: Removes device from push notifications

### 3. SendPushNotification
**Purpose**: Send push notification to a specific user
**Request**: `SendPushNotificationRequest`
**Response**: `SendPushNotificationResponse`
**Authentication**: User session required
**Role**: Sends targeted push notifications

### 4. SendPushNotificationToTopic
**Purpose**: Send push notification to a topic
**Request**: `SendPushNotificationToTopicRequest`
**Response**: `SendPushNotificationToTopicResponse`
**Authentication**: User session required
**Role**: Sends broadcast notifications

### 5. GetUserNotifications
**Purpose**: Get user's notifications
**Request**: `GetUserNotificationsRequest`
**Response**: `GetUserNotificationsResponse`
**Authentication**: User session required
**Role**: Returns user's notification history

### 6. MarkNotificationAsRead
**Purpose**: Mark a notification as read
**Request**: `MarkNotificationAsReadRequest`
**Response**: `MarkNotificationAsReadResponse`
**Authentication**: User session required
**Role**: Updates notification read status

### 7. MarkAllNotificationsAsRead
**Purpose**: Mark all notifications as read
**Request**: `MarkAllNotificationsAsReadRequest`
**Response**: `MarkAllNotificationsAsReadResponse`
**Authentication**: User session required
**Role**: Marks all user notifications as read

### 8. DeleteNotification
**Purpose**: Delete a notification
**Request**: `DeleteNotificationRequest`
**Response**: `DeleteNotificationResponse`
**Authentication**: User session required
**Role**: Removes notifications

### 9. GetNotificationSettings
**Purpose**: Get user's notification settings
**Request**: `GetNotificationSettingsRequest`
**Response**: `GetNotificationSettingsResponse`
**Authentication**: User session required
**Role**: Returns notification preferences

### 10. UpdateNotificationSettings
**Purpose**: Update user's notification settings
**Request**: `UpdateNotificationSettingsRequest`
**Response**: `UpdateNotificationSettingsResponse`
**Authentication**: User session required
**Role**: Updates notification preferences

### 11. GetUnreadNotificationCount
**Purpose**: Get count of unread notifications
**Request**: `GetUnreadNotificationCountRequest`
**Response**: `GetUnreadNotificationCountResponse`
**Authentication**: User session required
**Role**: Returns unread notification count

### 12. SubscribeToTopic
**Purpose**: Subscribe user to a notification topic
**Request**: `SubscribeToTopicRequest`
**Response**: `SubscribeToTopicResponse`
**Authentication**: User session required
**Role**: Topic subscription management

### 13. UnsubscribeFromTopic
**Purpose**: Unsubscribe user from a notification topic
**Request**: `UnsubscribeFromTopicRequest`
**Response**: `UnsubscribeFromTopicResponse`
**Authentication**: User session required
**Role**: Topic unsubscription management

---

## Presence Service (PresenceService)

**Service Package**: `hopen.presence.v1.PresenceService`
**Total Methods**: 19
**Role**: Manages user online status, presence updates, and presence-related operations

### 1. GetUserPresence
**Purpose**: Get user's current presence status
**Request**: `GetUserPresenceRequest`
**Response**: `GetUserPresenceResponse`
**Authentication**: User session required
**Role**: Returns user presence information

### 2. UpdateUserPresence
**Purpose**: Update user's presence status
**Request**: `UpdateUserPresenceRequest`
**Response**: `UpdateUserPresenceResponse`
**Authentication**: User session required
**Role**: Updates user presence

### 3. GetBatchPresence
**Purpose**: Get presence status for multiple users
**Request**: `GetBatchPresenceRequest`
**Response**: `GetBatchPresenceResponse`
**Authentication**: User session required
**Role**: Returns presence for multiple users

### 4. GetOnlineUsers
**Purpose**: Get list of currently online users
**Request**: `GetOnlineUsersRequest`
**Response**: `GetOnlineUsersResponse`
**Authentication**: User session required
**Role**: Returns online users list

### 5. GetPresenceHistory
**Purpose**: Get user's presence history
**Request**: `GetPresenceHistoryRequest`
**Response**: `GetPresenceHistoryResponse`
**Authentication**: User session required
**Role**: Returns presence change history

### 6. SetUserAway
**Purpose**: Set user status to away
**Request**: `SetUserAwayRequest`
**Response**: `SetUserAwayResponse`
**Authentication**: User session required
**Role**: Sets user to away status

### 7. SetUserOnline
**Purpose**: Set user status to online
**Request**: `SetUserOnlineRequest`
**Response**: `SetUserOnlineResponse`
**Authentication**: User session required
**Role**: Sets user to online status

### 8. GetPresenceStats
**Purpose**: Get presence statistics
**Request**: `GetPresenceStatsRequest`
**Response**: `GetPresenceStatsResponse`
**Authentication**: User session required
**Role**: Returns presence metrics

### 9. SubscribeToPresenceUpdates
**Purpose**: Subscribe to presence updates for users
**Request**: `SubscribeToPresenceUpdatesRequest`
**Response**: `stream PresenceUpdate`
**Authentication**: User session required
**Role**: Real-time presence updates

### 10. SetTypingStatus
**Purpose**: Set typing status for a user in a bubble. This method now serves as the unified entry point for all typing indicators, consolidating functionality previously handled by RealtimeService's SendTypingIndicator.
**Request**: `SetTypingStatusRequest`
**Response**: `SetTypingStatusResponse`
**Authentication**: User session required
**Authorization**: The authenticated user must be a member of the specified bubble.
**Role**: Sets typing status with validation and broadcasting.

### 11. GetTypingUsersInBubble
**Purpose**: Get typing users in a bubble.
**Request**: `GetTypingUsersInBubbleRequest`
**Response**: `GetTypingUsersInBubbleResponse`
**Authentication**: User session required
**Role**: Returns typing users in a bubble.

### 12. WatchTypingStatus
**Purpose**: Watch typing status in a bubble.
**Request**: `WatchTypingStatusRequest`
**Response**: `stream TypingStatusUpdate`
**Authentication**: User session required
**Role**: Watches typing status in a bubble.

### 13. ClearTypingStatus
**Purpose**: Clear typing status for a user in a bubble.
**Request**: `ClearTypingStatusRequest`
**Response**: `ClearTypingStatusResponse`
**Authentication**: User session required
**Role**: Clears typing status.

### 14. RegisterDeviceSession
**Purpose**: Register a device session.
**Request**: `RegisterDeviceSessionRequest`
**Response**: `RegisterDeviceSessionResponse`
**Authentication**: User session required
**Role**: Registers a device session.

### 15. EndDeviceSession
**Purpose**: End a device session.
**Request**: `EndDeviceSessionRequest`
**Response**: `EndDeviceSessionResponse`
**Authentication**: User session required
**Role**: Ends a device session.

### 16. GetUserActiveSessions
**Purpose**: Get user active sessions.
**Request**: `GetUserActiveSessionsRequest`
**Response**: `GetUserActiveSessionsResponse`
**Authentication**: User session required
**Role**: Returns user active sessions.

### 17. GetOnlineFriends
**Purpose**: Get online friends.
**Request**: `GetOnlineFriendsRequest`
**Response**: `GetOnlineFriendsResponse`
**Authentication**: User session required
**Role**: Returns online friends.

### 18. GetOnlineContacts
**Purpose**: Get online contacts.
**Request**: `GetOnlineContactsRequest`
**Response**: `GetOnlineContactsResponse`
**Authentication**: User session required
**Role**: Returns online contacts.

### 19. Health
**Purpose**: Check presence service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

---

## Realtime Communication Service (RealtimeService)

**Service Package**: `hopen.realtime.v1.RealtimeService`
**Total Methods**: 24
**Role**: Manages real-time messaging, chat functionality, and real-time communication via MQTT v5 (NO WebSockets)
**Technology**: gRPC coordination + MQTT v5 for real-time chat messaging
**Note**: Typing indicators have been consolidated into PresenceService's SetTypingStatus method for unified presence management.

### 1. SendMessage
**Purpose**: Send a real-time message
**Request**: `SendMessageRequest`
**Response**: `SendMessageResponse`
**Authentication**: User session required
**Role**: Sends messages in real-time

### 2. GetChatMessages
**Purpose**: Get chat messages for a specific conversation or bubble
**Request**: `GetChatMessagesRequest`
**Response**: `GetChatMessagesResponse`
**Authentication**: User session required
**Role**: Returns chat message history

### 3. EditMessage
**Purpose**: Edit an existing message
**Request**: `EditMessageRequest`
**Response**: `EditMessageResponse`
**Authentication**: User session required
**Role**: Allows users to edit their own messages

### 4. DeleteMessage
**Purpose**: Delete a message
**Request**: `DeleteMessageRequest`
**Response**: `DeleteMessageResponse`
**Authentication**: User session required
**Role**: Removes messages with authorization

### 5. GetConversations
**Purpose**: Get user's conversations list
**Request**: `GetConversationsRequest`
**Response**: `GetConversationsResponse`
**Authentication**: User session required
**Role**: Returns user's conversation history

### 7. SendDirectMessage
**Purpose**: Send a direct message to another user
**Request**: `SendDirectMessageRequest`
**Response**: `SendDirectMessageResponse`
**Authentication**: User session required
**Role**: Direct messaging functionality

### 8. GetConversationMessages
**Purpose**: Get messages for a specific conversation
**Request**: `GetConversationMessagesRequest`
**Response**: `GetConversationMessagesResponse`
**Authentication**: User session required
**Role**: Returns conversation message history

### 9. GetConversation
**Purpose**: Get conversation information
**Request**: `GetConversationRequest`
**Response**: `GetConversationResponse`
**Authentication**: User session required
**Role**: Returns conversation details

### 10. GetChatInfo
**Purpose**: Get comprehensive chat information and metadata
**Request**: `GetChatInfoRequest`
**Response**: `GetChatInfoResponse`
**Authentication**: User session required
**Authorization**: User must have access to the chat (member of bubble or participant in conversation)
**Role**: Provides optimized chat metadata retrieval with single query

### 11. SearchChatMessages
**Purpose**: Search for messages within conversations
**Request**: `SearchChatMessagesRequest`
**Response**: `SearchChatMessagesResponse`
**Authentication**: User session required
**Role**: Message search within conversations

### 12. MarkMessageAsRead
**Purpose**: Mark a message as read
**Request**: `MarkMessageAsReadRequest`
**Response**: `MarkMessageAsReadResponse`
**Authentication**: User session required
**Role**: Updates message read status

### 13. ForwardMessage
**Purpose**: Forward a message to another conversation
**Request**: `ForwardMessageRequest`
**Response**: `ForwardMessageResponse`
**Authentication**: User session required
**Role**: Message forwarding functionality

### 14. GetMessages
**Purpose**: Get messages between users or in a bubble
**Request**: `GetMessagesRequest`
**Response**: `GetMessagesResponse`
**Authentication**: User session required
**Role**: Returns message history

### 15. ReactToMessage
**Purpose**: Add a reaction to a message
**Request**: `ReactToMessageRequest`
**Response**: `ReactToMessageResponse`
**Authentication**: User session required
**Role**: Adds message reactions

### 16. RemoveReaction
**Purpose**: Remove a reaction from a message
**Request**: `RemoveReactionRequest`
**Response**: `RemoveReactionResponse`
**Authentication**: User session required
**Role**: Removes message reactions

### 17. GetMessageReactions
**Purpose**: Get reactions for a message
**Request**: `GetMessageReactionsRequest`
**Response**: `GetMessageReactionsResponse`
**Authentication**: User session required
**Role**: Returns message reactions

### 18. SubscribeToMessages
**Purpose**: Subscribe to real-time message updates
**Request**: `SubscribeToMessagesRequest`
**Response**: `stream MessageUpdate`
**Authentication**: User session required
**Role**: Real-time message streaming

### 19. GetMessageReadReceipts
**Purpose**: Get read receipts for a message
**Request**: `GetMessageReadReceiptsRequest`
**Response**: `GetMessageReadReceiptsResponse`
**Authentication**: User session required
**Role**: Returns message read status

### 20. GetBubbleParticipantReadStatus
**Purpose**: Get read status for all participants in a bubble
**Request**: `GetBubbleParticipantReadStatusRequest`
**Response**: `GetBubbleParticipantReadStatusResponse`
**Authentication**: User session required
**Role**: Returns bubble message read status

### 21. GetUserReadStatusInBubble
**Purpose**: Get user's read status in a specific bubble
**Request**: `GetUserReadStatusInBubbleRequest`
**Response**: `GetUserReadStatusInBubbleResponse`
**Authentication**: User session required
**Role**: Returns user's bubble read status

### 22. MarkMessageAsReadByParticipant
**Purpose**: Mark message as read by a specific participant
**Request**: `MarkMessageAsReadByParticipantRequest`
**Response**: `MarkMessageAsReadByParticipantResponse`
**Authentication**: User session required
**Role**: Updates participant read status

### 23. MarkAllMessagesAsRead
**Purpose**: Mark all messages as read in a conversation
**Request**: `MarkAllMessagesAsReadRequest`
**Response**: `MarkAllMessagesAsReadResponse`
**Authentication**: User session required
**Role**: Bulk read status update

### 24. GetUnreadCount
**Purpose**: Get unread message count for user
**Request**: `GetUnreadCountRequest`
**Response**: `GetUnreadCountResponse`
**Authentication**: User session required
**Role**: Returns unread message counts

### 25. PinMessage
**Purpose**: Pin a message in a bubble for all members to see
**Request**: `PinMessageRequest`
**Response**: `PinMessageResponse`
**Authentication**: User session required
**Authorization**: User must be a member of the bubble with appropriate permissions
**Role**: Message pinning for important announcements

### 26. UnpinMessage
**Purpose**: Unpin a previously pinned message in a bubble
**Request**: `UnpinMessageRequest`
**Response**: `UnpinMessageResponse`
**Authentication**: User session required
**Authorization**: User must be a member of the bubble with appropriate permissions
**Role**: Message unpinning management

### 27. GetPinnedMessages
**Purpose**: Retrieve all pinned messages in a bubble
**Request**: `GetPinnedMessagesRequest`
**Response**: `GetPinnedMessagesResponse`
**Authentication**: User session required
**Authorization**: User must be a member of the bubble
**Role**: Pinned message discovery

### 28. CreatePoll
**Purpose**: Create a poll in a bubble for member voting
**Request**: `CreatePollRequest`
**Response**: `CreatePollResponse`
**Authentication**: User session required
**Authorization**: User must be a member of the bubble
**Role**: Interactive polling for decision making

### 29. VoteOnPoll
**Purpose**: Cast votes on an existing poll in a bubble
**Request**: `VoteOnPollRequest`
**Response**: `VoteOnPollResponse`
**Authentication**: User session required
**Authorization**: User must be a member of the bubble and poll must be active
**Role**: Democratic voting participation

### 30. GetPollResults
**Purpose**: Retrieve current results and status of a poll
**Request**: `GetPollResultsRequest`
**Response**: `GetPollResultsResponse`
**Authentication**: User session required
**Authorization**: User must be a member of the bubble
**Role**: Poll result viewing and analysis

### 31. ReplyInThread
**Purpose**: Reply to a message in a threaded conversation
**Request**: `ReplyInThreadRequest`
**Response**: `SendMessageResponse`
**Authentication**: User session required
**Authorization**: User must be a member of the bubble
**Role**: Threaded conversation management

### 32. GetThreadMessages
**Purpose**: Retrieve messages in a specific thread
**Request**: `GetThreadMessagesRequest`
**Response**: `GetChatMessagesResponse`
**Authentication**: User session required
**Authorization**: User must be a member of the bubble
**Role**: Thread message retrieval and navigation

### 33. Health
**Purpose**: Check realtime service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

---

## Social Analytics Service (SocialAnalyticsService)

**Service Package**: `hopen.social_analytics.v1.SocialAnalyticsService`
**Total Methods**: 26
**Role**: Manages social analytics, user behavior tracking, and analytics-related operations

### 1. GetEnhancedProfile
**Purpose**: Get enhanced profile with comprehensive relationship data
**Request**: `GetEnhancedProfileRequest`
**Response**: `GetEnhancedProfileResponse`
**Authentication**: User session required
**Role**: Returns enhanced user profile with social analytics

### 2. GetProfileAnalytics
**Purpose**: Get profile analytics and insights
**Request**: `GetProfileAnalyticsRequest`
**Response**: `GetProfileAnalyticsResponse`
**Authentication**: User session required
**Role**: Returns profile analytics data

### 3. GetMutualFriendsAnalytics
**Purpose**: Get analytics about mutual friends
**Request**: `GetMutualFriendsAnalyticsRequest`
**Response**: `GetMutualFriendsAnalyticsResponse`
**Authentication**: User session required
**Role**: Returns mutual friends analytics

### 4. GetMutualContacts
**Purpose**: Get mutual contacts between users
**Request**: `GetMutualContactsRequest`
**Response**: `GetMutualContactsResponse`
**Authentication**: User session required
**Role**: Returns mutual contacts data

### 5. GetCommonBubbles
**Purpose**: Get common bubbles between users
**Request**: `GetCommonBubblesRequest`
**Response**: `GetCommonBubblesResponse`
**Authentication**: User session required
**Role**: Returns common bubble memberships

### 6. GetConnectionStrength
**Purpose**: Get connection strength between users
**Request**: `GetConnectionStrengthRequest`
**Response**: `GetConnectionStrengthResponse`
**Authentication**: User session required
**Role**: Returns connection strength metrics

### 7. GetSocialGraph
**Purpose**: Get social graph data for a user
**Request**: `GetSocialGraphRequest`
**Response**: `GetSocialGraphResponse`
**Authentication**: User session required
**Role**: Returns social graph structure

### 8. GetEngagementMetrics
**Purpose**: Get user engagement metrics
**Request**: `GetEngagementMetricsRequest`
**Response**: `GetEngagementMetricsResponse`
**Authentication**: User session required
**Role**: Returns engagement analytics

### 9. TrackUserActivity
**Purpose**: Track user activity for analytics
**Request**: `TrackUserActivityRequest`
**Response**: `TrackUserActivityResponse`
**Authentication**: User session required
**Role**: Records user activity events

### 10. GetUserAnalytics
**Purpose**: Get analytics for a specific user
**Request**: `GetUserAnalyticsRequest`
**Response**: `GetUserAnalyticsResponse`
**Authentication**: User session required
**Role**: Returns user analytics

### 11. GetBubbleAnalytics
**Purpose**: Get analytics for a bubble
**Request**: `GetSocialBubbleAnalyticsRequest`
**Response**: `GetSocialBubbleAnalyticsResponse`
**Authentication**: User session required
**Role**: Returns bubble analytics

### 12. GetSystemAnalytics
**Purpose**: Get system-wide analytics
**Request**: `GetSystemAnalyticsRequest`
**Response**: `GetSystemAnalyticsResponse`
**Authentication**: Admin session required
**Role**: Returns system analytics

### 13. TrackEvent
**Purpose**: Track custom events for analytics
**Request**: `TrackEventRequest`
**Response**: `TrackEventResponse`
**Authentication**: User session required
**Role**: Records custom analytics events

### 14. GetUserEngagement
**Purpose**: Get user engagement metrics
**Request**: `GetUserEngagementRequest`
**Response**: `GetUserEngagementResponse`
**Authentication**: User session required
**Role**: Returns engagement metrics

### 15. GetPopularContent
**Purpose**: Get popular content analytics
**Request**: `GetPopularContentRequest`
**Response**: `GetPopularContentResponse`
**Authentication**: User session required
**Role**: Returns popular content metrics

### 16. GetUserGrowth
**Purpose**: Get user growth analytics
**Request**: `GetUserGrowthRequest`
**Response**: `GetUserGrowthResponse`
**Authentication**: Admin session required
**Role**: Returns user growth metrics

### 17. GetRetentionMetrics
**Purpose**: Get user retention metrics
**Request**: `GetRetentionMetricsRequest`
**Response**: `GetRetentionMetricsResponse`
**Authentication**: Admin session required
**Role**: Returns retention analytics

### 18. ExportAnalytics
**Purpose**: Export analytics data
**Request**: `ExportAnalyticsRequest`
**Response**: `ExportAnalyticsResponse`
**Authentication**: Admin session required
**Role**: Exports analytics data

### 19. GetRealTimeMetrics
**Purpose**: Get real-time analytics metrics
**Request**: `GetRealTimeMetricsRequest`
**Response**: `GetRealTimeMetricsResponse`
**Authentication**: Admin session required
**Role**: Returns real-time metrics

### 20. ReconcileUserAnalytics
**Purpose**: Reconcile user analytics data for consistency
**Request**: `ReconcileUserAnalyticsRequest`
**Response**: `ReconcileUserAnalyticsResponse`
**Authentication**: Admin session required
**Role**: Data consistency maintenance for user analytics

### 21. ReconcileBubbleAnalytics
**Purpose**: Reconcile bubble analytics data for consistency
**Request**: `ReconcileBubbleAnalyticsRequest`
**Response**: `ReconcileBubbleAnalyticsResponse`
**Authentication**: Admin session required
**Role**: Data consistency maintenance for bubble analytics

### 22. ReconcileAllUserAnalytics
**Purpose**: Reconcile all user analytics data system-wide
**Request**: `ReconcileAllUserAnalyticsRequest`
**Response**: `ReconcileAllUserAnalyticsResponse`
**Authentication**: Admin session required
**Role**: System-wide user analytics reconciliation

### 23. ReconcileAllBubbleAnalytics
**Purpose**: Reconcile all bubble analytics data system-wide
**Request**: `ReconcileAllBubbleAnalyticsRequest`
**Response**: `ReconcileAllBubbleAnalyticsResponse`
**Authentication**: Admin session required
**Role**: System-wide bubble analytics reconciliation

### 24. GetReconciliationStatus
**Purpose**: Get status of reconciliation processes
**Request**: `GetReconciliationStatusRequest`
**Response**: `GetReconciliationStatusResponse`
**Authentication**: Admin session required
**Role**: Monitor reconciliation process status

### 25. GetReconciliationMetrics
**Purpose**: Get metrics about reconciliation processes
**Request**: `GetReconciliationMetricsRequest`
**Response**: `GetReconciliationMetricsResponse`
**Authentication**: Admin session required
**Role**: Analytics on reconciliation performance

### 26. Health
**Purpose**: Check social analytics service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

### 26. Health
**Purpose**: Check social analytics service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring

---

## Sync Service (SyncService)

**Service Package**: `hopen.sync.v1.SyncService`
**Total Methods**: 11
**Role**: Manages data synchronization, conflict resolution, and sync-related operations

### 1. SyncInitialState
**Purpose**: Get complete initial state for authenticated user
**Request**: `SyncInitialStateRequest`
**Response**: `SyncInitialStateResponse`
**Authentication**: User session required
**Role**: Provides complete initial data for new device synchronization

### 2. GetSyncStatus
**Purpose**: Get synchronization status
**Request**: `GetSyncStatusRequest`
**Response**: `GetSyncStatusResponse`
**Authentication**: User session required
**Role**: Returns sync status

### 3. ForceSyncRefresh
**Purpose**: Force a refresh of synchronization data
**Request**: `ForceSyncRefreshRequest`
**Response**: `ForceSyncRefreshResponse`
**Authentication**: User session required
**Role**: Forces refresh of sync data without full sync

### 4. GetSyncData
**Purpose**: Get data for synchronization
**Request**: `GetSyncDataRequest`
**Response**: `GetSyncDataResponse`
**Authentication**: User session required
**Role**: Returns data for client synchronization

### 5. PushSyncData
**Purpose**: Push data from client to server
**Request**: `PushSyncDataRequest`
**Response**: `PushSyncDataResponse`
**Authentication**: User session required
**Role**: Receives client data for synchronization

### 6. ResolveSyncConflict
**Purpose**: Resolve synchronization conflicts
**Request**: `ResolveSyncConflictRequest`
**Response**: `ResolveSyncConflictResponse`
**Authentication**: User session required
**Role**: Resolves data conflicts

### 7. GetSyncHistory
**Purpose**: Get synchronization history
**Request**: `GetSyncHistoryRequest`
**Response**: `GetSyncHistoryResponse`
**Authentication**: User session required
**Role**: Returns sync history

### 8. ClearSyncData
**Purpose**: Clear synchronization data
**Request**: `ClearSyncDataRequest`
**Response**: `ClearSyncDataResponse`
**Authentication**: User session required
**Role**: Clears sync data for device

### 9. GetSyncConflicts
**Purpose**: Get unresolved synchronization conflicts
**Request**: `GetSyncConflictsRequest`
**Response**: `GetSyncConflictsResponse`
**Authentication**: User session required
**Role**: Returns sync conflicts

### 10. GetAllPendingRequests
**Purpose**: Get all pending requests for a user (unified endpoint)
**Request**: `GetAllPendingRequestsRequest`
**Response**: `GetAllPendingRequestsResponse`
**Authentication**: User session required
**Role**: Aggregates pending requests from user and bubble services for efficiency

### 11. Health
**Purpose**: Check sync service health
**Request**: `hopen.common.v1.Empty`
**Response**: `hopen.common.v1.HealthResponse`
**Authentication**: Not required
**Authorization**: No authorization required (public endpoint)
**Role**: Service health monitoring
