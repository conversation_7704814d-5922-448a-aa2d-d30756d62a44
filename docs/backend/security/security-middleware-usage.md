# 🔐 Security Middleware Usage Guide
*Comprehensive Security Implementation for Hopen Backend*

**Last Updated:** August 2025  
**Version:** 3.0.0  
**Status:** ✅ **Production-Ready**

---

## 🌟 **Overview**

The Security Middleware system provides **comprehensive security protection** for the Hopen backend with multi-layered defense strategies, input validation, authentication, and threat prevention. This system implements zero-trust architecture with advanced security measures.

**📊 Implementation Status:**
- **Multi-layered security** with defense in depth
- **Input validation** and sanitization
- **Authentication middleware** with JWT validation
- **Rate limiting** and DDoS protection
- **CORS handling** and security headers
- **Threat detection** and monitoring

---

## 🏗️ **Security Architecture**

### **Security Layers**
```
Security Middleware Stack
├── Rate Limiting
├── CORS & Security Headers
├── Authentication & Authorization
├── Input Validation & Sanitization
├── Threat Detection
├── Logging & Monitoring
└── Error Handling
```

### **Technology Stack**
- **Authentication**: <PERSON><PERSON> + JWT
- **Rate Limiting**: Valkey (Redis-compatible)
- **Input Validation**: go-playground/validator
- **Content Sanitization**: bluemonday
- **Security Headers**: Custom middleware
- **Monitoring**: Prometheus + Grafana

---

## 🔐 **Authentication Middleware**

### **JWT Authentication**

**Middleware Implementation:**
```go
type AuthMiddleware struct {
    jwtSecret []byte
    userRepo  UserRepository
}

func (am *AuthMiddleware) Authenticate(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Extract JWT token from Authorization header
        authHeader := r.Header.Get("Authorization")
        if authHeader == "" {
            http.Error(w, "Authorization header required", http.StatusUnauthorized)
            return
        }
        
        // Parse Bearer token
        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        if tokenString == authHeader {
            http.Error(w, "Invalid authorization format", http.StatusUnauthorized)
            return
        }
        
        // Validate JWT token
        claims, err := am.validateJWT(tokenString)
        if err != nil {
            http.Error(w, "Invalid token", http.StatusUnauthorized)
            return
        }
        
        // Add user context to request
        ctx := context.WithValue(r.Context(), "user_id", claims.UserID)
        ctx = context.WithValue(ctx, "user_permissions", claims.Permissions)
        
        next.ServeHTTP(w, r.WithContext(ctx))
    })
}

func (am *AuthMiddleware) validateJWT(tokenString string) (*JWTClaims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
        if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
            return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
        }
        return am.jwtSecret, nil
    })
    
    if err != nil {
        return nil, err
    }
    
    if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
        return claims, nil
    }
    
    return nil, fmt.Errorf("invalid token")
}
```

### **Role-Based Access Control**

**Permission Middleware:**
```go
type PermissionMiddleware struct {
    requiredPermissions []string
}

func (pm *PermissionMiddleware) RequirePermissions(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        userPermissions := r.Context().Value("user_permissions").([]string)
        
        // Check if user has required permissions
        for _, required := range pm.requiredPermissions {
            hasPermission := false
            for _, userPerm := range userPermissions {
                if userPerm == required {
                    hasPermission = true
                    break
                }
            }
            if !hasPermission {
                http.Error(w, "Insufficient permissions", http.StatusForbidden)
                return
            }
        }
        
        next.ServeHTTP(w, r)
    })
}
```

---

## 🛡️ **Input Validation & Sanitization**

### **Request Validation**

**Validation Middleware:**
```go
type ValidationMiddleware struct {
    validator *validator.Validate
}

func (vm *ValidationMiddleware) ValidateRequest(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Parse request body
        var request interface{}
        if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
            http.Error(w, "Invalid request body", http.StatusBadRequest)
            return
        }
        
        // Validate request structure
        if err := vm.validator.Struct(request); err != nil {
            http.Error(w, fmt.Sprintf("Validation failed: %v", err), http.StatusBadRequest)
            return
        }
        
        // Sanitize input
        sanitized := vm.sanitizeInput(request)
        
        // Add sanitized data to context
        ctx := context.WithValue(r.Context(), "sanitized_data", sanitized)
        
        next.ServeHTTP(w, r.WithContext(ctx))
    })
}

func (vm *ValidationMiddleware) sanitizeInput(input interface{}) interface{} {
    // Implement input sanitization logic
    sanitizer := bluemonday.UGCPolicy()
    
    // Sanitize string fields
    if str, ok := input.(string); ok {
        return sanitizer.Sanitize(str)
    }
    
    // Recursively sanitize struct fields
    return vm.sanitizeStruct(input)
}
```

### **Content Sanitization**

**HTML Sanitization:**
```go
import "github.com/microcosm-cc/bluemonday"

type ContentSanitizer struct {
    policy *bluemonday.Policy
}

func NewContentSanitizer() *ContentSanitizer {
    // Create UGC policy for user-generated content
    policy := bluemonday.UGCPolicy()
    
    // Allow specific tags and attributes
    policy.AllowElements("p", "br", "strong", "em", "u")
    policy.AllowAttrs("class").OnElements("p", "span")
    
    return &ContentSanitizer{
        policy: policy,
    }
}

func (cs *ContentSanitizer) SanitizeHTML(content string) string {
    return cs.policy.Sanitize(content)
}

func (cs *ContentSanitizer) SanitizeText(content string) string {
    // Remove HTML tags and sanitize text
    return cs.policy.Sanitize(content)
}
```

---

## 🚫 **Rate Limiting**

### **Rate Limiter Implementation**

**Token Bucket Rate Limiter:**
```go
type RateLimiter struct {
    client *redis.Client
    limit  int
    window time.Duration
}

func NewRateLimiter(client *redis.Client, limit int, window time.Duration) *RateLimiter {
    return &RateLimiter{
        client: client,
        limit:  limit,
        window: window,
    }
}

func (rl *RateLimiter) RateLimit(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Get client identifier (IP, user ID, etc.)
        clientID := rl.getClientID(r)
        
        // Check rate limit
        allowed, remaining, resetTime, err := rl.checkRateLimit(clientID)
        if err != nil {
            http.Error(w, "Rate limit check failed", http.StatusInternalServerError)
            return
        }
        
        if !allowed {
            w.Header().Set("X-RateLimit-Limit", strconv.Itoa(rl.limit))
            w.Header().Set("X-RateLimit-Remaining", strconv.Itoa(remaining))
            w.Header().Set("X-RateLimit-Reset", strconv.FormatInt(resetTime, 10))
            http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
            return
        }
        
        // Add rate limit headers
        w.Header().Set("X-RateLimit-Limit", strconv.Itoa(rl.limit))
        w.Header().Set("X-RateLimit-Remaining", strconv.Itoa(remaining))
        w.Header().Set("X-RateLimit-Reset", strconv.FormatInt(resetTime, 10))
        
        next.ServeHTTP(w, r)
    })
}

func (rl *RateLimiter) checkRateLimit(clientID string) (bool, int, int64, error) {
    key := fmt.Sprintf("rate_limit:%s", clientID)
    now := time.Now().Unix()
    
    // Use Redis pipeline for atomic operations
    pipe := rl.client.Pipeline()
    
    // Add current timestamp to sorted set
    pipe.ZAdd(key, &redis.Z{Score: float64(now), Member: now})
    
    // Remove old entries outside window
    pipe.ZRemRangeByScore(key, "0", strconv.FormatInt(now-int64(rl.window.Seconds()), 10))
    
    // Count entries in window
    pipe.ZCard(key)
    
    // Set expiration
    pipe.Expire(key, rl.window)
    
    cmds, err := pipe.Exec()
    if err != nil {
        return false, 0, 0, err
    }
    
    count := cmds[2].(*redis.IntCmd).Val()
    allowed := count <= int64(rl.limit)
    remaining := int(rl.limit) - int(count)
    if remaining < 0 {
        remaining = 0
    }
    
    return allowed, remaining, now + int64(rl.window.Seconds()), nil
}
```

### **Per-Service Rate Limiting**

**Service-Specific Limits:**
```go
type ServiceRateLimiter struct {
    limiters map[string]*RateLimiter
}

func NewServiceRateLimiter(client *redis.Client) *ServiceRateLimiter {
    return &ServiceRateLimiter{
        limiters: map[string]*RateLimiter{
            "auth":      NewRateLimiter(client, 200, time.Minute),
            "user":      NewRateLimiter(client, 300, time.Minute),
            "bubble":    NewRateLimiter(client, 500, time.Minute),
            "call":      NewRateLimiter(client, 100, time.Minute),
            "email":     NewRateLimiter(client, 200, time.Minute),
            "search":    NewRateLimiter(client, 300, time.Minute),
            "media":     NewRateLimiter(client, 50, time.Minute),
            "realtime":  NewRateLimiter(client, 1000, time.Minute),
        },
    }
}

func (srl *ServiceRateLimiter) RateLimitByService(serviceName string) func(http.Handler) http.Handler {
    limiter, exists := srl.limiters[serviceName]
    if !exists {
        // Default rate limiter
        limiter = NewRateLimiter(srl.client, 200, time.Minute)
    }
    
    return limiter.RateLimit
}
```

---

## 🌐 **CORS & Security Headers**

### **CORS Middleware**

**CORS Configuration:**
```go
type CORSMiddleware struct {
    allowedOrigins []string
    allowedMethods []string
    allowedHeaders []string
}

func NewCORSMiddleware() *CORSMiddleware {
    return &CORSMiddleware{
        allowedOrigins: []string{"*"}, // Configure for production
        allowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
        allowedHeaders: []string{"Content-Type", "Authorization", "X-Requested-With"},
    }
}

func (cm *CORSMiddleware) CORS(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        origin := r.Header.Get("Origin")
        
        // Check if origin is allowed
        if cm.isOriginAllowed(origin) {
            w.Header().Set("Access-Control-Allow-Origin", origin)
        }
        
        w.Header().Set("Access-Control-Allow-Methods", strings.Join(cm.allowedMethods, ", "))
        w.Header().Set("Access-Control-Allow-Headers", strings.Join(cm.allowedHeaders, ", "))
        w.Header().Set("Access-Control-Allow-Credentials", "true")
        w.Header().Set("Access-Control-Max-Age", "3600")
        
        // Handle preflight requests
        if r.Method == "OPTIONS" {
            w.WriteHeader(http.StatusOK)
            return
        }
        
        next.ServeHTTP(w, r)
    })
}

func (cm *CORSMiddleware) isOriginAllowed(origin string) bool {
    for _, allowed := range cm.allowedOrigins {
        if allowed == "*" || allowed == origin {
            return true
        }
    }
    return false
}
```

### **Security Headers**

**Security Headers Middleware:**
```go
type SecurityHeadersMiddleware struct{}

func (shm *SecurityHeadersMiddleware) SecurityHeaders(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Security headers
        w.Header().Set("X-Content-Type-Options", "nosniff")
        w.Header().Set("X-Frame-Options", "DENY")
        w.Header().Set("X-XSS-Protection", "1; mode=block")
        w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
        w.Header().Set("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")
        w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        w.Header().Set("Permissions-Policy", "geolocation=(), microphone=(), camera=()")
        
        // Remove server information
        w.Header().Set("Server", "")
        
        next.ServeHTTP(w, r)
    })
}
```

---

## 🔍 **Threat Detection**

### **Threat Detection Middleware**

**Anomaly Detection:**
```go
type ThreatDetector struct {
    client *redis.Client
    alerts chan ThreatAlert
}

type ThreatAlert struct {
    Type        string    `json:"type"`
    ClientID    string    `json:"client_id"`
    IP          string    `json:"ip"`
    UserAgent   string    `json:"user_agent"`
    Timestamp   time.Time `json:"timestamp"`
    Severity    string    `json:"severity"`
    Description string    `json:"description"`
}

func (td *ThreatDetector) DetectThreats(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        clientID := td.getClientID(r)
        ip := td.getClientIP(r)
        
        // Check for suspicious patterns
        if td.isSuspiciousRequest(r) {
            alert := ThreatAlert{
                Type:        "suspicious_request",
                ClientID:    clientID,
                IP:          ip,
                UserAgent:   r.UserAgent(),
                Timestamp:   time.Now(),
                Severity:    "medium",
                Description: "Suspicious request pattern detected",
            }
            
            td.alerts <- alert
        }
        
        // Check for brute force attempts
        if td.isBruteForceAttempt(clientID, ip) {
            alert := ThreatAlert{
                Type:        "brute_force",
                ClientID:    clientID,
                IP:          ip,
                UserAgent:   r.UserAgent(),
                Timestamp:   time.Now(),
                Severity:    "high",
                Description: "Brute force attempt detected",
            }
            
            td.alerts <- alert
            
            // Block the request
            http.Error(w, "Access denied", http.StatusForbidden)
            return
        }
        
        next.ServeHTTP(w, r)
    })
}

func (td *ThreatDetector) isSuspiciousRequest(r *http.Request) bool {
    // Check for SQL injection patterns
    suspiciousPatterns := []string{
        "'; DROP TABLE",
        "UNION SELECT",
        "OR 1=1",
        "'; INSERT INTO",
    }
    
    query := r.URL.RawQuery + r.Header.Get("User-Agent")
    for _, pattern := range suspiciousPatterns {
        if strings.Contains(strings.ToLower(query), strings.ToLower(pattern)) {
            return true
        }
    }
    
    return false
}

func (td *ThreatDetector) isBruteForceAttempt(clientID, ip string) bool {
    key := fmt.Sprintf("brute_force:%s:%s", clientID, ip)
    
    count, err := td.client.Incr(key).Result()
    if err != nil {
        return false
    }
    
    // Set expiration for 1 hour
    td.client.Expire(key, time.Hour)
    
    // Block after 10 failed attempts
    return count > 10
}
```

---

## 📊 **Logging & Monitoring**

### **Security Logging**

**Security Logger:**
```go
type SecurityLogger struct {
    logger *zap.Logger
}

func NewSecurityLogger() *SecurityLogger {
    logger, _ := zap.NewProduction()
    return &SecurityLogger{logger: logger}
}

func (sl *SecurityLogger) LogSecurityEvent(event SecurityEvent) {
    sl.logger.Info("Security event",
        zap.String("type", event.Type),
        zap.String("client_id", event.ClientID),
        zap.String("ip", event.IP),
        zap.String("user_agent", event.UserAgent),
        zap.String("severity", event.Severity),
        zap.String("description", event.Description),
        zap.Time("timestamp", event.Timestamp),
    )
}

type SecurityEvent struct {
    Type        string    `json:"type"`
    ClientID    string    `json:"client_id"`
    IP          string    `json:"ip"`
    UserAgent   string    `json:"user_agent"`
    Severity    string    `json:"severity"`
    Description string    `json:"description"`
    Timestamp   time.Time `json:"timestamp"`
}
```

### **Metrics Collection**

**Security Metrics:**
```go
type SecurityMetrics struct {
    FailedAuthAttempts    prometheus.Counter
    RateLimitExceeded     prometheus.Counter
    SuspiciousRequests    prometheus.Counter
    BruteForceAttempts    prometheus.Counter
    SecurityAlerts        prometheus.Counter
    RequestDuration       prometheus.Histogram
}

func NewSecurityMetrics() *SecurityMetrics {
    return &SecurityMetrics{
        FailedAuthAttempts: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "security_failed_auth_attempts_total",
            Help: "Total number of failed authentication attempts",
        }),
        RateLimitExceeded: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "security_rate_limit_exceeded_total",
            Help: "Total number of rate limit exceeded events",
        }),
        SuspiciousRequests: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "security_suspicious_requests_total",
            Help: "Total number of suspicious requests detected",
        }),
        BruteForceAttempts: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "security_brute_force_attempts_total",
            Help: "Total number of brute force attempts",
        }),
        SecurityAlerts: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "security_alerts_total",
            Help: "Total number of security alerts",
        }),
        RequestDuration: prometheus.NewHistogram(prometheus.HistogramOpts{
            Name: "security_request_duration_seconds",
            Help: "Duration of security middleware processing",
        }),
    }
}
```

---

## 🚀 **Middleware Chain**

### **Complete Middleware Stack**

**Middleware Configuration:**
```go
type SecurityMiddleware struct {
    corsMiddleware        *CORSMiddleware
    securityHeaders      *SecurityHeadersMiddleware
    authMiddleware       *AuthMiddleware
    validationMiddleware *ValidationMiddleware
    rateLimiter         *RateLimiter
    threatDetector      *ThreatDetector
    securityLogger      *SecurityLogger
    metrics             *SecurityMetrics
}

func (sm *SecurityMiddleware) Apply(next http.Handler) http.Handler {
    // Apply middleware in order
    handler := next
    
    // Security logging
    handler = sm.loggingMiddleware(handler)
    
    // Threat detection
    handler = sm.threatDetector.DetectThreats(handler)
    
    // Rate limiting
    handler = sm.rateLimiter.RateLimit(handler)
    
    // Input validation
    handler = sm.validationMiddleware.ValidateRequest(handler)
    
    // Authentication
    handler = sm.authMiddleware.Authenticate(handler)
    
    // Security headers
    handler = sm.securityHeaders.SecurityHeaders(handler)
    
    // CORS
    handler = sm.corsMiddleware.CORS(handler)
    
    return handler
}

func (sm *SecurityMiddleware) loggingMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        start := time.Now()
        
        // Create response writer wrapper to capture status code
        wrapped := &responseWriter{ResponseWriter: w}
        
        next.ServeHTTP(wrapped, r)
        
        // Log request
        duration := time.Since(start)
        sm.securityLogger.LogRequest(r, wrapped.statusCode, duration)
        
        // Record metrics
        sm.metrics.RequestDuration.Observe(duration.Seconds())
    })
}

type responseWriter struct {
    http.ResponseWriter
    statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
    rw.statusCode = code
    rw.ResponseWriter.WriteHeader(code)
}
```

---

## 🧪 **Testing**

### **Security Tests**

**Middleware Tests:**
```go
func TestAuthMiddleware(t *testing.T) {
    // Setup test environment
    middleware := NewAuthMiddleware([]byte("test-secret"), nil)
    
    // Test valid token
    req := httptest.NewRequest("GET", "/test", nil)
    req.Header.Set("Authorization", "Bearer valid-token")
    
    w := httptest.NewRecorder()
    handler := middleware.Authenticate(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        userID := r.Context().Value("user_id")
        assert.NotNil(t, userID)
        w.WriteHeader(http.StatusOK)
    }))
    
    handler.ServeHTTP(w, req)
    assert.Equal(t, http.StatusOK, w.Code)
}

func TestRateLimiter(t *testing.T) {
    // Setup test environment
    client := redis.NewClient(&redis.Options{Addr: "localhost:6379"})
    limiter := NewRateLimiter(client, 5, time.Minute)
    
    // Test rate limiting
    for i := 0; i < 6; i++ {
        req := httptest.NewRequest("GET", "/test", nil)
        w := httptest.NewRecorder()
        
        handler := limiter.RateLimit(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            w.WriteHeader(http.StatusOK)
        }))
        
        handler.ServeHTTP(w, req)
        
        if i < 5 {
            assert.Equal(t, http.StatusOK, w.Code)
        } else {
            assert.Equal(t, http.StatusTooManyRequests, w.Code)
        }
    }
}
```

---

## 📚 **Best Practices**

### **Security Implementation**

1. **Defense in Depth**: Implement multiple security layers
2. **Input Validation**: Always validate and sanitize input
3. **Rate Limiting**: Implement per-service rate limiting
4. **Authentication**: Use strong authentication mechanisms
5. **Monitoring**: Monitor security events and metrics

### **Performance Optimization**

1. **Caching**: Cache authentication results
2. **Connection Pooling**: Use connection pools for databases
3. **Async Processing**: Process security events asynchronously
4. **Metrics**: Monitor security middleware performance
5. **Optimization**: Profile and optimize security checks

### **Deployment Security**

1. **HTTPS Only**: Use HTTPS in production
2. **Security Headers**: Implement comprehensive security headers
3. **CORS Policy**: Configure CORS for production
4. **Rate Limiting**: Set appropriate rate limits
5. **Monitoring**: Deploy security monitoring and alerting

---

## 📖 **References**

### **Documentation**
- [Ory Kratos Security](https://www.ory.sh/kratos/docs/security/)
- [JWT Security](https://jwt.io/introduction)
- [Redis Security](https://redis.io/topics/security)

### **Related Documentation**
- [Security Architecture](architecture.md)
- [HTML Sanitization](html-sanitization-guide.md)

---

*This Security Middleware system provides comprehensive protection for the Hopen backend with multi-layered security and advanced threat detection.* 