# Have I Been Pwned (HIBP) System Documentation

## Overview

The HIBP system in Hopen provides secure password breach checking using the Have I Been Pwned API. This system follows security best practices and implements k-anonymity to protect user privacy.

## Architecture

### Backend Implementation

The HIBP system is implemented in the auth microservice with the following components:

1. **CheckPasswordBreach gRPC Method** - Main entry point for breach checking
2. **HIBP Configuration** - Configurable settings for API integration
3. **Caching System** - In-memory cache to reduce API calls
4. **Security Validation** - Strict input validation and security checks

### Frontend Implementation

The frontend implements secure password hashing before sending requests:

1. **SecurityRepository** - Handles password breach checking
2. **Local SHA-1 Hashing** - Passwords are hashed client-side
3. **Error Handling** - Graceful degradation when service unavailable

## Security Features

### K-Anonymity Implementation

The system uses k-anonymity to protect user privacy:

- Only the first 5 characters of the SHA-1 hash are sent to HIBP API
- The remaining 35 characters are used for local matching
- No plaintext passwords are ever transmitted

### Input Validation

Strict validation is enforced at multiple levels:

- **Frontend**: Validates password format and generates SHA-1 hash
- **Backend**: Validates hash format and rejects plaintext passwords
- **API**: Validates request structure and parameters

### Security Headers

Proper HTTP headers are set for HIBP API requests:

- User-Agent identification
- Request ID for tracing
- Content-Type validation

## Configuration

### Backend Configuration (config.yaml)

```yaml
security:
  hibp:
    base_url: "https://api.pwnedpasswords.com"
    api_key: ""  # Optional premium API key
    user_agent: "HopenBackend/1.0 (Security Check)"
    timeout_seconds: 15
    max_response_size_mb: 10
    enable_caching: true
    cache_ttl_minutes: 60
```

### Environment Variables

- `HIBP_BASE_URL`: Override HIBP API base URL
- `HIBP_API_KEY`: Premium API key for higher rate limits
- `HIBP_TIMEOUT`: Request timeout in seconds
- `HIBP_ENABLE_CACHING`: Enable/disable response caching

## Caching Strategy

### Cache Implementation

- **In-Memory Cache**: Fast access with TTL-based expiration
- **Cache Key**: Hash prefix (first 5 characters)
- **Cache Value**: Breach status and count with timestamp
- **TTL**: Configurable (default: 60 minutes)

### Cache Benefits

- Reduces API calls to HIBP
- Improves response time
- Reduces bandwidth usage
- Provides resilience during API outages

## Error Handling

### Graceful Degradation

The system implements graceful degradation:

- **API Unavailable**: Returns "not compromised" as safe default
- **Network Timeout**: Logs error and returns safe default
- **Invalid Response**: Validates and handles malformed responses
- **Rate Limiting**: Handles 429 responses with appropriate backoff

### Error Types

1. **Network Errors**: Connection failures, timeouts
2. **API Errors**: 4xx/5xx HTTP responses
3. **Validation Errors**: Invalid hash format, malformed requests
4. **System Errors**: Internal service failures

## Monitoring and Metrics

### Key Metrics

- **Total Requests**: Number of breach check requests
- **Compromised Found**: Number of compromised passwords detected
- **Cache Hit Rate**: Percentage of requests served from cache
- **API Errors**: Number of API failures
- **Average Latency**: Response time metrics

### Logging

Comprehensive logging is implemented:

- **Request Logging**: Hash prefix, timing, cache status
- **Error Logging**: Detailed error information with context
- **Security Logging**: Attempts to send plaintext passwords
- **Performance Logging**: Latency and cache performance

## Testing

### Unit Tests

Comprehensive test coverage includes:

- **Cache Functionality**: Set, get, expiration, cleanup
- **Response Parsing**: Various response formats and edge cases
- **Configuration**: Default values and custom settings
- **Error Handling**: Network failures and invalid responses

### Integration Tests

- **Mock HIBP Server**: Simulates API responses
- **End-to-End Testing**: Frontend to backend integration
- **Performance Testing**: Load testing and benchmarks

## Best Practices

### Security Best Practices

1. **Never Send Plaintext**: Always hash passwords client-side
2. **Use K-Anonymity**: Only send hash prefixes to external APIs
3. **Validate Inputs**: Strict validation at all layers
4. **Log Security Events**: Monitor for security violations

### Performance Best Practices

1. **Enable Caching**: Reduce API calls and improve performance
2. **Set Appropriate Timeouts**: Balance responsiveness and reliability
3. **Monitor Metrics**: Track performance and error rates
4. **Implement Backoff**: Handle rate limiting gracefully

### Operational Best Practices

1. **Monitor API Health**: Track HIBP API availability
2. **Set Up Alerts**: Alert on high error rates or failures
3. **Regular Testing**: Verify system functionality
4. **Documentation**: Keep documentation up to date

## API Reference

### CheckPasswordBreach Request

```protobuf
message CheckPasswordBreachRequest {
  string password = 1 [deprecated = true];  // Never use
  string sha1_hash = 2;                     // Full SHA-1 hash
  string hash_prefix = 3;                   // First 5 characters
  string hash_suffix = 4;                   // Remaining 35 characters
}
```

### CheckPasswordBreach Response

```protobuf
message CheckPasswordBreachResponse {
  bool is_compromised = 1;                  // True if compromised
  int32 breach_count = 2;                   // Number of breaches
  string message = 3;                       // Human-readable message
  ApiResponse api_response = 4;             // Standard API response
}
```

## Troubleshooting

### Common Issues

1. **High Latency**: Check HIBP API status, enable caching
2. **Rate Limiting**: Implement request throttling, use premium API
3. **Cache Misses**: Verify cache configuration and TTL settings
4. **False Negatives**: Check hash generation and API connectivity

### Debug Steps

1. **Check Logs**: Review error logs for specific issues
2. **Verify Configuration**: Ensure proper HIBP settings
3. **Test Connectivity**: Verify network access to HIBP API
4. **Monitor Metrics**: Check cache hit rates and error rates

## Future Enhancements

### Planned Improvements

1. **Distributed Caching**: Redis-based caching for scalability
2. **Advanced Metrics**: Prometheus metrics integration
3. **Circuit Breaker**: Automatic failover during outages
4. **Batch Processing**: Bulk password checking capabilities

### Security Enhancements

1. **Additional Hash Algorithms**: Support for SHA-256, bcrypt
2. **Custom Breach Databases**: Integration with internal databases
3. **Advanced Analytics**: Pattern detection and reporting
4. **Compliance Features**: GDPR, SOC2 compliance enhancements
