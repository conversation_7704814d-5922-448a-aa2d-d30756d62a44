# 🧹 HTML Sanitization Guide
*Content Security for User-Generated Content*

**Last Updated:** August 2025  
**Version:** 3.0.0  
**Status:** ✅ **Production-Ready**

---

## 🌟 **Overview**

The HTML Sanitization system provides **comprehensive content security** for user-generated content in the Hopen backend. This system prevents XSS attacks, ensures content safety, and maintains data integrity while allowing safe HTML content.

**📊 Implementation Status:**
- **Comprehensive sanitization** with bluemonday library
- **Configurable policies** for different content types
- **XSS prevention** with advanced filtering
- **Content validation** with multiple layers
- **Performance optimization** with caching
- **Real-time sanitization** for user input

---

## 🏗️ **Sanitization Architecture**

### **Security Layers**
```
HTML Sanitization Stack
├── Input Validation
├── HTML Sanitization
├── Content Filtering
├── XSS Prevention
├── Output Encoding
└── Content Monitoring
```

### **Technology Stack**
- **Sanitization**: bluemonday (Go HTML sanitizer)
- **Validation**: go-playground/validator
- **Encoding**: html/template
- **Monitoring**: Custom security metrics
- **Caching**: Redis for performance
- **Testing**: Comprehensive test suite

---

## 🔧 **Implementation**

### **Basic Sanitization**

**Simple HTML Sanitization:**
```go
import "github.com/microcosm-cc/bluemonday"

type HTMLSanitizer struct {
    policy *bluemonday.Policy
}

func NewHTMLSanitizer() *HTMLSanitizer {
    // Create UGC policy for user-generated content
    policy := bluemonday.UGCPolicy()
    
    return &HTMLSanitizer{
        policy: policy,
    }
}

func (hs *HTMLSanitizer) SanitizeHTML(input string) string {
    return hs.policy.Sanitize(input)
}

func (hs *HTMLSanitizer) SanitizeText(input string) string {
    // Remove all HTML tags
    return hs.policy.Sanitize(input)
}
```

### **Advanced Configuration**

**Custom Policy Configuration:**
```go
func NewCustomHTMLSanitizer() *HTMLSanitizer {
    policy := bluemonday.NewPolicy()
    
    // Allow specific HTML elements
    policy.AllowElements("p", "br", "strong", "em", "u", "h1", "h2", "h3", "ul", "ol", "li")
    
    // Allow specific attributes
    policy.AllowAttrs("class").OnElements("p", "span", "div")
    policy.AllowAttrs("id").OnElements("div", "span")
    policy.AllowAttrs("style").OnElements("span", "div")
    
    // Allow specific CSS properties
    policy.AllowStyles("color", "background-color", "font-size", "text-align").OnElements("span", "div")
    
    // Allow links with restrictions
    policy.AllowAttrs("href").OnElements("a")
    policy.AllowURLSchemes("http", "https")
    
    // Set URL policy
    policy.RequireParseableURLs(true)
    
    return &HTMLSanitizer{
        policy: policy,
    }
}
```

---

## 🛡️ **Security Policies**

### **Content Type Policies**

**Message Content Policy:**
```go
func NewMessageContentPolicy() *bluemonday.Policy {
    policy := bluemonday.NewPolicy()
    
    // Basic text formatting
    policy.AllowElements("p", "br", "strong", "em", "u")
    
    // Lists
    policy.AllowElements("ul", "ol", "li")
    
    // Headers
    policy.AllowElements("h1", "h2", "h3", "h4")
    
    // Links (restricted)
    policy.AllowElements("a")
    policy.AllowAttrs("href").OnElements("a")
    policy.AllowURLSchemes("http", "https")
    
    // Block dangerous elements
    policy.DenyElements("script", "iframe", "object", "embed", "form", "input", "button")
    
    return policy
}
```

**Profile Bio Policy:**
```go
func NewProfileBioPolicy() *bluemonday.Policy {
    policy := bluemonday.NewPolicy()
    
    // Very restrictive for profile content
    policy.AllowElements("p", "br", "strong", "em")
    
    // No links or complex formatting
    policy.DenyElements("a", "script", "iframe", "object", "embed")
    
    return policy
}
```

**Bubble Description Policy:**
```go
func NewBubbleDescriptionPolicy() *bluemonday.Policy {
    policy := bluemonday.NewPolicy()
    
    // Allow basic formatting
    policy.AllowElements("p", "br", "strong", "em", "u")
    
    // Allow links with restrictions
    policy.AllowElements("a")
    policy.AllowAttrs("href").OnElements("a")
    policy.AllowURLSchemes("http", "https")
    
    // Block dangerous content
    policy.DenyElements("script", "iframe", "object", "embed")
    
    return policy
}
```

### **XSS Prevention**

**XSS Detection and Prevention:**
```go
type XSSDetector struct {
    dangerousPatterns []string
}

func NewXSSDetector() *XSSDetector {
    return &XSSDetector{
        dangerousPatterns: []string{
            "<script",
            "javascript:",
            "onload=",
            "onerror=",
            "onclick=",
            "onmouseover=",
            "eval(",
            "document.cookie",
            "window.location",
        },
    }
}

func (xd *XSSDetector) DetectXSS(input string) bool {
    inputLower := strings.ToLower(input)
    
    for _, pattern := range xd.dangerousPatterns {
        if strings.Contains(inputLower, pattern) {
            return true
        }
    }
    
    return false
}

func (xd *XSSDetector) SanitizeWithDetection(input string) (string, bool) {
    if xd.DetectXSS(input) {
        // Log XSS attempt
        log.Printf("XSS attempt detected: %s", input)
        return "", true
    }
    
    // Proceed with normal sanitization
    sanitizer := NewHTMLSanitizer()
    return sanitizer.SanitizeHTML(input), false
}
```

---

## 📊 **Content Validation**

### **Input Validation**

**Content Validation Middleware:**
```go
type ContentValidator struct {
    maxLength    int
    allowedTags  []string
    blockedWords []string
}

func NewContentValidator() *ContentValidator {
    return &ContentValidator{
        maxLength:    10000, // 10KB max
        allowedTags:  []string{"p", "br", "strong", "em", "u", "a"},
        blockedWords: []string{"spam", "advertisement", "click here"},
    }
}

func (cv *ContentValidator) ValidateContent(content string) error {
    // Check length
    if len(content) > cv.maxLength {
        return fmt.Errorf("content too long: %d characters (max: %d)", len(content), cv.maxLength)
    }
    
    // Check for blocked words
    contentLower := strings.ToLower(content)
    for _, word := range cv.blockedWords {
        if strings.Contains(contentLower, word) {
            return fmt.Errorf("content contains blocked word: %s", word)
        }
    }
    
    // Check for excessive links
    linkCount := strings.Count(content, "<a")
    if linkCount > 5 {
        return fmt.Errorf("too many links: %d (max: 5)", linkCount)
    }
    
    return nil
}
```

### **URL Validation**

**Safe URL Validation:**
```go
type URLValidator struct {
    allowedSchemes []string
    allowedDomains []string
}

func NewURLValidator() *URLValidator {
    return &URLValidator{
        allowedSchemes: []string{"http", "https"},
        allowedDomains: []string{
            "hopen.com",
            "trusted-site.com",
            "secure-content.com",
        },
    }
}

func (uv *URLValidator) ValidateURL(url string) error {
    parsed, err := url.Parse(url)
    if err != nil {
        return fmt.Errorf("invalid URL format: %v", err)
    }
    
    // Check scheme
    schemeAllowed := false
    for _, scheme := range uv.allowedSchemes {
        if parsed.Scheme == scheme {
            schemeAllowed = true
            break
        }
    }
    if !schemeAllowed {
        return fmt.Errorf("unsupported URL scheme: %s", parsed.Scheme)
    }
    
    // Check domain (optional)
    if len(uv.allowedDomains) > 0 {
        domainAllowed := false
        for _, domain := range uv.allowedDomains {
            if parsed.Host == domain || strings.HasSuffix(parsed.Host, "."+domain) {
                domainAllowed = true
                break
            }
        }
        if !domainAllowed {
            return fmt.Errorf("domain not allowed: %s", parsed.Host)
        }
    }
    
    return nil
}
```

---

## ⚡ **Performance Optimization**

### **Caching Strategy**

**Sanitization Cache:**
```go
type SanitizationCache struct {
    client *redis.Client
    ttl    time.Duration
}

func NewSanitizationCache(client *redis.Client) *SanitizationCache {
    return &SanitizationCache{
        client: client,
        ttl:    time.Hour, // Cache for 1 hour
    }
}

func (sc *SanitizationCache) GetSanitizedContent(input string) (string, bool) {
    // Create hash of input
    hash := fmt.Sprintf("%x", md5.Sum([]byte(input)))
    key := fmt.Sprintf("sanitized:%s", hash)
    
    // Try to get from cache
    result, err := sc.client.Get(context.Background(), key).Result()
    if err == nil {
        return result, true
    }
    
    return "", false
}

func (sc *SanitizationCache) SetSanitizedContent(input, sanitized string) error {
    hash := fmt.Sprintf("%x", md5.Sum([]byte(input)))
    key := fmt.Sprintf("sanitized:%s", hash)
    
    return sc.client.Set(context.Background(), key, sanitized, sc.ttl).Err()
}
```

### **Batch Processing**

**Batch Sanitization:**
```go
type BatchSanitizer struct {
    sanitizer *HTMLSanitizer
    cache     *SanitizationCache
    workers   int
}

func NewBatchSanitizer(workers int) *BatchSanitizer {
    return &BatchSanitizer{
        sanitizer: NewHTMLSanitizer(),
        cache:     NewSanitizationCache(redis.NewClient(&redis.Options{})),
        workers:   workers,
    }
}

func (bs *BatchSanitizer) SanitizeBatch(inputs []string) []string {
    results := make([]string, len(inputs))
    var wg sync.WaitGroup
    semaphore := make(chan struct{}, bs.workers)
    
    for i, input := range inputs {
        wg.Add(1)
        go func(index int, content string) {
            defer wg.Done()
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            // Try cache first
            if cached, found := bs.cache.GetSanitizedContent(content); found {
                results[index] = cached
                return
            }
            
            // Sanitize content
            sanitized := bs.sanitizer.SanitizeHTML(content)
            results[index] = sanitized
            
            // Cache result
            bs.cache.SetSanitizedContent(content, sanitized)
        }(i, input)
    }
    
    wg.Wait()
    return results
}
```

---

## 📊 **Monitoring & Metrics**

### **Sanitization Metrics**

**Metrics Collection:**
```go
type SanitizationMetrics struct {
    SanitizedContent    prometheus.Counter
    XSSAttempts        prometheus.Counter
    CacheHits          prometheus.Counter
    CacheMisses        prometheus.Counter
    ProcessingTime      prometheus.Histogram
    ContentLength       prometheus.Histogram
}

func NewSanitizationMetrics() *SanitizationMetrics {
    return &SanitizationMetrics{
        SanitizedContent: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "html_sanitization_total",
            Help: "Total number of content sanitization operations",
        }),
        XSSAttempts: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "html_sanitization_xss_attempts_total",
            Help: "Total number of XSS attempts detected",
        }),
        CacheHits: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "html_sanitization_cache_hits_total",
            Help: "Total number of sanitization cache hits",
        }),
        CacheMisses: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "html_sanitization_cache_misses_total",
            Help: "Total number of sanitization cache misses",
        }),
        ProcessingTime: prometheus.NewHistogram(prometheus.HistogramOpts{
            Name: "html_sanitization_duration_seconds",
            Help: "Duration of HTML sanitization operations",
        }),
        ContentLength: prometheus.NewHistogram(prometheus.HistogramOpts{
            Name: "html_sanitization_content_length_bytes",
            Help: "Length of sanitized content in bytes",
        }),
    }
}
```

### **Security Logging**

**Security Event Logging:**
```go
type SecurityLogger struct {
    logger *zap.Logger
}

func (sl *SecurityLogger) LogXSSAttempt(content, userID string) {
    sl.logger.Warn("XSS attempt detected",
        zap.String("user_id", userID),
        zap.String("content", content),
        zap.String("type", "xss_attempt"),
    )
}

func (sl *SecurityLogger) LogSanitizationEvent(content, sanitized string, userID string) {
    sl.logger.Info("Content sanitized",
        zap.String("user_id", userID),
        zap.String("original_length", strconv.Itoa(len(content))),
        zap.String("sanitized_length", strconv.Itoa(len(sanitized))),
        zap.String("type", "sanitization"),
    )
}
```

---

## 🧪 **Testing**

### **Sanitization Tests**

**Unit Tests:**
```go
func TestHTMLSanitizer(t *testing.T) {
    sanitizer := NewHTMLSanitizer()
    
    tests := []struct {
        name     string
        input    string
        expected string
    }{
        {
            name:     "Basic HTML",
            input:    "<p>Hello <strong>World</strong></p>",
            expected: "<p>Hello <strong>World</strong></p>",
        },
        {
            name:     "XSS Script",
            input:    "<script>alert('xss')</script><p>Hello</p>",
            expected: "<p>Hello</p>",
        },
        {
            name:     "Dangerous Attributes",
            input:    "<p onclick=\"alert('xss')\">Hello</p>",
            expected: "<p>Hello</p>",
        },
        {
            name:     "JavaScript URL",
            input:    "<a href=\"javascript:alert('xss')\">Click</a>",
            expected: "<a>Click</a>",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := sanitizer.SanitizeHTML(tt.input)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

**Performance Tests:**
```go
func TestSanitizationPerformance(t *testing.T) {
    sanitizer := NewHTMLSanitizer()
    
    // Test large content
    largeContent := strings.Repeat("<p>Test content with <strong>formatting</strong></p>", 1000)
    
    start := time.Now()
    result := sanitizer.SanitizeHTML(largeContent)
    duration := time.Since(start)
    
    // Should complete within 100ms
    assert.Less(t, duration, 100*time.Millisecond)
    assert.NotEmpty(t, result)
}
```

---

## 📚 **Best Practices**

### **Security Implementation**

1. **Always Sanitize**: Never trust user input
2. **Use Appropriate Policies**: Different content types need different policies
3. **Monitor XSS Attempts**: Log and monitor suspicious content
4. **Regular Updates**: Keep sanitization libraries updated
5. **Test Thoroughly**: Comprehensive testing of edge cases

### **Performance Optimization**

1. **Use Caching**: Cache sanitized content for performance
2. **Batch Processing**: Process multiple items together
3. **Async Processing**: Handle sanitization asynchronously
4. **Monitor Performance**: Track sanitization metrics
5. **Optimize Policies**: Use minimal policies for better performance

### **Content Management**

1. **Validate Input**: Check content before sanitization
2. **Set Limits**: Limit content length and complexity
3. **Monitor Usage**: Track content patterns and abuse
4. **Regular Audits**: Review sanitization policies regularly
5. **User Education**: Educate users about safe content

---

## 📖 **References**

### **Documentation**
- [bluemonday Documentation](https://github.com/microcosm-cc/bluemonday)
- [OWASP XSS Prevention](https://owasp.org/www-project-cheat-sheets/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html)
- [HTML Security](https://developer.mozilla.org/en-US/docs/Web/Security)

### **Related Documentation**
- [Security Middleware](security-middleware-usage.md)
- [Security Architecture](architecture.md)

---

*This HTML Sanitization system provides comprehensive content security for user-generated content with advanced XSS prevention and performance optimization.* 
Use the appropriate sanitization level for each use case and always sanitize user input before processing or storing it. The system provides comprehensive protection with excellent performance. 