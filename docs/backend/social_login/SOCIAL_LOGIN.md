# Social Login Implementation

## Overview

The Hopen backend now supports complete social login functionality with Google and Apple Sign-In. This implementation provides secure OAuth token validation, automatic user creation/linking, and seamless integration with the existing authentication infrastructure.

## Features

✅ **Google Sign-In** - Complete ID token validation with Google's OAuth2 API  
✅ **Apple Sign-In** - JWT token validation with Apple's public keys  
✅ **OAuth Session Management** - Dedicated session tokens for OAuth users  
✅ **Automatic User Creation** - New users are created automatically via registration saga  
✅ **User Account Linking** - Existing users can link OAuth providers  
✅ **Comprehensive Error Handling** - Detailed error responses for all failure scenarios  
✅ **Flutter Integration Ready** - Designed to work seamlessly with Flutter OAuth SDKs  

## Architecture

### OAuth Providers

The system supports pluggable OAuth providers through the `OAuthProvider` interface:

```go
type OAuthProvider interface {
    ValidateToken(ctx context.Context, token string) (*OAuthUserInfo, error)
    GetProviderName() string
    GetScopes() []string
}
```

#### Google OAuth Provider
- Validates ID tokens using Google's `tokeninfo` endpoint
- Verifies audience (client ID) matches configured value
- Extracts user profile information (email, name, picture)
- Requires `GOOGLE_OAUTH_CLIENT_ID` and `GOOGLE_OAUTH_CLIENT_SECRET`

#### Apple OAuth Provider  
- Validates JWT tokens using Apple's public keys
- Verifies token signature and claims
- Extracts user profile information (email, limited name data)
- Requires `APPLE_OAUTH_CLIENT_ID`, `APPLE_OAUTH_TEAM_ID`, `APPLE_OAUTH_KEY_ID`, and `APPLE_OAUTH_PRIVATE_KEY_PATH`

### Session Management

OAuth sessions use a dedicated token format:
```
oauth_{user_id}_{expiration_unix}_{session_id}
```

Sessions are:
- Cached in Valkey for fast validation
- Valid for 24 hours by default
- Include user permissions and roles
- Compatible with existing JWT infrastructure

## API Endpoints

### SocialLogin

**Endpoint:** `hopen.auth.v1.AuthService/SocialLogin`

**Request:**
```protobuf
message SocialLoginRequest {
  string provider = 1; // "google" or "apple"
  string token = 2;    // OAuth ID token from provider
}
```

**Response:**
```protobuf
message LoginUserResponse {
  string session_token = 1;        // OAuth session token
  hopen.common.v1.ApiResponse api_response = 2;
}
```

### ValidateSession (Enhanced)

The existing `ValidateSession` endpoint now supports OAuth sessions:

**Request:**
```protobuf
message ValidateSessionRequest {
  string session_token = 1; // Supports both Kratos and OAuth tokens
}
```

**Response:**
```protobuf
message ValidateSessionResponse {
  SessionInfo session_info = 1;
  hopen.common.v1.ApiResponse api_response = 2;
}
```

## Configuration

### Environment Variables

#### Google OAuth
```bash
GOOGLE_OAUTH_CLIENT_ID=your-google-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-google-client-secret
```

#### Apple OAuth
```bash
APPLE_OAUTH_CLIENT_ID=your-apple-client-id
APPLE_OAUTH_TEAM_ID=your-apple-team-id
APPLE_OAUTH_KEY_ID=your-apple-key-id
APPLE_OAUTH_PRIVATE_KEY_PATH=/path/to/apple-private-key.p8
```

### Kratos Configuration

The system also integrates with Ory Kratos for OAuth flows. See `hopenbackend/config/kratos/kratos.yml`:

```yaml
selfservice:
  methods:
    oidc:
      enabled: true
      config:
        providers:
          - id: google
            provider: google
            client_id: ${GOOGLE_OAUTH_CLIENT_ID}
            client_secret: ${GOOGLE_OAUTH_CLIENT_SECRET}
          - id: apple
            provider: apple
            client_id: ${APPLE_OAUTH_CLIENT_ID}
            apple_team_id: ${APPLE_OAUTH_TEAM_ID}
```

## Flutter Integration

### 1. Google Sign-In Flow

```dart
// Flutter calls this in auth_provider.dart
Future<void> loginWithGoogle() async {
  final authRepo = ref.read(authRepositoryModuleProvider);
  final result = await authRepo.loginWithGoogle();
  // Handle result...
}
```

### 2. Backend Integration

The Flutter app uses Kratos for OAuth flows, but the backend `SocialLogin` endpoint provides direct token validation for cases where you have the ID token directly.

### 3. Session Validation

```dart
// Flutter validates sessions
await authRemoteDataSource.validateSession(sessionToken);
```

## User Creation Flow

### New OAuth Users

1. **Token Validation** - Validate OAuth token with provider
2. **User Check** - Check if user exists by email
3. **User Creation** - Create new user via registration saga if not exists
4. **Session Creation** - Generate OAuth session token
5. **Response** - Return session token to client

### Existing Users

1. **Token Validation** - Validate OAuth token with provider  
2. **User Lookup** - Find existing user by email
3. **Account Linking** - Link OAuth provider to existing account
4. **Session Creation** - Generate OAuth session token
5. **Response** - Return session token to client

## Error Handling

### Validation Errors
- `INVALID_FORMAT` - Missing provider or token
- `INVALID_CREDENTIALS` - Invalid OAuth token
- `EXTERNAL_SERVICE` - Provider API errors

### User Errors  
- `USER_NOT_FOUND` - User lookup failures
- `DATABASE_ERROR` - User creation failures
- `INTERNAL_ERROR` - Saga execution failures

### Session Errors
- `INVALID_TOKEN` - Malformed session tokens
- `TOKEN_EXPIRED` - Expired OAuth sessions
- `UNAUTHORIZED` - Invalid session validation

## Testing

### Unit Tests
```bash
cd hopenbackend
go test ./tests/integration/social_login_test.go -v
```

### Integration Tests
```bash
cd hopenbackend  
go test ./tests/oauth_verification_test.go -v
```

### Example Usage
```bash
cd hopenbackend
go run ./examples/social_login_example.go
```

## Security Considerations

### Token Validation
- All OAuth tokens are validated with provider APIs
- Audience verification prevents token reuse attacks
- Expiration times are strictly enforced

### Session Security
- OAuth sessions are cached with TTL
- Session tokens include expiration timestamps
- Failed validations are logged for monitoring

### Provider Security
- Google tokens validated via official tokeninfo endpoint
- Apple tokens validated using Apple's public key infrastructure
- All network requests use HTTPS with timeouts

## Monitoring and Logging

### Metrics
- OAuth login attempts by provider
- Token validation success/failure rates
- Session creation and validation metrics
- User creation via OAuth flows

### Logging
- All OAuth operations logged with context
- Token validation results (success/failure)
- User creation and linking events
- Session management operations

## Future Enhancements

### Additional Providers
- Microsoft OAuth support
- GitHub OAuth support
- Custom OIDC provider support

### Enhanced Features
- OAuth token refresh capabilities
- Multi-provider account linking
- OAuth scope management
- Provider-specific user data sync

## Troubleshooting

### Common Issues

1. **Invalid Client ID** - Check environment variables
2. **Token Validation Failures** - Verify provider configuration
3. **User Creation Errors** - Check database connectivity
4. **Session Validation Issues** - Verify Valkey connectivity

### Debug Commands

```bash
# Test OAuth provider configuration
go run ./examples/social_login_example.go

# Check environment variables
env | grep OAUTH

# Test token validation
grpcurl -plaintext -d '{"provider":"google","token":"test"}' \
  localhost:4000 hopen.auth.v1.AuthService/SocialLogin
```
