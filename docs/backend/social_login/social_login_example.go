package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	authv1 "hopenbackend/protos/gen"
)

// SocialLoginExample demonstrates how to use the social login functionality
func main() {
	// Connect to the auth service
	conn, err := grpc.Dial("localhost:4000", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect to auth service: %v", err)
	}
	defer conn.Close()

	client := authv1.NewAuthServiceClient(conn)
	ctx := context.Background()

	fmt.Println("🚀 Social Login Example")
	fmt.Println("======================")

	// Example 1: Google Sign-In
	fmt.Println("\n📱 Example 1: Google Sign-In")
	fmt.Println("-----------------------------")
	
	// In a real application, you would get this token from the Google SDK
	// This is just an example of the request structure
	googleLoginReq := &authv1.SocialLoginRequest{
		Provider: "google",
		Token:    "example-google-id-token", // This would be a real Google ID token
	}

	fmt.Printf("Request: Provider=%s, Token=%s...\n", 
		googleLoginReq.Provider, 
		googleLoginReq.Token[:min(20, len(googleLoginReq.Token))])

	// Note: This will fail with the example token, but shows the API structure
	googleResp, err := client.SocialLogin(ctx, googleLoginReq)
	if err != nil {
		fmt.Printf("❌ Expected error with example token: %v\n", err)
	} else {
		fmt.Printf("✅ Google login successful! Session token: %s...\n", 
			googleResp.SessionToken[:min(20, len(googleResp.SessionToken))])
	}

	// Example 2: Apple Sign-In
	fmt.Println("\n🍎 Example 2: Apple Sign-In")
	fmt.Println("---------------------------")
	
	// In a real application, you would get this token from the Apple SDK
	appleLoginReq := &authv1.SocialLoginRequest{
		Provider: "apple",
		Token:    "example-apple-id-token", // This would be a real Apple ID token
	}

	fmt.Printf("Request: Provider=%s, Token=%s...\n", 
		appleLoginReq.Provider, 
		appleLoginReq.Token[:min(20, len(appleLoginReq.Token))])

	// Note: This will fail with the example token, but shows the API structure
	appleResp, err := client.SocialLogin(ctx, appleLoginReq)
	if err != nil {
		fmt.Printf("❌ Expected error with example token: %v\n", err)
	} else {
		fmt.Printf("✅ Apple login successful! Session token: %s...\n", 
			appleResp.SessionToken[:min(20, len(appleResp.SessionToken))])
	}

	// Example 3: Session Validation
	fmt.Println("\n🔐 Example 3: OAuth Session Validation")
	fmt.Println("--------------------------------------")
	
	// Example OAuth session token (in real usage, this would come from a successful login)
	oauthSessionToken := "oauth_user-123_1234567890_oauth_session_user-123"
	
	validateReq := &authv1.ValidateSessionRequest{
		SessionToken: oauthSessionToken,
	}

	fmt.Printf("Validating OAuth session: %s...\n", 
		validateReq.SessionToken[:min(30, len(validateReq.SessionToken))])

	// Note: This will fail because it's an example token, but shows the API structure
	validateResp, err := client.ValidateSession(ctx, validateReq)
	if err != nil {
		fmt.Printf("❌ Expected error with example token: %v\n", err)
	} else {
		fmt.Printf("✅ Session valid! User ID: %s, Active: %t\n", 
			validateResp.SessionInfo.UserId, 
			validateResp.SessionInfo.IsActive)
	}

	// Example 4: Environment Variables for OAuth Configuration
	fmt.Println("\n⚙️  Example 4: OAuth Configuration")
	fmt.Println("----------------------------------")
	
	fmt.Println("Required environment variables for OAuth providers:")
	fmt.Println("Google OAuth:")
	fmt.Printf("  GOOGLE_OAUTH_CLIENT_ID=%s\n", getEnvOrDefault("GOOGLE_OAUTH_CLIENT_ID", "not-set"))
	fmt.Printf("  GOOGLE_OAUTH_CLIENT_SECRET=%s\n", maskSecret(getEnvOrDefault("GOOGLE_OAUTH_CLIENT_SECRET", "not-set")))
	
	fmt.Println("Apple OAuth:")
	fmt.Printf("  APPLE_OAUTH_CLIENT_ID=%s\n", getEnvOrDefault("APPLE_OAUTH_CLIENT_ID", "not-set"))
	fmt.Printf("  APPLE_OAUTH_TEAM_ID=%s\n", getEnvOrDefault("APPLE_OAUTH_TEAM_ID", "not-set"))
	fmt.Printf("  APPLE_OAUTH_KEY_ID=%s\n", getEnvOrDefault("APPLE_OAUTH_KEY_ID", "not-set"))
	fmt.Printf("  APPLE_OAUTH_PRIVATE_KEY_PATH=%s\n", getEnvOrDefault("APPLE_OAUTH_PRIVATE_KEY_PATH", "not-set"))

	// Example 5: Flutter Integration Guide
	fmt.Println("\n📱 Example 5: Flutter Integration Guide")
	fmt.Println("---------------------------------------")
	
	fmt.Println("Flutter app flow:")
	fmt.Println("1. User taps 'Continue with Google' or 'Continue with Apple'")
	fmt.Println("2. Flutter calls Google/Apple SDK to get ID token")
	fmt.Println("3. Flutter sends ID token to backend via SocialLogin gRPC call")
	fmt.Println("4. Backend validates token with Google/Apple")
	fmt.Println("5. Backend creates or links user account")
	fmt.Println("6. Backend returns session token to Flutter")
	fmt.Println("7. Flutter stores session token for authenticated requests")
	fmt.Println("8. Flutter uses ValidateSession to check token validity")

	// Example 6: Error Handling
	fmt.Println("\n❌ Example 6: Error Handling")
	fmt.Println("---------------------------")
	
	// Test with missing provider
	invalidReq1 := &authv1.SocialLoginRequest{
		Provider: "",
		Token:    "valid-token",
	}
	
	_, err = client.SocialLogin(ctx, invalidReq1)
	if err != nil {
		fmt.Printf("✅ Correctly rejected missing provider: %v\n", err)
	}

	// Test with missing token
	invalidReq2 := &authv1.SocialLoginRequest{
		Provider: "google",
		Token:    "",
	}
	
	_, err = client.SocialLogin(ctx, invalidReq2)
	if err != nil {
		fmt.Printf("✅ Correctly rejected missing token: %v\n", err)
	}

	// Test with unsupported provider
	invalidReq3 := &authv1.SocialLoginRequest{
		Provider: "facebook", // Not supported
		Token:    "valid-token",
	}
	
	_, err = client.SocialLogin(ctx, invalidReq3)
	if err != nil {
		fmt.Printf("✅ Correctly rejected unsupported provider: %v\n", err)
	}

	fmt.Println("\n🎉 Social Login Example Complete!")
	fmt.Println("=================================")
	fmt.Println("The backend now supports:")
	fmt.Println("✅ Google Sign-In with ID token validation")
	fmt.Println("✅ Apple Sign-In with ID token validation")
	fmt.Println("✅ OAuth session management and validation")
	fmt.Println("✅ Automatic user creation for new OAuth users")
	fmt.Println("✅ User linking for existing accounts")
	fmt.Println("✅ Comprehensive error handling")
	fmt.Println("✅ Integration with existing JWT infrastructure")
}

// Helper functions
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func maskSecret(secret string) string {
	if secret == "not-set" || secret == "" {
		return secret
	}
	if len(secret) <= 8 {
		return "***"
	}
	return secret[:4] + "***" + secret[len(secret)-4:]
}
