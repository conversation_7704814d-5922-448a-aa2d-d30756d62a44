# Hopen Backend API Documentation
*Production-Ready gRPC Microservices Architecture*

**Last Updated:** August 2025  
**Version:** 3.0.0  
**Status:** ✅ **Production-Ready**

## Overview

The Hopen backend is built as a production-ready microservices architecture with 12 independently deployable services. All services communicate via gRPC and use NATS for event-driven communication. The system features advanced analytics, real-time processing, and comprehensive business logic.

**📊 Implementation Status:**
- **12 microservices** with complete implementations and zero placeholders
- **Advanced features** including real-time analytics, search, and synchronization
- **Production-grade patterns** with comprehensive error handling and validation
- **All services compiling successfully** with enterprise-level code quality

## Architecture

### Service Overview

| Service | Port | Description | Status |
|---------|------|-------------|--------|
| Auth | 4000 | Advanced authentication with Ory Kratos integration | ✅ Production-Ready |
| User | 4001 | User management with relationship handling | ✅ Production-Ready |
| Bubble | 4002 | Social bubble creation, lifecycle management, and unanimous voting system | ✅ Production-Ready |
| Email | 4003 | Email delivery with AWS SES integration | ✅ Production-Ready |
| Media | 4004 | File storage with MinIO integration | ✅ Production-Ready |
| Search | 4005 | OpenSearch-powered search with real-time indexing | ✅ Production-Ready |
| Call | 4006 | WebRTC call management with LiveKit | ✅ Production-Ready |
| Realtime | 4007 | Real-time messaging with ScyllaDB | ✅ Production-Ready |
| Presence | 4008 | Real-time user presence and status | ✅ Production-Ready |
| Notification | 4009 | Push notifications with FCM | ✅ Production-Ready |
| Social Analytics | 4010 | Comprehensive analytics and insights | ✅ Production-Ready |
| Sync | 4015 | Cross-device data synchronization | ✅ Production-Ready |

### gRPC Communication

All services use gRPC for inter-service communication with the following features:
- **HTTP/2 Protocol** - High-performance binary protocol
- **Type Safety** - Strongly typed interfaces with Protocol Buffers
- **Event-Driven Architecture** - NATS integration for asynchronous communication
- **Error Handling** - Comprehensive error management with proper status codes

**Example gRPC service calls (via Kong unified endpoint on port 8000):**
```bash
# Auth Service
grpcurl -plaintext localhost:8000 hopen.auth.v1.AuthService/GetAuthStatus

# User Service
grpcurl -plaintext localhost:8000 hopen.user.v1.UserService/GetUser

# Bubble Service
grpcurl -plaintext localhost:8000 hopen.bubble.v1.BubbleService/GetBubble
```

### Authentication

All API calls require authentication via Bearer token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

**JWT Configuration:**
- Issuer: Ory Kratos
- Algorithm: HS256
- Claims: exp, iss, sub
- Rate Limiting: Per-service configuration

**Public endpoints (no authentication required):**
- Health checks: `/grpc.health.v1.Health/Check`
- Service discovery: Kong Admin API

## Service APIs

### Auth Service (Port 4000)

**Ory Kratos Integration:**
- Session-based authentication
- Self-service registration/login
- Password policies and validation
- MQTT authentication hooks

**Verified Methods:**
- `GetAuthStatus` - Check user authentication status
- `ValidateSession` - Validate session tokens
- `CheckPasswordBreach` - Security password validation
- `GetUserPermissions` - Retrieve user permissions
- `GetUserProfile` - Get user profile data
- `GetUserRoles` - Get user roles
- `HasPermission` - Check specific permissions
- `Logout` - User logout functionality
- `RefreshToken` - Token refresh
- `RegisterUser` - User registration
- `ValidateMqttConnection` - MQTT authentication
- `ValidateSession` - Session validation

#### GetAuthStatus
```protobuf
service AuthService {
  rpc GetAuthStatus(GetAuthStatusRequest) returns (GetAuthStatusResponse);
}

message GetAuthStatusRequest {
  string session_token = 1;
}

message GetAuthStatusResponse {
  bool is_authenticated = 1;
  string user_id = 2;
  repeated string permissions = 3;
  ApiResponse api_response = 4;
}
```

#### RegisterUser
```protobuf
message RegisterUserRequest {
  string username = 1;
  string email = 2;
  string password = 3;
  string first_name = 4;
  string last_name = 5;
  string date_of_birth = 6;
}

message RegisterUserResponse {
  UserInfo user_info = 1;
  ApiResponse api_response = 2;
}
```

### User Service (Port 4001)

**User Management Features:**
- Profile management with privacy controls
- Relationship handling (friends, followers)
- Search optimization with OpenSearch integration
- Privacy settings and data protection

**Verified Methods:**
- `CreateUser` - Create new user profile
- `GetUser` - Retrieve user information
- `UpdateUser` - Update user profile
- `UpdateUserPartial` - Partial profile updates
- `DeleteUser` - Delete user account
- `GetUserFriends` - Get user's friends list
- `AddFriend` - Add friend relationship
- `RemoveFriend` - Remove friend relationship
- `GetUserFollowers` - Get user's followers
- `FollowUser` - Follow another user
- `UnfollowUser` - Unfollow user
- `SearchUsers` - Search users with filters
- `GetUserSuggestions` - Get user suggestions

#### GetUser
```protobuf
service UserService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
}

message GetUserRequest {
  string user_id = 1;
  bool include_private = 2;
}

message GetUserResponse {
  UserInfo user_info = 1;
  ApiResponse api_response = 2;
}
```

### Bubble Service (Port 4002)

**Social Bubble Features:**
- Bubble creation and lifecycle management
- Unanimous voting system for decisions
- Event-driven architecture with NATS
- Role-based access control
- Expiration and reminder systems

**Verified Methods:**
- `CreateBubble` - Create new social bubble
- `GetBubble` - Get bubble information
- `UpdateBubble` - Update bubble settings
- `DeleteBubble` - Delete bubble
- `AddMember` - Add member to bubble
- `RemoveMember` - Remove member from bubble
- `GetBubbleMembers` - Get bubble members
- `VoteOnKickout` - Vote on member removal
- `GetBubbleInvites` - Get bubble invitations
- `InviteToBubble` - Invite user to bubble
- `AcceptInvite` - Accept bubble invitation
- `DeclineInvite` - Decline bubble invitation
- `GetBubbleAnalytics` - Get bubble analytics

#### CreateBubble
```protobuf
service BubbleService {
  rpc CreateBubble(CreateBubbleRequest) returns (CreateBubbleResponse);
}

message CreateBubbleRequest {
  string name = 1;
  string description = 2;
  string creator_id = 3;
  BubbleType bubble_type = 4;
  repeated string initial_members = 5;
  BubbleSettings settings = 6;
}

message CreateBubbleResponse {
  BubbleInfo bubble_info = 1;
  ApiResponse api_response = 2;
}
```

### Call Service (Port 4006)

**WebRTC Call Management:**
- LiveKit integration for WebRTC calls
- Call state management with Valkey
- MQTT signaling for real-time communication
- Call recording and analytics

**Verified Methods:**
- `CreateCall` - Create new call session
- `JoinCall` - Join existing call
- `LeaveCall` - Leave call session
- `EndCall` - End call for all participants
- `GetCallInfo` - Get call information
- `GetCallParticipants` - Get call participants
- `MuteAudio` - Mute/unmute audio
- `MuteVideo` - Mute/unmute video
- `GetCallAnalytics` - Get call analytics
- `RecordCall` - Start/stop call recording

#### CreateCall
```protobuf
service CallService {
  rpc CreateCall(CreateCallRequest) returns (CreateCallResponse);
}

message CreateCallRequest {
  string creator_id = 1;
  repeated string participant_ids = 2;
  CallType call_type = 3;
  CallSettings settings = 4;
}

message CreateCallResponse {
  CallInfo call_info = 1;
  string room_token = 2;
  ApiResponse api_response = 3;
}
```

### Email Service (Port 4003)

**AWS SES Integration:**
- Email delivery with AWS SES
- Template management system
- Analytics tracking
- Event processing

**Verified Methods:**
- `SendEmail` - Send email with template
- `SendBulkEmail` - Send bulk emails
- `GetEmailTemplates` - Get available templates
- `CreateEmailTemplate` - Create new template
- `UpdateEmailTemplate` - Update template
- `DeleteEmailTemplate` - Delete template
- `GetEmailAnalytics` - Get email analytics
- `ValidateEmail` - Validate email address

#### SendEmail
```protobuf
service EmailService {
  rpc SendEmail(SendEmailRequest) returns (SendEmailResponse);
}

message SendEmailRequest {
  string to_email = 1;
  string template_name = 2;
  map<string, string> template_data = 3;
  string subject = 4;
  string from_email = 5;
}

message SendEmailResponse {
  string message_id = 1;
  ApiResponse api_response = 2;
}
```

### Search Service (Port 4005)

**OpenSearch Integration:**
- Real-time indexing and search
- Advanced user search with filters
- Relationship-aware search results
- Search suggestions and autocomplete

**Verified Methods:**
- `SearchUsers` - Search users with filters
- `SearchBubbles` - Search bubbles
- `SearchContent` - Search content
- `GetSearchSuggestions` - Get search suggestions
- `IndexUser` - Index user for search
- `UpdateUserIndex` - Update user index
- `DeleteUserIndex` - Delete user from index
- `GetSearchAnalytics` - Get search analytics

#### SearchUsers
```protobuf
service SearchService {
  rpc SearchUsers(SearchUsersRequest) returns (SearchUsersResponse);
}

message SearchUsersRequest {
  string query = 1;
  repeated string filters = 2;
  int32 page = 3;
  int32 page_size = 4;
  string user_id = 5; // For relationship filtering
}

message SearchUsersResponse {
  repeated UserInfo users = 1;
  int32 total_count = 2;
  ApiResponse api_response = 3;
}
```

### Social Analytics Service (Port 4010)

**Comprehensive Analytics:**
- User engagement tracking
- System analytics and metrics
- Popular content analysis
- Real-time insights

**Verified Methods:**
- `GetUserAnalytics` - Get user analytics
- `GetBubbleAnalytics` - Get bubble analytics
- `GetSystemAnalytics` - Get system-wide analytics
- `TrackUserActivity` - Track user activity
- `GetPopularContent` - Get popular content
- `GetEngagementMetrics` - Get engagement metrics
- `GetUserInsights` - Get user insights
- `ExportAnalytics` - Export analytics data

#### GetUserAnalytics
```protobuf
service SocialAnalyticsService {
  rpc GetUserAnalytics(GetUserAnalyticsRequest) returns (GetUserAnalyticsResponse);
}

message GetUserAnalyticsRequest {
  string user_id = 1;
  string time_period = 2;
  repeated string metrics = 3;
}

message GetUserAnalyticsResponse {
  UserAnalytics analytics = 1;
  ApiResponse api_response = 2;
}
```

### Sync Service (Port 4015)

**Cross-Device Synchronization:**
- Data synchronization across devices
- Conflict resolution
- Incremental updates
- Comprehensive data management

**Verified Methods:**
- `SyncData` - Synchronize data across devices
- `GetSyncStatus` - Get synchronization status
- `ResolveConflicts` - Resolve data conflicts
- `GetSyncHistory` - Get sync history
- `ForceSync` - Force full synchronization
- `GetDeviceList` - Get connected devices
- `RemoveDevice` - Remove device from sync
- `GetSyncAnalytics` - Get sync analytics

#### SyncData
```protobuf
service SyncService {
  rpc SyncData(SyncDataRequest) returns (SyncDataResponse);
}

message SyncDataRequest {
  string user_id = 1;
  string device_id = 2;
  repeated DataItem data_items = 3;
  string last_sync_timestamp = 4;
}

message SyncDataResponse {
  repeated DataItem updated_items = 1;
  repeated ConflictInfo conflicts = 2;
  string sync_timestamp = 3;
  ApiResponse api_response = 4;
}
```

### Presence Service (Port 4008)

**Real-Time Presence:**
- User presence tracking
- Activity status management
- Real-time status updates
- MQTT integration

**Verified Methods:**
- `UpdatePresence` - Update user presence
- `GetUserPresence` - Get user presence
- `GetOnlineUsers` - Get online users
- `SubscribeToPresence` - Subscribe to presence updates
- `UnsubscribeFromPresence` - Unsubscribe from updates
- `GetPresenceHistory` - Get presence history
- `SetUserStatus` - Set user status
- `GetUserStatus` - Get user status

#### UpdatePresence
```protobuf
service PresenceService {
  rpc UpdatePresence(UpdatePresenceRequest) returns (UpdatePresenceResponse);
}

message UpdatePresenceRequest {
  string user_id = 1;
  PresenceStatus status = 2;
  string activity = 3;
  string location = 4;
}

message UpdatePresenceResponse {
  PresenceInfo presence_info = 1;
  ApiResponse api_response = 2;
}
```

### Media Service (Port 4004)

**MinIO Integration:**
- File storage with MinIO
- Presigned URLs for secure access
- Content validation and sanitization
- Multi-layered security

**Verified Methods:**
- `UploadFile` - Upload file with presigned URL
- `DownloadFile` - Download file with presigned URL
- `DeleteFile` - Delete file
- `GetFileInfo` - Get file information
- `ListFiles` - List user files
- `ValidateFile` - Validate file content
- `GetStorageUsage` - Get storage usage
- `GeneratePresignedUrl` - Generate presigned URL

#### UploadFile
```protobuf
service MediaService {
  rpc UploadFile(UploadFileRequest) returns (UploadFileResponse);
}

message UploadFileRequest {
  string user_id = 1;
  string file_name = 2;
  string content_type = 3;
  int64 file_size = 4;
  string file_hash = 5;
}

message UploadFileResponse {
  string upload_url = 1;
  string file_id = 2;
  ApiResponse api_response = 3;
}
```

### Notification Service (Port 4009)

**FCM Integration:**
- Push notifications with Firebase
- Notification templates
- Delivery tracking
- Analytics

**Verified Methods:**
- `SendNotification` - Send push notification
- `SendBulkNotifications` - Send bulk notifications
- `GetNotificationTemplates` - Get notification templates
- `CreateNotificationTemplate` - Create template
- `UpdateNotificationTemplate` - Update template
- `DeleteNotificationTemplate` - Delete template
- `GetNotificationAnalytics` - Get notification analytics
- `UpdateDeviceToken` - Update device token

#### SendNotification
```protobuf
service NotificationService {
  rpc SendNotification(SendNotificationRequest) returns (SendNotificationResponse);
}

message SendNotificationRequest {
  string user_id = 1;
  string title = 2;
  string body = 3;
  map<string, string> data = 4;
  NotificationType type = 5;
}

message SendNotificationResponse {
  string notification_id = 1;
  ApiResponse api_response = 2;
}
```

### Realtime Service (Port 4007)

**ScyllaDB Integration:**
- Real-time messaging with ScyllaDB
- MQTT5 protocol support
- High-performance message storage
- Event-driven architecture

**Verified Methods:**
- `SendMessage` - Send real-time message
- `GetMessages` - Get message history
- `DeleteMessage` - Delete message
- `MarkAsRead` - Mark message as read
- `GetUnreadCount` - Get unread message count
- `SubscribeToChannel` - Subscribe to message channel
- `UnsubscribeFromChannel` - Unsubscribe from channel
- `GetMessageAnalytics` - Get message analytics

#### SendMessage
```protobuf
service RealtimeService {
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
}

message SendMessageRequest {
  string sender_id = 1;
  string channel_id = 2;
  string content = 3;
  MessageType type = 4;
  map<string, string> metadata = 5;
}

message SendMessageResponse {
  string message_id = 1;
  ApiResponse api_response = 2;
}
```

## Error Handling

All services return standardized error responses:

```protobuf
message ApiResponse {
  bool success = 1;
  string message = 2;
  string error_code = 3;
  repeated string details = 4;
}
```

**Common Error Codes:**
- `AUTHENTICATION_REQUIRED` - JWT token required
- `INVALID_TOKEN` - Invalid or expired token
- `PERMISSION_DENIED` - Insufficient permissions
- `RESOURCE_NOT_FOUND` - Requested resource not found
- `VALIDATION_ERROR` - Request validation failed
- `INTERNAL_ERROR` - Internal server error
- `RATE_LIMIT_EXCEEDED` - Rate limit exceeded

## Rate Limiting

Each service implements rate limiting with the following defaults:
- **Per-minute limit**: 200 requests
- **Per-hour limit**: 2000 requests
- **Per-user limits**: Configurable per service

## Health Checks

All services provide health check endpoints:

```bash
# Health check
grpcurl -plaintext localhost:8000 grpc.health.v1.Health/Check

# Service-specific health (via Kong)
grpcurl -plaintext localhost:8000 hopen.auth.v1.AuthService/Health
```

## Monitoring and Observability

**Metrics Available:**
- Request/response times
- Error rates
- Resource usage
- Custom business metrics

**Logging:**
- Structured logging with Zap
- Request correlation IDs
- Error tracking and alerting

**Tracing:**
- Distributed tracing with OpenTelemetry
- Request flow visualization
- Performance analysis

## Security

**Authentication:**
- JWT tokens with Ory Kratos
- Session-based authentication
- Token refresh mechanism

**Authorization:**
- Role-based access control (RBAC)
- Permission-based authorization
- Resource-level permissions

**Data Protection:**
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection

**Transport Security:**
- TLS/SSL encryption
- HTTP/2 protocol
- Secure headers

## Deployment

**Docker Compose:**
```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

**Kubernetes:**
```bash
# Deploy to Kubernetes
kubectl apply -f k8s/

# Check deployment status
kubectl get pods

# View service endpoints
kubectl get svc
```

## Testing

**Unit Tests:**
```bash
# Run unit tests
go test ./...

# Run with coverage
go test -cover ./...
```

**Integration Tests:**
```bash
# Run integration tests
go test ./tests/integration/...

# Run with database
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

**Load Tests:**
```bash
# Run load tests
go test ./tests/load/...

# Performance benchmarks
go test -bench=. ./tests/performance/...
```

## API Versioning

The API uses semantic versioning with the following format:
- **Major version**: Breaking changes
- **Minor version**: New features (backward compatible)
- **Patch version**: Bug fixes (backward compatible)

**Current Version:** v3.0.0

## Support

For API support and documentation:
- **Documentation**: [Backend Architecture](architecture.md)
- **Testing**: [Test Results](LATEST_COMPREHENSIVE_TEST_RESULTS.md)
- **Methods**: [Complete Method Reference](methods.md)
- **Gateway**: [Kong API Gateway](api_gateway.md)

Note: For an authoritative, up-to-date list of RPC methods per service, see the RPC index at the top of [methods.md](methods.md).