# Hopen Backend Architecture Documentation

*Production-Ready Microservices with Go, PostgreSQL, ScyllaDB, and NATS JetStream*

**Last Updated:** December 2024
**Version:** 4.0.0
**Status:** ✅ **Production-Ready**

---

## Table of Contents

1. [System Overview](#system-overview)
2. [Microservices Architecture](#microservices-architecture)
3. [Database Architecture](#database-architecture)
4. [Event-Driven Architecture](#event-driven-architecture)
5. [API Gateway & Communication](#api-gateway--communication)
6. [Authentication & Authorization](#authentication--authorization)
7. [Real-time Communication](#real-time-communication)
8. [Security Implementation](#security-implementation)
9. [Monitoring & Observability](#monitoring--observability)
10. [Deployment & Infrastructure](#deployment--infrastructure)
11. [Performance & Scalability](#performance--scalability)
12. [Development Workflow](#development-workflow)

---

## System Overview

Hopen is a modern social networking platform built with a microservices architecture using Go. The system is designed for high scalability, reliability, and real-time communication capabilities with comprehensive business logic and analytics.

### Core Principles

- **Microservices Architecture**: 12 independent Go services with clear boundaries
- **Event-Driven Design**: NATS JetStream for reliable asynchronous communication
- **Domain-Driven Design**: Services organized around business domains
- **API-First**: gRPC for internal communication, REST for external APIs via Kong
- **Security-First**: mTLS, OAuth2, RBAC with Ory stack integration
- **Observability**: Comprehensive logging, metrics, and tracing with Prometheus/Grafana
- **High Availability**: Multi-database architecture with caching and redundancy

### Key Features

- **Real-time Messaging**: MQTT 5.0 with EMQX broker for instant communication
- **Advanced Analytics**: Social analytics service with comprehensive metrics
- **Scalable Storage**: Multi-database approach (PostgreSQL, ScyllaDB, Valkey)
- **Robust Authentication**: Ory Kratos/Hydra/Keto stack integration
- **Event Sourcing**: NATS JetStream for reliable event processing
- **Circuit Breakers**: Resilience patterns for fault tolerance
- **API Gateway**: Kong for unified API management and security

### Architecture Highlights

- **12 Microservices**: Each service handles a specific domain with clear boundaries
- **3 Database Systems**: PostgreSQL (ACID), ScyllaDB (chat), Valkey (cache)
- **Event-Driven Communication**: NATS JetStream for reliable async messaging
- **Real-time Features**: MQTT 5.0 with EMQX for instant updates
- **Security Stack**: Complete Ory stack (Kratos, Hydra, Keto) integration
- **Monitoring**: Prometheus, Grafana, Jaeger for comprehensive observability
- **Container Orchestration**: Docker Compose for development, Kubernetes for production

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │   Web Client    │    │  Mobile Apps    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                       ┌─────────────────┐
                       │ Kong API Gateway│
                       └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Auth Service   │    │  User Service   │    │ Bubble Service  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                        ┌─────────────────┐
                        │ NATS JetStream  │
                        └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │    ScyllaDB     │    │     Valkey      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Microservices Architecture

### Core Services

#### 1. Auth Service (`cmd/auth`)
**Purpose**: Authentication, authorization, and user identity management
- **Port**: 4000
- **Responsibilities**:
  - User registration and login
  - JWT token management
  - Integration with Ory Kratos/Hydra
  - Saga-based user creation with rollback capabilities
  - MQTT topic authorization
- **Dependencies**: User Service, Email Service, Ory Stack
- **Database**: PostgreSQL (user sessions, auth logs)

#### 2. User Service (`cmd/user`)
**Purpose**: User profile and relationship management
- **Port**: 4001
- **Responsibilities**:
  - User profile CRUD operations
  - Friend and contact management
  - User search and discovery
  - Location privacy settings
  - Profile analytics
- **Dependencies**: Search Service, Auth Service
- **Database**: PostgreSQL (user profiles, relationships)

#### 3. Bubble Service (`cmd/bubble`)
**Purpose**: Core bubble (group) functionality
- **Port**: 4002
- **Responsibilities**:
  - Bubble creation and management
  - Member management and invitations
  - Dynamic location tracking based on most ancient member
  - Bubble dissolution and archival
  - Voting and kickout mechanisms
- **Dependencies**: User Service, Notification Service, Location Service
- **Database**: PostgreSQL (bubbles, members, location history)

#### 4. Call Service (`cmd/call`)
**Purpose**: Voice and video calling functionality
- **Port**: 4003
- **Responsibilities**:
  - Call initiation and management
  - LiveKit integration for WebRTC
  - Call analytics and recording
  - Participant management
- **Dependencies**: Bubble Service, LiveKit
- **Database**: PostgreSQL (call logs, analytics)

#### 5. Media Service (`cmd/media`)
**Purpose**: File upload, storage, and management
- **Port**: 4004
- **Responsibilities**:
  - File upload and download
  - Image/video processing
  - MinIO object storage integration
  - Media analytics and quotas
- **Dependencies**: MinIO, Image processing services
- **Database**: PostgreSQL (file metadata)

#### 6. Notification Service (`cmd/notification`)
**Purpose**: Push notifications and messaging
- **Port**: 4005
- **Responsibilities**:
  - Push notification delivery
  - Email notifications
  - In-app notifications
  - Notification preferences
- **Dependencies**: Email Service, FCM/APNS
- **Database**: PostgreSQL (notification logs, preferences)

#### 7. Realtime Service (`cmd/realtime`)
**Purpose**: Real-time messaging and presence
- **Port**: 4006
- **Responsibilities**:
  - WebSocket connections
  - Real-time messaging
  - User presence tracking
  - Live updates
- **Dependencies**: NATS, WebSocket infrastructure
- **Database**: ScyllaDB (messages), Valkey (presence)

#### 8. Search Service (`cmd/search`)
**Purpose**: Search functionality across the platform
- **Port**: 4007
- **Responsibilities**:
  - User search
  - Bubble discovery
  - Content search
  - Search analytics
- **Dependencies**: OpenSearch, PostgreSQL
- **Database**: OpenSearch (search index), PostgreSQL (fallback)

#### 9. Social Analytics Service (`cmd/social_analytics`)
**Purpose**: Social graph analysis and recommendations
- **Port**: 4008
- **Responsibilities**:
  - Connection strength analysis
  - Friend recommendations
  - Social graph insights
  - User behavior analytics
- **Dependencies**: User Service, Bubble Service
- **Database**: PostgreSQL (analytics data)

#### 10. Email Service (`cmd/email`)
**Purpose**: Email delivery and templating
- **Port**: 4009
- **Responsibilities**:
  - Transactional email sending
  - Email template management
  - Delivery tracking
  - Bounce handling
- **Dependencies**: SMTP providers, Email templates
- **Database**: PostgreSQL (email logs, templates)

#### 11. Presence Service (`cmd/presence`)
**Purpose**: User presence and activity tracking
- **Port**: 4010
- **Responsibilities**:
  - Online/offline status
  - Last seen tracking
  - Activity indicators
  - Presence broadcasting
- **Dependencies**: Realtime Service
- **Database**: Valkey (presence cache)

#### 12. Sync Service (`cmd/sync`)
**Purpose**: Data synchronization across devices
- **Port**: 4011
- **Responsibilities**:
  - Cross-device data sync
  - Conflict resolution
  - Offline support
  - Delta synchronization
- **Dependencies**: All core services
- **Database**: PostgreSQL (sync metadata)

### Service Communication Patterns

#### Synchronous Communication (gRPC)
- **Internal Service-to-Service**: All microservices communicate via gRPC
- **Type-Safe**: Protocol Buffers for schema definition
- **Performance**: HTTP/2 with multiplexing and compression
- **Security**: mTLS for all internal communication

#### Asynchronous Communication (NATS JetStream)
- **Event Publishing**: Services publish domain events
- **Event Consumption**: Services subscribe to relevant events
- **Reliability**: At-least-once delivery with acknowledgments
- **Ordering**: Stream-based ordering for related events

#### External Communication
- **Client APIs**: Kong API Gateway with REST/GraphQL endpoints
- **WebSocket**: Real-time bidirectional communication
- **MQTT**: IoT and mobile device communication

## Service Dependencies

### Dependency Graph

```mermaid
graph TD
    A[Auth Service] --> U[User Service]
    A --> E[Email Service]
    
    U --> S[Search Service]
    U --> SA[Social Analytics]
    
    B[Bubble Service] --> U
    B --> N[Notification Service]
    B --> A
    
    C[Call Service] --> B
    C --> LK[LiveKit]
    
    M[Media Service] --> MinIO[MinIO Storage]
    
    R[Realtime Service] --> NATS[NATS JetStream]
    R --> SC[ScyllaDB]
    
    N --> E
    N --> FCM[FCM/APNS]
    
    P[Presence Service] --> R
    P --> V[Valkey]
    
    SY[Sync Service] --> U
    SY --> B
    SY --> R
    
    S --> OS[OpenSearch]
    S --> PG[PostgreSQL]
    
    SA --> U
    SA --> B
```

### Cross-Service Communication

#### Service Client Injection
Services receive gRPC clients for other services through dependency injection:

```go
type Dependencies struct {
    UserServiceClient   userv1.UserServiceClient
    BubbleServiceClient bubblev1.BubbleServiceClient
    NotificationClient  notificationv1.NotificationServiceClient
    // ... other clients
}
```

#### Circuit Breakers and Retries
- **Retry Logic**: Exponential backoff with jitter
- **Circuit Breakers**: Fail-fast when services are unavailable
- **Timeouts**: Configurable per-service timeouts
- **Fallbacks**: Graceful degradation strategies

## Database Schema Relationships

### PostgreSQL (Primary Database)

#### Core Tables

**Users Table**
```sql
CREATE TABLE users (
    user_id UUID PRIMARY KEY,
    username VARCHAR(255) UNIQUE,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    -- Location tracking fields
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_updated_at TIMESTAMP WITH TIME ZONE,
    location_privacy_level VARCHAR(20) DEFAULT 'friends',
    -- ... other fields
);
```

**Bubbles Table**
```sql
CREATE TABLE bubbles (
    bubble_id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    creator_id UUID REFERENCES users(user_id),
    capacity INTEGER DEFAULT 8,
    current_members INTEGER DEFAULT 1,
    -- Dynamic location tracking
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    most_ancient_member_id UUID REFERENCES users(user_id),
    location_source VARCHAR(50) DEFAULT 'ancient_member',
    location_updated_at TIMESTAMP WITH TIME ZONE,
    -- ... other fields
);
```

**Bubble Members Table**
```sql
CREATE TABLE bubble_members (
    id UUID PRIMARY KEY,
    bubble_id UUID REFERENCES bubbles(bubble_id),
    user_id UUID REFERENCES users(user_id),
    status VARCHAR(20) DEFAULT 'active',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    role VARCHAR(20) DEFAULT 'member',
    UNIQUE(bubble_id, user_id)
);
```

#### Relationship Mapping

1. **User → Bubbles**: Many-to-Many through `bubble_members`
2. **User → Friends**: Many-to-Many through `friendships`
3. **User → Contacts**: Many-to-Many through `contacts`
4. **Bubble → Calls**: One-to-Many through `calls`
5. **User → Media**: One-to-Many through `media_files`

### ScyllaDB (Chat Database)

**Messages Table**
```cql
CREATE TABLE messages (
    bubble_id UUID,
    message_id TIMEUUID,
    user_id UUID,
    content TEXT,
    message_type VARCHAR,
    created_at TIMESTAMP,
    PRIMARY KEY (bubble_id, message_id)
) WITH CLUSTERING ORDER BY (message_id DESC);
```

### Valkey (Cache/Session Store)

- **User Sessions**: `session:{user_id}`
- **Presence Data**: `presence:{user_id}`
- **Rate Limiting**: `ratelimit:{service}:{user_id}`
- **Idempotency Keys**: `idempotency:{key}`

## API Endpoint Documentation

### gRPC Services

#### Auth Service API
```protobuf
service AuthService {
    rpc RegisterUser(RegisterUserRequest) returns (RegisterUserResponse);
    rpc LoginUser(LoginUserRequest) returns (LoginUserResponse);
    rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse);
    rpc LogoutUser(LogoutUserRequest) returns (LogoutUserResponse);
    rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse);
}
```

#### User Service API
```protobuf
service UserService {
    rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
    rpc GetUser(GetUserRequest) returns (GetUserResponse);
    rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
    rpc SearchUsers(SearchUsersRequest) returns (SearchUsersResponse);
    
    // Location services
    rpc UpdateUserLocation(UpdateUserLocationRequest) returns (UpdateUserLocationResponse);
    rpc GetUserLocation(GetUserLocationRequest) returns (GetUserLocationResponse);
    rpc UpdateLocationPrivacy(UpdateLocationPrivacyRequest) returns (UpdateLocationPrivacyResponse);
}
```

#### Bubble Service API
```protobuf
service BubbleService {
    rpc CreateBubble(CreateBubbleRequest) returns (CreateBubbleResponse);
    rpc GetBubble(GetBubbleRequest) returns (GetBubbleResponse);
    rpc JoinBubble(JoinBubbleRequest) returns (JoinBubbleResponse);
    rpc LeaveBubble(LeaveBubbleRequest) returns (LeaveBubbleResponse);
    rpc GetNearbyBubbles(GetNearbyBubblesRequest) returns (GetNearbyBubblesResponse);
    rpc GetBubbleLocationHistory(GetBubbleLocationHistoryRequest) returns (GetBubbleLocationHistoryResponse);
}
```

### REST API Gateway (Kong)

Kong API Gateway exposes REST endpoints that proxy to gRPC services:

- `POST /api/v1/auth/register` → Auth Service
- `GET /api/v1/users/{id}` → User Service  
- `POST /api/v1/bubbles` → Bubble Service
- `GET /api/v1/bubbles/nearby` → Bubble Service

### WebSocket Endpoints

- `/ws/realtime` - Real-time messaging and updates
- `/ws/presence` - User presence updates
- `/ws/calls/{call_id}` - Call-specific real-time events

## Authentication and Authorization

### Authentication Flow

#### 1. User Registration
```mermaid
sequenceDiagram
    participant C as Client
    participant AG as API Gateway
    participant AS as Auth Service
    participant US as User Service
    participant K as Kratos
    
    C->>AG: POST /auth/register
    AG->>AS: RegisterUser gRPC
    AS->>K: Create Identity
    K-->>AS: Identity Created
    AS->>US: CreateUser gRPC
    US-->>AS: User Created
    AS-->>AG: Registration Success
    AG-->>C: JWT Token
```

#### 2. User Login
```mermaid
sequenceDiagram
    participant C as Client
    participant AG as API Gateway
    participant AS as Auth Service
    participant K as Kratos
    
    C->>AG: POST /auth/login
    AG->>AS: LoginUser gRPC
    AS->>K: Validate Credentials
    K-->>AS: Credentials Valid
    AS-->>AG: JWT Token
    AG-->>C: JWT Token + Refresh Token
```

### Authorization (RBAC with Ory Keto)

#### Permission Model
```
namespace User {
    relation owner
    relation friend
    relation blocked
    
    permission view = owner + friend - blocked
    permission edit = owner
    permission message = owner + friend - blocked
}

namespace Bubble {
    relation creator
    relation member
    relation admin
    relation banned
    
    permission view = creator + member + admin - banned
    permission edit = creator + admin
    permission invite = creator + admin + member - banned
    permission kick = creator + admin
}
```

#### JWT Token Structure
```json
{
  "sub": "user-uuid",
  "iss": "hopen-auth",
  "aud": ["hopen-api"],
  "exp": 1640995200,
  "iat": 1640908800,
  "scope": ["read:profile", "write:bubbles"],
  "roles": ["user", "premium"],
  "permissions": {
    "bubbles": ["create", "join", "leave"],
    "users": ["view", "search"]
  }
}
```

### Security Measures

1. **mTLS**: All internal service communication
2. **JWT Validation**: Stateless token validation
3. **Rate Limiting**: Per-user and per-endpoint limits
4. **Input Validation**: Protocol Buffer validation rules
5. **SQL Injection Prevention**: Parameterized queries
6. **CORS**: Configured for web clients
7. **API Key Management**: For external integrations

## Location Tracking System

### Architecture Overview

The location tracking system provides dynamic bubble positioning based on the most ancient member's location, with comprehensive privacy controls.

#### Core Components

1. **User Location Service**: Manages individual user locations
2. **Bubble Location Tracker**: Updates bubble positions automatically
3. **Privacy Engine**: Enforces location sharing permissions
4. **Location History**: Maintains audit trails

#### Most Ancient Member Algorithm

```go
func GetMostAncientMember(ctx context.Context, bubbleID string) (*string, error) {
    query := `
        SELECT user_id 
        FROM bubble_members 
        WHERE bubble_id = $1 AND status = 'active'
        ORDER BY joined_at ASC, user_id ASC 
        LIMIT 1`
    // Implementation...
}
```

#### Location Update Triggers

1. **Bubble Creation**: Sets initial location to creator's position
2. **Member Joins**: Recalculates if new member is more ancient
3. **Member Leaves**: Updates to new most ancient member
4. **Member Kicked**: Updates to new most ancient member
5. **Location Privacy Changes**: Respects new privacy settings

#### Privacy Levels

```go
type LocationPrivacyLevel int32

const (
    LOCATION_PRIVACY_LEVEL_PUBLIC  = 1  // Everyone can see
    LOCATION_PRIVACY_LEVEL_FRIENDS = 2  // Only friends
    LOCATION_PRIVACY_LEVEL_BUBBLES = 3  // Only bubble members
    LOCATION_PRIVACY_LEVEL_CUSTOM  = 4  // Specified users only
    LOCATION_PRIVACY_LEVEL_PRIVATE = 5  // No one can see
)
```

#### Database Schema

**User Location Fields**
```sql
ALTER TABLE users ADD COLUMN latitude DECIMAL(10, 8);
ALTER TABLE users ADD COLUMN longitude DECIMAL(11, 8);
ALTER TABLE users ADD COLUMN location_updated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE users ADD COLUMN location_privacy_level VARCHAR(20) DEFAULT 'friends';
```

**Bubble Location Fields**
```sql
ALTER TABLE bubbles ADD COLUMN latitude DECIMAL(10, 8);
ALTER TABLE bubbles ADD COLUMN longitude DECIMAL(11, 8);
ALTER TABLE bubbles ADD COLUMN most_ancient_member_id UUID;
ALTER TABLE bubbles ADD COLUMN location_source VARCHAR(50) DEFAULT 'ancient_member';
```

**Location History**
```sql
CREATE TABLE bubble_location_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bubble_id UUID NOT NULL REFERENCES bubbles(bubble_id),
    previous_latitude DECIMAL(10, 8),
    previous_longitude DECIMAL(11, 8),
    new_latitude DECIMAL(10, 8),
    new_longitude DECIMAL(11, 8),
    change_reason VARCHAR(100) NOT NULL,
    distance_moved_km DECIMAL(8, 2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Location Privacy Function

```sql
CREATE OR REPLACE FUNCTION can_see_user_location(
    requesting_user_id UUID,
    target_user_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    privacy_level VARCHAR(20);
    are_friends BOOLEAN;
    share_bubble BOOLEAN;
BEGIN
    -- Implementation checks privacy level and relationships
    -- Returns true if requesting user can see target user's location
END;
$$ LANGUAGE plpgsql STABLE;
```

### Location API Endpoints

#### Update User Location
```protobuf
rpc UpdateUserLocation(UpdateUserLocationRequest) returns (UpdateUserLocationResponse);

message UpdateUserLocationRequest {
    string user_id = 1;
    double latitude = 2;
    double longitude = 3;
    optional double accuracy = 4;
    optional string source = 5; // "gps", "network", "manual"
}
```

#### Get Nearby Bubbles
```protobuf
rpc GetNearbyBubbles(GetNearbyBubblesRequest) returns (GetNearbyBubblesResponse);

message GetNearbyBubblesRequest {
    double latitude = 1;
    double longitude = 2;
    double radius_km = 3;
    int32 limit = 4;
    string user_id = 5; // For privacy checks
}
```

### Performance Optimizations

1. **Spatial Indexing**: PostGIS extensions for efficient location queries
2. **Caching**: Valkey cache for frequently accessed locations
3. **Async Updates**: Location history recorded asynchronously
4. **Batch Processing**: Bulk location updates for efficiency

## Event-Driven Architecture

### NATS JetStream Configuration

#### Streams

1. **User Events Stream**
   - Subjects: `user.created`, `user.updated`, `user.deleted`
   - Retention: 7 days
   - Replicas: 3

2. **Bubble Events Stream**
   - Subjects: `bubble.created`, `bubble.joined`, `bubble.left`, `bubble.location_updated`
   - Retention: 30 days
   - Replicas: 3

3. **Call Events Stream**
   - Subjects: `call.started`, `call.ended`, `call.participant_joined`
   - Retention: 90 days
   - Replicas: 3

#### Event Schema

```protobuf
message BaseEvent {
    string event_id = 1;
    string event_type = 2;
    google.protobuf.Timestamp timestamp = 3;
    string source_service = 4;
    string correlation_id = 5;
    map<string, string> metadata = 6;
}

message BubbleLocationUpdatedEvent {
    BaseEvent base = 1;
    string bubble_id = 2;
    optional double previous_latitude = 3;
    optional double previous_longitude = 4;
    optional double new_latitude = 5;
    optional double new_longitude = 6;
    string change_reason = 7;
    optional double distance_moved_km = 8;
}
```

### Event Patterns

#### 1. Event Sourcing
- **Audit Trail**: All state changes captured as events
- **Replay Capability**: Rebuild state from events
- **Temporal Queries**: Query state at any point in time

#### 2. CQRS (Command Query Responsibility Segregation)
- **Write Models**: Optimized for commands and business logic
- **Read Models**: Optimized for queries and reporting
- **Eventual Consistency**: Read models updated via events

#### 3. Saga Pattern
- **Distributed Transactions**: Coordinate across multiple services
- **Compensation**: Rollback on failure
- **State Management**: Track saga progress

### Event Handlers

```go
type EventHandler interface {
    Handle(ctx context.Context, event *Event) error
    GetEventTypes() []string
}

type BubbleLocationEventHandler struct {
    bubbleService *bubble.Service
    logger        *zap.Logger
}

func (h *BubbleLocationEventHandler) Handle(ctx context.Context, event *Event) error {
    switch event.Type {
    case "user.location_updated":
        return h.handleUserLocationUpdated(ctx, event)
    case "bubble.member_left":
        return h.handleMemberLeft(ctx, event)
    default:
        return nil
    }
}
```

## Deployment and Infrastructure

### Container Architecture

#### Docker Images
Each microservice is containerized with multi-stage builds:

```dockerfile
# Build stage
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o service ./cmd/auth

# Runtime stage
FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/service .
CMD ["./service"]
```

#### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: hopen/auth-service:latest
        ports:
        - containerPort: 4000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

### Infrastructure Components

#### Databases
- **PostgreSQL**: Primary database with read replicas
- **ScyllaDB**: High-performance chat storage
- **Valkey**: Redis-compatible cache and session store

#### Message Broker
- **NATS JetStream**: Event streaming and pub/sub
- **EMQX**: MQTT broker for IoT and mobile

#### Storage
- **MinIO**: S3-compatible object storage
- **Local Storage**: Temporary file processing

#### Monitoring
- **Prometheus**: Metrics collection
- **Grafana**: Visualization and alerting
- **Jaeger**: Distributed tracing
- **ELK Stack**: Centralized logging

#### Security
- **Ory Kratos**: Identity management
- **Ory Hydra**: OAuth2 and OpenID Connect
- **Ory Keto**: Authorization and RBAC
- **Kong**: API Gateway with security plugins

### Scaling Strategies

#### Horizontal Scaling
- **Stateless Services**: All services designed to be stateless
- **Load Balancing**: Kong API Gateway with upstream load balancing
- **Auto-scaling**: Kubernetes HPA based on CPU/memory/custom metrics

#### Database Scaling
- **Read Replicas**: PostgreSQL read replicas for query distribution
- **Sharding**: ScyllaDB automatic sharding for chat data
- **Connection Pooling**: PgBouncer for PostgreSQL connection management

#### Caching Strategy
- **Application Cache**: In-memory caching with TTL
- **Distributed Cache**: Valkey for shared cache across instances
- **CDN**: CloudFlare for static asset delivery

## Technology Stack

### Backend Technologies

#### Core Framework
- **Language**: Go 1.21+
- **gRPC**: Service-to-service communication
- **Protocol Buffers**: Schema definition and serialization
- **Gin**: HTTP router for REST endpoints (via Kong)

#### Databases
- **PostgreSQL 15**: Primary ACID database
- **ScyllaDB**: High-performance NoSQL for chat
- **Valkey**: Redis-compatible cache and session store

#### Message Broker
- **NATS JetStream**: Event streaming and messaging
- **EMQX**: MQTT broker for real-time communication

#### Authentication & Authorization
- **Ory Kratos**: Identity and user management
- **Ory Hydra**: OAuth2 and OpenID Connect provider
- **Ory Keto**: Fine-grained authorization and RBAC
- **JWT**: Stateless authentication tokens

#### Storage & Media
- **MinIO**: S3-compatible object storage
- **FFmpeg**: Video/audio processing
- **ImageMagick**: Image processing and optimization

#### Monitoring & Observability
- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization and dashboards
- **Jaeger**: Distributed tracing
- **Elasticsearch**: Log aggregation and search
- **Logstash**: Log processing pipeline
- **Kibana**: Log visualization

#### API Gateway & Proxy
- **Kong**: API Gateway with plugins
- **Envoy**: Service mesh proxy (future)

#### Development & Testing
- **Docker**: Containerization
- **Docker Compose**: Local development environment
- **Kubernetes**: Container orchestration
- **Testify**: Go testing framework
- **GoMock**: Mock generation for testing

### External Integrations

#### Communication
- **LiveKit**: WebRTC infrastructure for calls
- **Twilio**: SMS and voice services (backup)
- **SendGrid**: Email delivery service

#### Push Notifications
- **Firebase Cloud Messaging (FCM)**: Android push notifications
- **Apple Push Notification Service (APNS)**: iOS push notifications

#### Analytics & Monitoring
- **Sentry**: Error tracking and performance monitoring
- **DataDog**: Infrastructure monitoring (optional)
- **New Relic**: Application performance monitoring (optional)

#### Search
- **OpenSearch**: Full-text search and analytics
- **Elasticsearch**: Alternative search backend

### Development Tools

#### Code Quality
- **golangci-lint**: Go linting and static analysis
- **gofmt**: Code formatting
- **go vet**: Static analysis
- **gosec**: Security analysis

#### Documentation
- **Swagger/OpenAPI**: REST API documentation
- **Protocol Buffers**: gRPC service documentation
- **Mermaid**: Architecture diagrams

#### CI/CD
- **GitHub Actions**: Continuous integration
- **Docker Hub**: Container registry
- **Kubernetes**: Deployment automation

### Performance Characteristics

#### Latency Targets
- **gRPC Internal**: < 10ms p99
- **REST API**: < 100ms p99
- **WebSocket**: < 50ms p99
- **Database Queries**: < 5ms p95

#### Throughput Targets
- **Concurrent Users**: 100,000+
- **Messages/Second**: 10,000+
- **API Requests/Second**: 50,000+
- **File Uploads**: 1,000/minute

#### Availability
- **Uptime**: 99.9% SLA
- **Recovery Time**: < 5 minutes
- **Data Durability**: 99.999999999% (11 9's)

## Design Decisions and Trade-offs

### Microservices vs Monolith
**Decision**: Microservices architecture
**Rationale**:
- Independent scaling of different components
- Team autonomy and faster development cycles
- Technology diversity where appropriate
- Fault isolation and resilience

**Trade-offs**:
- Increased operational complexity
- Network latency between services
- Distributed system challenges (consistency, debugging)
- Higher infrastructure costs

### Database Per Service
**Decision**: Each service owns its data
**Rationale**:
- Data encapsulation and service autonomy
- Independent schema evolution
- Technology choice flexibility
- Reduced coupling between services

**Trade-offs**:
- No ACID transactions across services
- Data consistency challenges
- Increased storage overhead
- Complex reporting across services

### Event-Driven Architecture
**Decision**: NATS JetStream for async communication
**Rationale**:
- Loose coupling between services
- Scalability and resilience
- Event sourcing capabilities
- Real-time updates

**Trade-offs**:
- Eventual consistency
- Message ordering challenges
- Debugging complexity
- Infrastructure overhead

### gRPC for Internal Communication
**Decision**: gRPC instead of REST for service-to-service
**Rationale**:
- Type safety with Protocol Buffers
- Better performance (HTTP/2, binary protocol)
- Built-in load balancing and retries
- Strong ecosystem and tooling

**Trade-offs**:
- Learning curve for developers
- Limited browser support (requires proxy)
- Binary protocol debugging complexity

## Security Architecture

### Defense in Depth

#### Network Security
1. **VPC Isolation**: Services deployed in private subnets
2. **Security Groups**: Restrictive firewall rules
3. **mTLS**: Mutual TLS for all internal communication
4. **API Gateway**: Single entry point with security policies

#### Application Security
1. **Input Validation**: Protocol Buffer validation rules
2. **SQL Injection Prevention**: Parameterized queries only
3. **XSS Protection**: Content Security Policy headers
4. **CSRF Protection**: SameSite cookies and CSRF tokens

#### Authentication & Authorization
1. **Multi-Factor Authentication**: TOTP and SMS backup
2. **OAuth2/OIDC**: Standard protocols for external integrations
3. **RBAC**: Role-based access control with Ory Keto
4. **JWT**: Stateless tokens with short expiration

#### Data Protection
1. **Encryption at Rest**: Database and file storage encryption
2. **Encryption in Transit**: TLS 1.3 for all communications
3. **PII Handling**: GDPR-compliant data processing
4. **Data Retention**: Automated cleanup of sensitive data

### Threat Model

#### Identified Threats
1. **Data Breaches**: Unauthorized access to user data
2. **DDoS Attacks**: Service availability disruption
3. **Injection Attacks**: SQL, NoSQL, and command injection
4. **Privilege Escalation**: Unauthorized access to admin functions
5. **Man-in-the-Middle**: Interception of communications

#### Mitigations
1. **Access Controls**: Principle of least privilege
2. **Rate Limiting**: Per-user and per-endpoint limits
3. **Input Sanitization**: Comprehensive validation
4. **Audit Logging**: Complete audit trail
5. **Encryption**: End-to-end encryption where possible

## Monitoring and Observability

### Three Pillars of Observability

#### 1. Metrics (Prometheus + Grafana)
```go
// Service-level metrics
var (
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
        },
        []string{"method", "endpoint", "status"},
    )

    activeConnections = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "active_connections",
            Help: "Number of active connections",
        },
    )
)
```

**Key Metrics**:
- Request rate, latency, and error rate (RED metrics)
- CPU, memory, and disk utilization (USE metrics)
- Business metrics (user registrations, bubble creations)
- Database connection pool metrics
- Message queue depth and processing rate

#### 2. Logging (ELK Stack)
```go
// Structured logging with zap
logger.Info("User created",
    zap.String("user_id", userID),
    zap.String("email", email),
    zap.Duration("duration", time.Since(start)),
    zap.String("correlation_id", correlationID),
)
```

**Log Levels**:
- **ERROR**: Service errors requiring immediate attention
- **WARN**: Potential issues or degraded performance
- **INFO**: Important business events and state changes
- **DEBUG**: Detailed execution flow for troubleshooting

#### 3. Tracing (Jaeger)
```go
// Distributed tracing
span, ctx := opentracing.StartSpanFromContext(ctx, "CreateUser")
defer span.Finish()

span.SetTag("user.email", email)
span.SetTag("service.name", "user-service")
```

**Trace Context**:
- Request correlation across services
- Performance bottleneck identification
- Error propagation tracking
- Service dependency mapping

### Alerting Strategy

#### Critical Alerts (PagerDuty)
- Service down or unhealthy
- Database connection failures
- High error rates (>5% for 5 minutes)
- Memory/CPU usage >90% for 10 minutes

#### Warning Alerts (Slack)
- Elevated response times
- Queue depth increasing
- Disk space >80%
- Failed background jobs

#### SLI/SLO Monitoring
```yaml
# Service Level Objectives
availability_slo: 99.9%  # 43.8 minutes downtime/month
latency_slo: 95%         # 95% of requests < 100ms
error_rate_slo: 1%       # Error rate < 1%
```

## Disaster Recovery and Business Continuity

### Backup Strategy

#### Database Backups
- **PostgreSQL**: Daily full backups + continuous WAL archiving
- **ScyllaDB**: Incremental backups every 6 hours
- **Valkey**: RDB snapshots every hour + AOF persistence

#### File Storage Backups
- **MinIO**: Cross-region replication
- **Application Data**: Daily encrypted backups to S3

#### Backup Testing
- Monthly restore tests
- Automated backup verification
- Recovery time objective (RTO): 4 hours
- Recovery point objective (RPO): 1 hour

### High Availability

#### Service Redundancy
- Minimum 3 replicas per service
- Multi-zone deployment
- Health checks and auto-restart
- Circuit breakers for graceful degradation

#### Database HA
- **PostgreSQL**: Primary-replica setup with automatic failover
- **ScyllaDB**: Multi-node cluster with RF=3
- **Valkey**: Redis Sentinel for automatic failover

#### Load Balancing
- Kong API Gateway with upstream health checks
- Kubernetes service discovery and load balancing
- Geographic load balancing for global users

### Incident Response

#### Incident Classification
1. **P0 (Critical)**: Complete service outage
2. **P1 (High)**: Major feature unavailable
3. **P2 (Medium)**: Performance degradation
4. **P3 (Low)**: Minor issues or cosmetic bugs

#### Response Procedures
1. **Detection**: Automated monitoring and alerting
2. **Assessment**: Incident commander assignment
3. **Communication**: Status page updates and user notifications
4. **Resolution**: Fix implementation and verification
5. **Post-mortem**: Root cause analysis and prevention measures

## Performance Optimization

### Caching Strategy

#### Multi-Level Caching
1. **Application Cache**: In-memory LRU cache (5-minute TTL)
2. **Distributed Cache**: Valkey for shared data (1-hour TTL)
3. **CDN Cache**: CloudFlare for static assets (24-hour TTL)
4. **Database Cache**: Query result caching (15-minute TTL)

#### Cache Invalidation
```go
// Event-driven cache invalidation
func (s *Service) handleUserUpdatedEvent(ctx context.Context, event *UserUpdatedEvent) error {
    // Invalidate user cache
    s.cache.Delete(fmt.Sprintf("user:%s", event.UserID))

    // Invalidate related caches
    s.cache.DeletePattern(fmt.Sprintf("user_bubbles:%s", event.UserID))

    return nil
}
```

### Database Optimization

#### Query Optimization
- **Indexing Strategy**: Composite indexes for common query patterns
- **Query Analysis**: Regular EXPLAIN ANALYZE for slow queries
- **Connection Pooling**: PgBouncer with optimized pool sizes
- **Read Replicas**: Read-heavy queries routed to replicas

#### Schema Design
- **Normalization**: 3NF for transactional data
- **Denormalization**: Strategic denormalization for read performance
- **Partitioning**: Time-based partitioning for large tables
- **Archival**: Automated archival of old data

### API Performance

#### Response Optimization
- **Pagination**: Cursor-based pagination for large datasets
- **Field Selection**: GraphQL-style field selection
- **Compression**: gzip compression for large responses
- **Streaming**: Server-sent events for real-time updates

#### Request Optimization
- **Batching**: Batch multiple operations in single request
- **Prefetching**: Eager loading of related data
- **Caching**: Aggressive caching of immutable data
- **CDN**: Static asset delivery via CDN

## Future Roadmap

### Short-term (3-6 months)
1. **Service Mesh**: Implement Istio for advanced traffic management
2. **GraphQL Gateway**: Unified GraphQL API for mobile clients
3. **Advanced Analytics**: Real-time analytics with Apache Kafka
4. **Mobile Optimization**: Dedicated mobile API optimizations

### Medium-term (6-12 months)
1. **Multi-region Deployment**: Global deployment for reduced latency
2. **AI/ML Integration**: Recommendation engine and content moderation
3. **Advanced Search**: Elasticsearch with ML-powered relevance
4. **Blockchain Integration**: Decentralized identity and payments

### Long-term (12+ months)
1. **Edge Computing**: Edge deployment for ultra-low latency
2. **Serverless Migration**: Selective migration to serverless functions
3. **Advanced Security**: Zero-trust architecture implementation
4. **Global Scale**: Support for 10M+ concurrent users

---

*This architecture documentation is maintained by the Hopen development team and updated with each major release. Last updated: January 2025*
