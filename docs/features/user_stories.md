# Hopen User Stories

This document outlines user stories for Hopen, categorized by feature area, reflecting the current production-ready implementation with comprehensive request dialog persistence and recovery systems. A lot of the user stories follow the format: "As a [user type], I want to [action] so that [benefit]."

## 🧪 Backend Testing

For comprehensive backend testing that validates these user stories, see the [Enhanced grpcurl Test Plan](../tests/comprehensive_backend_test_plan.sh) which provides:

- **Sequential test execution** covering all major user flows
- **Real user scenario validation** from onboarding through bubble management
- **Critical business logic verification** including relationship transitions
- **Production-ready test cases** that match the user stories exactly

# User stories by pages

## Authentication & Onboarding

### @login_page.dart

    *   As a registered user, I want my login session to persist across app uses (unless explicitly logged out), so that I don't need to re-enter my credentials frequently.
    *   As a user, I want the application to check my authentication status upon launch, so that I am automatically logged in if my session is still valid or directed to login if not.
    *   As a registered user, when I launch the app and my session is not active, I must to be able to log in using my email and password, or my username and password, so that I can access my account.
    *   As a registered user, I want to be able to log in using my Google account by clicking on the "Continue with Google" button, so that I have an alternative and convenient way to access Hopen.
    *   As a registered user, I want to be able to log in using my Apple account by clicking on the "Continue with Apple" button, so that I have another alternative and convenient way to access Hopen.
    *   As a registered user, I must be able to reset my password by clicking on "Forgot password" button, so that I can regain access to my account if I forget it. When I click on this button, I must be taken to the forgot password page.
    *   As a new user, or a user who does not have an account, or a user who wants to create a new account, I must be able to create a new account by clicking on the "Sign up" button, so that I can use Hopen. When i click on this button, I must be taken to the multi-step signup page.
    *   As a user, when i click on "Continue with Google" button, I must be able to login with my Google account, so that I can use Hopen.
    *   As a user, when i click on "Continue with Apple" button, I must be able to login with my Apple account, so that I can use Hopen.
    *   As a user, when i click on "Continue with Google" button, and I click on my Google account, if this account is not linked to an Hopen account, I must be able to create a new Hopen account, so that I can use Hopen. All of the data from my Google account (profile picture, first name, last name, email, birthday) must be used to create my Hopen account. The process must automatically choose a username for me, based on my Google account username (firstnamelastname), if it is available, or another username if it is not available (firstnamelastname1, firstnamelastname2, etc.).
    *   As a user, when i click on "Continue with Apple" button, and I click on my Apple account, if this account is not linked to an Hopen account, I must be able to create a new Hopen account, so that I can use Hopen. All of the data from my Apple account (profile picture, first name, last name, email, birthday) must be used to create my Hopen account. The process must automatically choose a username for me, based on my Apple account username (firstnamelastname), if it is available, or another username if it is not available (firstnamelastname1, firstnamelastname2, etc.).

    
### @step1_name_page.dart

    *   As a user creating a new account, I must be able to provide my first name and last name on the first step of the signup process, so that I can create a new account.**
    *   As a user creating a new account, I must be able to proceed to the next step of the signup process by clicking on the "Next" button, so that I can complete the signup process.

### @step2_password_page.dart

    *   As a user creating a new account, I must be able to provide a secure password on the second step of the signup process, so that I can create a new account.
    *   When i enter a password, I must be able to see if the password is secure or not, so that I can create a secure password.
    *   When i enter a password, I must be able to see if the password is compromised or not, so that I can create a secure password. For more infos, read @docs/flutter/hopenpassword_breach_checking.md.
    *   When i enter a password, I must be able to see if the password meets the minimum requirements, so that I can create a secure password.
    *   As a user creating a new account, I must be able to provide a password confirmation on the second step of the signup process, so that I can create a new account.
    *   As a user creating a new account, I must be able to proceed to the next step of the signup process by clicking on the "Next" button, so that I can complete the signup process.

### @step3_birthday_page.dart

    *   As a user creating a new account, I must be able to provide my username on the third step of the signup process, so that I can create a new account.
    *   As a user creating a new account, I must be able to select my birthday from a calendar, so that I can create a new account.
    *   As a user creating a new account, I must be able to see if I am old enough to create an account or not in real time, so that I can create a new account.
    *   As a user creating a new account, I must be able to see if the username is available or not in real time, so that I can create a new account.
    *   As a user creating a new account, I must be able to proceed to the next step of the signup process by clicking on the "Next" button, so that I can complete the signup process.
    *   As a user creating a new account, I must be able to provide my email on the third step of the signup process, so that I can create a new account.
    *   As a user creating a new account, I must be able to see if the email is available or not in real time, so that I can create a new account.
    *   As a user creating a new account, I must be able to proceed to the next step of the signup process by clicking on the "Next" button, so that I can complete the signup process.

### @step4_email_verification_page.dart

    *   As a user creating a new account, I must be able to confirm my email adress by entering the verification code sent to my email, so that I can create a new account.
    *   As a user creating a new account, I must be able to resend the verification code if I did not receive it or lost it, so that I can create a new account.

### @step5_profile_picture_page.dart

    *   As a user creating a new account, I must be able to upload a profile picture on the fourth step of the signup process, so that I can create a new account.
    *   As a user creating a new account, I must be able to take a picture with my camera and use it as my profile picture on the fourth step of the signup process, so that I can create a new account.
    *   As a user creating a new account, I must be able to see if the profile picture or the picture I took with my camera meets the requirements or not in real time, so that I can create a new account.
    *   Here are the requirements for the profile picture (for more infos, read @docs/flutter/profile_picture_requirements.md) :
        *   The profile picture must be at least 640x640 pixels.
        *   The profile picture must not be above 8192x8192 pixels.
        *   The profile picture must be in JPEG, PNG, WebP, HEIF or HEIC format.
        *   The profile picture must be smaller than 10MB.
    * Here are the way the profile picture is processed :
        *   The profile picture is resized to 1440x1440 pixels if it is larger than that.
        *   The profile picture is converted to WebP format if it is not already in that format.
        *   The profile picture is cropped to a square if it is not already a square.
        *   The profile picture is compressed to reduce its size if it is larger than 10MB.
        *   The profile picture is stored on the backend.
        *   The profile picture is displayed on my profile.
    *   As a user creating a new account, I must be able to proceed to the next step of the signup process by clicking on the "Next" button, so that I can complete the signup process.

### @step6_notifications_page.dart

    *   As a user creating a new account, I must be able to enable or disable notifications on the fifth step of the signup process, so that I can create a new account.
    *   As a user creating a new account, I must be able to proceed to the next step of the signup process by clicking on the "Next" button, so that I can complete the signup process.
    *   As a user creating a new account, I must be able to see a confirmation message on the fifth step of the signup process, so that I know that my account has been created.

### @onboarding_page.dart

    *   As a user who just created an account, I must be able to see an onboarding page right after I create my account, so that I can learn more about the app.
    *   As a user who just created an account, I must not be able to see the onboarding page, so that I can go straight to the main app.
    *   As a user who just created an account, I must be able to skip the onboarding page, so that I can go straight to the main app.
    *  As a user who just created an account, I must be able to go back to the previous onboarding page, so that I can review the previous page.
    *  As a user who just created an account, I must be able to go to the next onboarding page, so that I can learn more about the app.
    *   As a user who just created an account, I must be able to complete the onboarding process, so that I can use the app.
    *   As a user who just created an account, When I complete the onboarding process, I must be able to connect to my account, and go to the bubble_page.dart.

## Main Pages

### @profile_page.dart

    *   As a user, I want to view my own profile, which displays my full name, username, profile picture, current online visibility status (e.g., "Online Visible," "Online Hidden"), and current bubble affiliation (e.g., "In a Bubble," "No Bubble"), so that I can see how my profile appears to myself and potentially others.
    *   As a user, when I click on my first name, my last name or my usernam, I must be able to edit them, so that my personal details remain accurate.
    *   As a user, when I click on my profile picture, I must to be able to change my profile picture, so that I can update my visual representation.
    *   For all of the other usecases related to @profile_page.dart, read @docs/features/profile.md.

### @friends_page.dart

    *   As a user, I must be able to see a list of my friends, so that I can see who my friends are.
    *   As a user, I must be able to load the chat page of a specific friend when i click on his card, so that I can chat with him.
    *   As a user, I must be able to see the online presence of my friends updated in real-time in my friends list, so that I can see who is online and who is not.
    *   As a user, I must be able to see the last message sent to me by my friends in my friends list, so that I can see what we were talking about.
    *   As a user, I must be able to see the time of the last message sent to me by my friends in my friends list, so that I can see when we were talking about.
    *   As a user, I must be able to see the number of unread messages I have from my friends in my friends list, so that I can see which messages I have not read yet.
    *   As a user, I want to search my friends list by name or username. (TODO in a future update)
    *   For all of the other usecases related to @friends_page.dart, read @docs/features/friends.md.

### bubble_page.dart

    *   As a user who is a member of an active bubble, I must be able to see a list of all the bubbles I am in, so that I can see which bubbles I am in. (TODO in a future update, for now, only one bubble is displayed).
    *   As a user who is a member of an active bubble, I must be able to see the name of the bubble, so that I can see which bubble I am in. These infos are shown on the @bubble_status_card.dart widget.
    *   As a user who is a member of an active bubble, I must be able to see the countdown of the bubble, so that I can see how much time is left before the bubble expires. It must be updated in real time. These infos are shown on the @bubble_status_card.dart widget.
    *   As a user who is a member of an active bubble, I must be able to see the progress of the bubble, so that I can see how much time is left before the bubble expires. It must be updated in real time. It must show the percentage of the bubble that is already completed. These infos are shown on the @bubble_status_card.dart widget.
    *   As a user who is a member of an active bubble, when i click on the bubble name, I must be able to change the name of the bubble, so that I can change the name of the bubble.
    *   As a user who is a member of an active bubble, when i click on the info button on the @bubble_status_card.dart widget, I must be able to see a dialog with the following infos :
        *   The date of birth of the bubble ("began on [Date]")
        *   The expected date of expiration of the bubble ("ends on [Date]")
        *   The number of days the bubble has be active ("duration [Number] days")
        *   The expected number of days left before the bubble expires ("pops in [Number] days")
    *   As a member of an active bubble, I want to see a list of all current members, their avatars, names, and their online status in real-time.
    *   As a member of an active bubble, I want to see unread message counts for each member within our bubble chat, updated in real-time.
    *   As a member of an active bubble, I want to see at which day the other members of the bubble joined the bubble. The text must show "Bubbler since [Date]", with the date being the day the member joined the bubble.
    *   As a member of an active bubble (if the bubble is not full), I want to be able to "propose a new bubbler," which currently navigates me to my contacts list with a prompt to invite someone, so that we can potentially add new members.
    *   As a user, from the main bubble page, I want an option to "Join more bubbles," which currently navigates to an `AnnouncementBannerPage` [TODO, Purpose of this page and actual flow for joining other bubbles TBD].
    *   As a member of an active bubble, I want to navigate to the profile of any other bubble member by tapping on their entry in the member list.
    *   As a member of an active bubble, I want to navigate to the chat page (@bubble_chat_page.dart) of my bubble by tapping on the @bubble_status_card.dart widget.
    *   For more informations, read @docs/features/bubble.md.

### @contacts_page.dart

    *   As a user, I must be able to see a list of my contacts, so that I can see who my contacts are.
    *   As a user, I want to view a list of my contacts, displaying their name, avatar, online status, and bubble status.
    *   As a user, I want to be able to search my contacts by name or username.
    *   As a user, I want to filter my contacts by their bubble status (e.g., "No Bubble," "In a Bubble," "Bubble Full") and relationship type (e.g., "contact," "no relation" for search results).
    *   As a user, I want to be able to navigate to the profile page (@unified_profile_page.dart) of a user or a contact by clicking on their profile in the contact list.
    *   For more informations, read @docs/features/contacts.md.

## Central Button Functionality

    *   As a user, I want the central button in the bottom navigation bar to have dynamic behavior based on my bubble status and call state.
    *   As a user, I want the central button to show a demo dialog when I am not in a bubble, so that I can learn about the different states of the button.
    *   As a user, I want the central button to show a colored gradient when I am in a bubble but it is not full, so that I can see that I am in a bubble.
    *   As a user, I want the central button to show an animated gradient when there is an active call in my bubble, so that I can see that there is an active call.
    *   For more informations, read @docs/features/central_button_functionality.md.

## Unified Profile Viewing (Other Users)

    *   As a user, I want to view the profile of another Hopen user (e.g., by navigating from a contacts list, friends list, or bubble member list).
    *   As a user, when viewing another user's profile, I want to see their full name, username, profile picture, online visibility status, and bubble affiliation status.
    *   As a user, when viewing another user's profile, I want to see any mutual friends and/or mutual contacts we share.
    *   As a user, I want the main action button on another user's profile to be contextually relevant to our relationship.
    *   For more informations, read @docs/flutter/unified_profile_pages.md.

## Communication

### @chat_page.dart (for friends) and @bubble_chat_page.dart (for bubble members)

    *   As a user, I want to engage in one-on-one text-based chats with my friends or the members of the bubble (together, not separately)
    *   As a user in a chat, I want to send messages containing text and optionally media attachments (images/videos).
    *   As a user in a chat, I want to see when other participants are typing.
    *   As a user in a chat, I want to receive messages in real-time.
    *   As a user in a chat, I want to see message status updates (e.g., read receipts).
    *   As a user in a chat, I want to be able to delete messages I've sent (for myself or for everyone).
    *   As a user in a chat, I want to load older messages when I scroll to the top of the conversation (pagination).
    *   As a user in a chat, I want to be able to search for specific messages within that chat.

### Real-time Calling (Audio & Video)

    *   As a user, I want to initiate a one-on-one audio or video call with a friend or with the members of the bubble (together, not separately)
    *   As a user, I want to initiate a group audio or video call within my active bubble.
    *   As a user, I want to be able to accept or reject incoming calls.
    *   As a user in a call, I want to be able to end/leave the call.
    *   As a user in a call, I want to toggle my microphone (mute/unmute).
    *   As a user in a video call, I want to toggle my camera (video on/off).
    *   As a user in a video call, I want to be able to switch between my front and rear cameras.
    *   As a user in a call (1-on-1 or bubble call), I want the option to share my screen.
    *   As a user, I want to see the duration of an active call.
    *   As a user, I want to be able to join an ongoing group call in my bubble.

## Notifications

### System Alerts & Activity Updates

    *   As a user, I want to receive notifications for new incoming contact requests.
    *   As a user, I want to be notified when a sent contact request is accepted.
    *   As a user, I want to receive notifications for invitations to join a bubble.
    *   As a user, I want to receive notifications for new unread messages in my chats.
    *   As a user, I want to be notified about significant bubble-related events (e.g., a new member joins, bubble expiration warnings) [Specific events TBD].
    *   As a user, I want to receive notifications for incoming calls.
    *   (Note: Detailed management of *which* notifications to receive is tied to the "Notifications" settings page).

## Accessibility & User Preferences

### Application Customization & Accessibility Features

    *   As a user, I want to choose between a light and dark theme for the application interface (TODO in a future update).
    *   As a user, I want to have control over media autoplay settings (TODO in a future update).

## Security & Privacy

### Data Protection & User Control

    *   As a user, I expect that my direct messages are secured (e.g., via end-to-end encryption) (TODO in a future update).
    *   As a user, I want a security feature to log out from all active sessions on other devices (TODO in a future update).

This user story document provides a comprehensive guide for feature development and prioritization based on the current understanding of the codebase. It should be treated as a living document, subject to review and updates as the Hopen application evolves.
