# Contact, <PERSON><PERSON><PERSON> and Friends Requests System

## Overview

Hopen implements a comprehensive request system that manages the social connections between users. This system handles **contact requests**, **bubble lifecycle requests** (start, invite, join, kick-out), and **auto-generated friendship requests** after a bubble expires. Each request has its own dedicated dialog widget and follows a specific flow in the application.

**🚨 CRITICAL: Friend requests are NEVER manually created by users. All friendship requests are automatically generated when a bubble expires and are handled through the auto-generated friends system.**

This document describes the different types of requests, their associated dialog widgets, and the user flows for each request type.

## Database Architecture

Hopen uses a hybrid database architecture optimized for different data patterns:

- **PostgreSQL**: Core relational data (users, bubbles, relationships, requests)
- **ScyllaDB**: Real-time messaging, presence, and time-series data

## User Relationship Types

Hopen defines several types of relationships between users:

- **None**: No established relationship between users
- **Blocked**: Asymmetrical relationship when one user blocks another
- **Contact**: Mutual connection established through accepted contact requests
- **Bubbler**: **Dynamically calculated relationship** when two people are currently in the same active bubble (not stored as a persistent relationship type)
- **Maybe Friend**: When a bubble has expired and two people who were in that bubble have not accepted or rejected the automatic friend request
- **Friend**: When a bubble has expired and two people who were in that bubble have mutually accepted the friend request
- **Contact (from Maybe Friend rejection)**: When one person in a maybe_friend relationship rejects the friend request, both users automatically become contacts again reciprocally

### Important Notes on Bubbler Relationship

The "bubbler" relationship is **dynamically calculated** based on current bubble membership rather than being stored as a persistent relationship type. This means:

- When checking if two users are "bubblers", the system queries their current bubble memberships
- No separate relationship record is created for bubble membership
- The relationship automatically disappears when users leave the bubble or the bubble dissolves
- When a bubble dissolves, ex-bubble members (bubblers) automatically become contacts again

## Bubble Location System

The bubble location system is implemented in the following directories:

- **Request UI Components**: `@hopen/lib/presentation/widgets/requests`
  - Contains all UI components for request handling (join, start, propose, invite, kickout, friend request)
  
- **Bubble Information Display**: `@hopen/lib/presentation/pages/bubble`
  - Contains components for displaying bubble members and information about the bubble the viewing user is in

## Bubble System Overview

A bubble is a group of 2 to 5 people with specific lifecycle rules:

### Bubble Creation
- Created when a user asks one of their contacts to start a bubble together, and the contact accepts the request

### Bubble Join Requirements
A user can join a bubble when all of the following conditions are met:
- They are not already in a bubble
- The bubble is not full (5 people)
- They are not banned from the bubble
- They are not already a member of the bubble
- They are not already invited to the bubble
- They are not already requested to join the bubble
- They are not already proposed to join the bubble
- The bubble is not expired
- The bubble is not dissolved
- The bubble is not archived
- The bubble is not deleted
- The bubble is not private

### Bubble Join Methods
There are three ways someone can join a bubble:
1. They are invited by a member of the bubble
2. They request to join the bubble
3. They are proposed to join the bubble by a member of the bubble

### Bubble Lifecycle and Countdown
- When a bubble begins, it has a countdown of 90 days
- When someone joins a bubble, the countdown is augmented by 30 days
- A bubble's countdown cannot exceed 90 days (e.g., if a bubble has 80 days remaining and someone joins, it becomes 90 days)
- When someone leaves a bubble or is kicked out, the countdown is not modified

### Bubble States and Transitions

#### Active State
- When a bubble is created or joined by its second member
- In this state, a bubble can be:
  - Joined by new members
  - Invited to new members
  - Proposed to new members
  - Requested to join by new members
  - Have members kicked out

#### Expired State
- When a bubble's countdown reaches 0
- In this state, a bubble cannot be:
  - Joined by new members
  - Invited to new members
  - Proposed to new members
  - Requested to join by new members
  - Have members kicked out

#### Dissolved State
- When a bubble has less than 2 active members
- In this state, a bubble cannot be:
  - Joined by new members
  - Invited to new members
  - Proposed to new members
  - Requested to join by new members

#### Archived State
- When a bubble is expired or dissolved
- In this state, a bubble cannot be:
  - Joined by new members
  - Invited to new members
  - Proposed to new members
  - Requested to join by new members
  - Have members kicked out
  - Have members leave voluntarily
  - Be dissolved
  - Expire further

### Special Rules
- When a bubble has only 2 active members, the kickout functionality is disabled. Members can only leave the bubble voluntarily.
- **When a bubble EXPIRES** (countdown reaches 0), a friend request is proposed to all members of the bubble, and their relationship is set to 'maybe_friend'
- The friend request is accepted if and only if both users accept it

**🚨 CRITICAL: When one person in a maybe_friend relationship rejects the friend request, both users automatically become contacts again reciprocally. This ensures that former bubble members maintain at least a contact relationship unless they explicitly block each other.**

### Bubble Expiration vs Dissolution - IMPORTANT DISTINCTION

**EXPIRATION** (countdown reaches 0):
- Bubble countdown timer reaches 0 days
- **Automatic friend requests** are created for every pair of former bubble members
- All former bubble members become 'maybe_friend' with each other
- If friend requests are rejected, users become contacts
- The dynamic "bubbler" relationship ends

**DISSOLUTION** (fewer than 2 active members):
- Bubble has fewer than 2 active members (due to leaving/kickouts)
- **Former bubble members become contacts again** (no friend request process)
- The dynamic "bubbler" relationship ends
- No 'maybe_friend' status is created

**🚨 CRITICAL: The distinction between expiration and dissolution is very important. Only EXPIRATION triggers the friend request system, while DISSOLUTION directly converts former bubblers back to contacts.**

### Bubble Dissolution and Contact Creation

When a bubble dissolves (expires or has fewer than 2 active members), the following automatic process occurs:

1. **Bubble Status Update**: The bubble is marked as dissolved/expired
2. **Automatic Friend Requests**: Bidirectional friend requests are created for every pair of former bubble members
3. **Relationship Status**: All former bubble members are set to 'maybe_friend' status
4. **Contact Fallback**: If a friend request is rejected by either party, both users automatically become contacts
5. **Bubbler Relationship Ends**: The dynamic "bubbler" relationship is automatically removed since the bubble no longer exists

This ensures that former bubble members (bubblers) maintain some form of social connection unless they explicitly choose to block each other.

## Request Types and Flows

### 1. Contact Request Flow (manual)

- **Trigger**: A user (stranger with no existing relationship) sends a contact request to the current user
- **Dialog Widget**: `ContactRequestDialog`
- **Location**: `lib/presentation/widgets/requests/contact_request_dialog.dart`
- **Flow**:
  1. User A sends a contact request to User B
  2. **User A's unified profile page** (viewing User B): Button shows "Contact request sent" and is deactivated until the request is accepted or declined
  3. **User B receives notification** and the `ContactRequestDialog` is shown
  4. **User B's unified profile page** (viewing User A): Button shows "Contact request received" and is deactivated (acceptance happens through the dialog)
  5. If accepted, the two users become contacts
  6. If declined, no relationship is established

### 2. Bubble Start Request Flow

- **Trigger**: A contact (not in a bubble) sends a request to start a bubble with the current user (who is also not in a bubble)
- **Dialog Widget**: `BubbleStartRequestDialog`
- **Location**: `lib/presentation/widgets/requests/bubble_start_request_dialog.dart`
- **Flow**:
  1. User A sends a bubble start request to User B
  2. **User A's unified profile page** (viewing User B): Button shows "Bubble start request sent" and is deactivated until the request is accepted or declined
  3. **User B receives notification** and the `BubbleStartRequestDialog` is shown
  4. **User B's unified profile page** (viewing User A): Button shows "Bubble start request received" and is deactivated (acceptance happens through the dialog)
  5. If accepted, a new bubble is created with both users as members
  6. The bubble countdown begins
  7. If declined, no bubble is created

### 3. Bubble Invite Request Flow

- **Trigger**: A contact (already in a non-full bubble) invites the current user (not in a bubble) to join their bubble
- **Dialog Widgets**:
  - `BubbleInviteRequestDialog` (for the invitee)
  - `BubbleProposeRequestDialog` (for existing bubble members)
- **Locations**:
  - `lib/presentation/widgets/requests/bubble_invite_request_dialog.dart`
  - `lib/presentation/widgets/requests/bubble_propose_request_dialog.dart`
- **Flow**:
  1. User A (in bubble) sends a bubble invite request to User B (not in bubble)
  2. **User A's unified profile page** (viewing User B): Button shows "Bubble invite request sent" and is deactivated until the request is accepted or declined
  3. **User B receives notification** and the `BubbleInviteRequestDialog` is shown
  4. **User B's unified profile page** (viewing User A): Button shows "Bubble invite request received" and is deactivated (acceptance happens through the dialog)
  5. If accepted by User B, the `BubbleProposeRequestDialog` is shown to all other members of the bubble
  6. All existing bubble members must accept the proposal
  7. If all accept, User B joins the bubble
  8. If any member declines, User B does not join the bubble

### 4. Bubble Join Request Flow

- **Trigger**: A contact (not in a bubble) requests to join the current user's bubble (which is not full)
- **Dialog Widgets**:
  - `BubbleJoinRequestDialog` (for the bubble member receiving the request)
  - `BubbleProposeRequestDialog` (for other bubble members)
- **Locations**:
  - `lib/presentation/widgets/requests/bubble_join_request_dialog.dart`
  - `lib/presentation/widgets/requests/bubble_propose_request_dialog.dart`
- **Flow**:
  1. User A (not in bubble) sends a bubble join request to User B (in bubble)
  2. **User A's unified profile page** (viewing User B): Button shows "Bubble join request sent" and is deactivated until the request is accepted or declined
  3. **User B receives notification** and the `BubbleJoinRequestDialog` is shown
  4. **User B's unified profile page** (viewing User A): Button shows "Bubble join request received" and is deactivated (acceptance happens through the dialog)
  5. If accepted by User B, the `BubbleProposeRequestDialog` is shown to all other members of the bubble
  6. All existing bubble members must accept the proposal
  7. If all accept, User A joins the bubble
  8. If any member declines, User A does not join the bubble

### 5. Friend Request Flow (auto-generated only)

- **Trigger**: When a bubble expires, automatic bidirectional friend requests are created for every pair of former bubble members
- **Dialog Widget**: `FriendsChoiceDialog`
- **Location**: `lib/presentation/widgets/requests/friends_choice_dialog.dart`
- **Flow**:
  1. Bubble expires and automatic friend requests are created for all former bubble members
  2. **User A's unified profile page** (viewing User B): If User A has accepted the friend request but User B hasn't, button shows "Friend request sent" and is deactivated
  3. **User B's unified profile page** (viewing User A): If User B hasn't responded to the friend request, button shows "Friend request received" and is deactivated (acceptance happens through the dialog)
  4. Users receive notifications and the `FriendsChoiceDialog` is shown
  5. If both users accept, they become friends
  6. If either user declines, both users automatically become contacts

### 6. Bubble Kickout Request Flow

- **Trigger**: A bubble member requests to kick out another member from the bubble
- **Dialog Widget**: `BubbleKickoutRequestDialog`
- **Location**: `lib/presentation/widgets/requests/bubble_kickout_request_dialog.dart`
- **Access Method**: Via unified profile page → three-dot menu → "Kickout from bubble"
- **Flow**:
  1. A bubble member goes to another member's profile page
  2. They select "Kickout from bubble" from the options menu
  3. The `BubbleKickoutRequestDialog` is shown to all other bubble members (except the target)
  4. All voting members must accept the kickout request
  5. If all accept, the target member is removed from the bubble
  6. If any member declines, the target member remains in the bubble
- **Key Features**:
  - **Unanimous approval required**: All bubble members (except target) must agree
  - **Target exclusion**: The member being kicked out doesn't receive the dialog
  - **Red accept button**: Emphasizes the serious nature of the action
  - **Confirmation dialogs**: Additional safeguards before final action
  - **Same UI consistency**: Follows the design pattern of other request dialogs

### 7. Informational Dialogs

In addition to request dialogs that require user action, the system also uses several informational dialogs to notify users of important events. These dialogs require a simple acknowledgment (e.g., pressing an "OK" button) and are consistent with the overall UI design.

#### 7.1. Contactship Established Dialog

- **Purpose**: To inform a user that their previously sent contact request has been accepted by another user.
- **Dialog Widget**: `ContactshipEstablishedDialog`
- **Location**: `lib/presentation/widgets/requests/contactship_established_dialog.dart`
- **Trigger**: Shown when another user accepts the current user's contact request.
- **Key Information**:
  - Displays the new contact's name and profile picture.
  - Shows a "New contact!" title.
  - Includes a timestamp for when the request was accepted.

#### 7.2. Friendship Established Dialog

- **Purpose**: To inform a user that a mutual friendship has been formed.
- **Dialog Widget**: `FriendshipEstablishedDialog`
- **Location**: `lib/presentation/widgets/requests/friendship_established_dialog.dart`
- **Trigger**: Shown after both users in a `maybe_friend` relationship have accepted the auto-generated friend request. This dialog appears for the user who accepted first, as soon as the second user accepts.
- **Key Information**:
  - Displays the new friend's name and profile picture.
  - Shows a "New friendship!" title.
  - Includes a timestamp for when the friendship was established.

#### 7.3. Bubble Member Joined Dialog

- **Purpose**: To notify existing members of a bubble that a new user has joined.
- **Dialog Widget**: `BubbleMemberJoinedDialog`
- **Location**: `lib/presentation/widgets/requests/bubble_member_joined_dialog.dart`
- **Trigger**: Shown to all members of a bubble when a new user successfully joins (either through an invite or a join request).
- **Key Information**:
  - Displays the new member's name and profile picture.
  - Shows a "New bubble member" title.
  - Mentions the name of the bubble that was joined.
  - Includes a timestamp for when the member joined.

#### 7.4. Bubble Dissolved Dialog

- **Purpose**: To inform a user that a bubble they were a member of has been dissolved.
- **Dialog Widget**: `BubbleDissolvedDialog`
- **Location**: `lib/presentation/widgets/requests/bubble_dissolved_dialog.dart`
- **Trigger**: Shown to all former members when a bubble is dissolved (i.e., has fewer than 2 members).
- **Key Information**:
  - Displays the name of the dissolved bubble.
  - Shows a "Your bubble has ended" title.
  - Explains the reason for dissolution.
  - Clarifies that all former members automatically become contacts.
  - Includes a timestamp for when the bubble was dissolved.



## Implementation Details

### BubbleProposeRequestDialog

- **Purpose**: Shown to all bubble members when a new member is proposed to join the bubble
- **Location**: `lib/presentation/widgets/requests/bubble_propose_request_dialog.dart`
- **Key Components**:
  - Title: "New bubbler proposal"
  - Message: "[ProposerName] wants to add [ProposedMemberName] to [BubbleName]"
  - Profile picture of the proposed member
  - Accept/Decline buttons
  - Success message: "Proposal accepted"
- **Integration**:
  - Used by both the Bubble Invite Request flow and the Bubble Join Request flow
  - Triggered automatically when a bubble member accepts an invite or join request
  - All bubble members must approve for the new member to join

### BubbleKickoutRequestDialog

- **Purpose**: Shown to all bubble members when another member requests to kick out a target member
- **Location**: `lib/presentation/widgets/requests/bubble_kickout_request_dialog.dart`
- **Key Components**:
  - Title: "Kickout request"
  - Message: "[RequesterName] wants to remove [TargetMemberName] from [BubbleName]"
  - Profile picture of the target member
  - Accept (red)/Decline (gray) buttons
  - Success message: "Kickout request accepted"
- **Integration**:
  - Accessed via unified profile page options menu
  - Triggered when "Kickout from bubble" is selected
  - All bubble members (except target) must approve for removal

### Service Integration

- **BubbleProposeRequestService**: Handles showing the dialog and notifying bubble members
- **Location**: `lib/data/services/bubble_propose_request_service.dart`
- **Key Methods**:
  - `showProposeRequestDialog`: Shows the dialog to a specific user
  - `notifyBubbleMembers`: Sends notifications to all bubble members
- **Integration with BLoCs**:
  - `RequestManagementBloc`: Handles all request types including join, invite, and propose requests
  - `BubbleKickoutRequestBloc`: Handles kickout request voting and state management

  ## Unified Profile Page Button States

The button at the bottom of the unified profile page reflects the current request status and relationship state:

### Request Sent States (Button Deactivated)
- **"Contact request sent"**: When viewing a user you sent a contact request to (pending)
- **"Friend request sent"**: When viewing a maybe_friend user you accepted the friend request for (pending from their side)
- **"Bubble join request sent"**: When viewing a user you sent a bubble join request to (pending)
- **"Bubble invite request sent"**: When viewing a user you sent a bubble invite request to (pending)
- **"Bubble start request sent"**: When viewing a user you sent a bubble start request to (pending)

### Request Received States (Button Deactivated)
- **"Contact request received"**: When viewing a user who sent you a contact request (acceptance happens through ContactRequestDialog)
- **"Friend request received"**: When viewing a maybe_friend user who accepted the friend request (pending from your side, acceptance happens through FriendsChoiceDialog)
- **"Bubble join request received"**: When viewing a user who sent you a bubble join request (acceptance happens through BubbleJoinRequestDialog)
- **"Bubble invite request received"**: When viewing a user who sent you a bubble invite request (acceptance happens through BubbleInviteRequestDialog)
- **"Bubble start request received"**: When viewing a user who sent you a bubble start request (acceptance happens through BubbleStartRequestDialog)

### Active Relationship States (Button Activated)
- **"Send contact request"**: When viewing a user with no relationship
- **"Message"**: When viewing a contact, friend, or bubbler
- **"Start bubble together"**: When viewing a contact (context-dependent)
- **"Invite to bubble"**: When viewing a contact and you're in a non-full bubble
- **"Unblock"**: When viewing a blocked user

**🚨 CRITICAL: The unified profile page NEVER directly handles request acceptance/rejection. All request handling happens through dedicated dialog widgets in @hopen/lib/presentation/widgets/requests.**

### State Management Architecture

All request dialogs follow a consistent pattern:

#### Events
- `Load*RequestEvent`: Initialize dialog with request data
- `Accept*RequestEvent`: Approve the request
- `Decline*RequestEvent`: Reject the request  
- `Reset*RequestEvent`: Clear dialog state

#### States
- `*RequestStatus.initial`: Dialog not yet loaded
- `*RequestStatus.loading`: Fetching request data
- `*RequestStatus.loaded`: Dialog ready for interaction
- `*RequestStatus.accepting`: Processing acceptance
- `*RequestStatus.accepted`: Successfully approved
- `*RequestStatus.declining`: Processing rejection
- `*RequestStatus.declined`: Successfully rejected
- `*RequestStatus.error`: Error occurred

## User Experience Guidelines

### Visual Consistency
- All dialogs use the same animated gradient title with glow effects
- Profile pictures are prominently displayed for context
- Color-coded buttons indicate action severity:
  - **Blue**: Standard positive actions (Accept for proposals/invites)
  - **Red**: Serious/destructive actions (Kickout, Block, Report)
  - **Gray**: Neutral/decline actions

### Interaction Patterns
- **Non-dismissible dialogs**: Require explicit user action
- **Confirmation steps**: Additional safeguards for destructive actions
- **Loading states**: Visual feedback during processing
- **Success feedback**: Clear confirmation when actions complete
- **Auto-dismissal**: Dialogs close automatically after successful actions

### Error Handling
- **Network errors**: Retry mechanisms and user-friendly messages
- **Validation errors**: Clear feedback on invalid actions
- **State persistence**: Maintain dialog state across interruptions
- **Graceful degradation**: Fallback behavior when services unavailable

# Bubble Completion and Friendship Auto-Generation

## 1. Overview

**🎯 FRIENDSHIP SYSTEM ARCHITECTURE:**

When a bubble reaches its expiry time, the backend archives it and publishes `events.bubble.expired` on NATS JetStream. The **friendship** micro-service consumes this event and automatically creates **bidirectional friend requests** for every pair of former bubble members.

**KEY POINTS:**
- ❌ **NO manual friend requests** - Users cannot manually send friend requests to each other
- ✅ **AUTO-GENERATED ONLY** - All friend requests originate from bubble expiration events
- ✅ **BIDIRECTIONAL** - Friend requests are created for every pair of former bubble members
- ✅ **SOURCE TRACKING** - All friend requests include a `source_bubble_id` field
- ✅ **MQTT DELIVERY** - Users receive these requests via MQTT (`hopen/friendship/requests/{userId}`)
- ✅ **SIMPLE UI** - Users simply accept or decline through the friends system

## 2. Backend Integration (Go micro-services)

### Contact Requests
```http
POST   /api/v1/contact/requests              # send contact request
GET    /api/v1/contact/requests/sent         # list sent
GET    /api/v1/contact/requests/received     # list received
POST   /api/v1/contact/requests/:id/accept   # accept
POST   /api/v1/contact/requests/:id/decline  # decline
```

### Bubble Membership Requests
```http
POST /api/v1/bubble/start-request            # start bubble with a contact
POST /api/v1/bubble/:id/invite               # invite user to bubble
POST /api/v1/bubble/:id/join                 # request to join bubble
POST /api/v1/bubble/:id/kickout              # kick out member
POST /api/v1/bubble/:id/requests/:requestId/accept   # accept bubble-specific request
POST /api/v1/bubble/:id/requests/:requestId/decline  # decline bubble-specific request
POST /api/v1/bubble/requests/:requestId/accept       # accept any request type
POST /api/v1/bubble/requests/:requestId/decline      # decline any request type
```

### Friendship Requests (auto-generated)
```http
GET    /api/v1/friendship/requests           # list pending auto-generated requests
POST   /api/v1/friendship/requests/:id/accept   # accept
POST   /api/v1/friendship/requests/:id/decline  # decline
GET    /api/v1/friendship/friends            # list friends
```

> **Note**  Users cannot manually create friend requests. All friendship requests originate from bubble expiration events and therefore always include a `source_bubble_id`.

---

## 3. Data Model (PostgreSQL)

**Note**: Friend requests are handled within the consolidated `user_relationships` table using `relationship_type='friend'` and `status='pending'`. This design provides a more flexible and normalized approach, avoiding separate tables for different request types, reducing duplication, and centralizing all user relationship management in one place.

### Consolidated User Relationships Table
```sql
CREATE TABLE user_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    to_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    relationship_type relationship_type NOT NULL DEFAULT 'friend',
    status relationship_status DEFAULT 'pending',
    source_bubble_id UUID, -- References bubbles(bubble_id)

    -- Bidirectional acceptance tracking
    from_user_accepted BOOLEAN DEFAULT false,
    to_user_accepted BOOLEAN DEFAULT false,
    from_user_accepted_at TIMESTAMP WITH TIME ZONE,
    to_user_accepted_at TIMESTAMP WITH TIME ZONE,
    
    -- Request metadata
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT user_relationships_no_self_relationship CHECK (from_user_id != to_user_id),
    CONSTRAINT user_relationships_unique_relationship UNIQUE (from_user_id, to_user_id, relationship_type),
    CONSTRAINT user_relationships_symmetric_order CHECK (
        relationship_type = 'blocked' OR from_user_id < to_user_id
    ),
    CONSTRAINT user_relationships_acceptance_logic CHECK (
        (status != 'accepted') OR 
        (from_user_accepted = true AND to_user_accepted = true AND accepted_at IS NOT NULL)
    ),
    CONSTRAINT user_relationships_expiry_logic CHECK (
        expires_at IS NULL OR expires_at > created_at
    )
);
```

**Note**: Bubbler relationships are dynamically calculated from the `bubble_members` table, not stored in `user_relationships`. All relationship types (friend, contact, blocked, maybe_friend) and their statuses (pending, accepted, declined) are managed here, including auto-generated friend requests from expired bubbles.

---

## 4. Frontend Implementation

### 4.1. Dialog Widget Architecture

All request dialogs inherit common patterns:

```dart
abstract class BaseRequestDialog extends StatefulWidget {
  // Common properties
  final String requestId;
  final DateTime requestTimestamp;
  
  // Common methods
  static Future<bool?> show(BuildContext context, {/* params */});
  
  @protected
  Widget buildDialogContent(BuildContext context);
  @protected
  void handleAccept(BuildContext context);
  @protected
  void handleDecline(BuildContext context);
}
```


### 4.3. Navigation Integration

Request dialogs are integrated with the app's navigation system:

```dart
// Unified Profile Page menu integration
PopupMenuButton<String>(
  onSelected: (String result) {
    switch (result) {
      case 'kickout':
        if (isBubbler) _showKickoutDialog();
        break;
      case 'invite':
        if (canInviteToBubble) _showInviteDialog();
        break;
      // Other menu options...
    }
  },
  itemBuilder: (context) => _buildMenuItems(context),
)

// Dialog trigger methods
void _showKickoutDialog() {
  BubbleKickoutRequestDialog.show(
    context,
    targetMemberId: widget.profileUserId,
    // Other required parameters...
  );
}
```

## 5. Edge cases

### 5.1.When multiples bubble requests are received (start, join, invite), we implemetend a queue system to manage them.

On the frontend side :
- the first request (start, join or invite) will be shown first.
- as long as it is pending, no next request will be shown.
- if it is accepted, all of the other queued requests will be refused
- if it is refused, the next request in line will be shown.

User Experience Flow
First request when bubble has capacity → Processed immediately
Subsequent requests when bubble is at capacity → Added to queue with position
User receives feedback: "Join request queued. You are #2 in the queue."
When capacity opens → Next request in queue is automatically promoted
User receives notification: "Your request is now being processed by members"
When request is accepted → All other queued requests are rejected
Affected users receive notification: "Request rejected - bubble capacity filled"

Queue-Based Approach is Industry Standard
FIFO (First-In-First-Out) Principle: Your approach follows the universally accepted fairness standard
Centralized Queue Management: Prevents race conditions by having a single source of truth
Sequential Processing: Only one request is active at a time, preventing capacity overflow
Transparent Ordering: Users understand their position and expected wait time

### 5.2. Blocking edge cases

If User A sends a contact request to User B, and B blocks A, the request is **immediately cancelled**.
If a `maybe_friend` request is pending and one user blocks the other, the relationship immediately becomes `Blocked`, superseding the pending request.

#### 5.3. User deactivation edge cases

Problem:** A user deactivates their account. What is the status of their
pending requests and active votes?
Solution:** All pending requests (incoming and outgoing) and any votes in
ongoing proposals are **invalidated**

### 5.4. Relationship change mid-request edge cases

* *** Problem:** User A sends a 'Bubble Start Request' to User B (a contact). Before B accepts, A removes B as a contact.
* **• Solution:** The bubble start request is **invalidated** because the precondition of being a contact is no longer met.

### 5.5. Kicked-out user tries to rejoin

Problem:** Can a user who was kicked out of a bubble rejoin it later?
* **• Solution:** No. A user who is kicked out is **permanently banned** from that specific bubble and cannot be invited back.

The system provides:

🛡️ Race Condition Prevention: FIFO queue eliminates capacity race conditions
🔄 Automatic Cleanup: User deactivation and blocking trigger proper cleanup
⏰ Timeout Management: 30-day automatic expiration prevents stale data
🚫 Ban Enforcement: Permanent bans for kicked-out users
💪 Robust Voting: Handles member departures during voting
📱 Frontend Ready: All notification types supported by existing UI

## 6. Security Considerations

### 6.1. Authorization
- Verify user permissions before showing dialogs
- Validate bubble membership for kickout requests
- Rate limiting for request creation
- Audit logging for all request actions

### 6.2. Privacy
- Hide sensitive information from unauthorized users
- Exclude targets from kickout notification distribution
- Secure storage of request data
- GDPR compliance for data retention

### 6.3. Abuse Prevention
- Spam detection for excessive requests
- Cooldown periods between similar requests
- Community reporting for malicious behavior
- Automatic moderation triggers

## 7. Future Enhancements

### 7.1. Advanced Features
- **Request expiration**: Auto-decline after time limits
- **Partial voting**: Handle offline member scenarios
- **Request analytics**: Track success rates and patterns

### 7.2. User Experience
- **Batch operations**: Multiple selections in single dialog

### 7.3. Moderation Tools
- **Admin overrides**: Moderator intervention capabilities
- **Appeal processes**: Contest unfair kickouts
- **Automated enforcement**: Rule-based request handling
- **Community guidelines**: Clear policy communication

This comprehensive system provides a robust foundation for managing all social interactions within the Hopen application, ensuring consistency, security, and user satisfaction across all request types.
