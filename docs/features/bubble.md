# Bubble Feature Technical Documentation

## Overview

The Bubble feature in Hopen provides users with a dedicated view to manage and interact with their established bubble. It focuses on users that are members of the user's bubble. 

## UI Components

### BubblePage

- **Location**: `lib/presentation/pages/bubble/bubble_page.dart`
- **Key Components**:
  1. Centered, cyan-colored title display ("Bubble") above the list.
  2. Widget displaying `BubbleStatusCard` widget. Uses standard 20px horizontal padding.
  3. It shows the bubble name, the bubble's countdown, and a radial progress indicator indicating the bubble's advancement.

### FriendsTile

- **Location**: `lib/presentation/widgets/friends_tile.dart`
- **Purpose**: Displays individual friend information, focused on messaging status or context.
- **Key Components**:
  1. Profile picture or initials (CircleAvatar)
  2. Friend's name
  3. Subtitle (e.g., last message snippet)
  4. Optional `trailing` widget (e.g., timestamp, `NotificationCountBadge` for unread count - a fixed 20×20px red circular badge that maintains consistent size regardless of the digit count)

### FriendProfilePage

- **Location**: `lib/presentation/pages/friends/friend_profile_page.dart`
- **Purpose**: Displays detailed information about a specific friend, provides communication shortcuts, and offers management options.
- **Key Components**:
  1. **AppBar**: Transparent background, back button, friend's username (if available) or name as title, and a "More Options" (`...`) menu.
  2. **Profile Picture**: Displayed prominently at the top with a gradient fade effect.
  3. **Name & Status**: Displays the friend's full name with an `OnlineStatusIndicator` (glowing green dot if online).
  4. **Relationship History**: 
      - Displays "Friends since [Date]".
      - Displays "Shared a bubble from [Date] to [Date]" (if applicable).
  5. **Mutual Connections**:
      - Title: "[Count] friends in common".
      - Displays a horizontally scrolling `Row` of `CircleAvatar`s representing mutual friends, with small names displayed below each avatar.
        - *Interaction*: Tapping an avatar navigates the user to the profile page of that specific mutual friend using `MaterialPageRoute` to create proper navigation stack.
      - Title: "[Count] contacts in common".
      - Displays a horizontally scrolling `Row` of `CircleAvatar`s representing mutual contacts, with small names displayed below each avatar.
        - *Interaction*: Tapping an avatar navigates the user to the profile page of that specific mutual contact using `MaterialPageRoute` to create proper navigation stack.
  6. **Action Buttons**: Glassmorphism-styled circular buttons for "Audio Call", "Video Call", and "Screen Share".
  7. **Friend Status / Bubble Action Button**: A fixed button at the bottom whose text and action depend on the bubble status of both the current user and the friend being viewed. Requires fetching bubble status (exists, isFull, members) for both users.
      - **Condition:** Friend = No Bubble **AND** User = No Bubble
        - *Button Text:* `Ask to begin a bubble together`
        - *Action:* Send request to friend. If friend accepts, create a new 90-day bubble containing both users. *(Backend Ready)*
      - **Condition:** Friend = No Bubble **AND** User = Bubble (Not Full < 5 members)
        - *Button Text:* `Ask to join your bubble`
        - *Action:* Send join request to friend AND approval request to all *other* members of the user's current bubble. If friend accepts AND all other members approve, add friend to the user's bubble. *(Backend Ready)*
      - **Condition:** Friend = No Bubble **AND** User = Bubble (Full = 5 members)
        - *Button Text:* `Friend` (Disabled)
        - *Action:* On tap, show `AlertDialog` styled like Help & Support page dialogs:
          - *Title:* `Your bubble is full`
          - *Content:* `You can't ask someone to join your bubble when it is full (5 bubblers max).`
      - **Condition:** Friend = Bubble (Full = 5 members) **AND** User = No Bubble
        - *Button Text:* `Friend` (Disabled)
        - *Action:* On tap, show `AlertDialog` styled like Help & Support page dialogs:
          - *Title:* `Their bubble is full`
          - *Content:* `You can't join a full bubble (5 bubblers max).`
      - **Condition:** Friend = Bubble (Not Full < 5 members) **AND** User = No Bubble
        - *Button Text:* `Ask to join bubble`
        - *Action:* Send join request to friend AND approval request to all *other* members of the friend's current bubble. If friend accepts AND all other members approve, add user to the friend's bubble. *(Backend Ready)*
      - **Condition:** Friend = In Any Bubble **AND** User = In Different Bubble
        - *Button Text:* `Friend` (Disabled)
        - *Action:* None.
      - **Condition:** Friend = In Bubble **AND** User = In Same Bubble
        - *Button Text:* `Friend, in your bubble`
        - *Action:* Navigate to the shared Bubble Chat Page. *(Routing Ready)*
  8. **More Options Menu (PopupMenuButton)**:
      - Triggered by the `...` icon in the AppBar.
      - Styled with a semi-transparent background and border matching the bottom button's inactive style.
      - Options:
        - `Block user` (Red text)
        - `Report user` (Red text)
        - `Unfriend` (Red text)
  9. **Background**: Uses a specific `LinearGradient` (`Color(0xFF00C4FF)` -> `Color(0xFF00A3E0)`) matching the `FriendsTile` background, instead of the default app gradient. **NEW**

## Sorting Logic

### Message Recency Sorting

- **Behavior**: The friends list is ordered by the recency of the last message exchanged (sent or received), with the most recent conversations appearing at the top.
- **Implementation Details**:
  1. Each friend entry includes timestamp information for the most recent message.
  2. Timestamps are stored in ISO 8601 format (or as DateTime objects in memory).
  3. For display purposes, relative time formats are used (e.g., "just now", "5 min ago", "yesterday").
  4. Special time formats like "Yesterday" or day names are converted to actual timestamps for sorting.
  
- **Sorting Algorithm**:
  dart
  // Pseudocode for sorting
  friends.sort((a, b) {
    final DateTime timestampA = parseTimestamp(a.lastMessageTime);
    final DateTime timestampB = parseTimestamp(b.lastMessageTime);
    return timestampB.compareTo(timestampA); // Descending order (newest first)
  });
  
- **Edge Cases Handling**:
  1. Friends with no messages are placed at the bottom of the list.
  2. When multiple messages have identical timestamps (rare but possible), secondary sorting is applied using the friend's ID for consistent ordering.
  3. If timestamp parsing fails, the system defaults to treating it as the oldest message (placing it at the bottom).

- **Performance Considerations**:
  1. Sorting is performed in-memory before rendering the list.
  2. For large friend lists (>1000), lazy loading and virtual scrolling techniques may be applied.
  3. Sorting is performed once on data load and again when new messages are sent/received.

## Related Documentation

For detailed information about profile pages including `FriendProfilePage`, see [Profile Pages Documentation](profile_pages.md).
