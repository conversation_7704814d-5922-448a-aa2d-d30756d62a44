Integration Plan: infinite_scroll_pagination

# Objective
To replace manual list loading and state management with a robust, efficient, and user-friendly pagination system for all major lists in the application (Contacts, Friends, Chat Messages). This will improve performance, reduce memory usage, and enhance the user experience.


# Phase 1: Setup and Initial Configuration

### Add Dependency
Open your pubspec.yaml file and add the latest version of the package under dependencies.
code
Yaml
dependencies:
  flutter:
    sdk: flutter
  # ... other dependencies
  infinite_scroll_pagination: ^4.0.0


Then, run flutter pub get.
Understand Core Concepts:
Familiarize your team with the three key components of the package:
PagingController<PageKeyType, ItemType>: The heart of the package. It manages page requests, stores the list of loaded items, and holds the current state (loading, error, completed). This will be managed within your Riverpod controllers.
PagedListView / PagedGridView / PagedSliverList: These are the UI widgets that listen to the PagingController and automatically build the list, showing loading indicators, error messages, or the "no items" view as needed.
addPageRequestListener: This is the method on the PagingController where you add a listener that will be called whenever the widget needs to fetch a new page of data.

# Phase 2: Data & Repository Layer Adaptation

The infinite_scroll_pagination package requires your backend and data layer to support paginated data fetching.

## API Requirement (Assumption):
This plan assumes your backend API can accept pagination parameters, such as a page number/key and a page size/limit. A typical API endpoint would change from GET /friends to GET /friends?page=1&limit=20.
Create a Generic Paginated Response Model:
To standardize responses from your repositories, create a generic model to hold a page of data and the key for the next page.

Create a new file: lib/data/models/paginated_response.dart
code
Dart
class PaginatedResponse<T> {
  final List<T> items;
  final bool isLastPage;
  final dynamic nextPageKey;

  PaginatedResponse({
    required this.items,
    required this.isLastPage,
    this.nextPageKey,
  });
}

## Update Repository Methods:

Modify your existing repository methods to support pagination. Let's use the FriendsRepository as an example.
Current (Hypothetical): Future<List<User>> getFriends();
New Signature: Future<PaginatedResponse<User>> getFriends({required int pageKey, required int pageSize});

Example Implementation (FriendsRepository):
code
Dart
// Inside your FriendsRepository implementation
Future<PaginatedResponse<User>> getFriends({required int pageKey, required int pageSize}) async {
  final response = await _apiClient.get('/friends', queryParameters: {
    'page': pageKey,
    'limit': pageSize,
  });

  final List<User> newFriends = (response.data['items'] as List)
      .map((item) => User.fromJson(item))
      .toList();
  
  final bool isLastPage = response.data['isLastPage'] ?? newFriends.length < pageSize;
  
  return PaginatedResponse(
    items: newFriends,
    isLastPage: isLastPage,
    nextPageKey: isLastPage ? null : pageKey + 1,
  );
}

## Action: Apply this pattern to all relevant repositories: ContactsRepository, ChatRepository, etc. For chat, the pageKey might be a DateTime cursor instead of an int.

# Phase 3: Business Logic Layer (Riverpod Controller) Integration

This is where you'll integrate the PagingController. We will refactor the existing Riverpod controllers (e.g., FriendsControllerProvider) to use it.
Refactor the State Controller:
The controller will now be responsible for creating, managing, and disposing of a PagingController.
Example: friends_provider.dart
code
Dart
// No longer need a complex state class for the list itself.
// The PagingController handles the list, loading, and error states.
// You can keep a state class if you have other UI state to manage.

final friendsPagingControllerProvider = StateProvider.autoDispose<PagingController<int, Map<String, dynamic>>>((ref) {
  final pagingController = PagingController<int, Map<String, dynamic>>(firstPageKey: 1);

  // Add the listener to fetch pages
  pagingController.addPageRequestListener((pageKey) {
    _fetchFriendsPage(ref, pagingController, pageKey);
  });

  // Dispose the controller when the provider is disposed
  ref.onDispose(() => pagingController.dispose());

  return pagingController;
});

// Helper function to fetch data
Future<void> _fetchFriendsPage(AutoDisposeRef ref, PagingController<int, Map<String, dynamic>> pagingController, int pageKey) async {
  try {
    // Assume a repository provider exists
    final friendsRepository = ref.read(friendsRepositoryProvider); 
    const pageSize = 20; // Define a page size constant

    final newPage = await friendsRepository.getFriends(pageKey: pageKey, pageSize: pageSize);

    if (newPage.isLastPage) {
      pagingController.appendLastPage(newPage.items);
    } else {
      pagingController.appendPage(newPage.items, newPage.nextPageKey as int);
    }
  } catch (error) {
    pagingController.error = error;
  }
}

Adapt for Search (ContactsPage):
For search functionality, you need to refresh the controller when the query changes.

Create a provider for the search query:
code
Dart
final contactSearchQueryProvider = StateProvider<String>((ref) => '');
Modify the ContactsController to listen to this query:
code
Dart
final contactsPagingControllerProvider = StateProvider.autoDispose<PagingController<int, UserContact>>((ref) {
  final pagingController = PagingController<int, UserContact>>(firstPageKey: 1);

  // Refresh the list whenever the search query changes
  ref.listen(contactSearchQueryProvider, (_, __) {
    pagingController.refresh();
  });

  pagingController.addPageRequestListener((pageKey) {
    _fetchContactsPage(ref, pagingController, pageKey);
  });

  ref.onDispose(() => pagingController.dispose());
  return pagingController;
});

Future<void> _fetchContactsPage(...) async {
  final query = ref.read(contactSearchQueryProvider);
  // ... call repository with the query ...
}

# Phase 4: Presentation Layer (UI) Implementation

Now, replace your ListView.builder widgets with PagedListView.
Implement on FriendsPage:

File: lib/presentation/pages/friends/friends_page.dart
code
Dart
// ... imports

class FriendsPage extends ConsumerWidget {
  const FriendsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pagingController = ref.watch(friendsPagingControllerProvider);

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: const MainAppBar(),
        body: RefreshIndicator(
          onRefresh: () => Future.sync(() => pagingController.refresh()),
          child: PagedListView<int, Map<String, dynamic>>(
            pagingController: pagingController,
            padding: const EdgeInsets.only(
              left: 20,
              right: 20,
              bottom: kBottomNavigationBarHeight + 20,
            ),
            builderDelegate: PagedChildBuilderDelegate<Map<String, dynamic>>(
              itemBuilder: (context, friendMap, index) => FriendsTile(
                name: '${friendMap['firstName']} ${friendMap['lastName']}'.trim(),
                subtitle: friendMap['username'] ?? 'unknown',
                avatarUrl: friendMap['profilePictureUrl'],
                isOnline: friendMap['isOnline'] ?? false,
                onTap: () { /* ... navigation ... */ },
              ),
              // --- CUSTOM INDICATORS (Highly Recommended) ---
              firstPageProgressIndicatorBuilder: (_) => const Center(child: CircularProgressIndicator()),
              newPageProgressIndicatorBuilder: (_) => const Center(child: CircularProgressIndicator(strokeWidth: 2)),
              noItemsFoundIndicatorBuilder: (_) => const Center(
                child: Text(
                  "You don't have any friends yet.\nComplete a bubble to make true friends",
                  style: TextStyle(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ),
              firstPageErrorIndicatorBuilder: (context) => _buildErrorIndicator(context, pagingController),
              newPageErrorIndicatorBuilder: (context) => _buildErrorIndicator(context, pagingController, isNewPage: true),
            ),
          ),
        ),
      ),
    );
  }

  // Helper for error indicators
  Widget _buildErrorIndicator(BuildContext context, PagingController controller, {bool isNewPage = false}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(isNewPage ? 'Could not load more friends.' : 'Something went wrong.', style: TextStyle(color: Colors.red)),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () => controller.retryLastFailedRequest(),
            child: const Text('Retry'),
          )
        ],
      ),
    );
  }
}

## Implement on ChatPage (Reverse List):

The chat page requires a reversed list. The package handles this perfectly.
File: lib/presentation/pages/chat/chat_page.dart

code
Dart
// In your ChatController, the pageKey would be the timestamp of the oldest message loaded.
// The _fetchPage logic would request messages *before* that timestamp.

// In the ChatPage widget build method:
PagedListView<DateTime, ChatMessage>( // Note the PageKeyType is DateTime
  pagingController: ref.watch(chatPagingControllerProvider(widget.chatId)),
  reverse: true, // This is the key for chat UIs
  builderDelegate: PagedChildBuilderDelegate<ChatMessage>(
    itemBuilder: (context, message, index) {
      // Your existing ChatBubble logic
      return ChatBubble(...);
    },
    // ... custom indicators
  ),
)

# Phase 5: Testing Strategy

## Controller/Provider Testing:
Use flutter_test and riverpod_test.
Create a mock repository that returns PaginatedResponse objects.
Test the _fetchPage logic:
Verify that pagingController.appendPage is called with the correct items and next page key.
Verify that pagingController.appendLastPage is called when the mock response has isLastPage: true.
Verify that pagingController.error is set when the mock repository throws an exception.
Widget Testing:
Create a test setup that provides a mock PagingController.
Test each UI state by manipulating the mock controller:
Set an error on the controller and verify the firstPageErrorIndicatorBuilder widget is displayed.
Append an empty list as the last page and verify the noItemsFoundIndicatorBuilder is displayed.
Verify that the itemBuilder correctly renders the items from the controller's itemList.

# Checklist for a Perfect Integration

- Dependency added to pubspec.yaml.
- PaginatedResponse model created.
- All relevant repository methods updated to support pagination (getFriends, getContacts, getMessages, etc.).
- Riverpod providers refactored to create and manage PagingController instances.
- addPageRequestListener is implemented correctly in each provider.
- PagingController is disposed via ref.onDispose.
- ListView.builder widgets are replaced with PagedListView.
- PagedListView is wrapped in a RefreshIndicator for pull-to-refresh functionality.
- Custom, on-brand widgets are provided for all indicator builders (firstPageProgress, noItemsFound, firstPageError, etc.).
- Search functionality is correctly wired to call pagingController.refresh().
- Chat screen uses reverse: true and a cursor-based pageKey (like DateTime).
- Unit tests are written for the controller logic.
- Widget tests are written for the UI states.
