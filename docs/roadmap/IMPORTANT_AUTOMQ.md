🚀 Architectural Proposal: Integrating AutoMQ
Date: August 30, 2025
Author: Gemini AI Assistant
Status: Proposal
This document outlines the strategic advantages of integrating AutoMQ into our backend and provides a phased implementation plan.

1. Executive Summary
Our current eventing system, built on NATS JetStream and the Transactional Outbox pattern, is robust and perfectly suited for its primary role: ensuring reliable, low-latency delivery of command-style events for inter-service communication and saga orchestration.
However, to prepare for future scale and unlock new data capabilities, we propose the phased introduction of AutoMQ in parallel with our existing NATS system.
📋 Recommendation Summary
Component	Recommendation	Rationale
NATS JetStream	Keep as is.	Ideal for low-latency, critical operational messaging and saga orchestration. It's our system's reliable command bus.
AutoMQ	Introduce in phases.	Target high-volume, long-retention workloads like analytics and event sourcing to leverage massive cost savings and scalability.
This dual-platform approach allows us to use the best tool for the job without a risky "rip-and-replace" migration.

2. What is AutoMQ?
AutoMQ is a modern, cloud-native streaming platform that is 100% compatible with the Apache Kafka API. Its key innovation is its architecture that decouples compute (brokers) from storage (S3).
This architecture eliminates the most significant operational burdens and costs associated with traditional streaming platforms at scale.
🏛️ Architectural Comparison
code
Mermaid
graph TD
    subgraph Traditional Streaming (NATS JetStream / Kafka)
        A[Broker 1 <br/> (Compute + Storage on local SSD)]
        B[Broker 2 <br/> (Compute + Storage on local SSD)]
        C[Broker 3 <br/> (Compute + Storage on local SSD)]
        A <--> B
        B <--> C
        A <--> C
    end

    subgraph AutoMQ Architecture
        D[Stateless Broker 1 <br/> (Compute)]
        E[Stateless Broker 2 <br/> (Compute)]
        F[Stateless Broker 3 <br/> (Compute)]
        G[Cloud Object Storage (S3) <br/> (Infinite, Cheap Storage)]
        D <--> G
        E <--> G
        F <--> G
    end

3. Core Advantages of AutoMQ
Integrating AutoMQ for specific use cases would provide the following strategic advantages.
Icon	Advantage	Description	Relevance to Our Backend

💰	Extreme Cost-Effectiveness	Storing terabytes of event data on S3 is 90-99% cheaper than on the provisioned SSDs required by traditional streaming platforms.	As our user base grows, the volume of analytics and audit events will explode. Storing this on NATS disks would become prohibitively expensive.

🚀	Elastic & Independent Scaling	Broker nodes are stateless. We can add or remove them in seconds to handle traffic spikes without waiting for data rebalancing. Storage scales infinitely with S3.	This allows us to handle peak loads (e.g., major live events in bubbles) cost-effectively by scaling compute up and down, while storage costs remain low.

♾️	Infinite & Affordable Retention	We can afford to store our event streams forever. This unlocks powerful capabilities like event sourcing, replaying historical events, and meeting long-term compliance requirements.	A game-changer for our social_analytics service. We could build new analytics models by replaying the entire history of user interactions from S3.

🛠️	Operational Simplicity (at Scale)	Eliminates the biggest headaches of managing Kafka/NATS clusters: disk space management, partition rebalancing, and complex broker replacement.	Our platform team would spend less time managing storage and more time optimizing performance. Brokers become disposable, fitting our Kubernetes model perfectly.

🤝	Full Apache Kafka Compatibility	We can leverage the entire, mature Kafka ecosystem of clients, connectors (Debezium, Kafka Connect), and tools (Flink, Spark) without changing a single line of code.	This is a massive advantage for building data pipelines, such as streaming database changes (CDC) directly from PostgreSQL into our analytics systems.

4. Implementation Strategy: A Phased Approach
We will introduce AutoMQ alongside NATS, allowing each system to handle the workloads it's best suited for.

Phase 0: 🟢 No Immediate Change (Current State)
Action: Continue using NATS JetStream with the Transactional Outbox pattern for all existing inter-service communication and saga orchestration.

Rationale: This system is robust, reliable, and perfectly meets our current operational needs.

Phase 1: 🟡 Introduction for Analytics (Coexistence)
This is the recommended starting point. We will build a new analytics pipeline using AutoMQ without touching existing critical messaging paths.
Deploy AutoMQ: Set up an AutoMQ cluster in Kubernetes, configured to use an S3 bucket.

Implement CDC with Debezium: Deploy Debezium to capture changes from our PostgreSQL database and stream them as events into AutoMQ topics.
Modify outbox_processor: Update the outbox_processor to dual-write specific, high-volume events (e.g., message.sent) to both NATS and AutoMQ.

Build a New Consumer: Create a new consumer that reads from AutoMQ to populate the social_analytics database.

Architecture - Phase 1
code
Mermaid
graph TD
    subgraph Microservices
        A[User Service] -->|Writes| B(PostgreSQL)
        C[Bubble Service] -->|Writes| B
    end

    subgraph Eventing
        B -- CDC via Debezium --> D{AutoMQ on S3}
        E[outbox_processor] -- Dual Write --> D
        E -- Dual Write --> F[NATS JetStream]
    end

    subgraph Consumers
        D -- Analytics Stream --> G[Social Analytics Consumer]
        F -- Operational Events --> H[Existing Service Consumers]
        G --> I(Analytics DB)
    end

Phase 2: 🔵 Migration of High-Volume Services (Optional)
If the realtime service's message volume becomes a storage bottleneck for NATS, we can migrate its chat history persistence to AutoMQ.
Dual-Write: The realtime service will write messages to both ScyllaDB (for immediate reads) and an AutoMQ topic (for long-term storage).
Migrate Consumers: Services needing full chat history will read from the AutoMQ topic.
Adjust ScyllaDB TTL: Significantly reduce the Time-To-Live (TTL) on the ScyllaDB bubble_messages table, using it only as a short-term cache.

5. Technical Implementation Details
🏗️ Infrastructure
Task	Description
S3 Bucket	Provision a dedicated S3 bucket for AutoMQ storage with appropriate lifecycle policies.
IAM Role	Create an IAM role with S3 permissions for the AutoMQ broker pods.
Kubernetes	Deploy the AutoMQ Helm chart to our EKS cluster.
⚙️ Configuration Changes
Update config/config.yaml to include a new section for Kafka/AutoMQ clients.
code
Yaml

# config/config.yaml

# ... existing config ...

kafka:
  bootstrap_servers: ${KAFKA_BOOTSTRAP_SERVERS:automq-broker.automq.svc.cluster.local:9092}
  client_id: ${KAFKA_CLIENT_ID:hopen-backend}

  # Add other Kafka client settings (security, etc.) as needed
💻 Code Changes
Add Kafka Client Library: Add segmentio/kafka-go to go.mod.
Create a Kafka Publisher: In pkg/events/, create a kafka_publisher.go that implements our existing EventPublisher interface.
code
Go
// pkg/events/kafka_publisher.go
package events

import (
    "context"
    "github.com/segmentio/kafka-go"
    // ...
)

type KafkaPublisher struct {
    writer *kafka.Writer
    logger *zap.Logger
}

func NewKafkaPublisher(...) *KafkaPublisher {
    // ... writer initialization ...
}

func (p *KafkaPublisher) PublishEvent(eventType EventType, event interface{}) error {
    // ... marshal event and publish to Kafka/AutoMQ ...
}
Update outbox_processor: Modify the processor to allow for multiple publishers. It will read an event from the outbox and, based on the EventType, decide whether to publish it to NATS, AutoMQ, or both.

6. Risks and Mitigation


⚠️	Operational Complexity	Introduce AutoMQ in a phased manner, starting with a non-critical analytics pipeline. Build robust monitoring and alerting for the new components.
⚠️	Performance Tuning	Conduct a Proof of Concept (PoC) to benchmark latency and throughput for our specific workloads to understand the performance characteristics of S3-backed storage.
⚠️	Cost Management	While storage is cheap, S3 API calls (GET, PUT) have costs. We must monitor API usage and configure caching and batching appropriately to manage costs.
🛡️	Vendor Lock-in	AutoMQ's 100% Kafka API compatibility significantly mitigates this risk. We can switch to any other Kafka-compatible system if needed.

7. Conclusion & Next Steps
Our current NATS-based eventing system is well-suited for our operational needs. The introduction of AutoMQ is a strategic, forward-looking decision to prepare our platform for the challenges of massive data scale and to unlock new analytical capabilities.
By adopting a phased, parallel implementation, we can gain the benefits of cloud-native streaming without disrupting our existing, critical infrastructure.

✅ Next Steps

Initiate Proof of Concept (PoC):

Deploy a staging AutoMQ cluster.

Configure Debezium to stream data from a staging PostgreSQL replica.

Develop a simple consumer to validate the end-to-end data flow.

Benchmark performance and estimate operational costs.