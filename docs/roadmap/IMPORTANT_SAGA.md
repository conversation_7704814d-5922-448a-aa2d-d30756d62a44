
# Guide to Saga Orchestration Frameworks

Here’s a breakdown of why you should consider a framework for saga, what your best options are, and a practical guide on how to implement one.

---

## 1. Why Use a Saga Framework? (The Problem with Manual Implementation)

Your current manual saga implementation requires you to handle several complex concerns:

-   **State Management:** You have to persist the saga's state (`CurrentStep`, `CompletedSteps`, `Data`, etc.) to PostgreSQL after every step. This is error-prone and adds significant database chatter.
-   **Error Handling & Retries:** You need to manually implement retry logic for each step (as seen with `retryManager`).
-   **Compensation Logic:** You manually trigger compensation in reverse order. If a compensation step itself fails, the system can be left in an inconsistent state, requiring manual intervention (which you've correctly identified with `CRITICAL` log messages).
-   **Observability:** Debugging a failed saga involves piecing together logs from different services and database states. It's difficult to get a clear, end-to-end picture of a single saga's execution.
-   **Boilerplate Code:** A lot of the code in `saga_handler.go` is orchestration logic (rebuilding state, looping through steps, calling compensation) rather than pure business logic.

A dedicated framework abstracts these concerns away, letting you focus on the business workflow itself.

---

## 2. Your Best Options for Saga Frameworks in Go

Here are the top contenders in the Go ecosystem, ranging from lightweight libraries to full-blown workflow orchestration engines.

### Option 1: Temporal (The Workflow-as-Code Engine)

Temporal is more than just a saga framework; it's a durable execution engine. It allows you to write complex, long-running, and fault-tolerant applications as simple-looking Go code. The state, retries, and timers are all managed by the Temporal cluster.

> 💡 **How it works:** You define your saga as a "workflow" function. Each step in the saga is an "activity." The Temporal server guarantees that your workflow code will run to completion, even if the server or your workers restart.

-   **✅ Pros:**
    -   **Extreme Durability:** Workflows are fully durable. If a worker crashes, the workflow's state is preserved and it will resume on another worker exactly where it left off.
    -   **Built-in Sagas:** Compensation is a first-class citizen, easily implemented with `defer`.
    -   **Powerful Features:** Includes built-in retries with exponential backoff, timers, signals for interacting with running workflows, and child workflows.
    -   **Excellent Observability:** Provides a web UI to inspect the state, inputs, outputs, and full execution history of every single workflow. This is a massive advantage for debugging.
    -   **Testability:** Provides a testing framework to test your workflow logic in-process without needing a running Temporal cluster.
-   **❌ Cons:**
    -   **Operational Overhead:** Requires running a separate Temporal cluster (though it's easy to run with Docker Compose for development).
    -   **Learning Curve:** It's a new paradigm ("workflows-as-code") that the team needs to learn.
-   **🏆 Best for:** Teams that anticipate complex, multi-step distributed transactions and value extreme reliability and observability. It's the most powerful and robust solution on this list.

### Option 2: Go-Saga (The Lightweight Library)

This is a pure Go library that provides a straightforward implementation of the Saga orchestration pattern. It's much closer to what you've built manually but provides a cleaner, more declarative API.

> 💡 **How it works:** You define saga steps with `Execute` and `Compensate` functions and register them with an orchestrator. The library then runs the steps and handles the compensation logic on failure.

-   **✅ Pros:**
    -   **Simplicity:** Very easy to understand and integrate. It's a direct replacement for your current manual implementation.
    -   **No External Dependencies:** It's just a Go library; it doesn't require a separate server to run.
    -   **Minimal Disruption:** The concepts will be immediately familiar to your team.
-   **❌ Cons:**
    -   **Lacks Durability:** The saga's state is held in memory by the orchestrator. If the process crashes mid-saga, you rely on your own persistence and recovery logic (similar to your current situation).
    -   **Limited Observability:** Debugging relies on logs, just like your current setup.
    -   **Manual Retries:** You still need to implement retry logic within your activity functions.
-   **🏆 Best for:** Teams that want to clean up their existing saga code with a simple, declarative library without introducing new infrastructure.

### Option 3: Watermill (The Event-Driven Application Toolkit)

Watermill is a comprehensive Go library for building event-driven applications. It's not just a saga framework, but it has a Saga component that works within its message-passing paradigm.

> 💡 **How it works:** Watermill provides a universal Publisher/Subscriber interface over various message brokers (like NATS, Kafka, RabbitMQ). You can build sagas that react to and produce events.

-   **✅ Pros:**
    -   **Highly Flexible:** Integrates with many message brokers.
    -   **Full Event-Driven Support:** If you plan to build more event-driven features, Watermill provides a great foundation for your entire application.
    -   **Good Middleware:** Comes with built-in middleware for things like correlation, throttling, and poison pill queue handling.
-   **❌ Cons:**
    -   **More Conceptual Overhead:** You need to buy into the full Watermill ecosystem (Routers, Pub/Sub, etc.), not just the saga part.
    -   **State Management:** Like Go-Saga, you are largely responsible for persisting the saga state yourself.
-   **🏆 Best for:** Teams building a fully event-driven system from the ground up, where sagas are just one part of a larger messaging architecture.

---

## 3. How to Choose

| Feature         | Temporal                      | Go-Saga                      | Watermill                  |
| :-------------- | :---------------------------- | :--------------------------- | :------------------------- |
| **Complexity**  | High (but powerful)           | Low                          | Medium                     |
| **Scope**       | Full Workflow Engine          | Saga Library                 | Event-Driven Toolkit       |
| **Durability**  | Very High (out-of-the-box)    | Low (DIY)                    | Low (DIY)                  |
| **Observability**| Excellent (Web UI, history)   | Logging-based                | Logging-based              |
| **Ecosystem**   | Large, dedicated              | Small, focused               | Medium, messaging-focused  |
| **Best For**    | Complex, critical transactions| Simple, direct replacement   | Full event-driven apps     |

> **Recommendation:** Given the robust and resilient patterns already present in your codebase (Outbox, Retry, Circuit Breaker), **Temporal** is the logical next step. It aligns perfectly with your goal of building a fault-tolerant system and will provide immense value in terms of reliability and debugging.

---

## 4. Practical Implementation Steps (Refactoring Your Saga with Temporal)

Here’s how your existing `RegisterUser` saga would look if refactored to use Temporal.

### Step 1: Define the Workflow

The workflow is the orchestrator. It's just a Go function.

```go
// file: auth/registration_workflow.go
package auth

import (
	"time"
	"go.temporal.io/sdk/workflow"
	"go.temporal.io/sdk/log"
	sagapkg "go.temporal.io/sdk/saga"
)

// RegistrationWorkflow orchestrates the user registration process.
func RegistrationWorkflow(ctx workflow.Context, req *authv1.RegisterUserRequest) (*authv1.RegisterUserResponse, error) {
	logger := workflow.GetLogger(ctx)
	logger.Info("Registration workflow started.", "Email", req.Email)

	// Set a short timeout for each activity. Temporal will handle retries.
	ao := workflow.ActivityOptions{
		StartToCloseTimeout: 30 * time.Second,
	}
	ctx = workflow.WithActivityOptions(ctx, ao)

	// Use Temporal's built-in Saga support for compensation.
	saga := &sagapkg.Saga{
		ParallelCompensation: false, // Compensate sequentially
	}
	defer saga.Compensate(ctx) // This will run if the workflow returns an error

	var activities *RegistrationActivities // Activities are like your saga steps

	// --- Step 1: Create Ory Identity ---
	var userID string
	err := workflow.ExecuteActivity(ctx, activities.CreateOryIdentity, req).Get(ctx, &userID)
	if err != nil {
		logger.Error("Activity CreateOryIdentity failed.", "Error", err)
		return nil, err
	}
	saga.AddCompensation(activities.DeleteOryIdentity, userID)
	logger.Info("Ory identity created.", "UserID", userID)

	// --- Step 2: Create User Profile ---
	createProfileReq := &userv1.CreateUserRequest{
		UserId:    userID,
		Username:  req.Username,
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
	}
	err = workflow.ExecuteActivity(ctx, activities.CreateUserProfile, createProfileReq).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity CreateUserProfile failed.", "Error", err)
		return nil, err
	}
	saga.AddCompensation(activities.DeleteUserProfile, userID)
	logger.Info("User profile created.")

	// --- Step 3: Assign Default Role ---
	err = workflow.ExecuteActivity(ctx, activities.AssignDefaultRole, userID).Get(ctx, nil)
	if err != nil {
		logger.Error("Activity AssignDefaultRole failed.", "Error", err)
		return nil, err // Compensation for Ory and Profile will run automatically
	}
	// Note: Role assignment is idempotent, so compensation might not be strictly necessary,
	// but you could add one to remove the role.
	logger.Info("Default role assigned.")

	// --- Build Success Response ---
	var response *authv1.RegisterUserResponse
	err = workflow.ExecuteActivity(ctx, activities.BuildSuccessResponse, userID).Get(ctx, &response)
	if err != nil {
		logger.Error("Activity BuildSuccessResponse failed.", "Error", err)
		return nil, err
	}

	logger.Info("Registration workflow completed successfully.")
	return response, nil
}
```

### Step 2: Define the Activities

Activities are the functions that do the actual work. They are just normal Go functions that can interact with external services.

```go
// file: auth/registration_activities.go
package auth

import (
	"context"
	// ... other imports
)

// RegistrationActivities contains the saga steps.
type RegistrationActivities struct {
	Service *Service // Your auth service with all its dependencies
}

// CreateOryIdentity is the first step.
func (a *RegistrationActivities) CreateOryIdentity(ctx context.Context, req *authv1.RegisterUserRequest) (string, error) {
	// This logic is moved from your RegistrationSaga.createOryIdentity method
	// ... call a.Service.oryClient.CreateIdentity ...
	// return identity.Id, nil
}

// DeleteOryIdentity is the compensation for CreateOryIdentity.
func (a *RegistrationActivities) DeleteOryIdentity(ctx context.Context, userID string) error {
	// This logic is moved from your RegistrationSaga.deleteOryIdentity method
	// ... call a.Service.oryClient.DeleteIdentity ...
	// return nil
}

// CreateUserProfile is the second step.
func (a *RegistrationActivities) CreateUserProfile(ctx context.Context, req *userv1.CreateUserRequest) error {
	// ... call a.Service.userClient.CreateUser ...
	// return nil
}

// DeleteUserProfile is the compensation for CreateUserProfile.
func (a *RegistrationActivities) DeleteUserProfile(ctx context.Context, userID string) error {
	// ... call a.Service.userClient.DeleteUser ...
	// return nil
}

// AssignDefaultRole is the third step.
func (a *RegistrationActivities) AssignDefaultRole(ctx context.Context, userID string) error {
	// ... call a.Service.assignDefaultRoleToUser ...
	// return nil
}

// BuildSuccessResponse builds the final response.
func (a *RegistrationActivities) BuildSuccessResponse(ctx context.Context, userID string) (*authv1.RegisterUserResponse, error) {
	// ... logic from your saga.buildSuccessResponse method ...
}
```

### Step 3: Start the Workflow from your gRPC Handler

Your `RegisterUser` gRPC method becomes much simpler. It just starts the workflow.

```go
// file: auth/service.go (modified)
import "go.temporal.io/sdk/client"

// ... in your Service struct
// temporalClient client.Client

func (s *Service) RegisterUser(ctx context.Context, req *authv1.RegisterUserRequest) (*authv1.RegisterUserResponse, error) {
	// ... validation and idempotency checks ...

	options := client.StartWorkflowOptions{
		ID:        "registration-" + req.Email, // Business-meaningful workflow ID
		TaskQueue: "REGISTRATION_TASK_QUEUE",
	}

	we, err := s.temporalClient.ExecuteWorkflow(ctx, RegistrationWorkflow, req)
	if err != nil {
		s.logger.Error("Unable to start registration workflow", "Error", err)
		return nil, errors.NewAppErrorFromContext(ctx, errors.ErrorCodeInternal, "could not start registration")
	}

	// Wait for the workflow to complete.
	var result authv1.RegisterUserResponse
	err = we.Get(ctx, &result)
	if err != nil {
		s.logger.Error("Registration workflow failed", "Error", err)
		return nil, errors.NewAppErrorFromContext(ctx, errors.ErrorCodeInternal, "registration process failed")
	}

	return &result, nil
}
```

This refactoring completely removes the need for `saga_handler.go` and `saga/persistence.go`, as Temporal now handles all state, retries, and compensation orchestration.