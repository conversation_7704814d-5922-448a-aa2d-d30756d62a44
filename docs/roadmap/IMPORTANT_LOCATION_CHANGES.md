flutter_location_wakeup is a Flutter plugin designed to listen for significant location changes on the device. When the changes are detected, the foreground app wakes up, or stays awake from suspension. Use this library when you need location changes to keep the foreground app alive, such as in the case of a navigation apps, or place based interaction apps.

https://github.com/MelbourneDeveloper/flutter_location_wakeup

https://pub.dev/packages/flutter_location_wakeup
