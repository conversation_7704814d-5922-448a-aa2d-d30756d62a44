Integration Plan: FFmpeg in Go Media Microservice

# Objective
To integrate FFmpeg into a Go-based "media" microservice for handling video uploads, processing (transcoding, compression, thumbnails, audio extraction), and returning optimized assets. This enhances video handling in the social app, reducing storage costs and ensuring compatibility across devices.

# Phase 1: Prerequisites and Project Setup

### Why FFmpeg?
FFmpeg is an open-source multimedia framework that excels at processing video, audio, and images. In a social app, it helps with tasks like optimizing user-uploaded videos to reduce storage costs, create previews, and ensure smooth playback across devices.

### Goals of This Microservice
- Handle video uploads from the social app.
- Process videos (transcode, compress, thumbnail).
- Return processed assets (e.g., URLs or files).
- Scale as a microservice using Go's concurrency.

### Prerequisites
Before diving in, ensure you have the following setup:

- **Go Installed**: Version 1.18 or higher. Download from golang.org.
- **FFmpeg Installed**: This is crucial as ffmpeg-go wraps FFmpeg commands.

  On Ubuntu/Debian: `sudo apt update && sudo apt install ffmpeg`
  
  On macOS: `brew install ffmpeg`
  
  On Windows: Download from ffmpeg.org and add to PATH.
  
  Verify: Run `ffmpeg -version` in your terminal.

- **Docker (Optional)**: For containerizing the microservice.
- **Basic Knowledge**: Familiarity with Go, HTTP servers (e.g., Gin or net/http), and microservices.
- **Storage Solution**: Use S3, local filesystem, or similar for storing processed videos.

### Setting Up the Go Project
Create the Project Directory:

```bash
mkdir media-microservice && cd media-microservice
go mod init github.com/yourusername/media-microservice
```

Install Dependencies:

```bash
go get -u github.com/u2takey/ffmpeg-go
go get -u github.com/gin-gonic/gin
```

Project Structure:

```
media-microservice/
├── main.go               # Entry point
├── handlers/             # API handlers
│   └── video.go          # Video processing logic
├── utils/                # Helper functions (e.g., FFmpeg wrappers)
│   └── ffmpeg_utils.go   # FFmpeg-specific functions
├── go.mod
└── go.sum
```

# Phase 2: Installing and Configuring FFmpeg in Go

FFmpeg must be available in your system's PATH. The ffmpeg-go library provides a fluent API to build and run FFmpeg commands.

### Test FFmpeg Integration
In utils/ffmpeg_utils.go, add a simple test function:

```go
package utils

import (
    "fmt"
    "github.com/u2takey/ffmpeg-go"
)

func TestFFmpeg() error {
    err := ffmpeg_go.Input("test.mp4").Output("test_output.mp4").Run()
    if err != nil {
        return fmt.Errorf("FFmpeg test failed: %v", err)
    }
    return nil
}
```

Call this in main.go to verify.

Error Handling Tip: Always check for errors and log them. Use ErrorToStdOut() for debugging FFmpeg output.

# Phase 3: Implementing Video Processing Features

In your "media" microservice, expose endpoints like /upload-video to receive files, process them with FFmpeg, and respond with results.

### 1. Basic Video Transcoding
Transcode videos to a standard format (e.g., H.265 for compression).

In utils/ffmpeg_utils.go:

```go
func TranscodeVideo(inputPath, outputPath string) error {
    return ffmpeg_go.Input(inputPath).
        Output(outputPath, ffmpeg_go.KwArgs{"c:v": "libx265", "preset": "medium"}).
        OverWriteOutput().Run()
}
```

Why? Reduces file size while maintaining quality for social app storage.

Usage in Handler: Call this after uploading a video.

### 2. Generating Thumbnails
Extract a frame as a preview image.

In utils/ffmpeg_utils.go:

```go
import (
    "bytes"
    "fmt"
    "io"
    "os"
    "github.com/disintegration/imaging" // Install: go get github.com/disintegration/imaging
)

func GenerateThumbnail(inputPath string, frameNum int, outputPath string) error {
    buf := bytes.NewBuffer(nil)
    err := ffmpeg_go.Input(inputPath).
        Filter("select", ffmpeg_go.Args{fmt.Sprintf("gte(n,%d)", frameNum)}).
        Output("pipe:", ffmpeg_go.KwArgs{"vframes": 1, "format": "image2", "vcodec": "mjpeg"}).
        WithOutput(buf, os.Stdout).
        Run()
    if err != nil {
        return err
    }
    img, err := imaging.Decode(buf)
    if err != nil {
        return err
    }
    return imaging.Save(img, outputPath)
}
```

Emoji Tip: Use frame 5 for a mid-video thumbnail.

Social App Use: Display previews in feeds.

### 3. Video Compression
Compress by resizing and adjusting bitrate.

In utils/ffmpeg_utils.go:

```go
func CompressVideo(inputPath, outputPath string) error {
    return ffmpeg_go.Input(inputPath).
        Output(outputPath, ffmpeg_go.KwArgs{"vf": "scale=1280:720", "b:v": "1M"}). // 720p at 1Mbps
        OverWriteOutput().Run()
}
```

Benefits: Faster uploads/downloads in social apps.

### 4. Extracting Audio
Pull audio from video for separate processing.

In utils/ffmpeg_utils.go:

```go
func ExtractAudio(inputPath, outputPath string) error {
    return ffmpeg_go.Input(inputPath).
        Output(outputPath, ffmpeg_go.KwArgs{"vn": "", "acodec": "libmp3lame"}). // No video, MP3 audio
        OverWriteOutput().Run()
}
```

Use Case: For audio-only posts or analysis.

# Phase 4: API Handler and Main Implementation

### 5. API Handler Example
In handlers/video.go:

```go
package handlers

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "yourproject/utils" // Replace with actual import
)

func UploadAndProcessVideo(c *gin.Context) {
    file, err := c.FormFile("video")
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "File upload failed"})
        return
    }
    inputPath := "/tmp/" + file.Filename
    outputPath := "/tmp/processed_" + file.Filename
    thumbnailPath := "/tmp/thumb_" + file.Filename + ".jpg"

    if err := c.SaveUploadedFile(file, inputPath); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Save failed"})
        return
    }

    // Process
    if err := utils.TranscodeVideo(inputPath, outputPath); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Transcode failed"})
        return
    }
    if err := utils.GenerateThumbnail(inputPath, 5, thumbnailPath); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Thumbnail failed"})
        return
    }

    // Upload to storage (e.g., S3) and return URLs
    c.JSON(http.StatusOK, gin.H{"video_url": outputPath, "thumb_url": thumbnailPath})
}
```

In main.go:

```go
package main

import (
    "github.com/gin-gonic/gin"
    "yourproject/handlers"
)

func main() {
    r := gin.Default()
    r.POST("/upload-video", handlers.UploadAndProcessVideo)
    r.Run(":8080")
}
```

# Phase 5: Best Practices and Scaling

- **Concurrency**: Use Go's goroutines for parallel processing. Wrap FFmpeg calls in workers for multiple videos.
- **Error Handling**: Capture FFmpeg stderr with WithErrorOutput(). Log using logrus or similar.
- **Security**: Validate file types and sizes to prevent abuse. Sanitize inputs.
- **Dockerization**: Create a Dockerfile for deployment:

```dockerfile
FROM golang:1.21-alpine
RUN apk add --no-cache ffmpeg
WORKDIR /app
COPY . .
RUN go build -o main .
CMD ["./main"]
```

- **Monitoring**: Track processing time and errors with Prometheus.
- **Limitations**: FFmpeg is CPU-intensive; consider cloud services like AWS Lambda for bursts.
- **Testing**: Write unit tests for utils and integration tests for endpoints.

# Conclusion
This integration of FFmpeg into a Go media microservice provides a scalable solution for video processing in your social application, enabling efficient handling of uploads and optimizations for better performance and user experience.

# Checklist for a Perfect Integration
- [ ] Install Go (1.18+) and FFmpeg on the development environment.
- [ ] Verify FFmpeg installation with `ffmpeg -version`.
- [ ] Initialize the Go project and install dependencies (ffmpeg-go, gin).
- [ ] Set up project structure with main.go, handlers/, and utils/.
- [ ] Implement and test basic FFmpeg integration in utils/ffmpeg_utils.go.
- [ ] Add video transcoding functionality using H.265.
- [ ] Implement thumbnail generation with frame extraction.
- [ ] Add video compression with resizing and bitrate adjustment.
- [ ] Implement audio extraction from videos.
- [ ] Create API handlers for video upload and processing in handlers/video.go.
- [ ] Set up the main.go server with Gin routes.
- [ ] Apply best practices: concurrency with goroutines, error handling, security validations.
- [ ] Containerize with Dockerfile including FFmpeg.
- [ ] Set up monitoring with Prometheus for processing metrics.
- [ ] Write unit and integration tests for all features.
- [ ] Test end-to-end: upload a video, process it, and verify outputs.
