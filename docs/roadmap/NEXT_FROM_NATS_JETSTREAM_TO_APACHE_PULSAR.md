Migration Plan: from NATS JetStream to Apache Pulsar

# Objective
To migrate the microservices' eventing backbone from NATS JetStream to Apache Pulsar. This is a strategic architectural upgrade that unlocks superior scalability, data governance, and operational flexibility, particularly for handling high-volume data streams like Change Data Capture (CDC).

# Phase 1: Understanding the Benefits

### Why Your Architecture Should Embrace Apache Pulsar
While NATS JetStream is a capable and simple messaging system, Apache Pulsar was designed from the ground up to unify high-performance message queuing and log-based streaming (like Kafka) into a single platform. For a sophisticated system like Hopenbackend, which already uses advanced patterns like Sagas and the Transactional Outbox, Pulsar is a natural evolution.

The Key Advantages for Your Codebase:
- **Unified Messaging and Streaming (The Perfect CDC Solution)**: Pulsar seamlessly handles both traditional message queuing (like your inter-service events) and infinite log streaming. This makes it the ideal platform for your ScyllaDB CDC events. You can have a single topic for CDC data that can be consumed like a queue by some services and replayed like a stream by others, without needing separate systems.
- **Tiered Storage and Infinite Retention**: This is Pulsar's killer feature for CDC and analytics. Pulsar can automatically offload older data segments from its high-performance storage (BookKeeper) to cheaper, long-term object storage. Your architecture already uses MinIO, making this a perfect fit. You can store the entire history of your CDC data in MinIO cost-effectively, allowing you to replay events from the beginning of time to rebuild analytics views or recover from errors.
- **Built-in Schema Registry**: Your codebase defines clear event schemas in pkg/events/schemas.go. Pulsar's built-in Schema Registry enforces this contract at the broker level. It prevents services from publishing malformed data, ensuring data integrity across your entire system and eliminating a common source of bugs in distributed architectures.
- **Superior Multi-Tenancy and Isolation**: Pulsar was built for multi-tenancy. You can create separate tenants and namespaces (e.g., hopen/production, hopen/staging) with their own policies for isolation, storage quotas, and access control. This is invaluable as your platform grows.
- **Geo-Replication**: For a social application, providing low-latency and high-availability across geographic regions is critical. Pulsar has built-in, configurable geo-replication, making it much easier to build a globally distributed and resilient backend.
- **Powerful Retry and Dead-Letter Queue (DLQ) Policies**: Your codebase has a sophisticated custom DLQ implementation. Pulsar provides this functionality out-of-the-box with more power. You can configure automatic retries with backoff and a dead-letter topic directly on the subscription, simplifying your consumer logic.

In short, migrating to Pulsar is not just a replacement; it's an upgrade that aligns perfectly with your architecture's focus on resilience, data integrity, and scalability.

# Phase 2: Preparing for Migration
A smooth migration requires a clear plan and the right setup.

### Audit Your Current NATS Usage
First, identify every point of interaction with NATS in your codebase. Based on the review, these are the primary use cases:
- General Event Publishing: Services use NATSPublisher to publish domain events (e.g., user.created, bubble.expired).
- Saga Cleanup Events: The auth service's SagaCleanupHandler subscribes to saga.cleanup.required events.
- Event Subscriptions: Various services subscribe to events to trigger actions (e.g., search service indexing data, notification service sending alerts).
- Service Discovery: The pkg/discovery package uses NATS as a transport for service registration and discovery.

Important Note: Pulsar is not designed for the ephemeral, request-reply patterns used in service discovery. This part of the NATS functionality will need to be replaced with a dedicated tool like Consul, etcd, or native Kubernetes services. This guide will focus on migrating the eventing and messaging patterns.

### Set Up a Local Pulsar Environment
Use Docker Compose to run a standalone Pulsar cluster for local development. Create a docker-compose.pulsar.yml file:

```yaml
# docker-compose.pulsar.yml
version: '3.8'
services:
  pulsar:
    image: apachepulsar/pulsar:3.2.0
    container_name: pulsar
    ports:
      - "6650:6650" # Pulsar binary protocol
      - "8080:8080" # Pulsar HTTP admin API
    command: >
      bash -c "bin/apply-config-from-env.py conf/standalone.conf &&
               bin/pulsar standalone"
    environment:
      - PULSAR_MEM=" -Xms512m -Xmx512m -XX:MaxDirectMemorySize=1g"
```

Run it with: `docker-compose -f docker-compose.pulsar.yml up -d`

# Phase 3: The Migration Process
The goal is a phased migration with zero downtime, leveraging your existing EventPublisher and EventSubscriber interfaces.

### Step 1: Add the Pulsar Go Client Dependency
Add the official Apache Pulsar client for Go to your go.mod file:

```bash
go get github.com/apache/pulsar-client-go/pulsar
```

### Step 2: Create a Pulsar Publisher
Create a new file pkg/events/pulsar_publisher.go. This new publisher will implement the same EventPublisher interface as your NATSPublisher, allowing for a seamless swap.

```go
// pkg/events/pulsar_publisher.go
package events

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/apache/pulsar-client-go/pulsar"
	"go.uber.org/zap"
)

type PulsarPublisher struct {
	client pulsar.Client
	logger *zap.Logger
}

func NewPulsarPublisher(pulsarURL string, logger *zap.Logger) (*PulsarPublisher, error) {
	client, err := pulsar.NewClient(pulsar.ClientOptions{
		URL:               pulsarURL,
		ConnectionTimeout: 30 * time.Second,
	})
	if err != nil {
		return nil, fmt.Errorf("could not instantiate Pulsar client: %w", err)
	}
	return &PulsarPublisher{client: client, logger: logger}, nil
}

// PublishEvent publishes an event to a Pulsar topic.
func (p *PulsarPublisher) PublishEvent(eventType EventType, event interface{}) error {
	topic := EventSubject(eventType) // Reusing your existing subject/topic naming
	return p.publish(topic, event)
}

// PublishUserEvent publishes a user-specific event.
func (p *PulsarPublisher) PublishUserEvent(eventType EventType, userID string, event interface{}) error {
	topic := UserEventSubject(eventType, userID)
	return p.publish(topic, event)
}

func (p *PulsarPublisher) publish(topic string, event interface{}) error {
	producer, err := p.client.CreateProducer(pulsar.ProducerOptions{
		Topic: topic,
	})
	if err != nil {
		return fmt.Errorf("failed to create Pulsar producer: %w", err)
	}
	defer producer.Close()

	payload, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	_, err = producer.Send(context.Background(), &pulsar.ProducerMessage{
		Payload: payload,
	})
	if err != nil {
		p.logger.Error("Failed to publish Pulsar message",
			zap.String("topic", topic),
			zap.Error(err),
		)
		return fmt.Errorf("failed to publish message: %w", err)
	}

	p.logger.Info("Pulsar message published", zap.String("topic", topic))
	return nil
}

func (p *PulsarPublisher) Close() {
	p.client.Close()
}
```

### Step 3: Create a Pulsar Subscriber
Create a PulsarSubscriber that handles subscriptions, message acknowledgements (Ack), and failures (Nack).

```go
// pkg/events/pulsar_subscriber.go
package events

import (
	"context"
	"github.com/apache/pulsar-client-go/pulsar"
	"go.uber.org/zap"
)

type PulsarSubscriber struct {
	client pulsar.Client
	logger *zap.Logger
}

// SubscribeToEvent subscribes to a Pulsar topic.
func (s *PulsarSubscriber) SubscribeToEvent(eventType EventType, handler func([]byte) error) error {
	topic := EventSubject(eventType)
	
	consumer, err := s.client.Subscribe(pulsar.ConsumerOptions{
		Topic:            topic,
		SubscriptionName: "my-subscription", // This should be unique per consumer group
		Type:             pulsar.Shared,
	})
	if err != nil {
		return err
	}

	go func() {
		defer consumer.Close()
		for {
			msg, err := consumer.Receive(context.Background())
			if err != nil {
				s.logger.Error("Failed to receive Pulsar message", zap.Error(err))
				continue
			}

			if err := handler(msg.Payload()); err != nil {
				// Message processing failed, negatively acknowledge it.
				// Pulsar will redeliver it later based on the configured policy.
				consumer.Nack(msg)
			} else {
				// Message processed successfully.
				consumer.Ack(msg)
			}
		}
	}()
	
	return nil
}
```

### Step 4: Integrate Pulsar with the Transactional Outbox
This is the most critical step for your CDC and real-time messaging. Your outbox.Processor needs to publish to Pulsar instead of NATS.

```go
// microservices/realtime/main.go (or wherever the processor is initialized)

// ...
import "hopenbackend/pkg/outbox"
import "hopenbackend/pkg/events"

// ...

// In your service initialization:
pulsarURL := "pulsar://localhost:6650"
pulsarPublisher, err := events.NewPulsarPublisher(pulsarURL, logger)
if err != nil {
    logger.Fatal("Could not create Pulsar publisher", zap.Error(err))
}

// The outbox.Processor takes an EventPublisher interface, so we can pass our new Pulsar publisher.
outboxProcessor := outbox.NewProcessor(
    scyllaOutboxRepo, // Your existing ScyllaDB outbox repository
    pulsarPublisher,  // The new Pulsar publisher!
    logger,
    outbox.DefaultProcessorConfig(),
)

// Start the processor
go outboxProcessor.Start(context.Background())
```

### Step 5: Migrate a Consumer Service (Example: notification)
Now, migrate a consumer. The notification service is a good candidate.

Before (NATS):

```go
// microservices/notification/service.go
// ...
dlqMiddleware.SubscribeWithDLQ(
    s.natsConn, 
    "events.contact.request_sent", 
    s.handleContactEvent, 
    "handleContactEvent",
)
```

After (Pulsar):

```go
// microservices/notification/service.go

// 1. Initialize a Pulsar client in the service dependencies.
pulsarClient, err := pulsar.NewClient(pulsar.ClientOptions{ URL: "pulsar://localhost:6650" })
// ...

// 2. Create a Pulsar subscriber.
pulsarSubscriber := events.NewPulsarSubscriber(pulsarClient, logger)

// 3. Subscribe to the topic.
pulsarSubscriber.SubscribeToEvent(
    events.EventTypeContactRequest, 
    func(data []byte) error {
        // The core handler logic remains the same.
        // The wrapper now handles Pulsar's Ack/Nack.
        var event events.ContactEvent
        if err := json.Unmarshal(data, &event); err != nil {
            return err // Returning an error will trigger a Nack
        }
        return s.processContactNotification(&event) // Your existing logic
    },
)
```

### Step 6: A Phased and Safe Rollout Strategy
Do not migrate everything at once. Follow this zero-downtime strategy:
- **Dual Publishing**: Modify your EventPublisher implementations (or create a new composite publisher) to publish critical events to both NATS and Pulsar simultaneously. This allows you to introduce Pulsar without affecting the existing NATS-based consumers.
- **Shadowing Consumers**: Deploy your new Pulsar-based consumer services. Have them consume and process messages from Pulsar but do not let them perform any side effects (e.g., disable database writes or external API calls). Log their actions extensively. This allows you to verify that the new consumers behave identically to the old ones.
- **Cutover**: Once you are confident in the new Pulsar consumers, perform a rolling update to switch them out of "shadow mode" and enable their side effects. At the same time, you can begin to disable the old NATS-based consumers.
- **Decommission NATS**: After the new system has been running stably on Pulsar for a sufficient period, modify your publishers to stop sending events to NATS. You can then decommission the NATS-related code and infrastructure.

# Phase 4: Advanced Topics & Best Practices

### Using Tiered Storage for CDC
To enable tiered storage for your CDC topic (e.g., persistent://public/default/scylla-cdc-log), you need to configure a namespace policy using the pulsar-admin CLI.

```bash
# First, configure an offloader (e.g., for MinIO/S3) in pulsar's broker.conf
# managedLedgerOffloadDriver=s3
# s3ManagedLedgerOffloadRegion=us-east-1
# s3ManagedLedgerOffloadBucket=my-pulsar-bucket
# s3ManagedLedgerOffloadServiceEndpoint=http://minio:9000

# Then, set the policy for your namespace
pulsar-admin namespaces set-offload-policies public/default \
  --driver s3 \
  --bucket my-pulsar-bucket \
  --endpoint http://minio:9000 \
  --offload-after-threshold-in-bytes 10M # Offload after 10MB
```

Pulsar will now automatically move older segments of your CDC log to MinIO, giving you infinite, cost-effective retention.

### Enforcing Schemas with the Schema Registry
To enforce your Go event structs, you can define a schema when creating a producer.

```go
// Define your Go struct for the event
type UserCreatedEvent struct {
    UserID    string `json:"user_id"`
    Email     string `json:"email"`
    Timestamp int64  `json:"timestamp"`
}

// Create a JSON schema from the struct
jsonSchema := pulsar.NewJSONSchema(reflect.TypeOf(UserCreatedEvent{}), nil)

// Create a producer with the schema
producer, err := client.CreateProducer(pulsar.ProducerOptions{
    Topic:  "user-events",
    Schema: jsonSchema,
})

// Pulsar will now reject any message that doesn't conform to the UserCreatedEvent struct.
```

### Replacing NATS-based Service Discovery
As mentioned, Pulsar is not a good fit for service discovery. The recommended approach is to replace pkg/discovery with a solution built for this purpose:
- **Kubernetes**: If you are deploying on Kubernetes, use its native Service discovery. This is the simplest and most robust option.
- **Consul/etcd**: For other environments, a dedicated service discovery tool like Consul or etcd is the industry standard. Your services would register themselves with Consul on startup and query it to find the addresses of other services.

# Conclusion
Migrating from NATS JetStream to Apache Pulsar is a significant architectural enhancement for the Hopenbackend platform. It directly addresses the challenges of managing high-volume CDC streams by providing a unified, scalable, and durable eventing backbone. With features like tiered storage, a built-in schema registry, and powerful multi-tenancy, Pulsar provides the foundation needed to support your application's growth in complexity, scale, and global reach. By following a phased migration strategy, you can perform this upgrade with confidence and zero downtime.

# Checklist for a Successful Migration
- [ ] Audit current NATS usage and identify all interaction points.
- [ ] Set up local Pulsar environment using Docker Compose.
- [ ] Add Pulsar Go client dependency to go.mod.
- [ ] Implement PulsarPublisher implementing EventPublisher interface.
- [ ] Implement PulsarSubscriber for event subscriptions.
- [ ] Integrate Pulsar with Transactional Outbox processor.
- [ ] Migrate at least one consumer service (e.g., notification) to Pulsar.
- [ ] Implement dual publishing for zero-downtime rollout.
- [ ] Deploy shadow consumers to verify behavior.
- [ ] Perform cutover and enable side effects in new consumers.
- [ ] Decommission NATS publishers and infrastructure.
- [ ] Configure tiered storage for CDC topics using offload policies.
- [ ] Set up schema registry enforcement for event schemas.
- [ ] Replace NATS-based service discovery with Kubernetes services or Consul/etcd.
