# 🚨 Urgent Edge Cases & Solutions 🚨

This document outlines critical edge cases and their required handling to ensure system resilience and a smooth user experience.

---

## 🔄 1. Concurrent and Conflicting Actions

### 💥 Simultaneous Bubble Requests

*   **🤔 Problem:** A user outside a bubble receives multiple `Bubble Start` and `Bubble Invite` requests simultaneously. How does the system prioritize them?
*   **💡 Solution:**
    *   Display a unified dialog listing all pending requests (contact, bubble start, bubble join).
    *   The user can navigate and view each request from this list.
    *   Accepting any **contact request** automatically declines all other pending contact requests.
    *   Accepting any **bubble request** (start or join) automatically declines all other pending bubble requests.

## 🔄 2. Apple signup edge cases

Profile Data: ✅ First name, last name, email (Apple doesn't provide profile pictures/birthday)
Username Logic: ✅ firstnamelastname → firstnamelastname1 → firstnamelastname2 → etc.
Conflict Resolution: ✅ Automatic fallback with numbered suffixes
Apple Limitations: ⚠️ No profile picture or birthday (Apple policy)
