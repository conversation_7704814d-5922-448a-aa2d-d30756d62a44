Integration Plan: GraphQL Federation

# Objective
To introduce a GraphQL Gateway using Federation to provide a unified API for clients while maintaining the existing gRPC-based microservices. This enhances flexibility for client applications without disrupting internal service boundaries.

# Phase 1: Architectural Strategy and Setup

### GraphQL Federation Overview
GraphQL Federation allows you to break up your GraphQL schema into smaller, manageable pieces called subgraphs. Each of your microservices will be responsible for its part of the graph. A central GraphQL Gateway then automatically composes these subgraphs into a single, unified schema for the client.

The best approach is to introduce a GraphQL Gateway (also known as a Backend-for-Frontend or BFF). This is a new, thin service whose sole responsibility is to process GraphQL queries, call the appropriate internal gRPC services, and stitch the results together.

The modern, scalable, and "perfect" way to implement this is with GraphQL Federation.

# Phase 2: Implement the GraphQL Gateway

### Step 1: Create a New graphql-gateway Service
This will be a new service in your cmd and microservices directories.

It will be a lightweight Node.js (using Apollo Server) or Go server. Apollo is the industry standard and has the most mature tooling, but Go options exist.

This service will be the only service that clients directly communicate with for data.

# Phase 3: Implement Subgraphs in Microservices

### Step 2: Implement a GraphQL Subgraph in Each Microservice
Each of your existing microservices (user, bubble, etc.) will now also expose a lightweight GraphQL server on a private port or path.

This server defines only the part of the schema relevant to its domain.

user-service will define type User, Query { user(id: ID!): User }, etc.

bubble-service will define type Bubble, Query { bubble(id: ID!): Bubble }, etc.

Crucially, services can extend types from other services. For example, the bubble-service can add a bubbles: [Bubble] field to the User type, even though User is defined in the user-service.

### Step 3: The GraphQL Gateway Composes the Subgraphs
The graphql-gateway is configured with the URLs of all your subgraph endpoints (e.g., http://user:4001/graphql, http://bubble:4002/graphql).

On startup, it fetches the schemas from each subgraph and intelligently composes them into one master schema. This process is automatic.

# Phase 4: Implement Resolvers and Optimizations

### Step 4: Implement Resolvers to Call gRPC Services
The "magic" happens in the resolvers of your subgraphs. A resolver is the function that fetches the data for a field.

Your resolvers will simply be gRPC clients.

The resolver for Query.user(id: ...) in the user-service's subgraph will call its own user.UserService.GetUser gRPC method.

The resolver for User.bubbles in the bubble-service's subgraph will receive a User object (with just the id), and it will call the bubble.BubbleService.GetUserBubbles gRPC method with that user ID.

### Step 5: Solve the N+1 Problem with Dataloader
A common pitfall in GraphQL is the "N+1 query problem," where fetching a list of items and a sub-field for each results in N+1 database/gRPC calls.

The solution is the Dataloader pattern. You will implement this within your GraphQL resolvers.

How it works: Dataloader collects all the IDs from a single GraphQL request, batches them into a single gRPC call (e.g., GetUsersByIDs), and then redistributes the results to the correct resolvers. Your user service already has a GetUsersByIDs method, which is perfect for this.

# Phase 5: Integrate Authentication, Authorization, and Gateway Configuration

### Step 6: Integrate Authentication and Authorization
Authentication: Your current setup with Kong/Oathkeeper is perfect. The client authenticates, and Oathkeeper forwards a JWT containing the user_id in a header (X-User-ID). This header is passed to the graphql-gateway, which then propagates it in the metadata of its downstream gRPC calls.

Authorization: Your resolvers are the perfect place to perform authorization checks. Before fetching data, a resolver can use the ketoClient to check if the user (from the gRPC context) has permission to access the requested resource.

### Step 7: Update Kong API Gateway Configuration
Your kong.yaml will be simplified. You will add a new service for graphql-gateway.

You will have a primary route: all traffic to /graphql is routed to the graphql-gateway service.

The individual gRPC routes for each microservice can be made internal/private, as only the GraphQL Gateway needs to access them.

# Phase 6: Architectural Visualization

### Architectural Diagram
```
+--------+      +-------------+      +------------------+      +---------------------+      +----------------+
| Client |----->| Kong Gateway|----->| GraphQL Gateway  |----->| gRPC Client         |----->| Microservice A |
+--------+      | (Oathkeeper)|      | (Apollo Server)  |      | (in resolver)       |      +----------------+
              +-------------+      +------------------+      |                     |
                                           |                 |                     |      +----------------+
                                           |                 +---------------------+----->| Microservice B |
                                           |                                              +----------------+
                                           |
                                           |  (Composes subgraphs from)
                                           |
                               +---------------------------+
                               | Microservice Subgraphs    |
                               | (User, Bubble, etc.)      |
                               +---------------------------+
```

# Conclusion
By adopting this federated GraphQL architecture, you gain immense flexibility for your client applications while maintaining the high performance and clear boundaries of your internal gRPC-based microservices. This is the "perfect" modern approach for a system of your complexity and quality.

# Checklist for a Perfect Integration
- [ ] Create the graphql-gateway service using Apollo Server or equivalent.
- [ ] Implement GraphQL subgraphs in each microservice (user, bubble, etc.).
- [ ] Configure the GraphQL Gateway to compose subgraphs from microservice endpoints.
- [ ] Implement resolvers in subgraphs to call corresponding gRPC methods.
- [ ] Integrate Dataloader pattern in resolvers to solve N+1 query problem.
- [ ] Set up authentication propagation from Kong to GraphQL Gateway and gRPC calls.
- [ ] Implement authorization checks using ketoClient in resolvers.
- [ ] Update Kong configuration to route /graphql to the gateway and secure internal routes.
- [ ] Test the full federation setup with sample queries spanning multiple services.
- [ ] Monitor and optimize resolver performance, especially for batched calls.
