# M2M Security Implementation Plan: Linkerd & Ory

This document outlines a complete, best-practice implementation plan to secure your backend's service-to-service (M2M) communication using Linkerd for transport security and the Ory stack for application-level authentication and authorization.

## High-Level Goal: Defense in Depth

Our goal is to achieve two layers of M2M security, adhering to the principle of **Zero Trust**.

---

### Layer 1: Transport Security (Authentication & Encryption)

-   **Technology:** Linkerd
-   **Mechanism:** Mutual TLS (mTLS)
-   **What it answers:** "Can I trust that the service calling me is actually the service it claims to be, and is our communication private?"
-   **How it works:** Linkerd's sidecar proxies automatically encrypt all traffic between your services and handle cryptographic workload identity verification using certificates.

### Layer 2: Application Security (Authorization)

-   **Technology:** <PERSON>y Hydra (Token Issuer) & <PERSON><PERSON> (Permission Server)
-   **Mechanism:** OAuth 2.0 Client Credentials and JWT-based authorization.
-   **What it answers:** "Now that I trust who is calling me, what are they *authorized* to do?"
-   **How it works:** The calling service obtains a short-lived JSON Web Token (JWT) from Hydra. The receiving service validates this token and checks with <PERSON><PERSON> if the action is permitted based on predefined policies.

---

## Prerequisites

Before you begin, ensure you have:

-   A running Kubernetes cluster.
-   `kubectl` configured to interact with your cluster.
-   Helm installed for managing Kubernetes packages.
-   The Linkerd CLI installed.
-   The Ory CLI installed.

---

## Phase 1: Implement Transport Security with Linkerd (mTLS)

This phase secures the network "pipes" between your services.

### Step 1.1: Install Linkerd onto your Cluster

First, install the Linkerd control plane into its own namespace.

```bash
# Check if your cluster is ready for Linkerd
linkerd check --pre

# Install the Linkerd control plane
linkerd install | kubectl apply -f -

# Verify the installation
linkerd check
```

### Step 1.2: "Mesh" Your Microservices

To enable Linkerd's features, inject its proxy sidecar into each of your service's pods by annotating the Kubernetes namespace.

Assuming your services are in the `hopen-backend` namespace:

```bash
kubectl annotate namespace hopen-backend linkerd.io/inject=enabled
```

Now, you must restart your deployments for the annotation to take effect and the sidecars to be injected.

```bash
# Rollout restart for all your deployments (auth, user, bubble, etc.)
kubectl rollout restart deployment -n hopen-backend
```

### Step 1.3: Enforce mTLS Globally

By default, Linkerd uses mTLS when possible but allows plaintext traffic. We will enforce mTLS for all communication between meshed pods.

Create a file named `enforce-mtls.yaml`:

```yaml
apiVersion: policy.linkerd.io/v1beta1
kind: Server
metadata:
  name: all-grpc-ports
  namespace: hopen-backend
spec:
  podSelector: {}
  port: grpc
---
apiVersion: policy.linkerd.io/v1alpha1
kind: AuthorizationPolicy
metadata:
  name: allow-meshed-traffic
  namespace: hopen-backend
spec:
  serverRef:
    name: all-grpc-ports
  requiredAuthenticationRefs:
    - name: meshed-clients
      kind: MeshTLSAuthentication
```

Apply this policy:

```bash
kubectl apply -f enforce-mtls.yaml
```

This policy tells all services in the `hopen-backend` namespace to only accept traffic on their `grpc` port if it is coming from another meshed pod and is secured with mTLS.

### Step 1.4: Verify mTLS is Active

Use the Linkerd dashboard or CLI to confirm that mTLS is active for your services.

```bash
# Install the Linkerd visualization extension
linkerd viz install | kubectl apply -f -

# Check traffic between two of your services
linkerd viz routes -n hopen-backend deploy/auth --to deploy/user
# Look for a 'tls' column in the output, it should show 'true'
```

> **Checkpoint:** All traffic between your services is now encrypted and mutually authenticated at the transport layer. You have a secure foundation.

---

## Phase 2: Implement Application-Level Authorization with Ory

This phase secures the application logic itself, ensuring services can only perform authorized actions.

### Step 2.1: Deploy and Configure Ory Hydra & Keto

Use Helm to deploy Ory Hydra and Keto to your cluster.

```bash
# Add the Ory Helm repository
helm repo add ory https://k8s.ory.sh/helm/charts
helm repo update

# Install Ory Hydra
helm install -n ory hydra ory/hydra -f your-hydra-values.yaml

# Install Ory Keto
helm install -n ory keto ory/keto -f your-keto-values.yaml
```

You will need to create `values.yaml` files to configure items like database connections and secrets.

### Step 2.2: Create M2M OAuth2 Clients in Hydra

For each microservice, create a client that it will use to authenticate itself to Hydra.

```bash
# Example for your 'auth' service
ory create oauth2-client \
  --project <your-ory-project-id> \
  --grant-type client_credentials \
  --response-type token \
  --name "Hopen Auth Service" \
  --audience "hopen/user-service,hopen/bubble-service" # List services it's allowed to call

# Securely save the returned client_id and client_secret in a Kubernetes secret
# for the auth-service to use. Repeat for all other microservices.
```

### Step 2.3: Define Authorization Policies in Keto

Define who can do what. Create a file `keto-policies.json` with your rules.

```json
[
  {
    "effect": "allow",
    "subjects": ["hopen:auth-service"],
    "actions": ["create"],
    "resources": ["hopen:user-service:users"]
  },
  {
    "effect": "allow",
    "subjects": ["hopen:bubble-service"],
    "actions": ["get-batch"],
    "resources": ["hopen:user-service:users"]
  },
  {
    "effect": "deny",
    "subjects": ["hopen:realtime-service"],
    "actions": ["delete"],
    "resources": ["hopen:user-service:users"]
  }
]
```

Import these policies into Keto:

```bash
ory import policies --project <your-ory-project-id> keto-policies.json
```

### Step 2.4: Create a Reusable Token Source in Go

Your services need a way to get M2M tokens from Hydra. Create a new component in `pkg/auth/tokensource.go`.

```go
package auth

import (
    "context"
    "golang.org/x/oauth2"
    "golang.org/x/oauth2/clientcredentials"
)

// TokenSource provides M2M access tokens.
type TokenSource interface {
    Token(ctx context.Context) (string, error)
}

type hydraTokenSource struct {
    tokenSource oauth2.TokenSource
}

// NewHydraTokenSource creates a token source that fetches M2M tokens from Ory Hydra.
func NewHydraTokenSource(ctx context.Context, tokenURL, clientID, clientSecret string, scopes []string) TokenSource {
    conf := &clientcredentials.Config{
        ClientID:     clientID,
        ClientSecret: clientSecret,
        TokenURL:     tokenURL,
        Scopes:       scopes,
    }
    return &hydraTokenSource{
        tokenSource: conf.TokenSource(ctx),
    }
}

func (s *hydraTokenSource) Token(ctx context.Context) (string, error) {
    token, err := s.tokenSource.Token()
    if err != nil {
        return "", err
    }
    return token.AccessToken, nil
}
```

### Step 2.5: Inject Tokens with a Client Interceptor

Update `pkg/service/grpc_client.go` to use an interceptor that attaches the token.

```go
// In CreateGenericClient function...

// Get the token source for the calling service
// (This would be initialized once per service runner)
// tokenSource := auth.NewHydraTokenSource(...)

dialOptions := []grpc.DialOption{
    // ... other options
    grpc.WithUnaryInterceptor(interceptor.M2MClientInterceptor(tokenSource)),
}

// conn, err := grpc.DialContext(...)
```

### Step 2.6: Validate Tokens with a Server Interceptor

Modify your existing `auth_interceptor.go` to validate the M2M token and check permissions with Keto.

```go
// In pkg/interceptor/auth_interceptor.go

// Add keto client to your AuthInterceptor struct
type AuthInterceptor struct {
    // ... existing fields
    ketoClient *keto.APIClient
}

// In UnaryInterceptor function...
func (ai *AuthInterceptor) UnaryInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
    // ...

    // Validate the Bearer token (JWT)
    // You would use a JWT library to parse and validate the signature against Hydra's public keys.
    token, err := jwt.Parse(tokenString, ...)
    if err != nil {
        return nil, status.Error(codes.Unauthenticated, "invalid M2M token")
    }
    claims := token.Claims.(jwt.MapClaims)
    subject := claims["sub"].(string)

    // AUTHORIZATION CHECK with Ory Keto
    resource := fmt.Sprintf("hopen:%s:rpc:%s", ai.config.App.Name, info.FullMethod)
    action := "call" // Or derive a more granular action from the method name

    checkReq := ai.ketoClient.PermissionAPI.CheckPermission(ctx).
        Namespace("hopen").
        Object(resource).
        Relation(action).
        SubjectId(subject)
    
    _, resp, err := checkReq.Execute()
    if err != nil || !resp.Allowed {
        return nil, status.Errorf(codes.PermissionDenied, "service '%s' is not authorized to call this method", subject)
    }

    // Add the authenticated service identity to the context
    ctx = auth.WithServiceIdentity(ctx, subject)

    return handler(ctx, req)
}
```

---

## Final Workflow Summary

1.  **Transport:** `auth-service` calls `user-service`. The Linkerd sidecars on each pod establish a secure mTLS connection, encrypting the traffic and verifying each other's workload identity.
2.  **Authentication:** The `auth-service` client interceptor attaches a JWT it previously fetched from Ory Hydra.
3.  **Authorization:** The `user-service` server interceptor receives the request.
    *   It validates the JWT's signature and expiry.
    *   It queries Ory Keto to check if the JWT's subject (e.g., `hopen:auth-service`) is allowed to perform the `call` action on the requested gRPC method resource.
    *   If Keto returns `allow`, the request proceeds to the business logic. Otherwise, it is rejected with a `PermissionDenied` error.

By implementing this complete plan, you will have a robust, secure, and standards-compliant M2M security architecture that adheres to the highest industry best practices.
