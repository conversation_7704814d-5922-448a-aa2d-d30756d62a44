# Deployment Secrets Configuration

This document outlines all the secrets required for the Hopen Backend CI/CD pipeline and production deployment.

## Required GitHub Secrets

### 🔐 Production Deployment Secrets

| Secret Name | Description | Required For | Example/Format |
|-------------|-------------|--------------|----------------|
| `SLACK_WEBHOOK` | Slack webhook URL for deployment notifications | Production CD | `https://hooks.slack.com/services/...` |
| `PRODUCTION_SERVER_SSH_KEY` | SSH private key for production server access | Self-hosted runner | RSA private key |
| `STAGING_SERVER_SSH_KEY` | SSH private key for staging server access | Self-hosted runner | RSA private key |

### 🔐 Container Registry Secrets

| Secret Name | Description | Required For | Notes |
|-------------|-------------|--------------|-------|
| `GITHUB_TOKEN` | GitHub token for container registry | CI/CD | Auto-provided by GitH<PERSON> |

### 🔐 Application Secrets (Environment Variables)

These secrets should be configured on your production servers:

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `POSTGRESQL_PASSWORD` | PostgreSQL database password | `secure_db_password_123` |
| `VALKEY_PASSWORD` | Valkey/Redis password | `secure_redis_password_456` |
| `JWT_SECRET` | JWT signing secret | `your-super-secret-jwt-key` |
| `JWT_REFRESH_SECRET` | JWT refresh token secret | `your-refresh-secret-key` |
| `MINIO_ACCESS_KEY` | MinIO S3 access key | `minioaccesskey123` |
| `MINIO_SECRET_KEY` | MinIO S3 secret key | `miniosecretkey456` |
| `MQTT_PASSWORD` | MQTT broker password | `mqtt_secure_password` |

## Setting Up Secrets

### 1. GitHub Repository Secrets

1. Go to your repository on GitHub
2. Navigate to **Settings** > **Secrets and variables** > **Actions**
3. Click **New repository secret**
4. Add each required secret from the tables above

### 2. Production Server Environment Variables

Create a `.env` file on your production server at `/opt/hopen-backend/.env`:

```bash
# Database Configuration
POSTGRESQL_PASSWORD=your_secure_db_password
POSTGRESQL_HOST=localhost
POSTGRESQL_PORT=5432
POSTGRESQL_DB=hopen
POSTGRESQL_USER=hopen

# Redis/Valkey Configuration
VALKEY_PASSWORD=your_secure_redis_password
VALKEY_HOST=localhost
VALKEY_PORT=6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
JWT_REFRESH_SECRET=your-refresh-secret-key-minimum-32-characters
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# MinIO S3 Configuration
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key_minimum_8_chars
MINIO_ENDPOINT=localhost:9000
MINIO_BUCKET=hopen-media

# MQTT Configuration
MQTT_HOST=localhost
MQTT_PORT=1883
MQTT_USERNAME=hopen
MQTT_PASSWORD=your_secure_mqtt_password

# External Services
OPENSEARCH_URL=http://localhost:9200
NATS_URL=nats://localhost:4222

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring
PROMETHEUS_PORT=9090
METRICS_ENABLED=true
```

### 3. Kubernetes Secrets (if using K8s)

Create Kubernetes secrets for production deployment:

```bash
# Create namespace
kubectl create namespace hopen

# Create database secrets
kubectl create secret generic hopenbackend-secrets \
  --from-literal=postgresql-password='your_secure_db_password' \
  --from-literal=valkey-password='your_secure_redis_password' \
  --from-literal=jwt-secret='your-super-secret-jwt-key' \
  --from-literal=jwt-refresh-secret='your-refresh-secret-key' \
  --from-literal=minio-access-key='your_minio_access_key' \
  --from-literal=minio-secret-key='your_minio_secret_key' \
  --from-literal=mqtt-password='your_secure_mqtt_password' \
  --namespace hopen

# Create Firebase credentials secret (if using Firebase)
kubectl create secret generic hopenbackend-firebase-credentials \
  --from-file=serviceAccount.json=/path/to/firebase-service-account.json \
  --namespace hopen
```

## Secret Generation Guidelines

### 🔐 Strong Password Generation

Use these commands to generate secure secrets:

```bash
# Generate 32-character random password
openssl rand -base64 32

# Generate JWT secret (64 characters)
openssl rand -hex 32

# Generate UUID-based secret
uuidgen | tr -d '-' | tr '[:upper:]' '[:lower:]'
```

### 🔐 Security Best Practices

1. **Rotation Policy**: Rotate secrets every 90 days
2. **Access Control**: Limit access to secrets to essential personnel only
3. **Environment Separation**: Use different secrets for staging and production
4. **Backup**: Securely backup secrets in a password manager
5. **Monitoring**: Monitor secret usage and access logs

## Slack Webhook Setup

### 1. Create Slack App

1. Go to https://api.slack.com/apps
2. Click **Create New App**
3. Choose **From scratch**
4. Name your app "Hopen Deployments"
5. Select your workspace

### 2. Enable Incoming Webhooks

1. Go to **Incoming Webhooks** in your app settings
2. Toggle **Activate Incoming Webhooks** to On
3. Click **Add New Webhook to Workspace**
4. Choose the channel for deployment notifications
5. Copy the webhook URL

### 3. Test Webhook

```bash
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"🚀 Test deployment notification from Hopen Backend!"}' \
  YOUR_WEBHOOK_URL
```

## Self-Hosted Runner Setup

### 1. Production Server Runner

```bash
# On your production server
mkdir actions-runner && cd actions-runner
curl -o actions-runner-linux-x64-2.311.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.311.0/actions-runner-linux-x64-2.311.0.tar.gz
tar xzf ./actions-runner-linux-x64-2.311.0.tar.gz

# Configure runner (use token from GitHub)
./config.sh --url https://github.com/YOUR_USERNAME/YOUR_REPO --token YOUR_TOKEN --labels self-hosted,production

# Install as service
sudo ./svc.sh install
sudo ./svc.sh start
```

### 2. Staging Server Runner

```bash
# On your staging server (similar to production)
./config.sh --url https://github.com/YOUR_USERNAME/YOUR_REPO --token YOUR_TOKEN --labels self-hosted,staging
```

## Environment Validation

Use this script to validate all secrets are properly configured:

```bash
#!/bin/bash
# validate-secrets.sh

echo "🔍 Validating deployment secrets..."

# Check GitHub secrets (run this in GitHub Actions)
if [[ -z "$SLACK_WEBHOOK" ]]; then
    echo "❌ SLACK_WEBHOOK not configured"
else
    echo "✅ SLACK_WEBHOOK configured"
fi

# Check production environment variables
required_vars=(
    "POSTGRESQL_PASSWORD"
    "VALKEY_PASSWORD" 
    "JWT_SECRET"
    "JWT_REFRESH_SECRET"
    "MINIO_ACCESS_KEY"
    "MINIO_SECRET_KEY"
    "MQTT_PASSWORD"
)

for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "❌ $var not set"
    else
        echo "✅ $var configured"
    fi
done

echo "🔍 Secret validation complete"
```

## Troubleshooting

### Common Issues

1. **Deployment fails with "secret not found"**
   - Check GitHub repository secrets are properly named
   - Ensure secrets are available in the correct environment

2. **Database connection fails**
   - Verify `POSTGRESQL_PASSWORD` matches database configuration
   - Check database is running and accessible

3. **JWT authentication fails**
   - Ensure `JWT_SECRET` is at least 32 characters
   - Verify `JWT_REFRESH_SECRET` is different from `JWT_SECRET`

4. **Slack notifications not working**
   - Test webhook URL manually with curl
   - Check webhook has permissions to post to channel

### Secret Rotation Checklist

- [ ] Generate new secret values
- [ ] Update GitHub repository secrets
- [ ] Update production server environment variables
- [ ] Update Kubernetes secrets (if applicable)
- [ ] Restart affected services
- [ ] Verify deployment pipeline works
- [ ] Update documentation
- [ ] Notify team of rotation completion

## Security Incident Response

If secrets are compromised:

1. **Immediate Actions**
   - Rotate all affected secrets immediately
   - Revoke old secrets from all systems
   - Check access logs for unauthorized usage

2. **Investigation**
   - Identify how secrets were compromised
   - Document timeline of events
   - Assess impact on systems and data

3. **Recovery**
   - Deploy new secrets to all environments
   - Verify all systems are functioning
   - Monitor for any ongoing issues

4. **Prevention**
   - Review and improve secret management practices
   - Update documentation and procedures
   - Conduct security training if needed