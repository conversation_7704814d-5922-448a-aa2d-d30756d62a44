# Hopen Backend Deployment Runbook

This runbook provides step-by-step procedures for deploying the Hopen Backend using the CI/CD pipeline.

## 🚀 Quick Reference

| Environment | Branch | Trigger | URL |
|-------------|--------|---------|-----|
| **Staging** | `develop` | Automatic on push | `http://staging.hopen.app` |
| **Production** | `main` | Manual workflow dispatch | `https://api.hopen.app` |

## 📋 Pre-Deployment Checklist

### Before Any Deployment

- [ ] All tests are passing in CI
- [ ] Code review completed and approved
- [ ] Database migrations are ready (if applicable)
- [ ] Feature flags configured (if applicable)
- [ ] Monitoring alerts are configured
- [ ] Rollback plan is prepared

### Production Deployment Only

- [ ] Staging deployment tested and verified
- [ ] Performance testing completed
- [ ] Security scan passed
- [ ] Stakeholder approval obtained
- [ ] Maintenance window scheduled (if needed)
- [ ] Team notified of deployment

## 🔄 Deployment Procedures

### Staging Deployment (Automatic)

Staging deployments happen automatically when code is pushed to the `develop` branch.

1. **Push to develop branch**
   ```bash
   git checkout develop
   git pull origin develop
   git merge feature-branch
   git push origin develop
   ```

2. **Monitor deployment**
   - Go to GitHub Actions tab
   - Watch "Deploy to Staging" workflow
   - Check deployment logs for any issues

3. **Verify deployment**
   ```bash
   # Health check
   curl -f http://staging.hopen.app/health
   
   # Check all services
   ./scripts/health-check-comprehensive.sh
   ```

4. **Test staging environment**
   - Verify new features work as expected
   - Run smoke tests
   - Check database migrations applied correctly

### Production Deployment (Manual)

Production deployments use blue/green deployment strategy for zero downtime.

1. **Prepare for deployment**
   ```bash
   # Ensure main branch is up to date
   git checkout main
   git pull origin main
   
   # Create release tag
   git tag -a v1.2.3 -m "Release version 1.2.3"
   git push origin v1.2.3
   ```

2. **Trigger production deployment**
   - Go to GitHub repository
   - Navigate to **Actions** tab
   - Select **Release to Production** workflow
   - Click **Run workflow**
   - Enter the Git SHA or tag (e.g., `v1.2.3`)
   - Click **Run workflow**

3. **Monitor deployment process**
   
   The deployment will automatically:
   - Deploy to inactive environment (blue/green)
   - Run health checks on new environment
   - Switch traffic to new environment
   - Shut down old environment
   - Send Slack notification

4. **Verify production deployment**
   ```bash
   # Health check
   curl -f https://api.hopen.app/health
   
   # Comprehensive check
   ssh production-server "cd /opt/hopen-backend && ./scripts/health-check-comprehensive.sh"
   ```

## 🔧 Troubleshooting Guide

### Common Deployment Issues

#### 1. CI Pipeline Failures

**Symptom**: Tests or linting fail in CI
```bash
# Check specific failure
git log --oneline -10
git show <commit-hash>

# Fix and re-push
git add .
git commit -m "fix: resolve CI issues"
git push origin develop
```

#### 2. Docker Build Failures

**Symptom**: Docker build fails during CI
```bash
# Test build locally
cd hopenbackend
docker build -t hopen-backend-test .

# Check Dockerfile syntax
docker run --rm -i hadolint/hadolint < Dockerfile
```

#### 3. Health Check Failures

**Symptom**: New deployment fails health checks
```bash
# Check service logs
docker logs hopen-backend-blue
docker logs hopen-backend-green

# Check database connectivity
./scripts/health-check-comprehensive.sh infrastructure

# Manual health check
curl -v http://localhost:8001/health
curl -v http://localhost:8002/health
```

#### 4. Traffic Switch Failures

**Symptom**: Traffic switch script fails
```bash
# Check NGINX status
sudo systemctl status nginx
sudo nginx -t

# Check NGINX logs
sudo tail -f /var/log/nginx/error.log

# Manual traffic switch (if needed)
sudo /opt/hopen-backend/scripts/switch-traffic.sh blue
```

#### 5. Database Migration Issues

**Symptom**: Database migrations fail
```bash
# Check migration status
docker exec -it postgres psql -U hopen -d hopen -c "SELECT * FROM schema_migrations;"

# Run migrations manually (if safe)
docker exec -it hopen-backend-blue /app/migrate up

# Rollback migrations (if needed)
docker exec -it hopen-backend-blue /app/migrate down 1
```

### Emergency Procedures

#### 🚨 Emergency Rollback

If the production deployment has critical issues:

1. **Immediate rollback via traffic switch**
   ```bash
   # SSH to production server
   ssh production-server
   
   # Check which environment is active
   ls -la /opt/hopen-backend/active_is_*
   
   # Switch back to previous environment
   sudo /opt/hopen-backend/scripts/switch-traffic.sh blue  # or green
   ```

2. **Verify rollback**
   ```bash
   # Health check
   curl -f https://api.hopen.app/health
   
   # Check application logs
   docker logs hopen-backend-blue
   ```

3. **Notify team**
   - Send immediate Slack notification
   - Create incident report
   - Schedule post-mortem

#### 🚨 Complete Service Outage

If all services are down:

1. **Check infrastructure**
   ```bash
   # Check server resources
   top
   df -h
   free -m
   
   # Check Docker
   docker ps -a
   docker system df
   
   # Check databases
   docker exec -it postgres pg_isready
   docker exec -it scylladb nodetool status
   ```

2. **Restart services**
   ```bash
   # Restart Docker Compose
   cd /opt/hopen-backend
   docker-compose down
   docker-compose up -d
   
   # Check service startup
   docker-compose logs -f
   ```

3. **Escalate if needed**
   - Contact on-call engineer
   - Engage incident response team
   - Update status page

## 📊 Monitoring and Alerts

### Key Metrics to Monitor

1. **Service Health**
   - HTTP response times
   - Error rates
   - Service availability

2. **Infrastructure**
   - CPU usage
   - Memory usage
   - Disk space
   - Network connectivity

3. **Database Performance**
   - Connection pool usage
   - Query performance
   - Replication lag

### Setting Up Alerts

```bash
# Example Prometheus alerting rules
groups:
- name: hopen-backend
  rules:
  - alert: ServiceDown
    expr: up{job="hopen-backend"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Hopen Backend service is down"
      
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
```

## 📚 Deployment Scripts Reference

### Available Scripts

| Script | Purpose | Usage |
|--------|---------|-------|
| `switch-traffic.sh` | Switch traffic between blue/green | `sudo ./switch-traffic.sh blue` |
| `setup-production-server.sh` | Initial server setup | `sudo ./setup-production-server.sh` |
| `health-check-comprehensive.sh` | Complete health check | `./health-check-comprehensive.sh full` |

### Script Parameters

#### health-check-comprehensive.sh
```bash
./health-check-comprehensive.sh quick          # Quick service check
./health-check-comprehensive.sh services       # Detailed service check
./health-check-comprehensive.sh infrastructure # Infrastructure check
./health-check-comprehensive.sh full           # Complete check (default)
```

## 🔐 Security Considerations

### Deployment Security

1. **Access Control**
   - Only authorized personnel can trigger production deployments
   - SSH keys are properly managed and rotated
   - Secrets are stored securely

2. **Network Security**
   - Firewall rules are properly configured
   - SSL certificates are valid and up to date
   - Internal services are not exposed publicly

3. **Container Security**
   - Images are scanned for vulnerabilities
   - Containers run as non-root users
   - Resource limits are enforced

### Security Incident During Deployment

If security issues are detected during deployment:

1. **Stop deployment immediately**
2. **Isolate affected systems**
3. **Assess security impact**
4. **Apply security patches**
5. **Re-run security scans**
6. **Resume deployment only after clearance**

## 📝 Post-Deployment Tasks

### After Successful Deployment

1. **Verify all services**
   ```bash
   ./scripts/health-check-comprehensive.sh full
   ```

2. **Update documentation**
   - Update CHANGELOG.md
   - Update API documentation if changed
   - Update deployment notes

3. **Monitor for issues**
   - Watch error logs for 30 minutes
   - Monitor performance metrics
   - Check user feedback channels

4. **Clean up**
   ```bash
   # Clean up old Docker images
   docker system prune -f
   
   # Archive deployment logs
   mv /var/log/hopen-deployment.log /var/log/archive/deployment-$(date +%Y%m%d).log
   ```

### Weekly Maintenance

1. **Review deployment metrics**
2. **Update dependencies** (via Dependabot PRs)
3. **Rotate secrets** (monthly)
4. **Update documentation**
5. **Test disaster recovery procedures**

## 📞 Contact Information

### Escalation Contacts

| Role | Contact | When to Contact |
|------|---------|----------------|
| On-Call Engineer | `@oncall` in Slack | Service outages, critical issues |
| DevOps Lead | `@devops-lead` | Deployment pipeline issues |
| Security Team | `@security` | Security incidents |
| Product Owner | `@product` | Feature rollback decisions |

### Communication Channels

- **Slack**: `#deployments` for deployment notifications
- **Slack**: `#incidents` for incident response
- **Email**: `<EMAIL>` for non-urgent issues
- **Status Page**: `https://status.hopen.app`

## 📋 Deployment Checklist Template

Copy this checklist for each production deployment:

```markdown
## Deployment Checklist - v1.2.3

### Pre-Deployment
- [ ] All tests passing
- [ ] Code review approved
- [ ] Staging tested
- [ ] Performance tested
- [ ] Security scan passed
- [ ] Team notified
- [ ] Rollback plan ready

### Deployment
- [ ] Workflow triggered with correct tag/SHA
- [ ] Blue/green deployment completed
- [ ] Health checks passed
- [ ] Traffic switched successfully
- [ ] Old environment cleaned up

### Post-Deployment
- [ ] Production health verified
- [ ] Performance metrics normal
- [ ] Error rates normal
- [ ] User feedback positive
- [ ] Documentation updated
- [ ] Team notified of success

### Issues (if any)
- Issue 1: Description and resolution
- Issue 2: Description and resolution

### Notes
- Deployment duration: X minutes
- Downtime: 0 seconds (blue/green)
- Special considerations: None
```

---

*This runbook should be reviewed and updated regularly to reflect current procedures and lessons learned.*