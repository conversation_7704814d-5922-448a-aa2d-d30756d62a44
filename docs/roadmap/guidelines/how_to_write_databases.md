Database Design and Migration Guidelines
1. Introduction
The database is the foundational layer of our application. Its design, performance, and integrity directly impact every feature, every user interaction, and the overall scalability of our platform. A well-architected database is a force multiplier for development, while a poorly designed one becomes a source of persistent technical debt.
These guidelines are derived from the best practices embodied in our consolidated production schema. They represent our "gold standard" for database design and are intended to ensure that ourdata layer remains robust, performant, and maintainable as we grow. Adherence to these principles is mandatory for all new schema development and modifications.
2. Core Principles
Every decision regarding the database schema should be guided by these four core principles:
A. Design for Intent, Not for History
A production schema should represent the final, intended state of the data model. It should not be a complex log of historical ALTER statements. For new environments, we use a single, consolidated set of migrations to build the perfect schema from scratch, ensuring clarity and efficiency.
Practice: Periodically, the historical chain of migrations will be consolidated into a new, clean "version 2" schema for setting up new environments. The old migration chain will be archived.
B. Performance is a Feature, Designed from the Start
Database performance is not an afterthought; it is a fundamental design requirement. Indexing strategies, query optimization, and the choice of database technology (e.g., PostgreSQL vs. ScyllaDB) must be considered during the initial design phase, not as a reaction to performance problems.
Practice: Every SELECT query pattern in the application code must be supported by an appropriate index. No feature will be approved if its data access patterns require inefficient full table scans.
C. Data Integrity is Non-Negotiable
The database is the ultimate guardian of data integrity. We must leverage the full power of the database to enforce rules, prevent invalid data, and maintain consistency. Application-level validation is a secondary layer of defense, not a substitute for database constraints.
Practice: Use FOREIGN KEY constraints for all relationships, CHECK constraints for data validation, NOT NULL wherever possible, and strongly-typed ENUMs instead of generic text fields for status or type columns.
D. Clarity and Maintainability are Paramount
A database schema should be self-documenting. A new developer should be able to understand the data model, its relationships, and its constraints by inspecting the schema directly. This reduces onboarding time, minimizes bugs, and facilitates long-term maintenance.
Practice: All tables, columns, and complex constraints must have COMMENTs. Naming conventions must be followed rigorously. Business logic encapsulated in the database (e.g., functions, triggers) must be thoroughly documented.
3. Detailed Guidelines
3.1. Schema & Table Design
Naming Conventions:
Tables: plural, snake_case (e.g., user_relationships, bubble_members).
Columns: snake_case (e.g., created_at, bubble_id).
Primary Keys: id for the primary key of the table itself (e.g., bubbles.id), and [table_name_singular]_id for foreign keys (e.g., bubble_members.bubble_id).
Indexes: idx_[table_name]_[column_names].
Functions: verb_noun format (e.g., get_user_relationship).
Triggers: trigger_[table_name]_[purpose].
Primary Keys:
Use UUID (gen_random_uuid()) as the default primary key type for all tables. This is crucial for distributed systems to avoid collisions and bottlenecks.
Data Types:
Timestamps: Always use TIMESTAMP WITH TIME ZONE (TIMESTAMPTZ). Never use TIMESTAMP without a time zone.
Statuses & Types: Use ENUM types for columns with a fixed set of possible values (e.g., bubble_status, relationship_type). This provides type safety and is more efficient than VARCHAR.
Text: Use VARCHAR(n) for text with a known, reasonable maximum length (e.g., username VARCHAR(40)). Use TEXT for user-generated content with no practical limit (e.g., message TEXT).
JSON: Use JSONB for storing unstructured or semi-structured metadata.
Nullability:
Default to NOT NULL for all columns unless a NULL value has a specific, defined meaning. This prevents a wide class of application-level bugs.
3.2. Performance & Indexing
B-Tree Index (Default): Use for standard equality (=), range (>, <), and ORDER BY queries on single columns.
GIN Index:
Full-Text Search: Use with to_tsvector for efficient, scalable text search (see user_search_index). This is mandatory for any user-facing search feature.
Array Columns: Use for columns of type UUID[] or TEXT[] to efficiently query for the existence of an element in the array (e.g., user_search_index.friends).
Partial Index: Use for large tables where queries frequently filter on a specific value. This creates a smaller, faster index.
Example: CREATE INDEX ... ON bubble_members (user_id, status) WHERE status = 'active';
Composite Index: Use for queries that filter or sort on multiple columns. The order of columns in the index must match the order in the WHERE and ORDER BY clauses.
Example: CREATE INDEX ... ON user_relationships (from_user_id, status, relationship_type, created_at DESC);
3.3. ScyllaDB / NoSQL Best Practices
Query-Driven Design: Design your tables to fit your queries, not the other way around. Denormalization is encouraged. Do not attempt to model a relational schema in ScyllaDB.
Partition Key: The partition key (the first part of the PRIMARY KEY) is the most critical design choice. It must have high cardinality and distribute data evenly across the cluster.
Clustering Columns: Use clustering columns to sort data within a partition. For time-series data like chat messages, created_at DESC is almost always the correct choice.
Counter Tables: For aggregations like unread message counts, use dedicated COUNTER tables (e.g., bubble_unread_counts). This avoids the deadly read-modify-write anti-pattern.
Materialized Views: Use materialized views to create new, server-side denormalized tables that support different query patterns without ALLOW FILTERING.
TTL (Time-To-Live): Use default_time_to_live on tables with ephemeral data (e.g., chat messages, presence indicators) to enable automatic data cleanup.
3.4. Data Integrity & Security
Foreign Keys: All relationships between tables must be enforced with FOREIGN KEY constraints.
Use ON DELETE CASCADE when child records should be deleted with the parent (e.g., bubble_members when a bubble is deleted).
Use ON DELETE RESTRICT or ON DELETE SET NULL when child records should be preserved.
CHECK Constraints: Use CHECK constraints to enforce business rules at the database level.
Example: CONSTRAINT user_relationships_no_self_relationship CHECK (from_user_id != to_user_id)
GDPR & Compliance: Embed compliance fields directly into core tables like users (data_retention_policy, data_deleted_at) to ensure data privacy is a first-class citizen.
3.5. Migrations
Structure: Every schema change must have a corresponding up and down migration file.
Atomicity: All migration scripts must be wrapped in a BEGIN; ... COMMIT; block to ensure they are applied atomically.
Documentation: Every migration file must include a header comment detailing its purpose, author, date, rollback safety, performance impact, and dependencies.
Consolidation: The full migration history is maintained in source control, but for creating new environments, a single, consolidated schema file for each major feature area is used.
3.6. Advanced Features
Functions: Encapsulate reusable business logic in PostgreSQL functions. This reduces code duplication in the application layer and ensures consistent logic.
Example: are_users_friends(user1_id UUID, user2_id UUID)
Triggers: Use triggers for automatic, guaranteed actions, such as updating an updated_at timestamp on every row modification.
Views: Use views to simplify complex queries and provide a stable interface over a changing underlying schema. The user_search_results view is a good example.
4. Migration Creation Checklist
Before committing a new migration, ensure it meets the following criteria:
Naming: Do all tables, columns, and indexes follow the naming conventions?
Primary Key: Does every new table have a UUID primary key?
Data Types: Are the most specific and correct data types used (TIMESTAMPTZ, ENUM, VARCHAR(n) vs. TEXT)?
Constraints: Are FOREIGN KEY, CHECK, and NOT NULL constraints applied where necessary?
Indexes: Does every SELECT query pattern introduced by your feature have a supporting index? Have you considered composite or partial indexes?
Atomicity: Is the entire up and down migration wrapped in a BEGIN/COMMIT block?
Rollback: Is the down migration complete and tested? Does it correctly reverse all changes made in the up migration?
Documentation: Does the migration file have a complete header comment? Have you added COMMENTs to new tables and complex columns?
Performance: Have you considered the performance impact of your changes, especially on large tables?
By adhering to these guidelines, we ensure our database remains a powerful, reliable, and scalable asset for the Hopen platform.