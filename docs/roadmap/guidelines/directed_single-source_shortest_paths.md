## Directed Single-Source Shortest Paths (DSSSP) and the BMSSP subroutine

### What this document covers
- **Context**: The result “Breaking the Sorting Barrier for Directed Single-Source Shortest Paths” achieving O(m log^{2/3} n) time in the comparison-addition model.
- **Core idea**: A recursive, bounded multi-source shortest-path subroutine (BMSSP) that avoids global sorting costs while preserving Dijkstra-like correctness via structured bucketing and pivoting.
- **This file**: Practical notes and a faithful outline of Algorithm 3 (BMSSP), its inputs/outputs, invariants, and how it composes into the full algorithm.

### References
- Paper: [Breaking the Sorting Barrier for Directed Single-Source Shortest Paths](https://arxiv.org/abs/2504.17033)
- Thread: [X/Twitter post by <PERSON><PERSON><PERSON>](https://x.com/Avinashabroy/status/1954595922865987747)

### Problem setting
- **Graph**: Directed graph G = (V, E) with non-negative real edge weights w_e ≥ 0.
- **Goal (DSSSP)**: For a fixed source s ∈ V, compute shortest-path distances d(v) for all v ∈ V.
- **Model**: Comparison-addition model; addition and comparisons on weights are O(1), no multiplication/division assumptions, and we avoid generic sorting in the inner loop.

### High-level strategy
- Replace global priority-queue operations with a bounded, windowed process that handles distances in controlled ranges [B′, B) using recursion.
- Use a carefully designed multi-bucket deque structure D with batching and prepend operations to maintain near-monotonic pulls without full sorting.
- Seed each recursive window with a small “complete” set S of vertices that cover shortest paths to all currently relevant “incomplete” vertices x with d(x) < B. This guarantees any shortest path to x touches S, enabling bounded multi-source relaxations.

### Key definitions and parameters
- l: current recursion level (non-negative integer). Base case at l = 0.
- t: a tuning parameter controlling the number of buckets and the growth factor across recursion.
- B: current upper boundary for the distance window at this recursion level; the subroutine only processes distances in [B′, B).
- B′: the (returned) refined lower boundary; after processing, vertices with d < B′ are considered settled for this window.
- S: a set of “complete” vertices used as multi-sources in this invocation. They satisfy the coverage requirement below.
- P, W: pivot and window helper sets chosen by FindPivots(B, S); W is used to add final winners with d[x] < B′.
- U: the set of vertices finalized (or “exposed”) by this call.
- M: number of buckets for the deque structure at this level; M = 2^{(l−1)} · t.
- D: a specialized bounded deque with operations Initialize(M, B), Insert(⟨x, key⟩), Pull(), BatchPrepend(...).
- k: a constant from the analysis controlling how many items may be extracted per level (appears as k · 2^l · t in the loop guard).

### Preconditions (requirements)
1. |S| ≤ 2^l · t
2. For every incomplete vertex x with d(x) < B, any shortest path from the source s to x visits some complete vertex y ∈ S

These ensure that, within the current window [B′, B), the multi-source expansion from S suffices to discover and relax all relevant vertices without “leaking” paths that bypass S.

### Output
- A refined boundary B′ ≤ B
- A set U of vertices whose distances have been finalized within the current window

### Algorithm 3: BMSSP (Bounded Multi-Source Shortest Path)

```pseudo
function BMSSP(l, B, S)
  // Requirement 1: |S| ≤ 2^l · t
  // Requirement 2: For every incomplete vertex x with d(x) < B,
  //                the shortest path to x visits some y ∈ S
  // Returns: boundary B′ ≤ B and a set U

  if l = 0 then
    return BaseCase(B, S)   // returns (B′, U)

  (P, W) ← FindPivots(B, S)
  M ← 2^{l−1} · t
  D.Initialize(M, B)

  for x ∈ P do
    D.Insert(⟨x, d[x]⟩)

  i ← 0
  B′_0 ← (P ≠ ∅ ? min_{x∈P} d[x] : B)
  U ← ∅

  while |U| < k · 2^l · t and D is not empty do
    i ← i + 1
    (B_i, S_i) ← D.Pull()
    (B′_i, U_i) ← BMSSP(l − 1, B_i, S_i)
    U ← U ∪ U_i

    K ← ∅
    for each edge e = (u, v) with u ∈ U_i do
      if d[u] + w(u,v) ≤ d[v] then
        d[v] ← d[u] + w(u,v)

        if d[v] ∈ [B_i, B) then
          D.Insert(⟨v, d[v]⟩)
        else if d[v] ∈ [B′_i, B_i) then
          K ← K ∪ {⟨v, d[v]⟩}   // Instance of Lemma 3.3

    // Prepend near-boundary relaxations and eligible seeds S_i
    D.BatchPrepend(
      K ∪ {⟨x, d[x]⟩ : x ∈ S_i and d[x] ∈ [B′_i, B_i)}
    )

  return (min({B′_i} ∪ {B}), U ∪ { x ∈ W : d[x] < min({B′_i} ∪ {B}) })
```

Notes on the pseudocode above
- BaseCase(B, S) handles l = 0 tightly, using direct bounded bucketing in a single-level structure and finalizing within [B′, B). It returns (B′, U).
- FindPivots(B, S) produces a small set P of pivot seeds and the helper set W used for the final return filter.
- D.Pull() returns a pair (B_i, S_i): the next sub-window boundary B_i and its associated multi-source seed set S_i.
- The BatchPrepend step exploits near-monotonicity: items whose keys fall just below B_i can be processed before the next standard Insert pulls, keeping the structure bounded without global re-sorting.

### Why this works (informal invariants)
- Coverage: Any shortest path to a relevant x (with d(x) < B) touches S, so a bounded multi-source relaxation suffices per level.
- Bounded work per level: The deque D has M = 2^{l−1} · t buckets; the loop caps work via |U| < k · 2^l · t. This keeps each level’s effort near-linear in the size of the window.
- Near-monotone ordering: Using BatchPrepend for keys in [B′_i, B_i) simulates priority-queue discipline within a bounded band, avoiding general-purpose sorting.
- Shrinking boundary: Each recursive call returns a B′_i ≤ B_i, and the final B′ is min over these, ensuring forward progress.

### Complexity (from the paper)
- The full SSSP algorithm built around BMSSP runs in **O(m log^{2/3} n)** time, deterministic, in the comparison-addition model.
- Space is near-linear; bucket counts and recursion parameters are tuned via t to balance levels.

### Implementation tips
- Data structure D:
  - Use a cyclic array of deques (buckets) for ranges partitioning [B′, B), supporting Insert (amortized O(1)), Pull (amortized O(1)), and BatchPrepend.
  - Keys are tentative distances; bucket mapping depends on the current window scale and M.
- Base case:
  - For l = 0, a small fixed number of buckets suffices; implement as a tight loop over a bounded range to finalize U.
- Pivots:
  - FindPivots should return few seeds P and a helper set W, satisfying |S| ≤ 2^l · t and maintaining requirement 2 for the sub-window.
- Graph relaxations:
  - Only edges out of U_i are scanned per sub-call; this concentrates work on vertices that just became tight in the current window.

### Relation to Dijkstra
- Dijkstra maintains a global priority queue over all unsettled vertices; here, BMSSP replaces it with a recursion over bounded windows and multi-source seeds, ensuring correctness while avoiding global sorting.
- The algorithm is deterministic and comparison-addition compliant, avoiding randomized bucketing tricks.

### Practical checklist
- Verify preconditions before calling BMSSP at each level: |S| bound and coverage property.
- Tune t and bucket mapping to the graph’s weight scales.
- Assert that BaseCase and FindPivots uphold the invariants; they are the linchpins for correctness and performance.

### Minimal worked example (sketch)
- Suppose B = 100, S = {s} with d[s] = 0, l = 1, t chosen so |S| ≤ 2^1 · t.
- BMSSP(1, 100, {s}) calls FindPivots to choose few pivots P around current frontier; D initialized with M = 2^{0} · t = t buckets.
- It pulls sub-boundaries B_i and seeds S_i, recurses with l − 1 = 0, collects U_i, relaxes out-edges, BatchPrepends near-boundary updates, and returns B′ ≤ 100 with U of finalized vertices.

### Caveats and scope
- Non-negative weights are required; negative edges violate the bounded window monotonicity used by D and the recursion.
- All constants (k, the exact bucket mapping, BaseCase granularity) follow the paper’s analysis; implementers should adhere to the prescribed ranges to preserve O(m log^{2/3} n).

### Attribution
This summary and pseudocode are adapted from the authors’ presentation in the paper above and discussion in the linked thread. For proofs and full derivations (including Lemma 3.3 and full correctness), see the paper.


