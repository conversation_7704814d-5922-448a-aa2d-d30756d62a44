Flutter tip: 
keep your UI silky smooth by offloading heavy work with 

compute()

It runs a top-level/static function in a background isolate and returns a Future.

'dart

/ This function will run in a background isolate
List<int> processImage(String path) {
// Heavy image processing
Future‹void> main() async {
const inputPath = 'assets/input.jpg';
const outputPath = 'assets/output.jpg';
print('Processing image ...');
// Run in background isolate
final result = await compute(processImage,
inputPath);
// Save processed image
await File(outputPath).writeAsBytes(result);
print('Done! Saved to outputPath');