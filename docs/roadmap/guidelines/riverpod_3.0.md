### Riverpod 3.0: Best Practices and Guidelines

#### 1. Embrace the Unified and Simplified API
*   **Use `Notifier` and `AsyncNotifier`:** These are now the primary providers for managing complex state. They replace the need for `StateNotifierProvider` and `ChangeNotifierProvider`.
*   **Migrate from Legacy Providers:** If your project uses `StateProvider`, `StateNotifierProvider`, or `ChangeNotifierProvider`, import them from `package:flutter_riverpod/legacy.dart` and plan to migrate them to `Notifier` or `AsyncNotifier` for a more modern and streamlined API.
*   **No More `AutoDispose`:** The `autoDispose` variants of providers have been removed. The standard providers now handle this automatically. You can safely remove `AutoDispose` from your code.

#### 2. Leverage Code Generation
*   **Use `@riverpod`:** While optional, code generation is the recommended approach. It leads to a cleaner, more concise, and less error-prone syntax.

#### 3. Understand and Utilize New Features
*   **Automatic Retry:** Be aware that failing providers now automatically retry with an exponential backoff. This improves resilience to transient errors. You can configure or disable this feature if needed.
*   **Paused Providers:** Providers are now automatically paused when they are not visible in the widget tree, which helps to conserve resources.
*   **Offline Persistence (Experimental):** For caching provider state, explore the new offline persistence feature. It allows you to easily save and restore state across app restarts.
*   **Mutations (Experimental):** Use mutations to handle side-effects like form submissions. This makes it easier to manage loading, success, and error states in the UI.

#### 4. Modernize Your Error Handling
*   **Handle `ProviderException`:** Errors within providers are now wrapped in a `ProviderException`. Update your `try/catch` blocks to handle this exception type and access the original error through the `exception` property.

#### 5. Improve Your Testing Workflow
*   **Use `ProviderContainer.test`:** This new utility simplifies testing by creating a container that is automatically disposed of after the test completes.
*   **Mock with `overrideWithBuild`:** When testing notifiers, use `NotifierProvider.overrideWithBuild` to mock only the `build` method without needing to mock the entire notifier.

#### 6. Optimize Resource Management
*   **Check with `Ref.mounted`:** Before performing actions in an async operation, use `ref.mounted` to check if the provider is still active. This is similar to `BuildContext.mounted` and helps prevent errors.

#### 7. Scoped Providers
*   **Isolate Logic:** Use scoped providers to define providers that are only active in a specific part of the widget tree. This is useful for isolating complex features and can improve performance.

#### 8. State Management and Error Handling
*   **Use Sealed Classes for State:** Instead of BLoC-style states, use sealed classes to represent your state. This makes your state management more robust and type-safe.
*   **Leverage `AsyncValue`:** For asynchronous operations, use `AsyncValue` to easily handle loading, data, and error states.
*   **Use `riverpod` Annotations:** Always use the `@riverpod` annotations with code generation for a better development experience.
*   **Explicit Imports:** Use explicit imports to avoid conflicts between different state management libraries or packages.
*   **Proper Error Handling with Result Patterns:** Implement proper error handling using `Result` patterns to gracefully manage errors in your application.