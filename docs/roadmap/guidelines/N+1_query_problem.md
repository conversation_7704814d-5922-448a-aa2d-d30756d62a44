Hunt down and eliminate your N+1s.

The N+1 query problem is dangerous yet easy to overlook: When a single query provides a result set, then a follow-up query is needed for each result. The number of queries needed to load a page is determined by the result set size, leading to bad performance when the N is LARGE.

An example in pseudocode:

// 1 query
users = db.query('SELECT * FROM users');

// ...leads to N more queries
for (const user of users) {
  posts = db.query(`
      SELECT * FROM posts 
      WHERE user_id = user->id`);
  user.posts = posts;
}
// Can finally return result to requester

Instead, you could rewrite this as a single query with a JOIN to get all the data in a single round-trip to the database:

results = await db.query(
  `SELECT u.*, p.* 
    FROM users u 
    LEFT JOIN posts p 
    ON u->id = p->user_id`);

The interesting thing about N+1 is that for some requests, the impact will be minimal. When N is a small number, things may still seem “fast” so long as the database is well tuned and near your app servers. But when N=100 you both start to notice the slowdown and you work the database more than necessary. Fixing these makes for a better UX and reduced load on the DB.

You could be doing it explicitly or your ORM might do this without you realizing. Either way, get rid of them!