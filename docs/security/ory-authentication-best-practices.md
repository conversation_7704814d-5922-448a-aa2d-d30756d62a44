# Ory Stack Authentication Best Practices - Analysis & Improvements

## Current Implementation Issues

### 1. Session Management Issues

#### Problem: Mixed Session Token Handling
- **Current**: Using `X-Session-Token` header instead of cookies in some places
- **Issue**: This bypasses <PERSON><PERSON>'s built-in CSRF protection and session management
- **Best Practice**: Use cookie-based sessions exclusively for web and mobile apps

#### Problem: Manual Session Extension
- **Current**: Custom session extension logic in `SessionManager`
- **Issue**: <PERSON><PERSON><PERSON> handles session lifecycle automatically
- **Best Practice**: Let Kratos manage session expiry and refresh

### 2. Authentication Flow Issues

#### Problem: Direct Backend Session Creation
- **Current**: Creating sessions programmatically in backend (`createNewSessionForIdentity`)
- **Issue**: Bypasses Kratos authentication flows and security checks
- **Best Practice**: All sessions should be created through Kratos authentication flows

#### Problem: Dual Session Validation
- **Current**: Validating sessions in both Kratos and backend gRPC
- **Issue**: Creates synchronization issues and potential security gaps
- **Best Practice**: Single source of truth - Kratos for identity, backend for app-specific data

### 3. Cookie Configuration Issues

#### Problem: Missing Session Configuration
- **Current**: No explicit session configuration in `kratos.yml`
- **Issue**: Using defaults which may not be secure enough
- **Best Practice**: Explicitly configure session lifespans, cookie settings, and security options

### 4. Oathkeeper Configuration Issues

#### Problem: Incomplete Access Rules
- **Current**: Using `cookie_session` authenticator but missing proper configuration
- **Issue**: May not properly validate Kratos sessions
- **Best Practice**: Configure proper session validation with Kratos integration

### 5. Frontend Issues

#### Problem: Cookie Handling in Flutter
- **Current**: Manual cookie jar management
- **Issue**: May not properly handle secure cookies and CSRF tokens
- **Best Practice**: Use platform-specific cookie storage with proper security

## Recommended Improvements

### 1. Update Kratos Configuration

```yaml
# kratos.yml - Add session configuration
version: v1.1.0
dsn: *****************************************/hopen_db?sslmode=disable

session:
  # Session lifespan configuration
  lifespan: 24h
  earliest_possible_extend: 1h
  
  # Cookie configuration
  cookie:
    name: ory_kratos_session
    same_site: Lax
    path: /
    # Set to true in production with HTTPS
    secure: false
    # Prevents JS access to cookies
    http_only: true
    
  # Enable whoami check for session validation
  whoami:
    required_aal: aal1
    
  # Session issuer for token generation
  issuer: https://auth.hopenapp.com

log:
  level: info
  format: json
  leak_sensitive_values: false

serve:
  public:
    base_url: http://localhost:4433/
    cors:
      enabled: true
      allowed_origins:
        - http://localhost:3000
        - http://localhost:8080
      allowed_methods:
        - POST
        - GET
        - PUT
        - PATCH
        - DELETE
      allowed_headers:
        - Authorization
        - Cookie
        - Content-Type
      exposed_headers:
        - Set-Cookie
      allow_credentials: true
      
  admin:
    base_url: http://localhost:4434/

selfservice:
  default_browser_return_url: http://localhost:3000/
  
  # Session after registration
  flows:
    registration:
      after:
        password:
          hooks:
            - hook: session
            - hook: show_verification_ui
              
    login:
      after:
        password:
          hooks:
            - hook: require_verified_address
            - hook: session
            
  methods:
    password:
      enabled: true
      config:
        haveibeenpwned_enabled: true
        min_password_length: 8
        
    oidc:
      enabled: true
      config:
        providers:
          - id: google
            provider: google
            client_id: ${GOOGLE_OAUTH_CLIENT_ID}
            client_secret: ${GOOGLE_OAUTH_CLIENT_SECRET}
            mapper_url: file:///etc/config/oidc.google.jsonnet
            scope:
              - openid
              - profile
              - email
              
          - id: apple
            provider: apple
            client_id: ${APPLE_OAUTH_CLIENT_ID}
            apple_team_id: ${APPLE_OAUTH_TEAM_ID}
            apple_private_key_id: ${APPLE_OAUTH_KEY_ID}
            apple_private_key: ${APPLE_OAUTH_PRIVATE_KEY}
            mapper_url: file:///etc/config/oidc.apple.jsonnet
            scope:
              - email

identity:
  default_schema_id: default
  schemas:
    - id: default
      url: file:///etc/config/identity.default.schema.json

hashers:
  argon2:
    parallelism: 1
    memory: 128MB
    iterations: 3
    salt_length: 16
    key_length: 32

secrets:
  cookie:
    - ${KRATOS_COOKIE_SECRET}
  cipher:
    - ${KRATOS_CIPHER_SECRET}
```

### 2. Update Backend Session Validation

```go
// pkg/ory/client.go - Improved session validation
package ory

import (
    "context"
    "fmt"
    "net/http"
    "time"
    
    ory "github.com/ory/client-go"
    "go.uber.org/zap"
)

// ValidateSession validates a session using cookie or token
func (c *Client) ValidateSession(ctx context.Context, sessionTokenOrCookie string) (*ory.Session, error) {
    if c.Kratos == nil {
        return nil, fmt.Errorf("kratos client not initialized")
    }
    
    // Create request - Kratos SDK will handle both cookie and token
    req := c.Kratos.FrontendAPI.ToSession(ctx)
    
    // Check if this is a cookie or a token
    // Cookies should be passed in Cookie header, tokens in X-Session-Token
    if isCookieFormat(sessionTokenOrCookie) {
        // This is a cookie - should be in the request already via Cookie header
        // The SDK handles this automatically
    } else {
        // This is a session token - use X-Session-Token header
        req = req.XSessionToken(sessionTokenOrCookie)
    }
    
    session, resp, err := req.Execute()
    if err != nil {
        if resp != nil {
            switch resp.StatusCode {
            case http.StatusUnauthorized:
                return nil, fmt.Errorf("invalid or expired session")
            case http.StatusForbidden:
                return nil, fmt.Errorf("session forbidden - may need reauthentication")
            default:
                return nil, fmt.Errorf("session validation failed (status %d): %w", resp.StatusCode, err)
            }
        }
        return nil, fmt.Errorf("failed to validate session: %w", err)
    }
    
    // Check if session is active
    if session == nil || session.Active == nil || !*session.Active {
        return nil, fmt.Errorf("session is not active")
    }
    
    // Check if session needs refresh (within 15 minutes of expiry)
    if session.ExpiresAt != nil {
        timeUntilExpiry := time.Until(*session.ExpiresAt)
        if timeUntilExpiry < 15*time.Minute {
            c.logger.Info("Session expiring soon", 
                zap.Duration("time_until_expiry", timeUntilExpiry),
                zap.String("session_id", session.Id))
        }
    }
    
    return session, nil
}

// isCookieFormat checks if the string looks like a cookie
func isCookieFormat(s string) bool {
    return strings.Contains(s, "ory_kratos_session=")
}

// RefreshSession handles session refresh through Kratos
func (c *Client) RefreshSession(ctx context.Context, sessionToken string) (*ory.Session, error) {
    // Kratos handles session refresh automatically through the whoami endpoint
    // Just validate the session - if it's valid, Kratos extends it automatically
    return c.ValidateSession(ctx, sessionToken)
}
```

### 3. Update Authentication Service

```go
// microservices/auth/service.go - Improved session handling
func (s *Service) ValidateSession(ctx context.Context, req *authv1.ValidateSessionRequest) (*authv1.ValidateSessionResponse, error) {
    s.logger.Info("ValidateSession called")
    
    if req.SessionToken == "" {
        return &authv1.ValidateSessionResponse{
            ApiResponse: createErrorResponse("MISSING_TOKEN", "Session token is required"),
        }, errors.NewAppErrorFromContext(ctx, errors.ErrorCodeMissingField, "session token is required").ToGRPCError()
    }
    
    // OAuth sessions handled separately (legacy support)
    if strings.HasPrefix(req.SessionToken, "oauth_") {
        return s.validateOAuthSessionResponse(ctx, req.SessionToken)
    }
    
    // Validate with Kratos - single source of truth for identity
    session, err := s.oryClient.ValidateSession(ctx, req.SessionToken)
    if err != nil {
        s.logger.Error("Session validation failed", zap.Error(err))
        return &authv1.ValidateSessionResponse{
            ApiResponse: createErrorResponse("INVALID_SESSION", err.Error()),
        }, errors.NewAppErrorFromContext(ctx, errors.ErrorCodeUnauthorized, "invalid session").WithCause(err).ToGRPCError()
    }
    
    // Sync user data if needed (async)
    go s.syncUserDataIfNeeded(context.Background(), session.Identity.Id)
    
    // Get app-specific permissions and roles from backend database
    permissions, _ := s.repository.GetUserPermissions(ctx, session.Identity.Id)
    roles, _ := s.repository.GetUserRoles(ctx, session.Identity.Id)
    
    // Build response with combined Kratos session and app data
    sessionInfo := &authv1.SessionInfo{
        UserId:      session.Identity.Id,
        SessionId:   session.Id,
        IsActive:    *session.Active,
        CreatedAt:   timestamppb.New(*session.IssuedAt),
        ExpiresAt:   timestamppb.New(*session.ExpiresAt),
        Permissions: permissions,
        Roles:       roles,
        // Add AAL level for security checks
        AuthLevel:   string(*session.AuthenticatorAssuranceLevel),
    }
    
    return &authv1.ValidateSessionResponse{
        SessionInfo: sessionInfo,
        ApiResponse: createSuccessResponse("Session validated successfully"),
    }, nil
}

// syncUserDataIfNeeded ensures backend user data is in sync with Kratos
func (s *Service) syncUserDataIfNeeded(ctx context.Context, identityID string) {
    // Check if user exists in backend
    exists, err := s.repository.UserExists(ctx, identityID)
    if err != nil {
        s.logger.Error("Failed to check user existence", zap.Error(err))
        return
    }
    
    if !exists {
        // Get identity from Kratos
        identity, err := s.oryClient.GetIdentity(ctx, identityID)
        if err != nil {
            s.logger.Error("Failed to get identity from Kratos", zap.Error(err))
            return
        }
        
        // Create user in backend
        err = s.createUserFromIdentity(ctx, identity)
        if err != nil {
            s.logger.Error("Failed to sync user from Kratos", zap.Error(err))
        }
    }
}
```

### 4. Update Flutter Authentication Service

```dart
// lib/data/services/auth/kratos_auth_service.dart - Improved cookie handling
import 'package:dio/dio.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:path_provider/path_provider.dart';

class KratosAuthService {
  late PersistCookieJar _cookieJar;
  late Dio _dio;
  
  Future<void> initialize() async {
    // ... existing initialization ...
    
    // Platform-specific cookie storage
    if (!kIsWeb) {
      // Use persistent cookie storage for mobile
      final appDocDir = await getApplicationDocumentsDirectory();
      final cookiePath = '${appDocDir.path}/.cookies';
      _cookieJar = PersistCookieJar(
        storage: FileStorage(cookiePath),
        ignoreExpires: false, // Respect cookie expiry
      );
    } else {
      // Web platforms handle cookies automatically
      _cookieJar = CookieJar();
    }
    
    // Configure Dio with proper cookie handling
    _dio = Dio(BaseOptions(
      baseUrl: kratosUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      validateStatus: (status) => status! < 500,
      // Important: Allow cookies to be sent with requests
      extra: {
        'withCredentials': true, // For web platforms
      },
    ));
    
    // Add cookie manager
    _dio.interceptors.add(CookieManager(_cookieJar));
    
    // Add logging for debugging
    _dio.interceptors.add(LogInterceptor(
      requestHeader: true,
      responseHeader: true,
      requestBody: true,
      responseBody: true,
    ));
    
    // Initialize Kratos client with configured Dio
    _kratosClient = OryClient(
      basePathOverride: kratosUrl,
      dio: _dio,
    );
  }
  
  /// Get session token from cookies for backend API calls
  Future<String?> getSessionToken() async {
    try {
      if (kIsWeb) {
        // On web, cookies are handled by browser
        // Return a placeholder - actual cookie will be sent automatically
        return 'browser-managed';
      }
      
      // Get cookies for Kratos URL
      final uri = Uri.parse(_kratosClient.basePath);
      final cookies = await _cookieJar.loadForRequest(uri);
      
      // Find the session cookie
      final sessionCookie = cookies.firstWhereOrNull(
        (cookie) => cookie.name == 'ory_kratos_session',
      );
      
      return sessionCookie?.value;
    } catch (e) {
      _logger.e('Failed to get session token', error: e);
      return null;
    }
  }
  
  /// Validate current session
  Future<bool> validateSession() async {
    try {
      final response = await _frontendApi.toSession();
      
      if (response.data != null && response.data!.active == true) {
        _currentSession = response.data;
        _currentIdentity = _currentSession?.identity;
        return true;
      }
      
      return false;
    } catch (e) {
      _logger.d('Session validation failed: $e');
      return false;
    }
  }
  
  /// Sign in with identifier (email or username)
  Future<KratosAuthResponse> signInWithIdentifier({
    required String identifier,
    required String password,
  }) async {
    _ensureInitialized();
    
    try {
      // Initialize login flow
      final flow = await _initLoginFlow();
      
      // Submit credentials
      final passwordMethod = UpdateLoginFlowWithPasswordMethod((b) => b
        ..method = 'password'
        ..identifier = identifier
        ..password = password
        ..csrfToken = flow.ui.nodes
            .firstWhere((node) => node.attributes.oneOf.value is UiNodeInputAttributes && 
                        (node.attributes.oneOf.value as UiNodeInputAttributes).name == 'csrf_token')
            .attributes.oneOf.value.value); // Include CSRF token
      
      // Update login flow
      final response = await _frontendApi.updateLoginFlow(
        flow: flow.id,
        updateLoginFlowBody: UpdateLoginFlowBody((b) => b
          ..oneOf = OneOfDynamic(
            typeIndex: 5, // Password method index
            types: const [
              UpdateLoginFlowWithCodeMethod,
              UpdateLoginFlowWithIdentifierFirstMethod,
              UpdateLoginFlowWithLookupSecretMethod,
              UpdateLoginFlowWithOidcMethod,
              UpdateLoginFlowWithPasskeyMethod,
              UpdateLoginFlowWithPasswordMethod,
              UpdateLoginFlowWithSamlMethod,
              UpdateLoginFlowWithTotpMethod,
              UpdateLoginFlowWithWebAuthnMethod,
            ],
            value: passwordMethod,
          )),
      );
      
      // Handle successful login
      if (response.data?.session != null) {
        _currentSession = response.data!.session;
        _currentIdentity = _currentSession?.identity;
        
        // Session cookie is automatically stored by CookieManager
        
        _authStateController.add(KratosAuthState.signedIn);
        _userController.add(_currentIdentity);
        
        return KratosAuthResponse(
          identity: _currentIdentity,
          session: _currentSession,
        );
      }
      
      throw const AuthenticationException(message: 'Login failed');
    } catch (e) {
      _logger.e('Sign in error', error: e);
      throw AuthenticationException(message: 'Login failed: $e');
    }
  }
}
```

### 5. Update Oathkeeper Configuration

```yaml
# config/oathkeeper/oathkeeper.yml - Improved configuration
version: v0.40.0

serve:
  proxy:
    port: 80
    timeout:
      read: 30s
      write: 30s
      idle: 120s
    cors:
      enabled: true
      allowed_origins:
        - http://localhost:3000
        - https://hopenapp.com
      allowed_methods:
        - GET
        - POST
        - PUT
        - DELETE
        - PATCH
        - OPTIONS
      allowed_headers:
        - Authorization
        - Content-Type
        - Cookie
      exposed_headers:
        - Set-Cookie
      allow_credentials: true
      max_age: 86400

  api:
    port: 4456

authenticators:
  cookie_session:
    enabled: true
    config:
      check_session_url: http://kratos:4433/sessions/whoami
      preserve_path: true
      extra_from: "@this"
      subject_from: "identity.id"
      only:
        - ory_kratos_session
      force_method: GET
      forward_http_headers:
        - Cookie
        - X-Forwarded-For

  bearer_token:
    enabled: true
    config:
      check_session_url: http://kratos:4433/sessions/whoami
      token_from:
        header: Authorization
        scheme: Bearer
      preserve_path: true
      extra_from: "@this"
      subject_from: "identity.id"
      force_method: GET

  anonymous:
    enabled: true
    config:
      subject: anonymous

authorizers:
  allow:
    enabled: true

  remote_json:
    enabled: true
    config:
      remote: http://keto:4466/relation-tuples/check
      forward_response_headers_to_upstream:
        - X-Keto-Revision
      payload: |
        {
          "namespace": "{{ .MatchContext.Namespace }}",
          "object": "{{ .MatchContext.Object }}",
          "relation": "{{ .MatchContext.Relation }}",
          "subject_id": "{{ .Subject }}"
        }
      retry:
        give_up_after: 2s
        max_delay: 100ms

mutators:
  header:
    enabled: true
    config:
      headers:
        X-User-Id: "{{ .Subject }}"
        X-User-Email: "{{ .Extra.identity.traits.email }}"
        X-Session-Id: "{{ .Extra.session.id }}"

  id_token:
    enabled: true
    config:
      issuer_url: https://api.hopenapp.com
      jwks_url: file:///etc/secrets/jwks.json
      ttl: 1h
      claims: |
        {
          "iss": "https://api.hopenapp.com",
          "sub": "{{ .Subject }}",
          "aud": ["hopen-backend"],
          "iat": {{ .Now | unixEpoch }},
          "exp": {{ .Now | unixEpoch | add 3600 }},
          "jti": "{{ .RequestID }}",
          "email": "{{ .Extra.identity.traits.email }}",
          "email_verified": {{ .Extra.identity.verifiable_addresses.[0].verified }},
          "session_id": "{{ .Extra.session.id }}",
          "aal": "{{ .Extra.session.authenticator_assurance_level }}"
        }

  noop:
    enabled: true

errors:
  fallback:
    - json

  handlers:
    redirect:
      enabled: true
      config:
        to: http://localhost:3000/login
        when:
          - error:
              - unauthorized
              - forbidden
        return_to_query_param: return_to

    json:
      enabled: true
      config:
        verbose: false # Set to true only in development
        when:
          - error:
              - unauthorized
              - forbidden
              - internal_server_error
              - not_found

access_rules:
  repositories:
    - file:///etc/oathkeeper/access-rules.yml
  matching_strategy: regexp

log:
  level: info
  format: json
  leak_sensitive_values: false
```

### 6. Improved Access Rules

```yaml
# config/oathkeeper/access-rules.yml - Better rule configuration
# Public endpoints - no authentication required
- id: public-health
  description: Health check endpoints
  upstream:
    preserve_host: true
    url: http://kong:8000
  match:
    url: <(http|https)>://<[^/]+>/(health|health/ready|health/alive)
    methods:
      - GET
      - HEAD
  authenticators:
    - handler: anonymous
  authorizer:
    handler: allow
  mutators:
    - handler: noop

# Authentication endpoints - special handling
- id: auth-endpoints
  description: Kratos authentication flows
  upstream:
    preserve_host: true
    url: http://kratos:4433
  match:
    url: <(http|https)>://<[^/]+>/self-service/(login|registration|recovery|verification|logout|settings)/<.*>
    methods:
      - GET
      - POST
  authenticators:
    - handler: anonymous
  authorizer:
    handler: allow
  mutators:
    - handler: noop

# Session endpoint - for session checks
- id: session-whoami
  description: Session validation endpoint
  upstream:
    preserve_host: true
    url: http://kratos:4433
  match:
    url: <(http|https)>://<[^/]+>/sessions/whoami
    methods:
      - GET
  authenticators:
    - handler: cookie_session
    - handler: bearer_token
  authorizer:
    handler: allow
  mutators:
    - handler: header

# Protected API endpoints
- id: api-protected
  description: Protected API endpoints requiring authentication
  upstream:
    preserve_host: true
    url: http://kong:8000
  match:
    url: <(http|https)>://<[^/]+>/api/v1/<.*>
    methods:
      - GET
      - POST
      - PUT
      - PATCH
      - DELETE
  authenticators:
    - handler: cookie_session
    - handler: bearer_token
  authorizer:
    handler: allow
  mutators:
    - handler: header
    - handler: id_token

# Admin endpoints - require additional authorization
- id: admin-endpoints
  description: Admin API endpoints with Keto authorization
  upstream:
    preserve_host: true
    url: http://kong:8000
  match:
    url: <(http|https)>://<[^/]+>/api/v1/admin/<.*>
    methods:
      - GET
      - POST
      - PUT
      - PATCH
      - DELETE
  authenticators:
    - handler: cookie_session
    - handler: bearer_token
  authorizer:
    handler: remote_json
    config:
      namespace: "permissions"
      object: "admin"
      relation: "access"
  mutators:
    - handler: header
    - handler: id_token
  errors:
    - handler: json
      config:
        when:
          - error:
              - forbidden
        verbose: true

# WebSocket endpoints
- id: websocket-endpoints
  description: WebSocket connections
  upstream:
    preserve_host: true
    url: http://kong:8000
  match:
    url: <(ws|wss)>://<[^/]+>/ws/<.*>
    methods:
      - GET
  authenticators:
    - handler: bearer_token
  authorizer:
    handler: allow
  mutators:
    - handler: header
```

### 7. Add Keto Permission Checks

```go
// pkg/authorization/permissions.go
package authorization

import (
    "context"
    "fmt"
    
    rts "github.com/ory/keto/proto/ory/keto/relation_tuples/v1alpha2"
    "google.golang.org/grpc"
)

type PermissionChecker struct {
    ketoClient rts.CheckServiceClient
}

func NewPermissionChecker(ketoAddr string) (*PermissionChecker, error) {
    conn, err := grpc.Dial(ketoAddr, grpc.WithInsecure())
    if err != nil {
        return nil, fmt.Errorf("failed to connect to Keto: %w", err)
    }
    
    return &PermissionChecker{
        ketoClient: rts.NewCheckServiceClient(conn),
    }, nil
}

// CheckPermission verifies if a user has a specific permission
func (p *PermissionChecker) CheckPermission(ctx context.Context, userID, namespace, object, relation string) (bool, error) {
    req := &rts.CheckRequest{
        Namespace: namespace,
        Object:    object,
        Relation:  relation,
        Subject: &rts.Subject{
            Ref: &rts.Subject_Id{
                Id: userID,
            },
        },
    }
    
    resp, err := p.ketoClient.Check(ctx, req)
    if err != nil {
        return false, fmt.Errorf("permission check failed: %w", err)
    }
    
    return resp.Allowed, nil
}

// Example usage in service
func (s *Service) checkUserPermission(ctx context.Context, userID, permission string) bool {
    allowed, err := s.permissionChecker.CheckPermission(
        ctx,
        userID,
        "permissions",
        permission,
        "access",
    )
    if err != nil {
        s.logger.Error("Permission check failed", zap.Error(err))
        return false
    }
    return allowed
}
```

## Security Best Practices Checklist

### Session Security
- [x] Use HTTP-only cookies for session storage
- [x] Enable Secure flag for cookies in production (HTTPS)
- [x] Configure proper SameSite cookie attribute
- [x] Set appropriate session lifespans
- [x] Implement session refresh mechanism
- [x] Use CSRF tokens for state-changing operations

### Authentication Flow
- [x] Use Kratos native flows (not custom session creation)
- [x] Validate sessions through Kratos whoami endpoint
- [x] Handle session expiry gracefully
- [x] Implement proper logout (invalidate sessions)
- [x] Support multi-factor authentication (MFA)

### Authorization
- [x] Use Keto for fine-grained permissions
- [x] Implement role-based access control (RBAC)
- [x] Check permissions at API gateway (Oathkeeper)
- [x] Verify permissions in microservices
- [x] Audit authorization decisions

### Integration
- [x] Single source of truth for identity (Kratos)
- [x] Proper error handling and logging
- [x] Secure communication between services
- [x] Token rotation and refresh
- [x] Rate limiting and abuse prevention

## Migration Plan

1. **Phase 1: Configuration Updates**
   - Update Kratos configuration with session settings
   - Configure Oathkeeper with improved rules
   - Set up proper cookie handling

2. **Phase 2: Backend Updates**
   - Remove custom session creation logic
   - Implement proper session validation
   - Add Keto permission checks

3. **Phase 3: Frontend Updates**
   - Implement persistent cookie storage
   - Update authentication flows
   - Add CSRF token handling

4. **Phase 4: Testing & Validation**
   - Test all authentication flows
   - Verify session persistence
   - Validate permission checks
   - Security audit

## Monitoring & Observability

### Key Metrics to Track
- Session creation rate
- Session validation failures
- Authentication errors by type
- Permission check latency
- Token refresh rate

### Logging Requirements
- Log all authentication attempts
- Track session lifecycle events
- Record permission checks
- Monitor suspicious patterns

## Conclusion

By implementing these improvements, your authentication system will:
1. Follow Ory best practices
2. Provide better security through proper session management
3. Eliminate synchronization issues between Kratos and backend
4. Support proper cookie-based authentication
5. Enable fine-grained authorization with Keto
6. Handle edge cases and errors gracefully

The key principle is to use Kratos as the single source of truth for identity and sessions, while your backend handles application-specific data and business logic.