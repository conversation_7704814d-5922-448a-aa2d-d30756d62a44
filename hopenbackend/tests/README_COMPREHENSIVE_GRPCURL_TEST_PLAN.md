# 🧪 Comprehensive gRPC Test Plan for Hopen Backend

This directory contains a complete end-to-end testing suite for the Hopen backend microservices architecture using grpcurl. The test plan validates all critical user journeys and business logic flows.

## 🎯 Overview

The comprehensive test plan covers:

- **Health Checks**: All microservices health validation
- **Authentication Flow**: Registration, login, session management, password reset
- **User Management**: Profile creation, contact requests, relationship management
- **Bubble Lifecycle**: Creation, invitations, voting, dissolution vs expiration
- **Real-time Features**: Messaging, presence updates
- **Media Services**: File upload/download functionality
- **Security Testing**: Password breach checks, authorization validation
- **Error Handling**: Edge cases and graceful failure scenarios

## 📁 Files

### Core Test Scripts

- **`comprehensive_grpcurl_test_runner.sh`** - Master test execution script with reporting
- **`enhanced_grpcurl_test_plan.sh`** - Complete test implementation
- **`README_COMPREHENSIVE_GRPCURL_TEST_PLAN.md`** - This documentation

### Supporting Files

- **`logs/`** - Test execution logs (auto-created)
- **`reports/`** - HTML test reports (auto-created)

## 🚀 Quick Start

### Prerequisites

1. **Backend Services Running**:
   ```bash
   cd hopenbackend
   docker-compose up -d
   ```

2. **Required Tools**:
   ```bash
   # Install grpcurl
   brew install grpcurl  # macOS
   # or
   go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest

   # Install jq for JSON parsing
   brew install jq  # macOS
   ```

3. **Verify Services**:
   ```bash
   # Check Kong Gateway
   curl localhost:8001/status

   # Check service accessibility
   grpcurl -plaintext localhost:4000 list
   ```

### Running Tests

#### Option 1: Quick Test (Recommended for first run)
```bash
cd hopenbackend/tests
./comprehensive_grpcurl_test_runner.sh --quick
```

#### Option 2: Full Test Suite
```bash
cd hopenbackend/tests
./comprehensive_grpcurl_test_runner.sh --full
```

#### Option 3: Full Tests with HTML Report
```bash
cd hopenbackend/tests
./comprehensive_grpcurl_test_runner.sh --full --report
```

#### Option 4: Verbose Output
```bash
cd hopenbackend/tests
./comprehensive_grpcurl_test_runner.sh --full --verbose
```

## 📊 Test Coverage

### Step 1: Initial Health Checks
- Kong Gateway accessibility
- All microservice health endpoints
- gRPC service availability

### Step 2: Authentication and User Lifecycle
- User registration (Alice, Bob, Charlie, David)
- Login and session token capture
- Session validation
- Password breach check (HIBP integration)
- Password reset flow
- Login with new password

### Step 3: User Profile & Social Graph
- Profile retrieval
- Contact request sending
- Contact request acceptance
- Relationship status verification

### Step 4: Full Bubble Lifecycle
- Bubble start request creation
- Bubble start request acceptance
- Bubble member verification
- Bubble invitation flow
- Voting on invitations
- Member addition verification
- Bubble dissolution testing
- Bubble expiration and maybe_friend flow

### Step 5: Realtime, Media, and Other Services
- Presence updates
- Batch presence checking
- Real-time messaging
- Chat message retrieval
- File upload testing
- File download testing

### Step 6: Logout and Session Cleanup
- User logout
- Session invalidation verification

### Step 7: Edge Cases and Error Handling
- Duplicate request handling
- Invalid ID handling
- Unauthorized access testing

## 🔍 Critical Business Logic Validation

The test plan specifically validates these critical distinctions:

### Bubble Dissolution vs Expiration
- **Dissolution** (member leaves 2-person bubble) → Maintains existing "contact" relationship
- **Expiration** (90-day timer) → Creates "maybe_friend" relationships → Potential "friend" status

### Relationship Progression
1. **Stranger** → Contact Request → **Contact**
2. **Contact** → Bubble Creation → **Contact** (with bubble context)
3. **Contact** → Bubble Expiration → **Maybe_Friend** → Mutual Acceptance → **Friend**

### Authentication Security
- Password breach detection using HIBP
- Session management and validation
- Secure password reset flow
- Authorization checks

## 📈 Understanding Test Results

### Success Indicators
```
✅ All tests passed! Backend is ready for production.
✅ All critical business logic flows verified successfully.
```

### Failure Analysis
- Check the generated log file in `logs/` directory
- Review failed test details
- Verify service status: `docker-compose ps`
- Check service logs: `docker-compose logs <service-name>`

### Common Issues and Solutions

#### Kong Gateway Not Accessible
```bash
# Check Kong status
curl localhost:8001/status

# Restart Kong if needed
docker-compose restart kong
```

#### Service Not Responding
```bash
# Check service status
docker-compose ps

# Check specific service logs
docker-compose logs auth
docker-compose logs user
```

#### grpcurl Connection Issues
```bash
# Test direct service connection
grpcurl -plaintext localhost:4000 list

# Check if service is listening
netstat -an | grep 4000
```

## 🛠 Customization

### Adding New Tests
1. Edit `enhanced_grpcurl_test_plan.sh`
2. Add new test functions following the existing pattern
3. Update the main execution function
4. Test your changes

### Modifying Test Data
- Update user registration data in the `test_user_registration()` function
- Modify test messages and bubble names as needed
- Adjust timeouts in the configuration section

### Environment Configuration
- Update service endpoints in the configuration section
- Modify Kong Gateway addresses if needed
- Adjust proto file paths if different

## 📝 Logs and Reports

### Log Files
- Location: `logs/test_run_YYYYMMDD_HHMMSS.log`
- Contains: Complete test execution output
- Format: Timestamped entries with color codes

### HTML Reports
- Location: `reports/test_report_YYYYMMDD_HHMMSS.html`
- Contains: Formatted test results with styling
- Generated: When using `--report` flag

## 🔧 Troubleshooting

### Test Script Issues
```bash
# Make scripts executable
chmod +x comprehensive_grpcurl_test_runner.sh
chmod +x enhanced_grpcurl_test_plan.sh

# Check script syntax
bash -n enhanced_grpcurl_test_plan.sh
```

### Service Discovery Issues
```bash
# Verify Docker network
docker network ls
docker network inspect hopenbackend_default

# Check service connectivity
docker exec -it hopenbackend_auth_1 ping user
```

### Authentication Issues
- Verify Ory Kratos is running
- Check authentication service logs
- Ensure proper session token handling

## 🎯 Production Readiness

When all tests pass, your backend is validated for:
- ✅ Complete user authentication flows
- ✅ Social relationship management
- ✅ Bubble lifecycle management
- ✅ Real-time communication
- ✅ Media handling
- ✅ Security compliance
- ✅ Error handling and resilience

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review service logs for specific errors
3. Verify all prerequisites are met
4. Test individual services with direct grpcurl calls

---

**Happy Testing! 🚀**
