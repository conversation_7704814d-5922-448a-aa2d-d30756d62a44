package search_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"hopenbackend/microservices/search"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
)

// setupTestDatabase creates a test database connection
func setupTestDatabase(t *testing.T) (*database.PostgreSQLClient, error) {
	cfg := &config.PostgreSQLConfig{
		Host:                  "localhost",
		Port:                  5432,
		Database:              "hopen_test",
		Username:              "hopen",
		Password:              "hopen123",
		SSLMode:               "disable",
		MaxConnections:        10,
		MinConnections:        2,
		MaxConnectionLifetime: time.Hour,
		MaxConnectionIdleTime: 30 * time.Minute,
		HealthCheckPeriod:     30 * time.Second,
	}

	logger := zap.NewNop()
	return database.NewPostgreSQLClient(cfg, logger)
}

func TestPostgreSQLSearchBubbles(t *testing.T) {
	// Skip if no database connection available
	if testing.Short() {
		t.Skip("Skipping database integration test in short mode")
	}

	// Setup test database
	client, err := setupTestDatabase(t)
	if err != nil {
		t.Skip("Database not available for testing:", err)
	}
	defer client.Close()

	logger := zap.NewNop()
	repo := search.NewPostgreSQLRepository(client.Pool, logger)

	ctx := context.Background()

	t.Run("SearchBubbles with empty query", func(t *testing.T) {
		filter := &search.BubbleSearchFilter{
			Query:    "",
			UserID:   "test-user-id",
			Page:     1,
			PageSize: 10,
		}

		results, err := repo.SearchBubbles(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		// Results can be empty if no bubbles exist
	})

	t.Run("SearchBubbles with query", func(t *testing.T) {
		filter := &search.BubbleSearchFilter{
			Query:    "test bubble",
			UserID:   "test-user-id",
			Page:     1,
			PageSize: 10,
		}

		results, err := repo.SearchBubbles(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, results)
	})

	t.Run("SearchBubbles with filters", func(t *testing.T) {
		now := time.Now()
		yesterday := now.Add(-24 * time.Hour)

		filter := &search.BubbleSearchFilter{
			Query:          "test",
			UserID:         "test-user-id",
			Page:           1,
			PageSize:       5,
			IncludePrivate: false,
			StatusFilter:   []string{"active"},
			CreatedAfter:   &yesterday,
			MinMembers:     1,
			MaxMembers:     50,
		}

		results, err := repo.SearchBubbles(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.LessOrEqual(t, len(results), 5) // Should respect page size
	})
}

func TestPostgreSQLGlobalSearch(t *testing.T) {
	// Skip if no database connection available
	if testing.Short() {
		t.Skip("Skipping database integration test in short mode")
	}

	// Setup test database
	client, err := setupTestDatabase(t)
	if err != nil {
		t.Skip("Database not available for testing:", err)
	}
	defer client.Close()

	logger := zap.NewNop()
	repo := search.NewPostgreSQLRepository(client.Pool, logger)

	ctx := context.Background()

	t.Run("GlobalSearch with empty query", func(t *testing.T) {
		filter := &search.GlobalSearchFilter{
			Query:    "",
			UserID:   "test-user-id",
			Page:     1,
			PageSize: 10,
		}

		results, err := repo.GlobalSearch(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, results)
	})

	t.Run("GlobalSearch with query", func(t *testing.T) {
		filter := &search.GlobalSearchFilter{
			Query:    "test",
			UserID:   "test-user-id",
			Page:     1,
			PageSize: 10,
		}

		results, err := repo.GlobalSearch(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, results)
	})

	t.Run("GlobalSearch with specific content types", func(t *testing.T) {
		filter := &search.GlobalSearchFilter{
			Query:        "test",
			UserID:       "test-user-id",
			Page:         1,
			PageSize:     10,
			ContentTypes: []string{"user", "bubble"},
		}

		results, err := repo.GlobalSearch(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, results)

		// Verify only requested content types are returned
		for _, result := range results {
			assert.Contains(t, []string{"user", "bubble"}, result.ContentType)
		}
	})

	t.Run("GlobalSearch pagination", func(t *testing.T) {
		filter := &search.GlobalSearchFilter{
			Query:    "test",
			UserID:   "test-user-id",
			Page:     1,
			PageSize: 5,
		}

		results, err := repo.GlobalSearch(ctx, filter)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.LessOrEqual(t, len(results), 5) // Should respect page size
	})
}

func TestSearchFallbackIntegration(t *testing.T) {
	// This test verifies that the PostgreSQL fallback works correctly
	// when integrated with the hybrid repository pattern

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Setup test database
	client, err := setupTestDatabase(t)
	if err != nil {
		t.Skip("Database not available for testing:", err)
	}
	defer client.Close()

	logger := zap.NewNop()
	postgresRepo := search.NewPostgreSQLRepository(client.Pool, logger)

	ctx := context.Background()

	t.Run("Bubble search fallback functionality", func(t *testing.T) {
		filter := &search.BubbleSearchFilter{
			Query:    "fallback test",
			UserID:   "test-user-id",
			Page:     1,
			PageSize: 10,
		}

		// This should work without errors, demonstrating the fallback is functional
		results, err := postgresRepo.SearchBubbles(ctx, filter)
		require.NoError(t, err, "PostgreSQL bubble search fallback should work")
		assert.NotNil(t, results)
	})

	t.Run("Global search fallback functionality", func(t *testing.T) {
		filter := &search.GlobalSearchFilter{
			Query:    "fallback test",
			UserID:   "test-user-id",
			Page:     1,
			PageSize: 10,
		}

		// This should work without errors, demonstrating the fallback is functional
		results, err := postgresRepo.GlobalSearch(ctx, filter)
		require.NoError(t, err, "PostgreSQL global search fallback should work")
		assert.NotNil(t, results)
	})
}
