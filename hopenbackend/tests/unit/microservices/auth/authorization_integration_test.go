package auth_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"google.golang.org/grpc/codes"

	"hopenbackend/pkg/auth"
	authv1 "hopenbackend/protos/gen"
	callv1 "hopenbackend/protos/gen"
	mediav1 "hopenbackend/protos/gen"
	notificationv1 "hopenbackend/protos/gen"
	syncv1 "hopenbackend/protos/gen"
	userv1 "hopenbackend/protos/gen"
)

// AuthorizationIntegrationTestSuite tests authorization across all services
type AuthorizationIntegrationTestSuite struct {
	suite.Suite
}

func TestAuthorizationIntegrationSuite(t *testing.T) {
	suite.Run(t, new(AuthorizationIntegrationTestSuite))
}

func (s *AuthorizationIntegrationTestSuite) SetupTest() {
	// Setup test environment
}

// Helper function to create context with user ID
func createContextWithUser(userID string) context.Context {
	ctx := context.Background()
	return auth.WithUserID(ctx, userID)
}

// Helper function to create context without authentication
func createUnauthenticatedContext() context.Context {
	return context.Background()
}

// ============================================================================
// USER SERVICE AUTHORIZATION TESTS
// ============================================================================

func (s *AuthorizationIntegrationTestSuite) TestUserService_GetUser_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		requestedUser     string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can access own profile",
			authenticatedUser: "user-123",
			requestedUser:     "user-123",
			expectError:       false,
			description:       "Users should be able to access their own profile",
		},
		{
			name:              "user can access public profile",
			authenticatedUser: "user-123",
			requestedUser:     "user-456",
			expectError:       false,
			description:       "Users should be able to access public profiles of other users",
		},
		{
			name:              "unauthenticated access denied",
			authenticatedUser: "",
			requestedUser:     "user-123",
			expectError:       true,
			expectedCode:      codes.Unauthenticated,
			description:       "Unauthenticated requests should be denied",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &userv1.GetUserRequest{
				UserId: tc.requestedUser,
			}

			// Validate request structure
			s.Require().NotNil(req)
			s.Require().Equal(tc.requestedUser, req.UserId)

			// Test authorization logic would go here
			// For now, we validate the test case structure
			if tc.expectError {
				s.Require().NotEqual(codes.OK, tc.expectedCode)
			}
		})
	}
}

func (s *AuthorizationIntegrationTestSuite) TestUserService_GetUserByEmail_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		requestedEmail    string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can access own profile by email",
			authenticatedUser: "user-123",
			requestedEmail:    "<EMAIL>",
			expectError:       false,
			description:       "Users should be able to access their own profile by email",
		},
		{
			name:              "admin can access any profile by email",
			authenticatedUser: "admin-123",
			requestedEmail:    "<EMAIL>",
			expectError:       false,
			description:       "Admins should be able to access any profile by email",
		},
		{
			name:              "regular user cannot access other profiles by email",
			authenticatedUser: "user-123",
			requestedEmail:    "<EMAIL>",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Regular users should not be able to access other profiles by email",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &userv1.GetUserByEmailRequest{
				Email: tc.requestedEmail,
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.requestedEmail, req.Email)
		})
	}
}

func (s *AuthorizationIntegrationTestSuite) TestUserService_BlockUser_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		blockerID         string
		blockedID         string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can block another user",
			authenticatedUser: "user-123",
			blockerID:         "user-123",
			blockedID:         "user-456",
			expectError:       false,
			description:       "Users should be able to block other users",
		},
		{
			name:              "user cannot block on behalf of others",
			authenticatedUser: "user-123",
			blockerID:         "user-456",
			blockedID:         "user-789",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to block on behalf of others",
		},
		{
			name:              "unauthenticated block denied",
			authenticatedUser: "",
			blockerID:         "user-123",
			blockedID:         "user-456",
			expectError:       true,
			expectedCode:      codes.Unauthenticated,
			description:       "Unauthenticated block requests should be denied",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &userv1.BlockUserRequest{
				UserId:        tc.blockerID,
				BlockedUserId: tc.blockedID,
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.blockerID, req.UserId)
			s.Require().Equal(tc.blockedID, req.BlockedUserId)
		})
	}
}

func (s *AuthorizationIntegrationTestSuite) TestUserService_GetFriends_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		requestedUser     string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can get own friends",
			authenticatedUser: "user-123",
			requestedUser:     "user-123",
			expectError:       false,
			description:       "Users should be able to get their own friends list",
		},
		{
			name:              "user cannot get others' friends",
			authenticatedUser: "user-123",
			requestedUser:     "user-456",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to get others' friends lists",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &userv1.GetFriendsRequest{
				UserId:   tc.requestedUser,
				Page:     1,
				PageSize: 20,
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.requestedUser, req.UserId)
		})
	}
}

// ============================================================================
// AUTH SERVICE AUTHORIZATION TESTS
// ============================================================================

func (s *AuthorizationIntegrationTestSuite) TestAuthService_GetDefaultRole_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		isAdmin           bool
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "admin can get default role",
			authenticatedUser: "admin-123",
			isAdmin:           true,
			expectError:       false,
			description:       "Admins should be able to get default role",
		},
		{
			name:              "regular user cannot get default role",
			authenticatedUser: "user-123",
			isAdmin:           false,
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Regular users should not be able to get default role",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &authv1.GetDefaultRoleRequest{}

			s.Require().NotNil(req)
		})
	}
}

func (s *AuthorizationIntegrationTestSuite) TestAuthService_SetDefaultRole_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		isAdmin           bool
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "admin can set default role",
			authenticatedUser: "admin-123",
			isAdmin:           true,
			expectError:       false,
			description:       "Admins should be able to set default role",
		},
		{
			name:              "regular user cannot set default role",
			authenticatedUser: "user-123",
			isAdmin:           false,
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Regular users should not be able to set default role",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &authv1.SetDefaultRoleRequest{
				RoleId: "user-role-id",
			}

			s.Require().NotNil(req)
			s.Require().Equal("user-role-id", req.RoleId)
		})
	}
}

// ============================================================================
// CALL SERVICE AUTHORIZATION TESTS
// ============================================================================

func (s *AuthorizationIntegrationTestSuite) TestCallService_LeaveCall_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		callID            string
		userID            string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can leave own call",
			authenticatedUser: "user-123",
			callID:            "call-456",
			userID:            "user-123",
			expectError:       false,
			description:       "Users should be able to leave calls they are in",
		},
		{
			name:              "user cannot leave call for others",
			authenticatedUser: "user-123",
			callID:            "call-456",
			userID:            "user-789",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to leave calls for others",
		},
		{
			name:              "unauthenticated leave denied",
			authenticatedUser: "",
			callID:            "call-456",
			userID:            "user-123",
			expectError:       true,
			expectedCode:      codes.Unauthenticated,
			description:       "Unauthenticated leave requests should be denied",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &callv1.LeaveCallRequest{
				CallId: tc.callID,
				UserId: tc.userID,
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.callID, req.CallId)
			s.Require().Equal(tc.userID, req.UserId)
		})
	}
}

func (s *AuthorizationIntegrationTestSuite) TestCallService_EndCall_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		callID            string
		userID            string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "call creator can end call",
			authenticatedUser: "user-123",
			callID:            "call-456",
			userID:            "user-123",
			expectError:       false,
			description:       "Call creators should be able to end their calls",
		},
		{
			name:              "admin can end any call",
			authenticatedUser: "admin-123",
			callID:            "call-456",
			userID:            "admin-123",
			expectError:       false,
			description:       "Admins should be able to end any call",
		},
		{
			name:              "regular user cannot end others' calls",
			authenticatedUser: "user-123",
			callID:            "call-456",
			userID:            "user-789",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Regular users should not be able to end others' calls",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &callv1.EndCallRequest{
				CallId: tc.callID,
				UserId: tc.userID,
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.callID, req.CallId)
			s.Require().Equal(tc.userID, req.UserId)
		})
	}
}

// ============================================================================
// MEDIA SERVICE AUTHORIZATION TESTS
// ============================================================================

func (s *AuthorizationIntegrationTestSuite) TestMediaService_UploadFile_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		uploaderID        string
		bubbleID          string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can upload to bubble they're in",
			authenticatedUser: "user-123",
			uploaderID:        "user-123",
			bubbleID:          "bubble-456",
			expectError:       false,
			description:       "Users should be able to upload files to bubbles they're members of",
		},
		{
			name:              "user cannot upload for others",
			authenticatedUser: "user-123",
			uploaderID:        "user-789",
			bubbleID:          "bubble-456",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to upload files for others",
		},
		{
			name:              "unauthenticated upload denied",
			authenticatedUser: "",
			uploaderID:        "user-123",
			bubbleID:          "bubble-456",
			expectError:       true,
			expectedCode:      codes.Unauthenticated,
			description:       "Unauthenticated upload requests should be denied",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &mediav1.UploadFileRequest{
				UserId:   tc.uploaderID,
				BubbleId: tc.bubbleID,
				FileName: "test.jpg",
				FileData: []byte("test-data"),
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.uploaderID, req.UserId)
			s.Require().Equal(tc.bubbleID, req.BubbleId)
		})
	}
}

func (s *AuthorizationIntegrationTestSuite) TestMediaService_DeleteFile_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		fileID            string
		userID            string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "file owner can delete file",
			authenticatedUser: "user-123",
			fileID:            "file-456",
			userID:            "user-123",
			expectError:       false,
			description:       "File owners should be able to delete their files",
		},
		{
			name:              "admin can delete any file",
			authenticatedUser: "admin-123",
			fileID:            "file-456",
			userID:            "admin-123",
			expectError:       false,
			description:       "Admins should be able to delete any file",
		},
		{
			name:              "user cannot delete others' files",
			authenticatedUser: "user-123",
			fileID:            "file-456",
			userID:            "user-789",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to delete others' files",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &mediav1.DeleteFileRequest{
				FileId: tc.fileID,
				UserId: tc.userID,
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.fileID, req.FileId)
			s.Require().Equal(tc.userID, req.UserId)
		})
	}
}

// ============================================================================
// NOTIFICATION SERVICE AUTHORIZATION TESTS
// ============================================================================

func (s *AuthorizationIntegrationTestSuite) TestNotificationService_RegisterFCMToken_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		userID            string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can register own FCM token",
			authenticatedUser: "user-123",
			userID:            "user-123",
			expectError:       false,
			description:       "Users should be able to register their own FCM tokens",
		},
		{
			name:              "user cannot register FCM token for others",
			authenticatedUser: "user-123",
			userID:            "user-456",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to register FCM tokens for others",
		},
		{
			name:              "unauthenticated registration denied",
			authenticatedUser: "",
			userID:            "user-123",
			expectError:       true,
			expectedCode:      codes.Unauthenticated,
			description:       "Unauthenticated FCM token registration should be denied",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &notificationv1.RegisterFCMTokenRequest{
				UserId:   tc.userID,
				FcmToken: "test-fcm-token",
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.userID, req.UserId)
			s.Require().Equal("test-fcm-token", req.FcmToken)
		})
	}
}

func (s *AuthorizationIntegrationTestSuite) TestNotificationService_SendPushNotification_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		senderID          string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can send own push notification",
			authenticatedUser: "user-123",
			senderID:          "user-123",
			expectError:       false,
			description:       "Users should be able to send push notifications as themselves",
		},
		{
			name:              "user cannot send push notification for others",
			authenticatedUser: "user-123",
			senderID:          "user-456",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to send push notifications for others",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &notificationv1.SendPushNotificationRequest{
				UserId: tc.senderID,
				Title:  "Test Notification",
				Body:   "Test message",
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.senderID, req.UserId)
		})
	}
}

// ============================================================================
// SYNC SERVICE AUTHORIZATION TESTS
// ============================================================================

func (s *AuthorizationIntegrationTestSuite) TestSyncService_PushSyncData_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		userID            string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can push own sync data",
			authenticatedUser: "user-123",
			userID:            "user-123",
			expectError:       false,
			description:       "Users should be able to push their own sync data",
		},
		{
			name:              "user cannot push sync data for others",
			authenticatedUser: "user-123",
			userID:            "user-456",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to push sync data for others",
		},
		{
			name:              "unauthenticated push denied",
			authenticatedUser: "",
			userID:            "user-123",
			expectError:       true,
			expectedCode:      codes.Unauthenticated,
			description:       "Unauthenticated sync data push should be denied",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &syncv1.PushSyncDataRequest{
				UserId:   tc.userID,
				DeviceId: "test-device",
				SyncData: &syncv1.SyncData{},
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.userID, req.UserId)
		})
	}
}

func (s *AuthorizationIntegrationTestSuite) TestSyncService_ClearSyncData_Authorization() {
	testCases := []struct {
		name              string
		authenticatedUser string
		userID            string
		expectError       bool
		expectedCode      codes.Code
		description       string
	}{
		{
			name:              "user can clear own sync data",
			authenticatedUser: "user-123",
			userID:            "user-123",
			expectError:       false,
			description:       "Users should be able to clear their own sync data",
		},
		{
			name:              "user cannot clear sync data for others",
			authenticatedUser: "user-123",
			userID:            "user-456",
			expectError:       true,
			expectedCode:      codes.PermissionDenied,
			description:       "Users should not be able to clear sync data for others",
		},
		{
			name:              "unauthenticated clear denied",
			authenticatedUser: "",
			userID:            "user-123",
			expectError:       true,
			expectedCode:      codes.Unauthenticated,
			description:       "Unauthenticated sync data clear should be denied",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			req := &syncv1.ClearSyncDataRequest{
				UserId: tc.userID,
			}

			s.Require().NotNil(req)
			s.Require().Equal(tc.userID, req.UserId)
		})
	}
}

// ============================================================================
// INTEGRATION TEST HELPERS
// ============================================================================

func (s *AuthorizationIntegrationTestSuite) TestAuthorizationTestCoverage() {
	// This test ensures we have covered all the critical authorization scenarios
	// that were identified in the original compliance analysis

	criticalMethods := []string{
		// UserService methods
		"GetUser",
		"GetUserByEmail",
		"BlockUser",
		"UnblockUser",
		"RemoveFriend",
		"GetFriends",
		"GetContacts",
		"SendFriendRequest",
		"GetFriendsByLocation",
		"GetFriendsByInterests",
		"GetFriendsByActivityLevel",
		"GetFriendsByEngagementLevel",
		"GetFriendsByCompatibilityScore",
		"GetFriendsBySocialDistance",

		// AuthService methods
		"GetDefaultRole",
		"SetDefaultRole",

		// CallService methods
		"LeaveCall",
		"EndCall",

		// MediaService methods
		"UploadFile",
		"DeleteFile",

		// NotificationService methods
		"RegisterFCMToken",
		"SendPushNotification",

		// SyncService methods
		"PushSyncData",
		"ClearSyncData",
	}

	s.Require().Greater(len(criticalMethods), 20, "Should have comprehensive test coverage for critical methods")

	// Verify that all critical authorization patterns are tested
	authorizationPatterns := []string{
		"self-only access",
		"admin permissions",
		"public/private profiles",
		"ownership validation",
		"unauthenticated access denial",
	}

	s.Require().Equal(5, len(authorizationPatterns), "Should test all critical authorization patterns")
}
