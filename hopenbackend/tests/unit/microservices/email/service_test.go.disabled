package email_test

import (
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"

	"hopenbackend/microservices/email"
	emailv1 "hopenbackend/protos/gen"
)

type EmailServiceTestSuite struct {
	suite.Suite
	service *email.Service
}

func TestEmailServiceTestSuite(t *testing.T) {
	suite.Run(t, new(EmailServiceTestSuite))
}

func (s *EmailServiceTestSuite) SetupTest() {
	// Note: This would need actual service setup in a real test
	logger := zap.NewNop()
	s.service = &email.Service{}
	s.Require().NotNil(logger)
}

func (s *EmailServiceTestSuite) TestSendEmail() {
	// Test successful email sending
	req := &emailv1.SendEmailRequest{
		ToEmails:  []string{"<EMAIL>"},
		Subject:   "Test Subject",
		TextBody:  "Test email body",
		FromEmail: "<EMAIL>",
	}

	s.Require().NotNil(req)
	s.Require().Equal([]string{"<EMAIL>"}, req.ToEmails)
	s.Require().Equal("Test Subject", req.Subject)
	s.Require().Equal("Test email body", req.TextBody)
	s.Require().Equal("<EMAIL>", req.FromEmail)
}

func (s *EmailServiceTestSuite) TestSendTemplateEmail() {
	// Test successful template email sending
	req := &emailv1.SendTemplateEmailRequest{
		To:         "<EMAIL>",
		TemplateId: "welcome-email",
		TemplateData: map[string]string{
			"username": "johndoe",
			"app_name": "Hopen",
		},
		From: "<EMAIL>",
	}

	s.Require().NotNil(req)
	s.Require().Equal("<EMAIL>", req.To)
	s.Require().Equal("welcome-email", req.TemplateId)
	s.Require().NotNil(req.TemplateData)
	s.Require().Equal("johndoe", req.TemplateData["username"])
	s.Require().Equal("Hopen", req.TemplateData["app_name"])
}

func (s *EmailServiceTestSuite) TestSendBulkEmail() {
	// Test successful bulk email sending
	req := &emailv1.SendBulkEmailRequest{
		Recipients: []string{"<EMAIL>", "<EMAIL>"},
		Subject:    "Bulk Email Subject",
		Body:       "Bulk email body",
		From:       "<EMAIL>",
	}

	s.Require().NotNil(req)
	s.Require().Len(req.Recipients, 2)
	s.Require().Contains(req.Recipients, "<EMAIL>")
	s.Require().Contains(req.Recipients, "<EMAIL>")
	s.Require().Equal("Bulk Email Subject", req.Subject)
	s.Require().Equal("Bulk email body", req.Body)
}

func (s *EmailServiceTestSuite) TestGetEmailStatus() {
	// Test successful email status retrieval
	req := &emailv1.GetEmailStatusRequest{
		EmailId: "email-123",
	}

	s.Require().NotNil(req)
	s.Require().Equal("email-123", req.EmailId)
}

func (s *EmailServiceTestSuite) TestGetEmailTemplate() {
	// Test successful email template retrieval
	req := &emailv1.GetEmailTemplateRequest{
		TemplateId: "welcome-email",
	}

	s.Require().NotNil(req)
	s.Require().Equal("welcome-email", req.TemplateId)
}

func (s *EmailServiceTestSuite) TestCreateEmailTemplate() {
	// Test successful email template creation
	req := &emailv1.CreateEmailTemplateRequest{
		TemplateId:   "new-template",
		TemplateName: "New Template",
		Subject:      "{{.subject}}",
		Body:         "Hello {{.username}}, welcome to {{.app_name}}!",
		IsHtml:       true,
	}

	s.Require().NotNil(req)
	s.Require().Equal("new-template", req.TemplateId)
	s.Require().Equal("New Template", req.TemplateName)
	s.Require().Contains(req.Subject, "{{.subject}}")
	s.Require().Contains(req.Body, "{{.username}}")
	s.Require().True(req.IsHtml)
}

func (s *EmailServiceTestSuite) TestUpdateEmailTemplate() {
	// Test successful email template update
	req := &emailv1.UpdateEmailTemplateRequest{
		TemplateId:   "existing-template",
		TemplateName: "Updated Template",
		Subject:      "Updated {{.subject}}",
		Body:         "Updated body with {{.username}}",
		IsHtml:       false,
	}

	s.Require().NotNil(req)
	s.Require().Equal("existing-template", req.TemplateId)
	s.Require().Equal("Updated Template", req.TemplateName)
	s.Require().Contains(req.Subject, "Updated")
	s.Require().False(req.IsHtml)
}

func (s *EmailServiceTestSuite) TestDeleteEmailTemplate() {
	// Test successful email template deletion
	req := &emailv1.DeleteEmailTemplateRequest{
		TemplateId: "template-to-delete",
	}

	s.Require().NotNil(req)
	s.Require().Equal("template-to-delete", req.TemplateId)
}

func (s *EmailServiceTestSuite) TestListEmailTemplates() {
	// Test successful email templates listing
	req := &emailv1.ListEmailTemplatesRequest{
		Page:     1,
		PageSize: 10,
	}

	s.Require().NotNil(req)
	s.Require().Equal(int32(1), req.Page)
	s.Require().Equal(int32(10), req.PageSize)
}

func (s *EmailServiceTestSuite) TestServiceInterface() {
	// Test that service implements the expected interface
	s.Require().NotNil(s.service)

	// Test that we can call service methods (they should exist)
	// Note: In a real test, these would be called with actual dependencies
}
