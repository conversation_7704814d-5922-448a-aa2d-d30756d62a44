package monitoring

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

func TestHealthCheckOptimization(t *testing.T) {
	// Create a metrics instance for testing
	logger, _ := zap.NewDevelopment()
	metrics := NewSocialAnalyticsMetrics(logger)

	t.Run("Lightweight health check is fast", func(t *testing.T) {
		ctx := context.Background()

		start := time.Now()
		result := metrics.HealthCheck(ctx)
		duration := time.Since(start)

		// Health check should complete very quickly (under 10ms for in-memory operations)
		assert.Less(t, duration, 10*time.Millisecond, "Lightweight health check should be very fast")

		// Verify basic response structure
		assert.Equal(t, "healthy", result["status"])
		assert.Equal(t, "social_analytics_metrics", result["service"])
		assert.Contains(t, result, "timestamp")
	})

	t.Run("Diagnostic health check provides detailed information", func(t *testing.T) {
		ctx := context.Background()

		start := time.Now()
		result := metrics.DiagnosticHealthCheck(ctx)
		duration := time.Since(start)

		// Diagnostic check can take longer but should still be reasonable
		assert.Less(t, duration, 100*time.Millisecond, "Diagnostic health check should complete within reasonable time")

		// Verify comprehensive response structure
		assert.Contains(t, result, "metrics_collection")
		assert.Contains(t, result, "cache_performance")
		assert.Contains(t, result, "error_rate")
		assert.Contains(t, result, "database_performance")
		assert.Contains(t, result, "overall")
		assert.Contains(t, result, "detailed_metrics")
		assert.Equal(t, "social_analytics_metrics", result["service"])

		// Verify detailed metrics are included
		detailedMetrics, ok := result["detailed_metrics"].(map[string]any)
		require.True(t, ok, "detailed_metrics should be a map")
		assert.Contains(t, detailedMetrics, "cache_hit_rate")
		assert.Contains(t, detailedMetrics, "total_events")
		assert.Contains(t, detailedMetrics, "error_rate")
		assert.Contains(t, detailedMetrics, "db_error_rate")
	})

	t.Run("Health check vs diagnostic check performance comparison", func(t *testing.T) {
		ctx := context.Background()

		// Measure lightweight health check
		start := time.Now()
		for i := 0; i < 100; i++ {
			metrics.HealthCheck(ctx)
		}
		lightweightDuration := time.Since(start)

		// Measure diagnostic health check
		start = time.Now()
		for i := 0; i < 100; i++ {
			metrics.DiagnosticHealthCheck(ctx)
		}
		diagnosticDuration := time.Since(start)

		// Lightweight should be significantly faster
		t.Logf("Lightweight health check (100 calls): %v", lightweightDuration)
		t.Logf("Diagnostic health check (100 calls): %v", diagnosticDuration)

		// The lightweight check should be at least 2x faster
		assert.Less(t, lightweightDuration*2, diagnosticDuration,
			"Lightweight health check should be significantly faster than diagnostic check")
	})

	t.Run("Health check response format is consistent", func(t *testing.T) {
		ctx := context.Background()

		// Test multiple calls to ensure consistency
		for i := 0; i < 10; i++ {
			result := metrics.HealthCheck(ctx)

			// Verify required fields are always present
			assert.Equal(t, "healthy", result["status"])
			assert.Equal(t, "social_analytics_metrics", result["service"])
			assert.Contains(t, result, "timestamp")

			// Verify timestamp is recent
			timestamp, ok := result["timestamp"].(time.Time)
			require.True(t, ok, "timestamp should be a time.Time")
			assert.WithinDuration(t, time.Now(), timestamp, time.Second,
				"timestamp should be recent")
		}
	})

	t.Run("Diagnostic health check handles edge cases", func(t *testing.T) {
		ctx := context.Background()

		result := metrics.DiagnosticHealthCheck(ctx)

		// Should handle zero values gracefully
		detailedMetrics, ok := result["detailed_metrics"].(map[string]any)
		require.True(t, ok)

		// Error rates should be valid numbers (not NaN or negative)
		errorRate, ok := detailedMetrics["error_rate"].(float64)
		require.True(t, ok)
		assert.GreaterOrEqual(t, errorRate, 0.0, "error rate should not be negative")
		assert.False(t, errorRate != errorRate, "error rate should not be NaN") // NaN != NaN is true

		dbErrorRate, ok := detailedMetrics["db_error_rate"].(float64)
		require.True(t, ok)
		assert.GreaterOrEqual(t, dbErrorRate, 0.0, "db error rate should not be negative")
		assert.False(t, dbErrorRate != dbErrorRate, "db error rate should not be NaN")
	})
}

func BenchmarkHealthChecks(b *testing.B) {
	logger, _ := zap.NewDevelopment()
	metrics := NewSocialAnalyticsMetrics(logger)
	ctx := context.Background()

	b.Run("Lightweight Health Check", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			metrics.HealthCheck(ctx)
		}
	})

	b.Run("Diagnostic Health Check", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			metrics.DiagnosticHealthCheck(ctx)
		}
	})
}

func TestHealthCheckMemoryUsage(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	metrics := NewSocialAnalyticsMetrics(logger)
	ctx := context.Background()

	t.Run("Health check doesn't leak memory", func(t *testing.T) {
		// Run many health checks to ensure no memory leaks
		for i := 0; i < 1000; i++ {
			result := metrics.HealthCheck(ctx)
			// Ensure result is used to prevent optimization
			_ = result["status"]
		}

		// If we get here without running out of memory, the test passes
		assert.True(t, true, "Health check completed without memory issues")
	})

	t.Run("Diagnostic check doesn't leak memory", func(t *testing.T) {
		// Run many diagnostic checks to ensure no memory leaks
		for i := 0; i < 100; i++ {
			result := metrics.DiagnosticHealthCheck(ctx)
			// Ensure result is used to prevent optimization
			_ = result["overall"]
		}

		// If we get here without running out of memory, the test passes
		assert.True(t, true, "Diagnostic check completed without memory issues")
	})
}
