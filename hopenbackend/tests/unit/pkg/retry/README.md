# Configuration-Based Retry System

This package provides a robust, configuration-driven retry mechanism for the Hopen backend services. Instead of hardcoded retry policies, all retry configurations are now externalized to YAML configuration files, allowing for easy tuning without code recompilation.

## Features

- **Configuration-Driven**: All retry policies defined in YAML configuration files
- **Service-Specific Policies**: Different retry strategies for different services (database, MinIO, LiveKit, etc.)
- **Multiple Strategies**: Fixed, linear, exponential, and exponential with jitter
- **Fallback to Defaults**: Services without specific configuration use global defaults
- **Environment Variables**: Support for environment variable overrides
- **Comprehensive Testing**: Full test coverage with integration tests

## Configuration Structure

### Global Retry Defaults

```yaml
resilience:
  retry:
    max_attempts: 3
    initial_backoff: 100ms
    max_backoff: 30s
    backoff_multiplier: 2.0
    jitter_factor: 0.1
    strategy: "exponential_jitter"
```

### Service-Specific Configurations

```yaml
resilience:
  retry:
    services:
      database:
        max_attempts: 5
        initial_delay: 50ms
        max_delay: 5s
        multiplier: 2.0
        jitter_factor: 0.1
        strategy: "exponential_jitter"
        enabled: true
        retryable_errors:
          - "connection refused"
          - "timeout"
          - "deadlock"
      
      minio:
        max_attempts: 3
        initial_delay: 100ms
        max_delay: 10s
        multiplier: 2.0
        jitter_factor: 0.2
        strategy: "exponential_jitter"
        enabled: true
        retryable_errors:
          - "502"
          - "503"
          - "504"
```

## Supported Services

The following services have predefined configurations:

- **database**: PostgreSQL/ScyllaDB operations (aggressive retry policy)
- **minio**: Object storage operations
- **livekit**: Real-time communication (less aggressive for latency)
- **nats**: Message queue operations
- **mqtt**: IoT messaging
- **ory**: Authentication service operations
- **fcm**: Firebase Cloud Messaging

## Retry Strategies

1. **fixed**: Fixed delay between retries
2. **linear**: Linearly increasing delay
3. **exponential**: Exponentially increasing delay
4. **exponential_jitter**: Exponential with random jitter (recommended)

## Usage Examples

### Using Service Retriers

```go
// Load configuration
cfg, err := config.Load()
if err != nil {
    log.Fatal(err)
}

// Create service retriers
serviceRetriers := retry.NewServiceRetriers(logger, cfg)

// Database operation with configured retry
err = serviceRetriers.ExecuteWithDatabaseRetry(ctx, func() error {
    return performDatabaseOperation()
})

// MinIO operation with configured retry
err = serviceRetriers.ExecuteWithMinIORetry(ctx, func() error {
    return uploadToMinIO()
})
```

### Custom Service Configuration

```go
// Get configuration for any service (falls back to global defaults)
serviceConfig := cfg.GetRetryConfig("my_custom_service")
retryConfig := retry.ConfigFromServiceConfig(serviceConfig)
retrier := retry.New(retryConfig, logger)

err := retrier.Execute(ctx, func() error {
    return performCustomOperation()
})
```

### Individual Service Runner

```go
// In service initialization
runner := service.NewRunner(logger, cfg).
    WithRetryType("database"). // Uses database-specific retry config
    Build()
```

## Environment Variable Overrides

All configuration values can be overridden with environment variables:

```bash
# Global retry settings
export RETRY_MAX_ATTEMPTS=5
export RETRY_INITIAL_BACKOFF=200ms
export RETRY_STRATEGY=exponential

# Service-specific settings
export DB_RETRY_MAX_ATTEMPTS=10
export MINIO_RETRY_INITIAL_DELAY=500ms
export LIVEKIT_RETRY_ENABLED=false
```

## Configuration Files

- **Development**: `hopen/config/development.yaml`
- **Backend Services**: `hopenbackend/config/config.yaml`

## Migration from Hardcoded Values

The old hardcoded functions are still available for backward compatibility:

- `retry.DatabaseConfig()` → Use `cfg.GetRetryConfig("database")`
- `retry.ExternalServiceConfig()` → Use `cfg.GetRetryConfig("minio")` or similar

## Testing

Run the retry package tests:

```bash
cd hopenbackend
go test ./pkg/retry -v
```

## Benefits

1. **No Recompilation**: Tune retry policies without rebuilding
2. **Environment-Specific**: Different policies for dev/staging/production
3. **Service-Optimized**: Each service gets appropriate retry behavior
4. **Operational Control**: Ops teams can adjust policies based on monitoring
5. **Consistent Logging**: All retry operations are properly logged with context
