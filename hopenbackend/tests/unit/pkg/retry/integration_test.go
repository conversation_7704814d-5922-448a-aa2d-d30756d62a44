package retry_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/retry"
)

func TestServiceRetriersWithConfig(t *testing.T) {
	// Create a mock config directly instead of loading from file
	cfg := &config.Config{
		Resilience: config.ResilienceConfig{
			Retry: config.RetryConfig{
				MaxAttempts:       3,
				InitialBackoff:    100 * time.Millisecond,
				MaxBackoff:        30 * time.Second,
				BackoffMultiplier: 2.0,
				JitterFactor:      0.1,
				Strategy:          "exponential_jitter",
				Services: map[string]config.ServiceRetryConfig{
					"database": {
						MaxAttempts:  5,
						InitialDelay: 50 * time.Millisecond,
						MaxDelay:     5 * time.Second,
						Multiplier:   2.0,
						JitterFactor: 0.1,
						Strategy:     "exponential_jitter",
						Enabled:      true,
						RetryableErrors: []string{
							"connection refused",
							"timeout",
							"deadlock",
						},
					},
					"minio": {
						MaxAttempts:  3,
						InitialDelay: 100 * time.Millisecond,
						MaxDelay:     10 * time.Second,
						Multiplier:   2.0,
						JitterFactor: 0.2,
						Strategy:     "exponential_jitter",
						Enabled:      true,
						RetryableErrors: []string{
							"502",
							"503",
							"504",
						},
					},
				},
			},
		},
	}

	// Create logger
	logger, err := zap.NewDevelopment()
	require.NoError(t, err)

	// Test service retriers creation
	serviceRetriers := retry.NewServiceRetriers(logger, cfg)
	require.NotNil(t, serviceRetriers)

	// Test that all retriers are created successfully
	assert.NotNil(t, serviceRetriers.Database)
	assert.NotNil(t, serviceRetriers.MinIO)
	assert.NotNil(t, serviceRetriers.LiveKit)
	assert.NotNil(t, serviceRetriers.NATS)
	assert.NotNil(t, serviceRetriers.MQTT)
	assert.NotNil(t, serviceRetriers.Ory)
	assert.NotNil(t, serviceRetriers.FCM)

	// Test that the manager is accessible
	manager := serviceRetriers.GetManager()
	assert.NotNil(t, manager)

	// Test that circuit breaker manager is accessible
	cbManager := serviceRetriers.GetCircuitBreakerManager()
	assert.NotNil(t, cbManager)
}

func TestGetRetryConfigFallback(t *testing.T) {
	// Create minimal config with only global retry settings
	cfg := &config.Config{
		Resilience: config.ResilienceConfig{
			Retry: config.RetryConfig{
				MaxAttempts:       5,
				InitialBackoff:    200 * time.Millisecond,
				MaxBackoff:        60 * time.Second,
				BackoffMultiplier: 3.0,
				JitterFactor:      0.3,
				Strategy:          "linear",
				Services:          map[string]config.ServiceRetryConfig{}, // Empty services map
			},
		},
	}

	// Test getting config for a service not explicitly defined
	unknownServiceConfig := cfg.GetRetryConfig("unknown_service")

	// Should return global defaults
	assert.Equal(t, 5, unknownServiceConfig.MaxAttempts)
	assert.Equal(t, 200*time.Millisecond, unknownServiceConfig.InitialDelay)
	assert.Equal(t, 60*time.Second, unknownServiceConfig.MaxDelay)
	assert.Equal(t, 3.0, unknownServiceConfig.Multiplier)
	assert.Equal(t, 0.3, unknownServiceConfig.JitterFactor)
	assert.Equal(t, "linear", unknownServiceConfig.Strategy)
	assert.True(t, unknownServiceConfig.Enabled)
	assert.Contains(t, unknownServiceConfig.RetryableErrors, "connection refused")
	assert.Contains(t, unknownServiceConfig.RetryableErrors, "timeout")
}
