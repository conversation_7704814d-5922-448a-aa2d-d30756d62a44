package retry_test

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/retry"
)

func TestConfigFromServiceConfig(t *testing.T) {
	tests := []struct {
		name           string
		serviceConfig  config.ServiceRetryConfig
		expectedConfig retry.Config
	}{
		{
			name: "database config",
			serviceConfig: config.ServiceRetryConfig{
				MaxAttempts:  5,
				InitialDelay: 50 * time.Millisecond,
				MaxDelay:     5 * time.Second,
				Multiplier:   2.0,
				JitterFactor: 0.1,
				Strategy:     "exponential_jitter",
				RetryableErrors: []string{
					"connection refused",
					"timeout",
					"deadlock",
				},
				Enabled: true,
			},
			expectedConfig: retry.Config{
				MaxAttempts:  5,
				InitialDelay: 50 * time.Millisecond,
				MaxDelay:     5 * time.Second,
				Multiplier:   2.0,
				JitterFactor: 0.1,
				Strategy:     retry.StrategyExponentialJitter,
				RetryableErrors: []string{
					"connection refused",
					"timeout",
					"deadlock",
				},
			},
		},
		{
			name: "minio config with different strategy",
			serviceConfig: config.ServiceRetryConfig{
				MaxAttempts:  3,
				InitialDelay: 100 * time.Millisecond,
				MaxDelay:     10 * time.Second,
				Multiplier:   2.0,
				JitterFactor: 0.2,
				Strategy:     "exponential",
				RetryableErrors: []string{
					"502",
					"503",
					"504",
				},
				Enabled: true,
			},
			expectedConfig: retry.Config{
				MaxAttempts:  3,
				InitialDelay: 100 * time.Millisecond,
				MaxDelay:     10 * time.Second,
				Multiplier:   2.0,
				JitterFactor: 0.2,
				Strategy:     retry.StrategyExponential,
				RetryableErrors: []string{
					"502",
					"503",
					"504",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := retry.ConfigFromServiceConfig(tt.serviceConfig)

			assert.Equal(t, tt.expectedConfig.MaxAttempts, result.MaxAttempts)
			assert.Equal(t, tt.expectedConfig.InitialDelay, result.InitialDelay)
			assert.Equal(t, tt.expectedConfig.MaxDelay, result.MaxDelay)
			assert.Equal(t, tt.expectedConfig.Multiplier, result.Multiplier)
			assert.Equal(t, tt.expectedConfig.JitterFactor, result.JitterFactor)
			assert.Equal(t, tt.expectedConfig.Strategy, result.Strategy)
			assert.Equal(t, tt.expectedConfig.RetryableErrors, result.RetryableErrors)

			// Test IsRetryable function
			assert.NotNil(t, result.IsRetryable)
			for _, errPattern := range tt.expectedConfig.RetryableErrors {
				testErr := errors.New(errPattern)
				assert.True(t, result.IsRetryable(testErr), "Should be retryable for pattern: %s", errPattern)
			}
		})
	}
}

func TestParseStrategy(t *testing.T) {
	tests := []struct {
		input    string
		expected retry.Strategy
	}{
		{"fixed", retry.StrategyFixed},
		{"linear", retry.StrategyLinear},
		{"exponential", retry.StrategyExponential},
		{"exponential_jitter", retry.StrategyExponentialJitter},
		{"EXPONENTIAL_JITTER", retry.StrategyExponentialJitter}, // Test case insensitive
		{"unknown", retry.StrategyExponentialJitter},            // Default fallback
		{"", retry.StrategyExponentialJitter},                   // Empty string fallback
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := retry.ParseStrategy(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestStrategyString(t *testing.T) {
	tests := []struct {
		strategy retry.Strategy
		expected string
	}{
		{retry.StrategyFixed, "fixed"},
		{retry.StrategyLinear, "linear"},
		{retry.StrategyExponential, "exponential"},
		{retry.StrategyExponentialJitter, "exponential_jitter"},
		{retry.Strategy(999), "unknown"}, // Invalid strategy
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			result := tt.strategy.String()
			assert.Equal(t, tt.expected, result)
		})
	}
}
