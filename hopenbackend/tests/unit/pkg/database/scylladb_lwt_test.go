package database

import (
	"context"
	"testing"
	"time"

	"github.com/gocql/gocql"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
)

// TestScyllaDBLightweightTransactions tests the LWT functionality
func TestScyllaDBLightweightTransactions(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping ScyllaDB LWT integration tests in short mode")
	}

	// Create test configuration
	cfg := &config.ScyllaDBConfig{
		Hosts:    []string{"127.0.0.1"},
		Keyspace: "hopen_test",
	}

	logger := zap.NewNop()
	client, err := database.NewScyllaDBClient(cfg, logger)
	require.NoError(t, err)
	defer client.Close()

	ctx := context.Background()

	// Initialize test keyspace and tables
	err = client.InitializeKeyspace(ctx)
	require.NoError(t, err)

	t.Run("ExecuteConditional", func(t *testing.T) {
		// Test basic conditional execution
		testUserID := gocql.TimeUUID()

		// First insert should succeed
		applied, err := client.ExecuteConditional(ctx,
			"INSERT INTO users (id, username, email, created_at, updated_at) VALUES (?, ?, ?, ?, ?) IF NOT EXISTS",
			testUserID.String(), "testuser", "<EMAIL>", time.Now(), time.Now())

		require.NoError(t, err)
		assert.True(t, applied, "First insert should be applied")

		// Second insert should fail (user already exists)
		applied, err = client.ExecuteConditional(ctx,
			"INSERT INTO users (id, username, email, created_at, updated_at) VALUES (?, ?, ?, ?, ?) IF NOT EXISTS",
			testUserID.String(), "testuser2", "<EMAIL>", time.Now(), time.Now())

		require.NoError(t, err)
		assert.False(t, applied, "Second insert should not be applied")
	})

	t.Run("ExecuteConditionalWithResult", func(t *testing.T) {
		// Test conditional execution with result data
		testUserID := gocql.TimeUUID()
		username := "conditionaluser"
		email := "<EMAIL>"

		// First insert should succeed
		applied, result, err := client.ExecuteConditionalWithResult(ctx,
			"INSERT INTO users (id, username, email, created_at, updated_at) VALUES (?, ?, ?, ?, ?) IF NOT EXISTS",
			testUserID.String(), username, email, time.Now(), time.Now())

		require.NoError(t, err)
		assert.True(t, applied, "First insert should be applied")
		assert.Empty(t, result, "Result should be empty for successful insert")

		// Second insert should fail and return existing data
		applied, result, err = client.ExecuteConditionalWithResult(ctx,
			"INSERT INTO users (id, username, email, created_at, updated_at) VALUES (?, ?, ?, ?, ?) IF NOT EXISTS",
			testUserID.String(), "newusername", "<EMAIL>", time.Now(), time.Now())

		require.NoError(t, err)
		assert.False(t, applied, "Second insert should not be applied")
		assert.NotEmpty(t, result, "Result should contain existing data")

		// Check that existing data is returned
		if existingUsername, ok := result["username"]; ok {
			assert.Equal(t, username, existingUsername, "Existing username should be returned")
		}
	})

	// NOTE: CreateUserIfNotExists was moved to user service during refactoring
	// This test is commented out as the functionality is now tested in user service tests
	/*
		t.Run("CreateUserIfNotExists", func(t *testing.T) {
			// Test the helper method for creating users
			user := &user.User{
				ID:          gocql.TimeUUID().String(),
				Username:    stringPtr("lwtuser"),
				Email:       "<EMAIL>",
				DisplayName: stringPtr("LWT User"),
				AvatarURL:   stringPtr("https://example.com/avatar.jpg"),
				IsVerified:  false,
				IsPrivate:   false,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}

			// First creation should succeed
			applied, err := client.CreateUserIfNotExists(ctx, user)
			require.NoError(t, err)
			assert.True(t, applied, "User creation should be applied")

			// Second creation should fail
			applied, err = client.CreateUserIfNotExists(ctx, user)
			require.NoError(t, err)
			assert.False(t, applied, "Duplicate user creation should not be applied")
		})
	*/

	t.Run("CreateConversationIfNotExists", func(t *testing.T) {
		// Test conversation creation with LWT
		conversation := &database.Conversation{
			ConversationID: gocql.TimeUUID(),
			Participant1ID: gocql.TimeUUID(),
			Participant2ID: gocql.TimeUUID(),
			LastMessageAt:  time.Now(),
			CreatedAt:      time.Now(),
		}

		// First creation should succeed
		applied, err := client.CreateConversationIfNotExists(ctx, conversation)
		require.NoError(t, err)
		assert.True(t, applied, "Conversation creation should be applied")

		// Second creation should fail
		applied, err = client.CreateConversationIfNotExists(ctx, conversation)
		require.NoError(t, err)
		assert.False(t, applied, "Duplicate conversation creation should not be applied")
	})

	t.Run("UpdateConversationLastMessageConditional", func(t *testing.T) {
		// Test conditional conversation update
		conversationID := gocql.TimeUUID()
		participant1 := gocql.TimeUUID()
		participant2 := gocql.TimeUUID()

		// Create initial conversation
		conversation := &database.Conversation{
			ConversationID: conversationID,
			Participant1ID: participant1,
			Participant2ID: participant2,
			LastMessageAt:  time.Now().Add(-1 * time.Hour), // 1 hour ago
			CreatedAt:      time.Now(),
		}

		applied, err := client.CreateConversationIfNotExists(ctx, conversation)
		require.NoError(t, err)
		require.True(t, applied)

		// Update with newer message should succeed
		newMessageID := gocql.TimeUUID()
		newMessageTime := time.Now()

		applied, err = client.UpdateConversationLastMessageConditional(ctx,
			conversationID, newMessageID, newMessageTime, conversation.LastMessageAt)
		require.NoError(t, err)
		assert.True(t, applied, "Update with newer message should be applied")

		// Update with older message should fail
		olderMessageID := gocql.TimeUUID()
		olderMessageTime := time.Now().Add(-2 * time.Hour) // 2 hours ago

		applied, err = client.UpdateConversationLastMessageConditional(ctx,
			conversationID, olderMessageID, olderMessageTime, newMessageTime)
		require.NoError(t, err)
		assert.False(t, applied, "Update with older message should not be applied")
	})

	t.Run("IncrementCounterIfExists", func(t *testing.T) {
		// Test conditional counter increment
		userID := gocql.TimeUUID()

		// Try to increment counter for non-existent user (should fail)
		applied, err := client.IncrementCounterIfExists(ctx,
			"user_message_counts", "messages_sent", "user_id", userID, 1)
		require.NoError(t, err)
		assert.False(t, applied, "Counter increment for non-existent user should not be applied")

		// Create user message count record first
		err = client.Query("INSERT INTO user_message_counts (user_id, messages_sent, messages_received) VALUES (?, 0, 0)",
			userID).WithContext(ctx).Exec()
		require.NoError(t, err)

		// Now increment should succeed
		applied, err = client.IncrementCounterIfExists(ctx,
			"user_message_counts", "messages_sent", "user_id", userID, 5)
		require.NoError(t, err)
		assert.True(t, applied, "Counter increment for existing user should be applied")
	})
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
