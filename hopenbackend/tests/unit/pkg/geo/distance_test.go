package geo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCalculateDistance(t *testing.T) {
	tests := []struct {
		name           string
		lat1, lon1     *float64
		lat2, lon2     *float64
		expectedResult *float64
		tolerance      float64
	}{
		{
			name:           "San Francisco to Los Angeles",
			lat1:           floatPtr(37.7749),
			lon1:           floatPtr(-122.4194),
			lat2:           floatPtr(34.0522),
			lon2:           floatPtr(-118.2437),
			expectedResult: floatPtr(559.12),
			tolerance:      50.0, // 50km tolerance
		},
		{
			name:           "Same location",
			lat1:           floatPtr(37.7749),
			lon1:           floatPtr(-122.4194),
			lat2:           floatPtr(37.7749),
			lon2:           floatPtr(-122.4194),
			expectedResult: floatPtr(0.0),
			tolerance:      0.1,
		},
		{
			name:           "Nil inputs",
			lat1:           nil,
			lon1:           nil,
			lat2:           nil,
			lon2:           nil,
			expectedResult: nil,
			tolerance:      0,
		},
		{
			name:           "Partial nil inputs",
			lat1:           floatPtr(37.7749),
			lon1:           nil,
			lat2:           floatPtr(34.0522),
			lon2:           floatPtr(-118.2437),
			expectedResult: nil,
			tolerance:      0,
		},
		{
			name:           "New York to London",
			lat1:           floatPtr(40.7128),
			lon1:           floatPtr(-74.0060),
			lat2:           floatPtr(51.5074),
			lon2:           floatPtr(-0.1278),
			expectedResult: floatPtr(5585.0),
			tolerance:      100.0, // 100km tolerance for long distances
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateDistance(tt.lat1, tt.lon1, tt.lat2, tt.lon2)

			if tt.expectedResult == nil {
				assert.Nil(t, result)
			} else {
				require.NotNil(t, result)
				assert.InDelta(t, *tt.expectedResult, *result, tt.tolerance)
			}
		})
	}
}

func TestValidateCoordinates(t *testing.T) {
	tests := []struct {
		name      string
		latitude  float64
		longitude float64
		expectErr bool
	}{
		{"Valid coordinates", 37.7749, -122.4194, false},
		{"Valid edge case - North Pole", 90.0, 0.0, false},
		{"Valid edge case - South Pole", -90.0, 0.0, false},
		{"Valid edge case - International Date Line", 0.0, 180.0, false},
		{"Valid edge case - Prime Meridian", 0.0, -180.0, false},
		{"Invalid latitude - too high", 91.0, 0.0, true},
		{"Invalid latitude - too low", -91.0, 0.0, true},
		{"Invalid longitude - too high", 0.0, 181.0, true},
		{"Invalid longitude - too low", 0.0, -181.0, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateCoordinates(tt.latitude, tt.longitude)
			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNewPoint(t *testing.T) {
	t.Run("Valid point", func(t *testing.T) {
		point, err := NewPoint(37.7749, -122.4194)
		assert.NoError(t, err)
		assert.NotNil(t, point)
		assert.Equal(t, 37.7749, point.Latitude)
		assert.Equal(t, -122.4194, point.Longitude)
	})

	t.Run("Invalid point", func(t *testing.T) {
		point, err := NewPoint(91.0, 0.0)
		assert.Error(t, err)
		assert.Nil(t, point)
	})
}

func TestCalculateDistancePoints(t *testing.T) {
	sf := Point{Latitude: 37.7749, Longitude: -122.4194}
	la := Point{Latitude: 34.0522, Longitude: -118.2437}

	distance := CalculateDistancePoints(sf, la)
	assert.InDelta(t, 559.12, distance, 50.0)
}

func TestIsWithinRadius(t *testing.T) {
	lat1 := 37.7749
	lon1 := -122.4194
	lat2 := 37.7849 // About 1.1 km north
	lon2 := -122.4194

	// Should be within 2km radius
	assert.True(t, IsWithinRadius(&lat1, &lon1, &lat2, &lon2, 2.0))

	// Should not be within 0.5km radius
	assert.False(t, IsWithinRadius(&lat1, &lon1, &lat2, &lon2, 0.5))

	// Test with nil values
	assert.False(t, IsWithinRadius(nil, &lon1, &lat2, &lon2, 2.0))
}

func TestCalculateDistanceMiles(t *testing.T) {
	lat1 := 37.7749
	lon1 := -122.4194
	lat2 := 34.0522
	lon2 := -118.2437

	milesDistance := CalculateDistanceMiles(&lat1, &lon1, &lat2, &lon2)
	require.NotNil(t, milesDistance)

	// SF to LA is approximately 347 miles
	assert.InDelta(t, 347.0, *milesDistance, 30.0)
}

func TestBoundingBox(t *testing.T) {
	t.Run("Valid bounding box", func(t *testing.T) {
		ne := Point{Latitude: 40.0, Longitude: -70.0}
		sw := Point{Latitude: 35.0, Longitude: -75.0}

		bb, err := NewBoundingBox(ne, sw)
		assert.NoError(t, err)
		assert.NotNil(t, bb)
	})

	t.Run("Invalid bounding box - north < south", func(t *testing.T) {
		ne := Point{Latitude: 35.0, Longitude: -70.0}
		sw := Point{Latitude: 40.0, Longitude: -75.0}

		bb, err := NewBoundingBox(ne, sw)
		assert.Error(t, err)
		assert.Nil(t, bb)
	})

	t.Run("Contains point", func(t *testing.T) {
		ne := Point{Latitude: 40.0, Longitude: -70.0}
		sw := Point{Latitude: 35.0, Longitude: -75.0}
		bb := &BoundingBox{NorthEast: ne, SouthWest: sw}

		// Point inside
		inside := Point{Latitude: 37.0, Longitude: -72.0}
		assert.True(t, bb.Contains(inside))

		// Point outside
		outside := Point{Latitude: 42.0, Longitude: -72.0}
		assert.False(t, bb.Contains(outside))
	})
}

func TestCreateBoundingBoxFromCenter(t *testing.T) {
	center := Point{Latitude: 37.7749, Longitude: -122.4194}
	radiusKm := 10.0

	bb := CreateBoundingBoxFromCenter(center, radiusKm)
	assert.NotNil(t, bb)

	// Center should be contained in the bounding box
	assert.True(t, bb.Contains(center))

	// Bounding box should be roughly symmetric around center
	latRange := bb.NorthEast.Latitude - bb.SouthWest.Latitude
	lonRange := bb.NorthEast.Longitude - bb.SouthWest.Longitude

	assert.Greater(t, latRange, 0.0)
	assert.Greater(t, lonRange, 0.0)
}

func TestBearing(t *testing.T) {
	// Test bearing from SF to LA (should be roughly southeast)
	bearing := Bearing(37.7749, -122.4194, 34.0522, -118.2437)

	// Should be in the southeast quadrant (90-180 degrees)
	assert.Greater(t, bearing, 90.0)
	assert.Less(t, bearing, 180.0)
}

func TestDestinationPoint(t *testing.T) {
	// Start at SF, go 100km due east (90 degrees)
	start := Point{Latitude: 37.7749, Longitude: -122.4194}
	dest := DestinationPoint(start.Latitude, start.Longitude, 90.0, 100.0)

	// Should be east of starting point
	assert.Greater(t, dest.Longitude, start.Longitude)

	// Latitude should be roughly the same (going due east)
	assert.InDelta(t, start.Latitude, dest.Latitude, 0.1)

	// Distance should be approximately 100km
	distance := CalculateDistancePoints(start, dest)
	assert.InDelta(t, 100.0, distance, 5.0)
}

func TestCalculateDistanceSafe(t *testing.T) {
	lat1 := 37.7749
	lon1 := -122.4194
	defaultValue := 999.0

	// Test with valid coordinates
	distance := CalculateDistanceSafe(&lat1, &lon1, &lat1, &lon1, defaultValue)
	assert.InDelta(t, 0.0, distance, 0.1)

	// Test with nil coordinates
	distance = CalculateDistanceSafe(nil, &lon1, &lat1, &lon1, defaultValue)
	assert.Equal(t, defaultValue, distance)
}

// Helper function to create float64 pointer
func floatPtr(f float64) *float64 {
	return &f
}
