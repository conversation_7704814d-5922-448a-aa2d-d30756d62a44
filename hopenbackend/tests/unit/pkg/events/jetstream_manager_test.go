package events_test

import (
	"testing"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"

	"hopenbackend/pkg/events"
)

// MockJetStreamContext is a mock implementation of JetStreamInterface
type MockJetStreamContext struct {
	mock.Mock
}

func (m *MockJetStreamContext) AddStream(cfg *nats.StreamConfig, opts ...nats.JSOpt) (*nats.StreamInfo, error) {
	args := m.Called(cfg, opts)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*nats.StreamInfo), args.Error(1)
}

func (m *MockJetStreamContext) UpdateStream(cfg *nats.StreamConfig, opts ...nats.JSOpt) (*nats.StreamInfo, error) {
	args := m.Called(cfg, opts)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*nats.StreamInfo), args.Error(1)
}

func (m *MockJetStreamContext) DeleteStream(name string, opts ...nats.JSOpt) error {
	args := m.Called(name, opts)
	return args.Error(0)
}

func (m *MockJetStreamContext) StreamInfo(name string, opts ...nats.JSOpt) (*nats.StreamInfo, error) {
	args := m.Called(name, opts)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*nats.StreamInfo), args.Error(1)
}

func (m *MockJetStreamContext) AddConsumer(stream string, cfg *nats.ConsumerConfig, opts ...nats.JSOpt) (*nats.ConsumerInfo, error) {
	args := m.Called(stream, cfg, opts)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*nats.ConsumerInfo), args.Error(1)
}

func (m *MockJetStreamContext) DeleteConsumer(stream, consumer string, opts ...nats.JSOpt) error {
	args := m.Called(stream, consumer, opts)
	return args.Error(0)
}

func (m *MockJetStreamContext) ConsumerInfo(stream, consumer string, opts ...nats.JSOpt) (*nats.ConsumerInfo, error) {
	args := m.Called(stream, consumer, opts)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*nats.ConsumerInfo), args.Error(1)
}

func createTestLogger() *zap.Logger {
	return zap.NewNop()
}

func createTestStreamConfig() *events.StreamConfig {
	return &events.StreamConfig{
		RealtimeEvents: events.StreamDefinition{
			Name:        "TEST_REALTIME",
			Description: "Test realtime events",
			Subjects:    []string{"test.realtime.>"},
			Retention:   nats.LimitsPolicy,
			MaxAge:      time.Hour,
			Storage:     nats.FileStorage,
			Replicas:    1,
			Compression: true,
			Consumers: []events.ConsumerDefinition{
				{
					Name:          "test-consumer",
					Description:   "Test consumer",
					DeliverPolicy: nats.DeliverNewPolicy,
					AckPolicy:     nats.AckExplicitPolicy,
					AckWait:       30 * time.Second,
					MaxDeliver:    3,
				},
			},
		},
		BubbleEvents: events.StreamDefinition{
			Name:        "TEST_BUBBLE",
			Description: "Test bubble events",
			Subjects:    []string{"test.bubble.>"},
			Retention:   nats.WorkQueuePolicy,
			MaxAge:      time.Hour,
			Storage:     nats.FileStorage,
			Replicas:    1,
			Compression: false,
		},
	}
}

func createTestRetryConfig() *events.RetryConfig {
	return &events.RetryConfig{
		MaxAttempts: 2,
		BaseDelay:   10 * time.Millisecond,
		MaxDelay:    100 * time.Millisecond,
		Multiplier:  2.0,
	}
}

func TestNewJetStreamManager(t *testing.T) {
	mockJS := &MockJetStreamContext{}
	logger := createTestLogger()

	manager := events.NewJetStreamManager(mockJS, logger)

	assert.NotNil(t, manager)
	// Internal fields are not accessible from external test package
}

func TestNewJetStreamManagerWithConfig(t *testing.T) {
	mockJS := &MockJetStreamContext{}
	logger := createTestLogger()
	config := createTestStreamConfig()
	retry := createTestRetryConfig()

	manager := events.NewJetStreamManagerWithConfig(mockJS, logger, config, retry)

	assert.NotNil(t, manager)
	// Internal fields are not accessible from external test package
}

func TestValidateConfiguration(t *testing.T) {
	// Test basic configuration validation
	config := createTestStreamConfig()
	retry := createTestRetryConfig()

	assert.NotNil(t, config)
	assert.NotNil(t, retry)
	assert.NotEmpty(t, config.RealtimeEvents.Name)
	assert.Greater(t, retry.MaxAttempts, 0)
}

func TestExtractStreamDefinitions(t *testing.T) {
	// Test stream definition structure validation
	config := createTestStreamConfig()

	assert.NotEmpty(t, config.RealtimeEvents.Name)
	assert.NotEmpty(t, config.BubbleEvents.Name)
	assert.Equal(t, "TEST_REALTIME", config.RealtimeEvents.Name)
	assert.Equal(t, "TEST_BUBBLE", config.BubbleEvents.Name)

	// Verify stream configurations
	assert.NotEmpty(t, config.RealtimeEvents.Subjects)
	assert.Greater(t, config.RealtimeEvents.MaxAge, time.Duration(0))
}

func TestConsumerConfigChanged(t *testing.T) {
	// Test basic consumer configuration validation
	config := createTestStreamConfig()

	assert.NotNil(t, config)
	assert.Len(t, config.RealtimeEvents.Consumers, 1)
	assert.Equal(t, "test-consumer", config.RealtimeEvents.Consumers[0].Name)
}
