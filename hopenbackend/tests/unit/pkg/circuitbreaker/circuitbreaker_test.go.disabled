package circuitbreaker_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"

	"hopenbackend/pkg/circuitbreaker"
	"hopenbackend/pkg/config"
)

type CircuitBreakerTestSuite struct {
	suite.Suite
	cb      *circuitbreaker.CircuitBreaker
	manager *circuitbreaker.Manager
}

func TestCircuitBreakerTestSuite(t *testing.T) {
	suite.Run(t, new(CircuitBreakerTestSuite))
}

func (s *CircuitBreakerTestSuite) SetupTest() {
	logger := zap.NewNop()

	// Create circuit breaker with test configuration
	config := circuitbreaker.Config{
		Name:             "test-service",
		MaxRequests:      3,
		Interval:         time.Second * 10,
		Timeout:          time.Second * 5,
		FailureThreshold: 2,
		SuccessThreshold: 1,
		OnStateChange:    func(name string, from, to circuitbreaker.State) {},
		IsFailure:        func(err error) bool { return err != nil },
	}

	s.cb = circuitbreaker.New(config, logger)

	// Create a minimal config for the manager
	appConfig := &config.Config{}
	s.manager = circuitbreaker.NewManager(logger, appConfig)

	s.Require().NotNil(s.cb)
	s.Require().NotNil(s.manager)
}

func (s *CircuitBreakerTestSuite) TestCircuitBreakerCreation() {
	// Test circuit breaker creation
	logger := zap.NewNop()
	config := circuitbreaker.Config{
		Name:        "test-cb",
		MaxRequests: 5,
		Interval:    time.Second * 30,
		Timeout:     time.Second * 10,
	}

	cb := circuitbreaker.New(config, logger)
	s.Require().NotNil(cb)
}

func (s *CircuitBreakerTestSuite) TestCircuitBreakerStates() {
	// Test circuit breaker state constants
	s.Require().Equal("CLOSED", circuitbreaker.StateClosed.String())
	s.Require().Equal("HALF_OPEN", circuitbreaker.StateHalfOpen.String())
	s.Require().Equal("OPEN", circuitbreaker.StateOpen.String())
}

func (s *CircuitBreakerTestSuite) TestSuccessfulExecution() {
	// Test successful function execution
	ctx := context.Background()

	err := s.cb.Execute(ctx, func() error {
		return nil // success
	})

	s.Require().NoError(err)
}

func (s *CircuitBreakerTestSuite) TestFailedExecution() {
	// Test failed function execution
	ctx := context.Background()
	expectedError := errors.New("test error")

	err := s.cb.Execute(ctx, func() error {
		return expectedError
	})

	s.Require().Error(err)
	s.Require().Equal(expectedError, err)
}

func (s *CircuitBreakerTestSuite) TestMetrics() {
	// Test circuit breaker metrics
	metrics := s.cb.GetMetrics()
	s.Require().NotNil(metrics)
	s.Require().Equal("test-service", metrics.Name)
	s.Require().GreaterOrEqual(metrics.TotalRequests, uint64(0))
	s.Require().GreaterOrEqual(metrics.TotalSuccesses, uint64(0))
	s.Require().GreaterOrEqual(metrics.TotalFailures, uint64(0))
}

func (s *CircuitBreakerTestSuite) TestManagerRegistration() {
	// Test circuit breaker manager registration
	config := circuitbreaker.Config{
		Name:        "managed-cb",
		MaxRequests: 3,
		Interval:    time.Second * 10,
		Timeout:     time.Second * 5,
	}

	cb := s.manager.GetOrCreate("managed-cb", config)
	s.Require().NotNil(cb)

	// Getting the same circuit breaker should return the same instance
	cb2 := s.manager.GetOrCreate("managed-cb", config)
	s.Require().Equal(cb, cb2)
}

func (s *CircuitBreakerTestSuite) TestDefaultConfig() {
	// Test default configuration
	defaultConfig := circuitbreaker.DefaultConfig("default-service")

	s.Require().Equal("default-service", defaultConfig.Name)
	s.Require().Greater(defaultConfig.MaxRequests, uint32(0))
	s.Require().Greater(defaultConfig.Interval, time.Duration(0))
	s.Require().Greater(defaultConfig.Timeout, time.Duration(0))
}

func (s *CircuitBreakerTestSuite) TestConfigValidation() {
	// Test configuration validation
	validConfig := circuitbreaker.Config{
		Name:        "valid-cb",
		MaxRequests: 5,
		Interval:    time.Second * 30,
		Timeout:     time.Second * 10,
	}

	invalidConfig := circuitbreaker.Config{
		Name:        "",
		MaxRequests: 0,
		Interval:    0,
		Timeout:     0,
	}

	// Test parameter validation
	s.Require().NotEmpty(validConfig.Name)
	s.Require().Greater(validConfig.MaxRequests, uint32(0))
	s.Require().Greater(validConfig.Interval, time.Duration(0))
	s.Require().Greater(validConfig.Timeout, time.Duration(0))

	s.Require().Empty(invalidConfig.Name)
	s.Require().Equal(uint32(0), invalidConfig.MaxRequests)
	s.Require().Equal(time.Duration(0), invalidConfig.Interval)
	s.Require().Equal(time.Duration(0), invalidConfig.Timeout)
}
