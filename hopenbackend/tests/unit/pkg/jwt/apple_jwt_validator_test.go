package jwt_test

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"

	jwtpkg "hopenbackend/pkg/jwt"
)

type AppleJWTValidatorTestSuite struct {
	suite.Suite
	validator *jwtpkg.AppleJWTValidator
	logger    *zap.Logger
	clientID  string
}

func TestAppleJWTValidatorTestSuite(t *testing.T) {
	suite.Run(t, new(AppleJWTValidatorTestSuite))
}

func (s *AppleJWTValidatorTestSuite) SetupTest() {
	s.logger = zap.NewNop() // No-op logger for testing
	s.clientID = "com.test.app"

	// Note: In real tests, we would mock the JWKS endpoint
	// For now, we'll test the validator creation and basic functionality
}

func (s *AppleJWTValidatorTestSuite) TestNewAppleJWTValidator() {
	// Test validator creation with valid client ID
	validator, err := jwtpkg.NewAppleJWTValidator(s.logger, s.clientID)
	s.Require().NoError(err)
	s.Require().NotNil(validator)
}

func (s *AppleJWTValidatorTestSuite) TestNewAppleJWTValidatorEmptyClientID() {
	// Test validator creation with empty client ID
	validator, err := jwtpkg.NewAppleJWTValidator(s.logger, "")
	s.Require().Error(err)
	s.Require().Nil(validator)
	s.Require().Contains(err.Error(), "client_id is required")
}

func (s *AppleJWTValidatorTestSuite) TestValidateAppleIDTokenInvalidFormat() {
	// Create validator (this will fail to fetch JWKS in test environment, but that's OK)
	validator, err := jwtpkg.NewAppleJWTValidator(s.logger, s.clientID)
	if err != nil {
		// If we can't create the validator due to network issues, skip this test
		s.T().Skip("Cannot create Apple JWT validator in test environment")
		return
	}

	ctx := context.Background()

	// Test with invalid token format
	invalidTokens := []string{
		"",
		"invalid",
		"invalid.token",
		"invalid.token.format.extra",
	}

	for _, token := range invalidTokens {
		s.Run(fmt.Sprintf("Invalid token: %s", token), func() {
			claims, err := validator.ValidateAppleIDToken(ctx, token)
			s.Require().Error(err)
			s.Require().Nil(claims)
		})
	}
}

func (s *AppleJWTValidatorTestSuite) TestValidateAppleIDTokenExpired() {
	// Create a mock expired JWT token
	expiredToken := s.createMockJWT(map[string]interface{}{
		"iss": "https://appleid.apple.com",
		"aud": s.clientID,
		"sub": "test-user-id",
		"exp": time.Now().Add(-1 * time.Hour).Unix(), // Expired 1 hour ago
		"iat": time.Now().Add(-2 * time.Hour).Unix(), // Issued 2 hours ago
	})

	validator, err := jwtpkg.NewAppleJWTValidator(s.logger, s.clientID)
	if err != nil {
		s.T().Skip("Cannot create Apple JWT validator in test environment")
		return
	}

	ctx := context.Background()
	claims, err := validator.ValidateAppleIDToken(ctx, expiredToken)
	s.Require().Error(err)
	s.Require().Nil(claims)
}

func (s *AppleJWTValidatorTestSuite) TestValidateAppleIDTokenInvalidIssuer() {
	// Create a mock JWT token with invalid issuer
	invalidIssuerToken := s.createMockJWT(map[string]interface{}{
		"iss": "https://evil.com",
		"aud": s.clientID,
		"sub": "test-user-id",
		"exp": time.Now().Add(1 * time.Hour).Unix(),
		"iat": time.Now().Unix(),
	})

	validator, err := jwtpkg.NewAppleJWTValidator(s.logger, s.clientID)
	if err != nil {
		s.T().Skip("Cannot create Apple JWT validator in test environment")
		return
	}

	ctx := context.Background()
	claims, err := validator.ValidateAppleIDToken(ctx, invalidIssuerToken)
	s.Require().Error(err)
	s.Require().Nil(claims)
}

func (s *AppleJWTValidatorTestSuite) TestValidateAppleIDTokenInvalidAudience() {
	// Create a mock JWT token with invalid audience
	invalidAudienceToken := s.createMockJWT(map[string]interface{}{
		"iss": "https://appleid.apple.com",
		"aud": "com.different.app",
		"sub": "test-user-id",
		"exp": time.Now().Add(1 * time.Hour).Unix(),
		"iat": time.Now().Unix(),
	})

	validator, err := jwtpkg.NewAppleJWTValidator(s.logger, s.clientID)
	if err != nil {
		s.T().Skip("Cannot create Apple JWT validator in test environment")
		return
	}

	ctx := context.Background()
	claims, err := validator.ValidateAppleIDToken(ctx, invalidAudienceToken)
	s.Require().Error(err)
	s.Require().Nil(claims)
}

// Helper function to create a mock JWT token for testing
// Note: This creates an unsigned token for basic structure testing
func (s *AppleJWTValidatorTestSuite) createMockJWT(claims map[string]interface{}) string {
	// Create header
	header := map[string]interface{}{
		"alg": "RS256",
		"kid": "test-key-id",
		"typ": "JWT",
	}

	headerBytes, _ := json.Marshal(header)
	claimsBytes, _ := json.Marshal(claims)

	headerEncoded := base64.RawURLEncoding.EncodeToString(headerBytes)
	claimsEncoded := base64.RawURLEncoding.EncodeToString(claimsBytes)

	// Create a fake signature (this won't validate, but it's for structure testing)
	signature := base64.RawURLEncoding.EncodeToString([]byte("fake-signature"))

	return fmt.Sprintf("%s.%s.%s", headerEncoded, claimsEncoded, signature)
}

// Test Apple JWT Claims structure
func TestAppleJWTClaims(t *testing.T) {
	claims := &jwtpkg.AppleJWTClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    "https://appleid.apple.com",
			Subject:   "test-user-id",
			Audience:  []string{"com.test.app"},
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(1 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
		Email:         "<EMAIL>",
		EmailVerified: "true",
	}

	assert.Equal(t, "https://appleid.apple.com", claims.Issuer)
	assert.Equal(t, "test-user-id", claims.Subject)
	assert.Contains(t, claims.Audience, "com.test.app")
	assert.Equal(t, "<EMAIL>", claims.Email)
	assert.Equal(t, "true", claims.EmailVerified)
}

// Test security aspects
func TestAppleJWTSecurityValidation(t *testing.T) {
	t.Run("Rejects tokens without signature verification", func(t *testing.T) {
		// This test ensures that our validator requires proper signature verification
		// and doesn't accept tokens that are just base64 decoded without crypto verification

		logger := zap.NewNop()
		clientID := "com.test.app"

		validator, err := jwtpkg.NewAppleJWTValidator(logger, clientID)
		if err != nil {
			t.Skip("Cannot create Apple JWT validator in test environment")
			return
		}

		// Create an unsigned token (security risk)
		unsignedToken := createUnsignedToken(map[string]interface{}{
			"iss": "https://appleid.apple.com",
			"aud": clientID,
			"sub": "test-user-id",
			"exp": time.Now().Add(1 * time.Hour).Unix(),
			"iat": time.Now().Unix(),
		})

		ctx := context.Background()
		claims, err := validator.ValidateAppleIDToken(ctx, unsignedToken)

		// Should fail because signature verification is required
		assert.Error(t, err)
		assert.Nil(t, claims)
	})
}

// Helper function to create unsigned token for security testing
func createUnsignedToken(claims map[string]interface{}) string {
	header := map[string]interface{}{
		"alg": "none", // No signature
		"typ": "JWT",
	}

	headerBytes, _ := json.Marshal(header)
	claimsBytes, _ := json.Marshal(claims)

	headerEncoded := base64.RawURLEncoding.EncodeToString(headerBytes)
	claimsEncoded := base64.RawURLEncoding.EncodeToString(claimsBytes)

	return fmt.Sprintf("%s.%s.", headerEncoded, claimsEncoded)
}
