#!/bin/bash

# ============================================================================
# 🧪 ENHANCED GRPCURL TEST PLAN FOR HOPEN BACKEND
# ============================================================================
# 
# This script provides a comprehensive grpcurl-based test plan that validates
# the backend implementation against your detailed user stories. It covers:
# 
# - User onboarding and registration flow with HIBP integration
# - Contact request and relationship establishment  
# - Bubble creation and management with countdown logic
# - Bubble invitation and voting system with countdown augmentation
# - CRITICAL: Bubble dissolution vs expiration (different outcomes)
# - Real-time communication features
# - Friend request flows from maybe_friend relationships
#
# Key Improvements:
# - Direct service endpoint testing (no Kong dependency)
# - Enhanced error handling and response validation
# - ID extraction and variable management
# - Comprehensive relationship status verification
# - Sequential test execution with dependency checking
#
# Prerequisites:
# - All microservices running (docker-compose up -d)
# - grpcurl installed
# 
# Usage: ./enhanced_grpcurl_test_plan.sh
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration - Gateway and service endpoints
KONG_GATEWAY="localhost:8000"
KONG_ADMIN="localhost:8001"
PROTO_PATH="hopenbackend/protos"

# Direct service endpoints (matching docker-compose port mappings)
AUTH_ENDPOINT="localhost:4000"
USER_ENDPOINT="localhost:4002"
BUBBLE_ENDPOINT="localhost:4004"
REALTIME_ENDPOINT="localhost:4008"
PRESENCE_ENDPOINT="localhost:4018"
MEDIA_ENDPOINT="localhost:4014"
EMAIL_ENDPOINT="localhost:4010"
SEARCH_ENDPOINT="localhost:4024"
CALL_ENDPOINT="localhost:4006"
NOTIFICATION_ENDPOINT="localhost:4016"
SOCIAL_ANALYTICS_ENDPOINT="localhost:4020"
SYNC_ENDPOINT="localhost:4022"

# Gateway endpoints (preferred for production-like testing)
GATEWAY_ADDR="$KONG_GATEWAY"
TEST_TIMEOUT=10

# Session tokens (will be captured during login)
ALICE_SESSION_TOKEN=""
BOB_SESSION_TOKEN=""
CHARLIE_SESSION_TOKEN=""
DAVID_SESSION_TOKEN=""

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# User IDs (will be captured from registration responses)
USER_A_ID=""
USER_B_ID=""
USER_C_ID=""
USER_D_ID=""

# Request and Bubble IDs (will be captured during tests)
CONTACT_REQUEST_AB_ID=""
BUBBLE_START_REQUEST_ID=""
BUBBLE_AB_ID=""
BUBBLE_CD_ID=""
INVITE_AC_REQUEST_ID=""

# Helper functions
print_header() {
    echo -e "\n${BLUE}═══ $1 ═══${NC}"
}

print_test() {
    echo -e "${YELLOW}Testing: $1${NC}"
    ((TOTAL_TESTS++))
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    ((PASSED_TESTS++))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    ((FAILED_TESTS++))
}

print_info() {
    echo -e "${CYAN}ℹ $1${NC}"
}

print_critical() {
    echo -e "${RED}🚨 CRITICAL: $1${NC}"
}

# Function to execute grpcurl with enhanced error handling
execute_grpc() {
    local endpoint=$1
    local method=$2
    local data=$3
    local description=$4

    print_test "$description"

    local response
    if response=$(grpcurl -plaintext -d "$data" -connect-timeout $TEST_TIMEOUT "$endpoint" "$method" 2>&1); then
        print_success "$description"
        echo "$response"
        return 0
    else
        print_error "$description - $response"
        return 1
    fi
}

# Function to extract ID from JSON response with better error handling
extract_id() {
    local json=$1
    local field=$2
    local extracted_id
    
    # Try multiple extraction methods for robustness
    extracted_id=$(echo "$json" | grep -o "\"$field\"[[:space:]]*:[[:space:]]*\"[^\"]*\"" | cut -d'"' -f4)
    
    if [[ -z "$extracted_id" ]]; then
        # Try alternative extraction for different JSON formats
        extracted_id=$(echo "$json" | sed -n "s/.*\"$field\"[[:space:]]*:[[:space:]]*\"\([^\"]*\)\".*/\1/p")
    fi
    
    echo "$extracted_id"
}

# Function to validate response contains expected fields
validate_response() {
    local response=$1
    local expected_field=$2
    local description=$3
    
    if echo "$response" | grep -q "\"$expected_field\""; then
        print_success "$description - Response contains $expected_field"
        return 0
    else
        print_error "$description - Response missing $expected_field"
        return 1
    fi
}

# Function to check if services are running
check_prerequisites() {
    print_header "CHECKING PREREQUISITES"

    print_test "grpcurl availability"
    if command -v grpcurl &> /dev/null; then
        print_success "grpcurl is available"
    else
        print_error "grpcurl is not installed"
        exit 1
    fi

    print_test "jq availability"
    if command -v jq &> /dev/null; then
        print_success "jq is available"
    else
        print_error "jq is not installed (required for JSON parsing)"
        exit 1
    fi

    # Check Kong Gateway
    print_test "Kong Gateway accessibility"
    if curl -f -s "$KONG_ADMIN/status" >/dev/null 2>&1; then
        print_success "Kong Gateway is accessible at $KONG_ADMIN"
    else
        print_error "Kong Gateway is not accessible at $KONG_ADMIN"
        exit 1
    fi

    # Check individual service accessibility
    local services=("Auth:$AUTH_ENDPOINT" "User:$USER_ENDPOINT" "Bubble:$BUBBLE_ENDPOINT" "Realtime:$REALTIME_ENDPOINT" "Presence:$PRESENCE_ENDPOINT" "Media:$MEDIA_ENDPOINT")

    for service_info in "${services[@]}"; do
        local service_name="${service_info%%:*}"
        local service_endpoint="${service_info##*:}"

        print_test "$service_name service accessibility"
        if grpcurl -plaintext -connect-timeout 5 "$service_endpoint" list > /dev/null 2>&1; then
            print_success "$service_name service is accessible at $service_endpoint"
        else
            print_error "$service_name service is not accessible at $service_endpoint"
            exit 1
        fi
    done
}

# Step 1: Initial Service Connectivity Checks
test_health_checks() {
    print_header "STEP 1: INITIAL SERVICE CONNECTIVITY CHECKS"
    print_info "Testing service accessibility and basic functionality"

    # Test service connectivity by listing available services
    local endpoints=("Auth:$AUTH_ENDPOINT" "User:$USER_ENDPOINT" "Bubble:$BUBBLE_ENDPOINT" "Realtime:$REALTIME_ENDPOINT" "Presence:$PRESENCE_ENDPOINT" "Media:$MEDIA_ENDPOINT")

    for endpoint_info in "${endpoints[@]}"; do
        local service_name="${endpoint_info%%:*}"
        local service_endpoint="${endpoint_info##*:}"

        print_test "Service connectivity for $service_name"
        if grpcurl -plaintext -connect-timeout 5 "$service_endpoint" list > /dev/null 2>&1; then
            print_success "$service_name service is accessible at $service_endpoint"
        else
            print_error "$service_name service is not accessible at $service_endpoint"
        fi
    done

    # Test Kong Gateway gRPC routing
    print_test "Kong Gateway gRPC routing"
    if grpcurl -plaintext -connect-timeout 5 "$GATEWAY_ADDR" list > /dev/null 2>&1; then
        print_success "Kong Gateway is routing gRPC traffic correctly"
    else
        print_error "Kong Gateway gRPC routing failed"
    fi
}

# Step 2: Authentication and User Lifecycle
test_user_registration() {
    print_header "STEP 2: AUTHENTICATION AND USER LIFECYCLE"

    print_info "Creating test users: Alice, Bob, Charlie, and David"

    # Generate unique timestamps for test users
    local timestamp=$(date +%s)

    # Register Alice
    local alice_email="alice${timestamp}@hopentest.com"
    local alice_response
    alice_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/RegisterUser" "{
        \"username\": \"alice_test_${timestamp}\",
        \"email\": \"${alice_email}\",
        \"password\": \"SecurePass123!\",
        \"first_name\": \"Alice\",
        \"last_name\": \"Alpha\"
    }" "Register Alice")

    if [[ $? -eq 0 ]]; then
        USER_A_ID=$(extract_id "$alice_response" "user_id")
        if [[ -z "$USER_A_ID" ]]; then
            USER_A_ID=$(extract_id "$alice_response" "id")
        fi
        print_info "Alice ID: $USER_A_ID"
        validate_response "$alice_response" "user_id\|id" "Alice registration response validation"

        # Login Alice and capture session token
        local alice_login_response
        alice_login_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/LoginUser" "{
            \"email\": \"${alice_email}\",
            \"password\": \"SecurePass123!\"
        }" "Login Alice")

        if [[ $? -eq 0 ]]; then
            ALICE_SESSION_TOKEN=$(extract_id "$alice_login_response" "sessionToken")
            if [[ -z "$ALICE_SESSION_TOKEN" ]]; then
                ALICE_SESSION_TOKEN=$(extract_id "$alice_login_response" "session_token")
            fi
            print_info "Alice Session Token: ${ALICE_SESSION_TOKEN:0:20}..."

            # Validate Alice's session
            execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/ValidateSession" "{
                \"session_token\": \"$ALICE_SESSION_TOKEN\"
            }" "Validate Alice's session"
        fi
    fi
    
    # Register Bob
    local bob_email="bob${timestamp}@hopentest.com"
    local bob_response
    bob_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/RegisterUser" "{
        \"username\": \"bob_test_${timestamp}\",
        \"email\": \"${bob_email}\",
        \"password\": \"SecurePass123!\",
        \"first_name\": \"Bob\",
        \"last_name\": \"Bravo\"
    }" "Register Bob")

    if [[ $? -eq 0 ]]; then
        USER_B_ID=$(extract_id "$bob_response" "user_id")
        if [[ -z "$USER_B_ID" ]]; then
            USER_B_ID=$(extract_id "$bob_response" "id")
        fi
        print_info "Bob ID: $USER_B_ID"
        validate_response "$bob_response" "user_id\|id" "Bob registration response validation"

        # Login Bob and capture session token
        local bob_login_response
        bob_login_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/LoginUser" "{
            \"email\": \"${bob_email}\",
            \"password\": \"SecurePass123!\"
        }" "Login Bob")

        if [[ $? -eq 0 ]]; then
            BOB_SESSION_TOKEN=$(extract_id "$bob_login_response" "sessionToken")
            if [[ -z "$BOB_SESSION_TOKEN" ]]; then
                BOB_SESSION_TOKEN=$(extract_id "$bob_login_response" "session_token")
            fi
            print_info "Bob Session Token: ${BOB_SESSION_TOKEN:0:20}..."
        fi
    fi
    
    # Register Charlie
    local charlie_response
    charlie_response=$(execute_grpc "$AUTH_ENDPOINT" "hopen.auth.v1.AuthService/RegisterUser" '{
        "username": "charlie_test_'$(date +%s)'",
        "email": "charlie'$(date +%s)'@hopentest.com",
        "password": "SecurePass123!",
        "first_name": "Charlie", 
        "last_name": "Charlie"
    }' "Register Charlie")
    
    if [[ $? -eq 0 ]]; then
        USER_C_ID=$(extract_id "$charlie_response" "user_id")
        if [[ -z "$USER_C_ID" ]]; then
            USER_C_ID=$(extract_id "$charlie_response" "id")
        fi
        print_info "Charlie ID: $USER_C_ID"
        validate_response "$charlie_response" "user_id\|id" "Charlie registration response validation"
    fi
    
    # Register David
    local david_response
    david_response=$(execute_grpc "$AUTH_ENDPOINT" "hopen.auth.v1.AuthService/RegisterUser" '{
        "username": "david_test_'$(date +%s)'",
        "email": "david'$(date +%s)'@hopentest.com",
        "password": "SecurePass123!",
        "first_name": "David",
        "last_name": "Delta"
    }' "Register David")
    
    if [[ $? -eq 0 ]]; then
        USER_D_ID=$(extract_id "$david_response" "user_id")
        if [[ -z "$USER_D_ID" ]]; then
            USER_D_ID=$(extract_id "$david_response" "id")
        fi
        print_info "David ID: $USER_D_ID"
        validate_response "$david_response" "user_id\|id" "David registration response validation"
    fi
    
    # Test password breach check (HIBP integration)
    print_info "Testing password breach check with known compromised password"
    local hibp_response
    hibp_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/CheckPasswordBreach" '{
        "sha1_hash": "5BAA61E4C9B93F3F0682250B6CF8331B7EE68FD8"
    }' "Check password breach for common password 'password'")

    if [[ $? -eq 0 ]]; then
        if echo "$hibp_response" | grep -q "isCompromised.*true"; then
            print_success "Password breach check correctly identified compromised password"
        else
            print_error "Password breach check failed to identify compromised password"
        fi
    fi

    # Test password reset flow
    print_info "Testing password reset flow for Alice"
    local forgot_response
    forgot_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/ForgotPassword" "{
        \"email\": \"${alice_email}\"
    }" "Initiate forgot password for Alice")

    if [[ $? -eq 0 ]]; then
        local reset_token
        reset_token=$(extract_id "$forgot_response" "resetToken")
        if [[ -z "$reset_token" ]]; then
            reset_token=$(extract_id "$forgot_response" "reset_token")
        fi

        if [[ -n "$reset_token" ]]; then
            print_info "Reset Token: ${reset_token:0:20}..."

            # Reset the password
            execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/ResetPassword" "{
                \"email\": \"${alice_email}\",
                \"reset_token\": \"$reset_token\",
                \"new_password\": \"NewSecurePass456!\",
                \"confirm_password\": \"NewSecurePass456!\"
            }" "Reset Alice's password"

            # Verify login with new password
            execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/LoginUser" "{
                \"email\": \"${alice_email}\",
                \"password\": \"NewSecurePass456!\"
            }" "Login Alice with new password"
        fi
    fi
}

# Step 3: User Profile & Social Graph
test_contact_requests() {
    print_header "STEP 3: USER PROFILE & SOCIAL GRAPH"

    if [[ -z "$USER_A_ID" || -z "$USER_B_ID" || -z "$ALICE_SESSION_TOKEN" || -z "$BOB_SESSION_TOKEN" ]]; then
        print_error "User IDs or session tokens not available, skipping contact request tests"
        return 1
    fi

    # Alice gets her own profile first
    execute_grpc "$GATEWAY_ADDR" "hopen.user.v1.UserService/GetUser" "{
        \"user_id\": \"$USER_A_ID\"
    }" "Alice gets her own profile" || true

    # Alice sends contact request to Bob
    local contact_response
    contact_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.user.v1.UserService/SendContactRequest" "{
        \"user_id\": \"$USER_A_ID\",
        \"target_user_id\": \"$USER_B_ID\",
        \"message\": \"Hi Bob, let's connect!\"
    }" "Alice sends contact request to Bob")

    if [[ $? -eq 0 ]]; then
        CONTACT_REQUEST_AB_ID=$(extract_id "$contact_response" "request_id")
        print_info "Contact Request ID: $CONTACT_REQUEST_AB_ID"
        validate_response "$contact_response" "request_id" "Contact request response validation"
    fi

    # Check relationship status from Bob's perspective (should show pending request)
    local relationship_response
    relationship_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.user.v1.UserService/GetUserRelationshipStatus" "{
        \"user_id\": \"$USER_B_ID\",
        \"target_user_id\": \"$USER_A_ID\"
    }" "Check relationship status (Bob's perspective)")

    if [[ $? -eq 0 ]]; then
        validate_response "$relationship_response" "has_pending_contact_request_to_me" "Pending contact request validation"
    fi

    # Bob accepts the contact request
    execute_grpc "$GATEWAY_ADDR" "hopen.user.v1.UserService/AcceptContactRequest" "{
        \"user_id\": \"$USER_B_ID\",
        \"requester_id\": \"$USER_A_ID\"
    }" "Bob accepts contact request from Alice"

    # Verify final relationship status is "contact"
    local final_relationship_response
    final_relationship_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.user.v1.UserService/GetUserRelationshipStatus" "{
        \"user_id\": \"$USER_A_ID\",
        \"target_user_id\": \"$USER_B_ID\"
    }" "Verify Alice-Bob relationship is 'contact'")

    if [[ $? -eq 0 ]]; then
        if echo "$final_relationship_response" | grep -q "contact"; then
            print_success "Relationship correctly established as 'contact'"
        else
            print_error "Relationship not established as 'contact'"
        fi
    fi
}

# Step 4: Full Bubble Lifecycle
test_bubble_creation() {
    print_header "STEP 4: FULL BUBBLE LIFECYCLE"

    if [[ -z "$USER_A_ID" || -z "$USER_B_ID" || -z "$ALICE_SESSION_TOKEN" || -z "$BOB_SESSION_TOKEN" ]]; then
        print_error "User IDs or session tokens not available, skipping bubble creation tests"
        return 1
    fi

    # Alice sends bubble start request to Bob
    local bubble_start_response
    bubble_start_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.bubble.v1.BubbleService/CreateBubbleStartRequest" "{
        \"requester_id\": \"$USER_A_ID\",
        \"target_user_id\": \"$USER_B_ID\",
        \"bubble_name\": \"Alpha Bravo Test Bubble\"
    }" "Alice sends bubble start request to Bob")

    if [[ $? -eq 0 ]]; then
        BUBBLE_START_REQUEST_ID=$(extract_id "$bubble_start_response" "request_id")
        print_info "Bubble Start Request ID: $BUBBLE_START_REQUEST_ID"
        validate_response "$bubble_start_response" "request_id" "Bubble start request response validation"
    fi

    # Bob accepts the bubble start request
    local bubble_accept_response
    bubble_accept_response=$(execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/AcceptBubbleStartRequest" "{
        \"request_id\": \"$BUBBLE_START_REQUEST_ID\",
        \"user_id\": \"$USER_B_ID\"
    }" "Bob accepts bubble start request")

    if [[ $? -eq 0 ]]; then
        BUBBLE_AB_ID=$(extract_id "$bubble_accept_response" "bubble_id")
        print_info "Bubble ID: $BUBBLE_AB_ID"
        validate_response "$bubble_accept_response" "bubble_id" "Bubble creation response validation"
    fi

    # Verify bubble state and member count
    if [[ -n "$BUBBLE_AB_ID" ]]; then
        local bubble_details_response
        bubble_details_response=$(execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/GetBubble" "{
            \"bubble_id\": \"$BUBBLE_AB_ID\"
        }" "Verify bubble state and countdown")

        if [[ $? -eq 0 ]]; then
            validate_response "$bubble_details_response" "status\|member_count" "Bubble details validation"

            # Check for ACTIVE status and member count of 2
            if echo "$bubble_details_response" | grep -q "ACTIVE\|active"; then
                print_success "Bubble status is ACTIVE"
            else
                print_error "Bubble status is not ACTIVE"
            fi
        fi

        execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/GetBubbleMembers" "{
            \"bubble_id\": \"$BUBBLE_AB_ID\",
            \"user_id\": \"$USER_A_ID\"
        }" "Verify bubble members"
    fi

    # Verify relationship shows bubble membership (still "contact" but with bubble context)
    execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/GetUserRelationshipStatus" "{
        \"user_id\": \"$USER_A_ID\",
        \"target_user_id\": \"$USER_B_ID\"
    }" "Verify relationship shows bubble membership"
}

# Phase 4: Bubble Invitation and Voting Flow
test_bubble_invitations() {
    print_header "PHASE 4: BUBBLE INVITATION AND VOTING FLOW"

    if [[ -z "$USER_A_ID" || -z "$USER_C_ID" || -z "$BUBBLE_AB_ID" ]]; then
        print_error "Required IDs not available, skipping invitation tests"
        return 1
    fi

    # Setup: Alice and Charlie become contacts first
    execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/SendContactRequest" "{
        \"user_id\": \"$USER_A_ID\",
        \"target_user_id\": \"$USER_C_ID\",
        \"message\": \"Hi Charlie, let's connect!\"
    }" "Alice sends contact request to Charlie"

    execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/AcceptContactRequest" "{
        \"user_id\": \"$USER_C_ID\",
        \"requester_id\": \"$USER_A_ID\"
    }" "Charlie accepts contact request from Alice"

    # Alice (in bubble) invites Charlie (contact, not in bubble)
    local invite_response
    invite_response=$(execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/SendBubbleInvite" "{
        \"bubble_id\": \"$BUBBLE_AB_ID\",
        \"inviter_id\": \"$USER_A_ID\",
        \"invitee_ids\": [\"$USER_C_ID\"]
    }" "Alice invites Charlie to bubble")

    if [[ $? -eq 0 ]]; then
        INVITE_AC_REQUEST_ID=$(extract_id "$invite_response" "request_id")
        print_info "Invite Request ID: $INVITE_AC_REQUEST_ID"
        validate_response "$invite_response" "request_id" "Bubble invite response validation"
    fi

    # Charlie accepts the invite (triggers proposal flow for Bob)
    if [[ -n "$INVITE_AC_REQUEST_ID" ]]; then
        execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/AcceptBubbleInvite" "{
            \"request_id\": \"$INVITE_AC_REQUEST_ID\",
            \"user_id\": \"$USER_C_ID\"
        }" "Charlie accepts bubble invite"

        # Bob (existing member) votes to approve the proposal
        execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/VoteOnInviteRequest" "{
            \"request_id\": \"$INVITE_AC_REQUEST_ID\",
            \"user_id\": \"$USER_B_ID\",
            \"vote\": \"approve\"
        }" "Bob votes to approve Charlie's invitation"

        # Verify Charlie is now a member
        local members_response
        members_response=$(execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/GetBubbleMembers" "{
            \"bubble_id\": \"$BUBBLE_AB_ID\",
            \"user_id\": \"$USER_A_ID\"
        }" "Verify Charlie is now a bubble member")

        if [[ $? -eq 0 ]]; then
            if echo "$members_response" | grep -q "$USER_C_ID"; then
                print_success "Charlie successfully added to bubble"
            else
                print_error "Charlie not found in bubble members"
            fi
        fi

        # Verify bubble countdown was augmented (should show extended expiry)
        execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/GetBubble" "{
            \"bubble_id\": \"$BUBBLE_AB_ID\"
        }" "Verify bubble countdown was extended"
    fi
}

# Phase 5: Bubble Dissolution (Critical Test)
test_bubble_dissolution() {
    print_header "PHASE 5: BUBBLE DISSOLUTION (FEWER THAN 2 MEMBERS)"
    print_critical "Testing the CRITICAL distinction: dissolution → contact relationship"

    if [[ -z "$USER_C_ID" || -z "$USER_D_ID" ]]; then
        print_error "User IDs not available, skipping dissolution tests"
        return 1
    fi

    # Setup: Create a new bubble for Charlie and David
    execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/SendContactRequest" "{
        \"user_id\": \"$USER_C_ID\",
        \"target_user_id\": \"$USER_D_ID\",
        \"message\": \"Hi David, let's connect!\"
    }" "Charlie sends contact request to David"

    execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/AcceptContactRequest" "{
        \"user_id\": \"$USER_D_ID\",
        \"requester_id\": \"$USER_C_ID\"
    }" "David accepts contact request from Charlie"

    # Create bubble between Charlie and David
    local cd_bubble_start_response
    cd_bubble_start_response=$(execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/CreateBubbleStartRequest" "{
        \"requester_id\": \"$USER_C_ID\",
        \"target_user_id\": \"$USER_D_ID\",
        \"bubble_name\": \"CD Test Bubble\"
    }" "Charlie sends bubble start request to David")

    local cd_request_id
    if [[ $? -eq 0 ]]; then
        cd_request_id=$(extract_id "$cd_bubble_start_response" "request_id")

        local cd_accept_response
        cd_accept_response=$(execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/AcceptBubbleStartRequest" "{
            \"request_id\": \"$cd_request_id\",
            \"user_id\": \"$USER_D_ID\"
        }" "David accepts bubble start request")

        if [[ $? -eq 0 ]]; then
            BUBBLE_CD_ID=$(extract_id "$cd_accept_response" "bubble_id")
            print_info "CD Bubble ID: $BUBBLE_CD_ID"
        fi
    fi

    # Charlie leaves the 2-person bubble, triggering dissolution
    if [[ -n "$BUBBLE_CD_ID" ]]; then
        execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/LeaveBubble" "{
            \"bubble_id\": \"$BUBBLE_CD_ID\",
            \"user_id\": \"$USER_C_ID\"
        }" "Charlie leaves the bubble (triggers dissolution)"

        # Verify bubble status is "dissolved"
        local dissolved_bubble_response
        dissolved_bubble_response=$(execute_grpc "$BUBBLE_ENDPOINT" "hopen.bubble.v1.BubbleService/GetBubble" "{
            \"bubble_id\": \"$BUBBLE_CD_ID\"
        }" "Verify bubble status is dissolved")

        if [[ $? -eq 0 ]]; then
            if echo "$dissolved_bubble_response" | grep -q "DISSOLVED\|dissolved"; then
                print_success "Bubble correctly marked as DISSOLVED"
            else
                print_error "Bubble not marked as DISSOLVED"
            fi
        fi

        # CRITICAL: Verify Charlie and David are still contacts (not maybe_friend)
        local critical_relationship_response
        critical_relationship_response=$(execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/GetUserRelationshipStatus" "{
            \"user_id\": \"$USER_C_ID\",
            \"target_user_id\": \"$USER_D_ID\"
        }" "CRITICAL: Verify Charlie-David relationship is still 'contact'")

        if [[ $? -eq 0 ]]; then
            if echo "$critical_relationship_response" | grep -q "contact"; then
                print_critical "SUCCESS: Dissolution correctly maintains 'contact' relationship"
            else
                print_critical "FAILURE: Dissolution did not maintain 'contact' relationship"
            fi
        fi
    fi
}

# Phase 6: Bubble Expiration and Friend Flow (Critical Test)
test_bubble_expiration() {
    print_header "PHASE 6: BUBBLE EXPIRATION AND MAYBE_FRIEND FLOW"
    print_critical "Testing the CRITICAL distinction: expiration → maybe_friend → friend flow"

    if [[ -z "$USER_A_ID" || -z "$USER_B_ID" || -z "$BUBBLE_AB_ID" ]]; then
        print_error "Required IDs not available, skipping expiration tests"
        return 1
    fi

    # Simulate bubble expiration (triggers maybe_friend flow)
    local friendship_response
    friendship_response=$(execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/CreateFriendshipsFromBubble" "{
        \"bubble_id\": \"$BUBBLE_AB_ID\",
        \"members\": [
            {\"user_id\": \"$USER_A_ID\", \"bubble_id\": \"$BUBBLE_AB_ID\", \"status\": \"active\"},
            {\"user_id\": \"$USER_B_ID\", \"bubble_id\": \"$BUBBLE_AB_ID\", \"status\": \"active\"}
        ]
    }" "Simulate bubble expiration (creates maybe_friend relationships)")

    if [[ $? -eq 0 ]]; then
        validate_response "$friendship_response" "friendships_created\|created" "Friendship creation response validation"
    fi

    # Verify relationship is "maybe_friend"
    local maybe_friend_response
    maybe_friend_response=$(execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/GetUserRelationshipStatus" "{
        \"user_id\": \"$USER_A_ID\",
        \"target_user_id\": \"$USER_B_ID\"
    }" "Verify relationship is 'maybe_friend'")

    if [[ $? -eq 0 ]]; then
        if echo "$maybe_friend_response" | grep -q "maybe_friend"; then
            print_critical "SUCCESS: Expiration correctly creates 'maybe_friend' relationship"
        else
            print_critical "FAILURE: Expiration did not create 'maybe_friend' relationship"
        fi
    fi

    # Test mutual acceptance to become friends
    execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/AcceptFriendRequestRelationship" "{
        \"user_id\": \"$USER_A_ID\",
        \"other_user_id\": \"$USER_B_ID\"
    }" "Alice accepts friend request"

    execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/AcceptFriendRequestRelationship" "{
        \"user_id\": \"$USER_B_ID\",
        \"other_user_id\": \"$USER_A_ID\"
    }" "Bob accepts friend request"

    # Verify final relationship is "friend"
    local friend_response
    friend_response=$(execute_grpc "$USER_ENDPOINT" "hopen.user.v1.UserService/GetUserRelationshipStatus" "{
        \"user_id\": \"$USER_A_ID\",
        \"target_user_id\": \"$USER_B_ID\"
    }" "Verify final relationship is 'friend'")

    if [[ $? -eq 0 ]]; then
        if echo "$friend_response" | grep -q "friend"; then
            print_critical "SUCCESS: Mutual acceptance creates 'friend' relationship"
        else
            print_critical "FAILURE: Mutual acceptance did not create 'friend' relationship"
        fi
    fi
}

# Step 5: Realtime, Media, and Other Services
test_realtime_features() {
    print_header "STEP 5: REALTIME, MEDIA, AND OTHER SERVICES"

    if [[ -z "$USER_A_ID" || -z "$USER_B_ID" ]]; then
        print_error "Required IDs not available, skipping realtime tests"
        return 1
    fi

    print_info "Testing realtime messaging and communication features"

    # Test presence update with authentication
    if [[ -n "$ALICE_SESSION_TOKEN" ]]; then
        execute_grpc "$GATEWAY_ADDR" "hopen.presence.v1.PresenceService/UpdateUserPresence" "{
            \"user_id\": \"$USER_A_ID\",
            \"is_present\": true,
            \"status_message\": \"Testing with grpcurl\"
        }" "Alice updates presence status" || true
    fi

    # Test batch presence check
    local presence_response
    presence_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.presence.v1.PresenceService/GetBatchPresence" "{
        \"user_ids\": [\"$USER_A_ID\", \"$USER_B_ID\"]
    }" "Get batch presence for Alice and Bob")

    if [[ $? -eq 0 ]]; then
        validate_response "$presence_response" "presences\|users" "Batch presence response validation"
    fi

    # Test message sending (if bubble exists)
    if [[ -n "$BUBBLE_AB_ID" && -n "$ALICE_SESSION_TOKEN" ]]; then
        local message_response
        message_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.realtime.v1.RealtimeService/SendMessage" "{
            \"bubble_id\": \"$BUBBLE_AB_ID\",
            \"content\": \"Hello from grpcurl test!\",
            \"message_type\": \"TEXT\"
        }" "Alice sends message to bubble")

        if [[ $? -eq 0 ]]; then
            validate_response "$message_response" "message_id\|id" "Message sending response validation"

            # Get chat messages
            execute_grpc "$GATEWAY_ADDR" "hopen.realtime.v1.RealtimeService/GetChatMessages" "{
                \"bubble_id\": \"$BUBBLE_AB_ID\",
                \"limit\": 10
            }" "Get chat messages from bubble"
        fi
    fi

    # Test media service
    test_media_service
}

# Media service testing
test_media_service() {
    print_info "Testing media upload and download functionality"

    if [[ -z "$USER_A_ID" || -z "$ALICE_SESSION_TOKEN" ]]; then
        print_error "Required authentication not available, skipping media tests"
        return 1
    fi

    # Create a test file
    local test_file_content="VGhpcyBpcyBhIHRlc3QgZmlsZSBmb3IgZ3JwY3VybCB0ZXN0aW5nLg=="  # Base64 encoded "This is a test file for grpcurl testing."

    # Test file upload
    local upload_response
    upload_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.media.v1.MediaService/UploadFile" "{
        \"user_id\": \"$USER_A_ID\",
        \"file_name\": \"test.txt\",
        \"file_type\": \"document\",
        \"file_data\": \"$test_file_content\"
    }" "Upload test file")

    if [[ $? -eq 0 ]]; then
        local file_id
        file_id=$(extract_id "$upload_response" "file_id")
        if [[ -n "$file_id" ]]; then
            print_info "Uploaded File ID: $file_id"

            # Test file download
            execute_grpc "$GATEWAY_ADDR" "hopen.media.v1.MediaService/GetFile" "{
                \"file_id\": \"$file_id\",
                \"user_id\": \"$USER_A_ID\"
            }" "Download uploaded file"
        fi
    fi
}

# Step 6: Logout and Session Cleanup
test_logout() {
    print_header "STEP 6: LOGOUT AND SESSION CLEANUP"

    if [[ -z "$USER_A_ID" || -z "$ALICE_SESSION_TOKEN" ]]; then
        print_error "Required authentication not available, skipping logout tests"
        return 1
    fi

    # Alice logs out
    execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/Logout" "{
        \"user_id\": \"$USER_A_ID\",
        \"session_id\": \"$ALICE_SESSION_TOKEN\"
    }" "Alice logs out"

    # Verify Alice's session is now invalid
    local validation_response
    validation_response=$(execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/ValidateSession" "{
        \"session_token\": \"$ALICE_SESSION_TOKEN\"
    }" "Verify Alice's session is invalid after logout" || true)

    if [[ $? -ne 0 ]]; then
        print_success "Session correctly invalidated after logout"
    else
        print_error "Session still valid after logout"
    fi

    # Bob logs out
    if [[ -n "$BOB_SESSION_TOKEN" ]]; then
        execute_grpc "$GATEWAY_ADDR" "hopen.auth.v1.AuthService/Logout" "{
            \"user_id\": \"$USER_B_ID\",
            \"session_id\": \"$BOB_SESSION_TOKEN\"
        }" "Bob logs out"
    fi
}

# Phase 8: Edge Cases and Error Handling
test_edge_cases() {
    print_header "PHASE 8: EDGE CASES AND ERROR HANDLING"

    if [[ -z "$USER_A_ID" || -z "$USER_B_ID" ]]; then
        print_error "Required IDs not available, skipping edge case tests"
        return 1
    fi

    # Test duplicate contact request (should fail gracefully)
    execute_grpc "$GATEWAY_ADDR" "hopen.user.v1.UserService/SendContactRequest" "{
        \"user_id\": \"$USER_A_ID\",
        \"target_user_id\": \"$USER_B_ID\",
        \"message\": \"Duplicate request\"
    }" "Test duplicate contact request (should fail gracefully)" || true

    # Test invalid user ID (should fail gracefully)
    execute_grpc "$GATEWAY_ADDR" "hopen.user.v1.UserService/GetUserRelationshipStatus" "{
        \"user_id\": \"invalid-user-id\",
        \"target_user_id\": \"$USER_B_ID\"
    }" "Test invalid user ID (should fail gracefully)" || true

    # Test invalid bubble ID (should fail gracefully)
    execute_grpc "$GATEWAY_ADDR" "hopen.bubble.v1.BubbleService/GetBubble" "{
        \"bubble_id\": \"invalid-bubble-id\"
    }" "Test invalid bubble ID (should fail gracefully)" || true

    # Test unauthorized access (without session token)
    execute_grpc "$GATEWAY_ADDR" "hopen.user.v1.UserService/GetUser" "{
        \"user_id\": \"$USER_A_ID\"
    }" "Test unauthorized access (should fail)" || true
}

# Main execution function
main() {
    echo -e "${BLUE}🧪 COMPREHENSIVE GRPCURL TEST PLAN FOR HOPEN BACKEND${NC}"
    echo -e "${BLUE}======================================================${NC}\n"

    print_info "This test plan validates the complete user journey and backend functionality"
    print_info "Testing through Kong Gateway for production-like conditions"
    echo ""

    # Execute all test phases in order
    check_prerequisites
    test_health_checks
    test_user_registration
    test_contact_requests
    test_bubble_creation
    test_bubble_invitations
    test_bubble_dissolution
    test_bubble_expiration
    test_realtime_features
    test_logout
    test_edge_cases

    # Print summary
    print_header "TEST RESULTS SUMMARY"
    echo -e "Total Tests: $TOTAL_TESTS"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"

    # Print critical test results
    print_header "COMPREHENSIVE BACKEND VERIFICATION"
    echo -e "${CYAN}Complete Test Coverage:${NC}"
    echo -e "  • ✅ Health Checks for All Services"
    echo -e "  • ✅ User Registration and Authentication"
    echo -e "  • ✅ Session Management and Validation"
    echo -e "  • ✅ Password Security (HIBP Integration)"
    echo -e "  • ✅ Password Reset Flow"
    echo -e "  • ✅ Contact Request and Relationship Management"
    echo -e "  • ✅ Bubble Creation and Lifecycle Management"
    echo -e "  • ✅ Bubble Invitation and Voting System"
    echo -e "  • ✅ Bubble Dissolution vs Expiration Logic"
    echo -e "  • ✅ Maybe_Friend to Friend Conversion"
    echo -e "  • ✅ Real-time Messaging and Presence"
    echo -e "  • ✅ Media Upload and Download"
    echo -e "  • ✅ Session Logout and Cleanup"
    echo -e "  • ✅ Error Handling and Edge Cases"

    print_header "CRITICAL BUSINESS LOGIC VERIFICATION"
    echo -e "${CYAN}Key Distinctions Tested:${NC}"
    echo -e "  • Bubble Dissolution → Contact Relationship (maintains existing relationship)"
    echo -e "  • Bubble Expiration → Maybe_Friend → Friend Flow (creates new friendship path)"
    echo -e "  • Contact Request → Contact Relationship"
    echo -e "  • Bubble Creation and Invitation Flows"
    echo -e "  • Real-time Communication Features"
    echo -e "  • Authentication and Authorization Flows"

    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 ALL TESTS PASSED! BACKEND IS PRODUCTION READY!${NC}"
        echo -e "${GREEN}✅ Complete end-to-end functionality verified successfully.${NC}"
        echo -e "${GREEN}✅ All critical business logic flows working correctly.${NC}"
        echo -e "${GREEN}✅ Authentication, authorization, and security features validated.${NC}"
        echo -e "${GREEN}✅ Real-time communication and media services operational.${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ SOME TESTS FAILED. PLEASE REVIEW THE OUTPUT ABOVE.${NC}"
        echo -e "${RED}⚠️  Critical business logic may not be working as expected.${NC}"
        echo -e "${RED}⚠️  Backend may not be ready for production deployment.${NC}"
        exit 1
    fi
}

# Run the comprehensive test suite
main "$@"
