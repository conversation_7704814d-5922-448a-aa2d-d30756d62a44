#!/bin/bash

# ============================================================================
# 🧪 SIMPLE GRPCURL TEST FOR HOPEN BACKEND
# ============================================================================
# 
# This script tests the core functionality without health checks
# 
# Prerequisites:
# - All microservices running (docker-compose up -d)
# - grpcurl installed
# 
# Usage: ./simple_grpcurl_test.sh
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Direct service endpoints (matching docker-compose port mappings)
AUTH_ENDPOINT="localhost:4000"
USER_ENDPOINT="localhost:4002"
BUBBLE_ENDPOINT="localhost:4004"
REALTIME_ENDPOINT="localhost:4008"
PRESENCE_ENDPOINT="localhost:4018"
MEDIA_ENDPOINT="localhost:4014"

echo -e "${CYAN}🧪 SIMPLE GRPCURL TEST PLAN FOR HOPEN BACKEND${NC}"
echo "======================================================"
echo ""
echo -e "${BLUE}ℹ Testing core functionality without health checks${NC}"
echo ""

# Function to print test step
print_step() {
    echo -e "${YELLOW}📋 $1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to extract value from JSON response
extract_json_value() {
    local json="$1"
    local key="$2"
    echo "$json" | grep -o "\"$key\":[^,}]*" | cut -d':' -f2 | tr -d '"' | tr -d ' '
}

# Test variables
ALICE_EMAIL="<EMAIL>"
ALICE_USERNAME="alice_smith"
ALICE_PASSWORD="SecurePass123!"
BOB_EMAIL="<EMAIL>"
BOB_USERNAME="bob_jones"
BOB_PASSWORD="SecurePass456!"

echo "═══ STEP 1: SERVICE AVAILABILITY ═══"

print_step "Testing Auth Service availability"
if grpcurl -plaintext "$AUTH_ENDPOINT" list hopen.auth.v1.AuthService >/dev/null 2>&1; then
    print_success "Auth Service is available"
else
    print_error "Auth Service is not available"
    exit 1
fi

print_step "Testing User Service availability"
if grpcurl -plaintext "$USER_ENDPOINT" list hopen.user.v1.UserService >/dev/null 2>&1; then
    print_success "User Service is available"
else
    print_error "User Service is not available"
    exit 1
fi

print_step "Testing Bubble Service availability"
if grpcurl -plaintext "$BUBBLE_ENDPOINT" list hopen.bubble.v1.BubbleService >/dev/null 2>&1; then
    print_success "Bubble Service is available"
else
    print_error "Bubble Service is not available"
    exit 1
fi

echo ""
echo "═══ STEP 2: USER REGISTRATION ═══"

print_step "Registering Alice"
ALICE_RESPONSE=$(grpcurl -plaintext -d "{\"username\": \"$ALICE_USERNAME\", \"email\": \"$ALICE_EMAIL\", \"password\": \"$ALICE_PASSWORD\", \"first_name\": \"Alice\", \"last_name\": \"Smith\"}" "$AUTH_ENDPOINT" hopen.auth.v1.AuthService/RegisterUser 2>&1)

if echo "$ALICE_RESPONSE" | grep -q "user_id"; then
    ALICE_USER_ID=$(extract_json_value "$ALICE_RESPONSE" "user_id")
    print_success "Alice registered successfully with ID: $ALICE_USER_ID"
else
    print_error "Alice registration failed: $ALICE_RESPONSE"
    echo "Continuing with other tests..."
fi

print_step "Registering Bob"
BOB_RESPONSE=$(grpcurl -plaintext -d "{\"username\": \"$BOB_USERNAME\", \"email\": \"$BOB_EMAIL\", \"password\": \"$BOB_PASSWORD\", \"first_name\": \"Bob\", \"last_name\": \"Jones\"}" "$AUTH_ENDPOINT" hopen.auth.v1.AuthService/RegisterUser 2>&1)

if echo "$BOB_RESPONSE" | grep -q "user_id"; then
    BOB_USER_ID=$(extract_json_value "$BOB_RESPONSE" "user_id")
    print_success "Bob registered successfully with ID: $BOB_USER_ID"
else
    print_error "Bob registration failed: $BOB_RESPONSE"
    echo "Continuing with other tests..."
fi

echo ""
echo "═══ STEP 3: USER LOGIN ═══"

if [ ! -z "$ALICE_USER_ID" ]; then
    print_step "Logging in Alice"
    ALICE_LOGIN_RESPONSE=$(grpcurl -plaintext -d "{\"email\": \"$ALICE_EMAIL\", \"password\": \"$ALICE_PASSWORD\"}" "$AUTH_ENDPOINT" hopen.auth.v1.AuthService/LoginUser 2>&1)
    
    if echo "$ALICE_LOGIN_RESPONSE" | grep -q "session_token"; then
        ALICE_SESSION_TOKEN=$(extract_json_value "$ALICE_LOGIN_RESPONSE" "session_token")
        print_success "Alice logged in successfully"
    else
        print_error "Alice login failed: $ALICE_LOGIN_RESPONSE"
    fi
fi

if [ ! -z "$BOB_USER_ID" ]; then
    print_step "Logging in Bob"
    BOB_LOGIN_RESPONSE=$(grpcurl -plaintext -d "{\"email\": \"$BOB_EMAIL\", \"password\": \"$BOB_PASSWORD\"}" "$AUTH_ENDPOINT" hopen.auth.v1.AuthService/LoginUser 2>&1)
    
    if echo "$BOB_LOGIN_RESPONSE" | grep -q "session_token"; then
        BOB_SESSION_TOKEN=$(extract_json_value "$BOB_LOGIN_RESPONSE" "session_token")
        print_success "Bob logged in successfully"
    else
        print_error "Bob login failed: $BOB_LOGIN_RESPONSE"
    fi
fi

echo ""
echo "═══ STEP 4: SESSION VALIDATION ═══"

if [ ! -z "$ALICE_SESSION_TOKEN" ]; then
    print_step "Validating Alice's session"
    ALICE_VALIDATION_RESPONSE=$(grpcurl -plaintext -d "{\"session_token\": \"$ALICE_SESSION_TOKEN\"}" "$AUTH_ENDPOINT" hopen.auth.v1.AuthService/ValidateSession 2>&1)
    
    if echo "$ALICE_VALIDATION_RESPONSE" | grep -q "valid.*true"; then
        print_success "Alice's session is valid"
    else
        print_error "Alice's session validation failed: $ALICE_VALIDATION_RESPONSE"
    fi
fi

echo ""
echo "═══ TEST SUMMARY ═══"
echo -e "${CYAN}Test completed. Check the results above for any failures.${NC}"
echo ""

if [ ! -z "$ALICE_USER_ID" ] && [ ! -z "$BOB_USER_ID" ] && [ ! -z "$ALICE_SESSION_TOKEN" ] && [ ! -z "$BOB_SESSION_TOKEN" ]; then
    print_success "Core authentication flow is working!"
    echo ""
    echo "Next steps you can test manually:"
    echo "- User profile management"
    echo "- Bubble creation and management"
    echo "- Real-time messaging"
    echo "- Contact requests and relationships"
else
    print_error "Some core functionality is not working. Check the logs above."
fi
