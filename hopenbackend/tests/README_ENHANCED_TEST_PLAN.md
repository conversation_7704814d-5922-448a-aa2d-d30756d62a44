# Enhanced gRPC Test Plan for Hopen Backend

## Overview

This enhanced test plan provides comprehensive validation of your Hopen backend against the detailed user stories and business requirements. It focuses on testing the **critical distinctions** between different relationship flows and bubble lifecycle events.

## Key Features

### 🎯 **Critical Business Logic Testing**
- **Bubble Dissolution vs Expiration**: Tests the crucial difference between these two scenarios
  - **Dissolution** (< 2 members) → Maintains existing **contact** relationships
  - **Expiration** (natural timeout) → Creates **maybe_friend** relationships → Friend request flow

### 🔄 **Complete User Journey Testing**
1. **User Registration** with HIBP password breach checking
2. **Contact Request Flow** (send → accept → verify relationship)
3. **Bubble Creation** (request → accept → verify state)
4. **Bubble Invitation & Voting** (invite → accept → vote → verify membership)
5. **Bubble Dissolution** (leave → verify dissolution → verify contact relationship maintained)
6. **Bubble Expiration Simulation** (expire → verify maybe_friend → mutual accept → verify friend)
7. **Real-time Features** (presence, messaging structure validation)
8. **Edge Cases** (duplicate requests, invalid IDs, error handling)

### 🛠 **Technical Improvements**
- **Direct Service Endpoints**: Tests services directly without Kong dependency
- **Enhanced Error Handling**: Robust response validation and error reporting
- **ID Management**: Automatic extraction and tracking of user/bubble/request IDs
- **Response Validation**: Verifies expected fields in responses
- **Sequential Dependencies**: Each phase builds on previous phases

## Service Endpoints

Based on your `docker-compose.yml` configuration:

```bash
Auth Service:     localhost:4000
User Service:     localhost:4002  
Bubble Service:   localhost:4004
Realtime Service: localhost:4008
Presence Service: localhost:4018
```

## Prerequisites

1. **Backend Services Running**:
   ```bash
   cd hopenbackend
   docker-compose up -d
   ```

2. **grpcurl Installed**:
   ```bash
   # macOS
   brew install grpcurl
   
   # Linux
   go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest
   ```

3. **Services Health Check**:
   ```bash
   # Verify all services are responding
   grpcurl -plaintext localhost:4000 list
   grpcurl -plaintext localhost:4002 list
   grpcurl -plaintext localhost:4004 list
   grpcurl -plaintext localhost:4008 list
   grpcurl -plaintext localhost:4018 list
   ```

## Usage

### Run the Complete Test Suite

```bash
cd hopenbackend/tests
./enhanced_grpcurl_test_plan.sh
```

### Run Individual Test Phases

You can also run individual test functions by modifying the `main()` function in the script:

```bash
# Example: Run only user registration and contact tests
# Edit the script and comment out unwanted test phases
```

## Test Phases Explained

### Phase 1: User Registration
- Creates 4 test users (Alice, Bob, Charlie, David)
- Tests HIBP password breach checking
- Captures user IDs for subsequent tests

### Phase 2: Contact Requests
- Alice sends contact request to Bob
- Verifies pending request status
- Bob accepts the request
- Verifies final "contact" relationship

### Phase 3: Bubble Creation
- Alice sends bubble start request to Bob
- Bob accepts the request
- Verifies bubble is ACTIVE with 2 members
- Confirms 90-day countdown is set

### Phase 4: Bubble Invitations
- Alice and Charlie become contacts
- Alice invites Charlie to the bubble
- Charlie accepts (triggers voting)
- Bob votes to approve
- Verifies Charlie is added and countdown is extended

### Phase 5: Bubble Dissolution (CRITICAL)
- Creates new bubble between Charlie and David
- Charlie leaves the 2-person bubble
- Verifies bubble status becomes DISSOLVED
- **CRITICAL**: Verifies Charlie and David remain "contacts" (not maybe_friend)

### Phase 6: Bubble Expiration (CRITICAL)
- Simulates bubble expiration for Alice-Bob bubble
- **CRITICAL**: Verifies relationship becomes "maybe_friend"
- Tests mutual friend request acceptance
- Verifies final "friend" relationship

### Phase 7: Real-time Features
- Tests presence update functionality
- Tests batch presence checking
- Tests message sending structure (without auth)

### Phase 8: Edge Cases
- Tests duplicate contact requests
- Tests invalid user/bubble IDs
- Verifies graceful error handling

## Expected Outcomes

### ✅ Success Indicators
- All services respond to health checks
- User registration creates valid user IDs
- Contact requests establish "contact" relationships
- Bubble creation results in ACTIVE bubbles
- Bubble dissolution maintains "contact" relationships
- Bubble expiration creates "maybe_friend" relationships
- Friend acceptance creates "friend" relationships

### ❌ Failure Indicators
- Services not responding
- Missing required fields in responses
- Incorrect relationship states
- Bubble dissolution creating "maybe_friend" (should be "contact")
- Bubble expiration not creating "maybe_friend"

## Output Format

The script provides color-coded output:
- 🔵 **Blue**: Section headers
- 🟡 **Yellow**: Individual test descriptions
- 🟢 **Green**: Successful tests
- 🔴 **Red**: Failed tests
- 🔵 **Cyan**: Informational messages
- 🔴 **Red with 🚨**: Critical business logic tests

## Troubleshooting

### Common Issues

1. **Services Not Running**:
   ```bash
   docker-compose ps
   docker-compose logs [service-name]
   ```

2. **Port Conflicts**:
   - Check if ports 4000, 4002, 4004, 4008, 4018 are available
   - Modify docker-compose.yml if needed

3. **grpcurl Not Found**:
   ```bash
   which grpcurl
   # Install if missing
   ```

4. **Connection Timeouts**:
   - Increase `TEST_TIMEOUT` in the script
   - Check service health with `docker-compose logs`

### Debug Mode

Add debug output by modifying the script:
```bash
# Add this near the top of the script
set -x  # Enable debug mode
```

## Integration with Existing Tests

This enhanced test plan complements your existing test infrastructure:
- `comprehensive_backend_test_plan.sh` - Your original comprehensive tests
- `manual_grpcurl_commands.sh` - Individual command reference
- `quick_backend_check.sh` - Quick health checks

## Next Steps

1. **Run the Test Plan**: Execute the script and verify all tests pass
2. **Address Failures**: Fix any failing tests in your backend implementation
3. **Integrate into CI/CD**: Add this script to your automated testing pipeline
4. **Extend Coverage**: Add additional test cases for specific edge cases
5. **Performance Testing**: Consider adding timing measurements for critical operations

## Critical Business Logic Verification

This test plan specifically validates the **most important distinction** in your system:

> **Bubble Dissolution** (due to insufficient members) results in former members maintaining their existing **contact** relationship, while **Bubble Expiration** (natural timeout) creates **maybe_friend** relationships that can evolve into **friend** relationships through mutual acceptance.

This distinction is crucial for your user experience and social graph integrity.
