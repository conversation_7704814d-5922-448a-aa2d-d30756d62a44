#!/bin/bash

# Test all backend microservice methods
# This script tests the internal ports within containers

# Color codes for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

echo -e "${BLUE}═══════════════════════════════════════════════════════════════════${NC}"
echo -e "${BLUE}           HOPEN BACKEND COMPREHENSIVE METHOD TESTS                ${NC}"
echo -e "${BLUE}═══════════════════════════════════════════════════════════════════${NC}"
echo

# Function to test a specific gRPC method
test_method() {
    local container=$1
    local port=$2
    local service_name=$3
    local method=$4
    local data=$5
    
    ((TOTAL_TESTS++))
    
    echo -e "${YELLOW}Testing ${service_name}/${method}...${NC}"
    
    # Check if container is running
    if ! docker ps --format '{{.Names}}' | grep -q "^${container}$"; then
        echo -e "${RED}  ✗ Container ${container} is not running${NC}"
        ((FAILED_TESTS++))
        return 1
    fi
    
    # Execute the gRPC call
    response=$(docker exec "${container}" grpcurl -plaintext -d "${data}" localhost:${port} ${service_name}/${method} 2>&1)
    exit_code=$?

    # Success if grpcurl exit code is 0
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}  ✓ Method is accessible${NC}"
        ((PASSED_TESTS++))
        return 0
    fi

    # If response contains known missing method errors -> fail
    if [[ "$response" == *"not expose service"* ]] || [[ "$response" == *"does not include a method"* ]] || [[ "$response" == *"Unimplemented desc = unknown service"* ]]; then
        echo -e "${RED}  ✗ Method not found: ${response}${NC}"
        ((FAILED_TESTS++))
        return 1
    fi

    # If response looks like JSON (server returned a valid payload), treat as pass
    if echo "$response" | grep -q '^\s*{'; then
        echo -e "${GREEN}  ✓ Method returned JSON payload (treated as accessible)${NC}"
        ((PASSED_TESTS++))
        return 0
    fi

    # Otherwise treat as pass if it includes a gRPC error code (means method is wired)
    if echo "$response" | grep -q "Code:"; then
        echo -e "${GREEN}  ✓ Method is accessible (gRPC error as expected for dummy data)${NC}"
        ((PASSED_TESTS++))
        return 0
    fi

    # Fallback unexpected response -> fail
    echo -e "${RED}  ✗ Unexpected response: ${response}${NC}"
    ((FAILED_TESTS++))
    return 1
}

# Function to list available methods for a service
list_methods() {
    local container=$1
    local port=$2
    local service_name=$3
    
    echo -e "${BLUE}Available methods for ${service_name}:${NC}"
    
    if ! docker ps --format '{{.Names}}' | grep -q "^${container}$"; then
        echo -e "${RED}  Container ${container} is not running${NC}"
        return 1
    fi
    
    docker exec "${container}" grpcurl -plaintext localhost:${port} describe ${service_name} 2>/dev/null || echo "  Could not list methods"
    echo
}

# Function to restart a crashed service
restart_service() {
    local service=$1
    echo -e "${YELLOW}Restarting ${service}...${NC}"
    docker-compose restart ${service} 2>&1 | grep -v "WARNING"
    sleep 5
}

echo -e "${BLUE}═══ AUTH SERVICE (Port 4000) ═══${NC}"
# First check if auth is running, if not restart it
if ! docker ps --format '{{.Names}}' | grep -q "^hopen_auth$"; then
    restart_service "auth"
fi

if docker ps --format '{{.Names}}' | grep -q "^hopen_auth$"; then
    list_methods "hopen_auth" "4000" "hopen.auth.v1.AuthService"
    test_method "hopen_auth" "4000" "hopen.auth.v1.AuthService" "RegisterUser" '{"email":"<EMAIL>","password":"Test123!@#","username":"testuser","first_name":"Test","last_name":"User"}'
    test_method "hopen_auth" "4000" "hopen.auth.v1.AuthService" "ValidateSession" '{"session_token":"test-token"}'
    test_method "hopen_auth" "4000" "hopen.auth.v1.AuthService" "RefreshToken" '{"refresh_token":"test-refresh"}'
    test_method "hopen_auth" "4000" "hopen.auth.v1.AuthService" "Logout" '{"user_id":"test-user","session_id":"test-token"}'
else
    echo -e "${RED}Auth service is not running - critical failure${NC}"
fi
echo

echo -e "${BLUE}═══ USER SERVICE (Port 4001) ═══${NC}"
list_methods "hopen_user" "4001" "hopen.user.v1.UserService"
test_method "hopen_user" "4001" "hopen.user.v1.UserService" "CreateUser" '{"email":"<EMAIL>","username":"testuser"}'
test_method "hopen_user" "4001" "hopen.user.v1.UserService" "GetUser" '{"user_id":"test-id"}'
test_method "hopen_user" "4001" "hopen.user.v1.UserService" "UpdateUser" '{"user_id":"test-id","username":"newname"}'
test_method "hopen_user" "4001" "hopen.user.v1.UserService" "DeleteUser" '{"user_id":"test-id"}'
test_method "hopen_user" "4001" "hopen.user.v1.UserService" "SearchUsers" '{"query":"test"}'
echo

echo -e "${BLUE}═══ BUBBLE SERVICE (Port 4002) ═══${NC}"
list_methods "hopen_bubble" "4002" "hopen.bubble.v1.BubbleService"
test_method "hopen_bubble" "4002" "hopen.bubble.v1.BubbleService" "CreateBubble" '{"name":"Test Bubble","description":"Test"}'
test_method "hopen_bubble" "4002" "hopen.bubble.v1.BubbleService" "GetBubble" '{"bubble_id":"test-id"}'
test_method "hopen_bubble" "4002" "hopen.bubble.v1.BubbleService" "UpdateBubble" '{"bubble_id":"test-id","name":"Updated"}'
test_method "hopen_bubble" "4002" "hopen.bubble.v1.BubbleService" "DeleteBubble" '{"bubble_id":"test-id"}'
test_method "hopen_bubble" "4002" "hopen.bubble.v1.BubbleService" "JoinBubble" '{"bubble_id":"test-id","user_id":"user-id"}'
echo

echo -e "${BLUE}═══ CALL SERVICE (Port 4006) ═══${NC}"
list_methods "hopen_call" "4006" "hopen.call.v1.CallService"
test_method "hopen_call" "4006" "hopen.call.v1.CallService" "StartCall" '{"user_id":"caller","bubble_id":"test-bubble","call_type":"CALL_TYPE_AUDIO"}'
test_method "hopen_call" "4006" "hopen.call.v1.CallService" "EndCall" '{"call_id":"test-call"}'
test_method "hopen_call" "4006" "hopen.call.v1.CallService" "GetCallHistory" '{"user_id":"test-user"}'
echo

echo -e "${BLUE}═══ REALTIME SERVICE (Port 4007) ═══${NC}"
list_methods "hopen_realtime" "4007" "hopen.realtime.v1.RealtimeService"
test_method "hopen_realtime" "4007" "hopen.realtime.v1.RealtimeService" "SendMessage" '{"bubble_id":"test-bubble","content":"hello","message_type":"TEXT"}'
test_method "hopen_realtime" "4007" "hopen.realtime.v1.RealtimeService" "GetMessages" '{"bubble_id":"test-bubble","limit":50}'
echo

echo -e "${BLUE}═══ EMAIL SERVICE (Port 4010) ═══${NC}"
list_methods "hopen_email" "4010" "hopen.email.v1.EmailService"
test_method "hopen_email" "4010" "hopen.email.v1.EmailService" "SendEmail" '{"from_email":"<EMAIL>","to_emails":["<EMAIL>"],"subject":"Test","html_body":"Test email"}'
test_method "hopen_email" "4010" "hopen.email.v1.EmailService" "VerifyEmailAddress" '{"email":"<EMAIL>"}'
test_method "hopen_email" "4010" "hopen.email.v1.EmailService" "SendTemplatedEmail" '{"from_email":"<EMAIL>","to_emails":["<EMAIL>"],"template_name":"welcome"}'
echo

echo -e "${BLUE}═══ NOTIFICATION SERVICE (Port 4012) ═══${NC}"
list_methods "hopen_notification" "4012" "hopen.notification.v1.NotificationService"
test_method "hopen_notification" "4012" "hopen.notification.v1.NotificationService" "SendPushNotification" '{"user_id":"test-user","title":"Test","body":"Test notification"}'
test_method "hopen_notification" "4012" "hopen.notification.v1.NotificationService" "GetUserNotifications" '{"user_id":"test-user"}'
test_method "hopen_notification" "4012" "hopen.notification.v1.NotificationService" "MarkNotificationAsRead" '{"user_id":"00000000-0000-0000-0000-000000000000","notification_id":"test-id"}'
echo

echo -e "${BLUE}═══ PRESENCE SERVICE (Port 4013) ═══${NC}"
list_methods "hopen_presence" "4013" "hopen.presence.v1.PresenceService"
test_method "hopen_presence" "4013" "hopen.presence.v1.PresenceService" "UpdateUserPresence" '{"user_id":"test-user","status":"PRESENCE_STATUS_ONLINE"}'
test_method "hopen_presence" "4013" "hopen.presence.v1.PresenceService" "GetUserPresence" '{"user_id":"test-user"}'
test_method "hopen_presence" "4013" "hopen.presence.v1.PresenceService" "GetBatchPresence" '{"user_ids":["user1","user2"]}'
echo

echo -e "${BLUE}═══ SOCIAL ANALYTICS SERVICE (Port 4014) ═══${NC}"
list_methods "hopen_social_analytics" "4014" "hopen.social_analytics.v1.SocialAnalyticsService"
test_method "hopen_social_analytics" "4014" "hopen.social_analytics.v1.SocialAnalyticsService" "GetUserAnalytics" '{"user_id":"test-user"}'
test_method "hopen_social_analytics" "4014" "hopen.social_analytics.v1.SocialAnalyticsService" "GetBubbleAnalytics" '{"bubble_id":"test-bubble"}'
test_method "hopen_social_analytics" "4014" "hopen.social_analytics.v1.SocialAnalyticsService" "TrackEvent" '{"user_id":"test-user","event_name":"VIEW"}'
echo

echo -e "${BLUE}═══ SYNC SERVICE (Port 4015) ═══${NC}"
list_methods "hopen_sync" "4015" "hopen.sync.v1.SyncService"
test_method "hopen_sync" "4015" "hopen.sync.v1.SyncService" "GetSyncStatus" '{"user_id":"test-user"}'
test_method "hopen_sync" "4015" "hopen.sync.v1.SyncService" "GetSyncData" '{"user_id":"test-user","data_types":["messages"]}'
echo

echo -e "${BLUE}═══ SEARCH SERVICE (Port 4016) ═══${NC}"
list_methods "hopen_search" "4016" "hopen.search.v1.SearchService"
test_method "hopen_search" "4016" "hopen.search.v1.SearchService" "SearchUsers" '{"query":"test"}'
test_method "hopen_search" "4016" "hopen.search.v1.SearchService" "SearchMessages" '{"query":"test","limit":10}'
echo

echo -e "${BLUE}═══ MEDIA SERVICE (Port 4016) ═══${NC}"
list_methods "hopen_media" "4016" "hopen.media.v1.MediaService"
test_method "hopen_media" "4016" "hopen.media.v1.MediaService" "UploadFile" '{"file_name":"test.jpg","file_type":"image/jpeg"}'
test_method "hopen_media" "4016" "hopen.media.v1.MediaService" "GetFileInfo" '{"file_id":"test-id"}'
test_method "hopen_media" "4016" "hopen.media.v1.MediaService" "DeleteFile" '{"file_id":"test-id"}'
echo

echo -e "${BLUE}═══════════════════════════════════════════════════════════════════${NC}"
echo -e "${BLUE}                          TEST SUMMARY                             ${NC}"
echo -e "${BLUE}═══════════════════════════════════════════════════════════════════${NC}"
echo

# Calculate success rate
if [ $TOTAL_TESTS -gt 0 ]; then
    SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
else
    SUCCESS_RATE=0
fi

echo -e "Total Tests: ${TOTAL_TESTS}"
echo -e "${GREEN}✓ Passed: ${PASSED_TESTS}${NC}"
echo -e "${RED}✗ Failed: ${FAILED_TESTS}${NC}"
echo -e "Success Rate: ${SUCCESS_RATE}%"
echo

if [ $SUCCESS_RATE -ge 80 ]; then
    echo -e "${GREEN}✅ BACKEND IS READY FOR USE!${NC}"
    echo -e "${GREEN}   Most methods are accessible and responding correctly.${NC}"
    exit 0
elif [ $SUCCESS_RATE -ge 60 ]; then
    echo -e "${YELLOW}⚠️  BACKEND IS PARTIALLY READY${NC}"
    echo -e "${YELLOW}   Some services have issues that should be investigated.${NC}"
    exit 1
else
    echo -e "${RED}❌ BACKEND HAS CRITICAL ISSUES${NC}"
    echo -e "${RED}   Many services are failing. Please check container logs.${NC}"
    exit 2
fi
