#!/bin/bash

# Comprehensive Test Runner for Hopen Backend
# Runs all test suites with proper setup, execution, and reporting

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Test configuration
TEST_TIMEOUT=${TEST_TIMEOUT:-30m}
COVERAGE_THRESHOLD=${COVERAGE_THRESHOLD:-80}
PARALLEL_TESTS=${PARALLEL_TESTS:-true}
GENERATE_REPORTS=${GENERATE_REPORTS:-true}
CLEANUP_AFTER=${CLEANUP_AFTER:-true}

# Directories
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_RESULTS_DIR="$PROJECT_ROOT/test-results"
COVERAGE_DIR="$TEST_RESULTS_DIR/coverage"

# Test counters
TOTAL_SUITES=0
PASSED_SUITES=0
FAILED_SUITES=0
FAILED_SUITE_NAMES=()

# Utility functions
print_header() {
    echo -e "\n${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}\n"
}

print_section() {
    echo -e "\n${CYAN}▶ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ $1${NC}"
}

# Setup test environment
setup_test_environment() {
    print_section "Setting up test environment"
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    mkdir -p "$COVERAGE_DIR"
    
    # Set Go test environment variables
    export GO_ENV=test
    export CGO_ENABLED=1
    
    
    # Set test database URLs
    export TEST_POSTGRES_HOST=${TEST_POSTGRES_HOST:-localhost}
    export TEST_POSTGRES_DB=${TEST_POSTGRES_DB:-hopen_test}
    export TEST_POSTGRES_USER=${TEST_POSTGRES_USER:-hopen}
    export TEST_POSTGRES_PASSWORD=${TEST_POSTGRES_PASSWORD:-hopen123}
    export TEST_SCYLLA_HOST=${TEST_SCYLLA_HOST:-localhost}
    export TEST_SCYLLA_KEYSPACE=${TEST_SCYLLA_KEYSPACE:-hopen_test}
    export TEST_NATS_URL=${TEST_NATS_URL:-nats://localhost:4222}
    
    print_success "Test environment configured"
}

# Check prerequisites
check_prerequisites() {
    print_section "Checking prerequisites"
    
    # Check Go installation
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed"
        exit 1
    fi
    print_success "Go is available: $(go version)"
    
    # Check if services are running
    local required_services=("postgresql" "scylladb" "nats" "valkey")
    for service in "${required_services[@]}"; do
        if docker ps --format "{{.Names}}" | grep -q "hopen_$service"; then
            print_success "$service is running"
        else
            print_warning "$service is not running - some tests may fail"
        fi
    done
    
    # Check test dependencies
    go mod download
    print_success "Dependencies downloaded"
}

# Run unit tests
run_unit_tests() {
    print_section "Running unit tests"
    TOTAL_SUITES=$((TOTAL_SUITES + 1))
    
    local test_args=()
    test_args+=("-v")
    test_args+=("-timeout" "$TEST_TIMEOUT")
    test_args+=("-coverprofile=$COVERAGE_DIR/unit.out")
    test_args+=("-covermode=atomic")
    
    if [[ "$PARALLEL_TESTS" == "true" ]]; then
        test_args+=("-parallel" "4")
    fi
    
    if go test "${test_args[@]}" ./tests/unit/...; then
        print_success "Unit tests passed"
        PASSED_SUITES=$((PASSED_SUITES + 1))
    else
        print_error "Unit tests failed"
        FAILED_SUITES=$((FAILED_SUITES + 1))
        FAILED_SUITE_NAMES+=("unit")
    fi
}

# Run integration tests
run_integration_tests() {
    print_section "Running integration tests"
    TOTAL_SUITES=$((TOTAL_SUITES + 1))
    
    local test_args=()
    test_args+=("-v")
    test_args+=("-timeout" "$TEST_TIMEOUT")
    test_args+=("-coverprofile=$COVERAGE_DIR/integration.out")
    test_args+=("-covermode=atomic")
    test_args+=("-tags=integration")
    
    if go test "${test_args[@]}" ./tests/integration/...; then
        print_success "Integration tests passed"
        PASSED_SUITES=$((PASSED_SUITES + 1))
    else
        print_error "Integration tests failed"
        FAILED_SUITES=$((FAILED_SUITES + 1))
        FAILED_SUITE_NAMES+=("integration")
    fi
}

# Run component tests
run_component_tests() {
    print_section "Running component tests"
    TOTAL_SUITES=$((TOTAL_SUITES + 1))
    
    local test_args=()
    test_args+=("-v")
    test_args+=("-timeout" "$TEST_TIMEOUT")
    test_args+=("-coverprofile=$COVERAGE_DIR/component.out")
    test_args+=("-covermode=atomic")
    test_args+=("-tags=component")
    
    if go test "${test_args[@]}" ./tests/component/...; then
        print_success "Component tests passed"
        PASSED_SUITES=$((PASSED_SUITES + 1))
    else
        print_error "Component tests failed"
        FAILED_SUITES=$((FAILED_SUITES + 1))
        FAILED_SUITE_NAMES+=("component")
    fi
}

# Run performance tests
run_performance_tests() {
    print_section "Running performance tests"
    TOTAL_SUITES=$((TOTAL_SUITES + 1))
    
    local test_args=()
    test_args+=("-v")
    test_args+=("-timeout" "$TEST_TIMEOUT")
    test_args+=("-bench=.")
    test_args+=("-benchmem")
    test_args+=("-benchtime=5s")
    
    if go test "${test_args[@]}" ./tests/performance/... > "$TEST_RESULTS_DIR/performance.txt"; then
        print_success "Performance tests completed"
        PASSED_SUITES=$((PASSED_SUITES + 1))
    else
        print_error "Performance tests failed"
        FAILED_SUITES=$((FAILED_SUITES + 1))
        FAILED_SUITE_NAMES+=("performance")
    fi
}

# Run load tests
run_load_tests() {
    print_section "Running load tests"
    TOTAL_SUITES=$((TOTAL_SUITES + 1))
    
    # Check if k6 is available
    if command -v k6 &> /dev/null; then
        if k6 run tests/load/k6_scenario.js --out json="$TEST_RESULTS_DIR/load_test.json"; then
            print_success "Load tests completed"
            PASSED_SUITES=$((PASSED_SUITES + 1))
        else
            print_error "Load tests failed"
            FAILED_SUITES=$((FAILED_SUITES + 1))
            FAILED_SUITE_NAMES+=("load")
        fi
    else
        print_warning "k6 not available, skipping load tests"
    fi
}

# Generate coverage report
generate_coverage_report() {
    if [[ "$GENERATE_REPORTS" != "true" ]]; then
        return
    fi
    
    print_section "Generating coverage report"
    
    # Merge coverage files
    local coverage_files=()
    for file in "$COVERAGE_DIR"/*.out; do
        if [[ -f "$file" ]]; then
            coverage_files+=("$file")
        fi
    done
    
    if [[ ${#coverage_files[@]} -gt 0 ]]; then
        # Merge coverage profiles
        echo "mode: atomic" > "$COVERAGE_DIR/merged.out"
        for file in "${coverage_files[@]}"; do
            tail -n +2 "$file" >> "$COVERAGE_DIR/merged.out"
        done
        
        # Generate HTML report
        go tool cover -html="$COVERAGE_DIR/merged.out" -o "$COVERAGE_DIR/coverage.html"
        
        # Calculate coverage percentage
        local coverage_percent
        coverage_percent=$(go tool cover -func="$COVERAGE_DIR/merged.out" | grep total | awk '{print $3}' | sed 's/%//')
        
        print_info "Coverage report generated: $COVERAGE_DIR/coverage.html"
        print_info "Total coverage: ${coverage_percent}%"
        
        # Check coverage threshold
        if (( $(echo "$coverage_percent >= $COVERAGE_THRESHOLD" | bc -l) )); then
            print_success "Coverage threshold met (${coverage_percent}% >= ${COVERAGE_THRESHOLD}%)"
        else
            print_warning "Coverage below threshold (${coverage_percent}% < ${COVERAGE_THRESHOLD}%)"
        fi
    else
        print_warning "No coverage files found"
    fi
}

# Generate test reports
generate_test_reports() {
    if [[ "$GENERATE_REPORTS" != "true" ]]; then
        return
    fi
    
    print_section "Generating test reports"
    
    # Create summary report
    cat > "$TEST_RESULTS_DIR/summary.md" << EOF
# Test Execution Summary

**Date:** $(date)
**Total Test Suites:** $TOTAL_SUITES
**Passed:** $PASSED_SUITES
**Failed:** $FAILED_SUITES

## Test Results

| Suite | Status |
|-------|--------|
| Unit Tests | $(if [[ " ${FAILED_SUITE_NAMES[*]} " =~ " unit " ]]; then echo "❌ Failed"; else echo "✅ Passed"; fi) |
| Integration Tests | $(if [[ " ${FAILED_SUITE_NAMES[*]} " =~ " integration " ]]; then echo "❌ Failed"; else echo "✅ Passed"; fi) |
| Component Tests | $(if [[ " ${FAILED_SUITE_NAMES[*]} " =~ " component " ]]; then echo "❌ Failed"; else echo "✅ Passed"; fi) |
| Performance Tests | $(if [[ " ${FAILED_SUITE_NAMES[*]} " =~ " performance " ]]; then echo "❌ Failed"; else echo "✅ Passed"; fi) |
| Load Tests | $(if [[ " ${FAILED_SUITE_NAMES[*]} " =~ " load " ]]; then echo "❌ Failed"; else echo "✅ Passed"; fi) |

## Failed Suites
$(if [[ ${#FAILED_SUITE_NAMES[@]} -gt 0 ]]; then
    for suite in "${FAILED_SUITE_NAMES[@]}"; do
        echo "- $suite"
    done
else
    echo "None"
fi)

## Coverage
- HTML Report: [coverage.html](coverage/coverage.html)
- Threshold: ${COVERAGE_THRESHOLD}%

## Performance Results
- Performance Report: [performance.txt](performance.txt)
- Load Test Results: [load_test.json](load_test.json)
EOF
    
    print_success "Test summary generated: $TEST_RESULTS_DIR/summary.md"
}

# Cleanup function
cleanup() {
    if [[ "$CLEANUP_AFTER" == "true" ]]; then
        print_section "Cleaning up"
        
        # Clean up test data
        if [[ -f "$PROJECT_ROOT/tests/testutils/cleanup.sh" ]]; then
            bash "$PROJECT_ROOT/tests/testutils/cleanup.sh"
        fi
        
        print_success "Cleanup completed"
    fi
}

# Main execution
main() {
    print_header "🧪 COMPREHENSIVE BACKEND TEST SUITE"
    
    # Setup
    setup_test_environment
    check_prerequisites
    
    # Run test suites
    run_unit_tests
    run_integration_tests
    run_component_tests
    run_performance_tests
    run_load_tests
    
    # Generate reports
    generate_coverage_report
    generate_test_reports
    
    # Cleanup
    cleanup
    
    # Final summary
    print_header "📊 TEST EXECUTION SUMMARY"
    echo -e "Total Test Suites: ${BLUE}$TOTAL_SUITES${NC}"
    echo -e "Passed: ${GREEN}$PASSED_SUITES${NC}"
    echo -e "Failed: ${RED}$FAILED_SUITES${NC}"
    
    if [[ $FAILED_SUITES -gt 0 ]]; then
        echo -e "\n${RED}Failed Suites:${NC}"
        for suite in "${FAILED_SUITE_NAMES[@]}"; do
            echo -e "  ${RED}• $suite${NC}"
        done
        echo -e "\n${RED}❌ Some tests failed${NC}"
        exit 1
    else
        echo -e "\n${GREEN}✅ All tests passed!${NC}"
        exit 0
    fi
}

# Handle script interruption
trap cleanup EXIT

# Run main function
main "$@"
