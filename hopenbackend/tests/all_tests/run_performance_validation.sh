#!/bin/bash

# Performance Validation Test Runner
# This script runs comprehensive tests to validate the performance optimizations
# and social analytics fixes implemented.

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_RESULTS_DIR="$SCRIPT_DIR/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create results directory
mkdir -p "$TEST_RESULTS_DIR"

echo -e "${BLUE}🚀 Starting Performance Validation Test Suite${NC}"
echo -e "${BLUE}================================================${NC}"
echo "Timestamp: $TIMESTAMP"
echo "Project Root: $PROJECT_ROOT"
echo "Results Directory: $TEST_RESULTS_DIR"
echo ""

# Function to print section headers
print_section() {
    echo -e "\n${YELLOW}📋 $1${NC}"
    echo -e "${YELLOW}$(printf '=%.0s' {1..50})${NC}"
}

# Function to run a test and capture results
run_test() {
    local test_name="$1"
    local test_command="$2"
    local result_file="$TEST_RESULTS_DIR/${test_name}_${TIMESTAMP}.log"
    
    echo -e "${BLUE}🔄 Running: $test_name${NC}"
    
    if eval "$test_command" > "$result_file" 2>&1; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        echo -e "${RED}   Check log: $result_file${NC}"
        return 1
    fi
}

# Function to run benchmark and capture metrics
run_benchmark() {
    local bench_name="$1"
    local bench_command="$2"
    local result_file="$TEST_RESULTS_DIR/${bench_name}_benchmark_${TIMESTAMP}.log"
    
    echo -e "${BLUE}📊 Running benchmark: $bench_name${NC}"
    
    if eval "$bench_command" > "$result_file" 2>&1; then
        echo -e "${GREEN}✅ COMPLETED: $bench_name${NC}"
        
        # Extract key metrics from benchmark results
        if grep -q "ns/op" "$result_file"; then
            local avg_time=$(grep "ns/op" "$result_file" | awk '{print $3}' | head -1)
            echo -e "${GREEN}   Average time: $avg_time ns/op${NC}"
        fi
        
        if grep -q "B/op" "$result_file"; then
            local memory_usage=$(grep "B/op" "$result_file" | awk '{print $5}' | head -1)
            echo -e "${GREEN}   Memory usage: $memory_usage B/op${NC}"
        fi
        
        return 0
    else
        echo -e "${RED}❌ FAILED: $bench_name${NC}"
        echo -e "${RED}   Check log: $result_file${NC}"
        return 1
    fi
}

# Change to project root
cd "$PROJECT_ROOT"

# Initialize test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 1. Infrastructure Check
print_section "Infrastructure Validation"

echo "🔍 Checking database connectivity..."
if run_test "database_connectivity" "go test -v ./tests/integration -run TestDatabaseConnectivity -timeout 30s"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "🔍 Checking required services..."
if run_test "service_health" "go test -v ./tests/integration -run TestServiceHealth -timeout 30s"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# 2. Performance Optimization Tests
print_section "Performance Optimization Validation"

echo "🚀 Testing server-side sorting performance..."
if run_test "server_side_sorting" "go test -v ./tests/integration -run TestPerformanceOptimizationSuite/TestServerSideSortingPerformance -timeout 2m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "🚀 Testing client vs server performance comparison..."
if run_test "performance_comparison" "go test -v ./tests/integration -run TestPerformanceOptimizationSuite/TestClientSideVsServerSideComparison -timeout 5m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "🚀 Testing sorting accuracy..."
if run_test "sorting_accuracy" "go test -v ./tests/integration -run TestPerformanceOptimizationSuite/TestSortingAccuracy -timeout 1m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "🚀 Testing pagination performance..."
if run_test "pagination_performance" "go test -v ./tests/integration -run TestPerformanceOptimizationSuite/TestPaginationPerformance -timeout 3m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# 3. Social Analytics Validation
print_section "Social Analytics Microservice Validation"

echo "🔍 Testing database schema consistency..."
if run_test "schema_consistency" "go test -v ./tests/integration -run TestSocialAnalyticsValidationSuite/TestDatabaseSchemaConsistency -timeout 1m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "🔍 Testing analytics data integrity..."
if run_test "data_integrity" "go test -v ./tests/integration -run TestSocialAnalyticsValidationSuite/TestAnalyticsDataIntegrity -timeout 2m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "🔍 Testing enhanced profile data consistency..."
if run_test "profile_consistency" "go test -v ./tests/integration -run TestSocialAnalyticsValidationSuite/TestEnhancedProfileDataConsistency -timeout 1m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "🔍 Testing performance optimizations..."
if run_test "analytics_performance" "go test -v ./tests/integration -run TestSocialAnalyticsValidationSuite/TestPerformanceOptimizations -timeout 2m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "🔍 Testing error handling..."
if run_test "error_handling" "go test -v ./tests/integration -run TestSocialAnalyticsValidationSuite/TestErrorHandling -timeout 1m"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# 4. Performance Benchmarks
print_section "Performance Benchmarks"

echo "📊 Running user service benchmarks..."
if run_benchmark "user_service" "go test -bench=BenchmarkUserService -benchmem -benchtime=10s ./tests/performance/"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "📊 Running database operation benchmarks..."
if run_benchmark "database_ops" "go test -bench=BenchmarkDatabaseOperations -benchmem -benchtime=10s ./tests/performance/"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

echo "📊 Running sorting algorithm benchmarks..."
if run_benchmark "sorting_algorithms" "go test -bench=BenchmarkSorting -benchmem -benchtime=10s ./tests/performance/"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# 5. Load Testing (Optional - only if infrastructure supports it)
print_section "Load Testing (Optional)"

if command -v k6 &> /dev/null; then
    echo "📈 Running light load test..."
    if run_test "light_load" "k6 run --duration=30s --vus=10 ./tests/performance/load/comprehensive_load_test.js"; then
        ((PASSED_TESTS++))
    else
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
else
    echo -e "${YELLOW}⚠️  k6 not found, skipping load tests${NC}"
fi

# 6. Generate Summary Report
print_section "Test Results Summary"

echo -e "\n${BLUE}📊 PERFORMANCE VALIDATION RESULTS${NC}"
echo -e "${BLUE}=================================${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

# Calculate success rate
if [ $TOTAL_TESTS -gt 0 ]; then
    SUCCESS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "Success Rate: $SUCCESS_RATE%"
    
    if [ $SUCCESS_RATE -ge 90 ]; then
        echo -e "\n${GREEN}🎉 EXCELLENT: Performance validation passed with $SUCCESS_RATE% success rate!${NC}"
        echo -e "${GREEN}✅ System is ready for production deployment.${NC}"
    elif [ $SUCCESS_RATE -ge 75 ]; then
        echo -e "\n${YELLOW}⚠️  GOOD: Performance validation passed with $SUCCESS_RATE% success rate.${NC}"
        echo -e "${YELLOW}🔧 Consider addressing failed tests before production deployment.${NC}"
    else
        echo -e "\n${RED}❌ POOR: Performance validation failed with only $SUCCESS_RATE% success rate.${NC}"
        echo -e "${RED}🚫 DO NOT deploy to production until issues are resolved.${NC}"
    fi
else
    echo -e "\n${RED}❌ No tests were executed.${NC}"
fi

# Generate detailed report
REPORT_FILE="$TEST_RESULTS_DIR/performance_validation_report_${TIMESTAMP}.md"
echo "📄 Generating detailed report: $REPORT_FILE"

cat > "$REPORT_FILE" << EOF
# Performance Validation Report

**Generated:** $(date)
**Test Suite:** Performance Optimization and Social Analytics Validation

## Summary

- **Total Tests:** $TOTAL_TESTS
- **Passed:** $PASSED_TESTS
- **Failed:** $FAILED_TESTS
- **Success Rate:** $SUCCESS_RATE%

## Test Categories

### 1. Infrastructure Validation
- Database connectivity
- Service health checks

### 2. Performance Optimization Validation
- Server-side sorting performance
- Client vs server performance comparison
- Sorting accuracy validation
- Pagination performance testing

### 3. Social Analytics Microservice Validation
- Database schema consistency
- Analytics data integrity
- Enhanced profile data consistency
- Performance optimizations
- Error handling

### 4. Performance Benchmarks
- User service benchmarks
- Database operation benchmarks
- Sorting algorithm benchmarks

### 5. Load Testing
- Light load testing (if k6 available)

## Detailed Results

See individual log files in: $TEST_RESULTS_DIR

## Recommendations

EOF

if [ $SUCCESS_RATE -ge 90 ]; then
    echo "✅ System is production-ready with excellent performance." >> "$REPORT_FILE"
elif [ $SUCCESS_RATE -ge 75 ]; then
    echo "⚠️ System shows good performance but review failed tests." >> "$REPORT_FILE"
else
    echo "❌ System requires significant improvements before production." >> "$REPORT_FILE"
fi

echo -e "\n${GREEN}📄 Detailed report saved to: $REPORT_FILE${NC}"
echo -e "${GREEN}📁 All test logs available in: $TEST_RESULTS_DIR${NC}"

# Exit with appropriate code
if [ $SUCCESS_RATE -ge 75 ]; then
    exit 0
else
    exit 1
fi
