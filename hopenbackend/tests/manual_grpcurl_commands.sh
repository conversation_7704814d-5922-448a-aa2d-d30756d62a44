#!/bin/bash

# ============================================================================
# 🔧 MANUAL GRPCURL COMMANDS FOR HOPEN BACKEND TESTING
# ============================================================================
# 
# This script provides individual grpcurl commands for manual testing and
# debugging of the Hopen backend services. Use these commands to test
# specific endpoints or troubleshoot issues.
#
# Prerequisites:
# - Backend services running (docker-compose up -d)
# - grpcurl installed
# - Kong Gateway accessible on localhost:8000
#
# Usage: 
# - Copy and paste individual commands
# - Replace placeholder values with actual IDs
# - Modify JSON payloads as needed
# ============================================================================

# Colors for output
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🔧 MANUAL GRPCURL COMMANDS FOR HOPEN BACKEND${NC}\n"

# ============================================================================
echo -e "${CYAN}📋 QUICK REFERENCE COMMANDS${NC}"
echo "Replace USER_ID, BUBBLE_ID, etc. with actual values from responses"
echo ""

# ============================================================================
echo -e "${CYAN}🔐 AUTHENTICATION SERVICE${NC}"
echo ""

echo "# Register a new user"
echo 'grpcurl -plaintext -d '"'"'{
  "username": "testuser",
  "email": "<EMAIL>", 
  "password": "SecurePass123!",
  "first_name": "Test",
  "last_name": "User"
}'"'"' localhost:8000 hopen.auth.v1.AuthService/RegisterUser'
echo ""

echo "# Check password breach (HIBP)"
echo 'grpcurl -plaintext -d '"'"'{
  "sha1_hash": "5BAA61E4C9B93F3F0682250B6CF8331B7EE68FD8"
}'"'"' localhost:8000 hopen.auth.v1.AuthService/CheckPasswordBreach'
echo ""

echo "# Get auth status"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "USER_ID"
}'"'"' localhost:8000 hopen.auth.v1.AuthService/GetAuthStatus'
echo ""

# ============================================================================
echo -e "${CYAN}👥 USER SERVICE - CONTACT MANAGEMENT${NC}"
echo ""

echo "# Send contact request"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "SENDER_USER_ID",
  "target_user_id": "TARGET_USER_ID",
  "message": "Hi, let'"'"'s connect!"
}'"'"' localhost:8000 hopen.user.v1.UserService/SendContactRequest'
echo ""

echo "# Accept contact request"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "RECIPIENT_USER_ID",
  "requester_id": "SENDER_USER_ID"
}'"'"' localhost:8000 hopen.user.v1.UserService/AcceptContactRequest'
echo ""

echo "# Decline contact request"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "RECIPIENT_USER_ID",
  "requester_id": "SENDER_USER_ID",
  "reason": "Not interested"
}'"'"' localhost:8000 hopen.user.v1.UserService/DeclineContactRequest'
echo ""

echo "# Get relationship status"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "USER_ID",
  "target_user_id": "TARGET_USER_ID"
}'"'"' localhost:8000 hopen.user.v1.UserService/GetUserRelationshipStatus'
echo ""

echo "# Get contacts list"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "USER_ID",
  "page": 1,
  "page_size": 20
}'"'"' localhost:8000 hopen.user.v1.UserService/GetContacts'
echo ""

echo "# Get pending contact requests"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "USER_ID",
  "direction": "both",
  "page": 1,
  "page_size": 20
}'"'"' localhost:8000 hopen.user.v1.UserService/GetPendingContactRequests'
echo ""

# ============================================================================
echo -e "${CYAN}🫧 BUBBLE SERVICE - BUBBLE MANAGEMENT${NC}"
echo ""

echo "# Create bubble start request"
echo 'grpcurl -plaintext -d '"'"'{
  "requester_id": "REQUESTER_USER_ID",
  "target_user_id": "TARGET_USER_ID",
  "bubble_name": "My Test Bubble"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/CreateBubbleStartRequest'
echo ""

echo "# Accept bubble start request"
echo 'grpcurl -plaintext -d '"'"'{
  "request_id": "REQUEST_ID",
  "user_id": "TARGET_USER_ID"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/AcceptBubbleStartRequest'
echo ""

echo "# Decline bubble start request"
echo 'grpcurl -plaintext -d '"'"'{
  "request_id": "REQUEST_ID",
  "user_id": "TARGET_USER_ID",
  "reason": "Not interested right now"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/DeclineBubbleStartRequest'
echo ""

echo "# Get bubble details"
echo 'grpcurl -plaintext -d '"'"'{
  "bubble_id": "BUBBLE_ID"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/GetBubble'
echo ""

echo "# Get bubble members"
echo 'grpcurl -plaintext -d '"'"'{
  "bubble_id": "BUBBLE_ID",
  "user_id": "USER_ID"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/GetBubbleMembers'
echo ""

echo "# Send bubble invite"
echo 'grpcurl -plaintext -d '"'"'{
  "bubble_id": "BUBBLE_ID",
  "inviter_id": "INVITER_USER_ID",
  "invitee_ids": ["INVITEE_USER_ID"]
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/SendBubbleInvite'
echo ""

echo "# Accept bubble invite"
echo 'grpcurl -plaintext -d '"'"'{
  "request_id": "INVITE_REQUEST_ID",
  "user_id": "INVITEE_USER_ID"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/AcceptBubbleInvite'
echo ""

echo "# Vote on invite request"
echo 'grpcurl -plaintext -d '"'"'{
  "request_id": "INVITE_REQUEST_ID",
  "user_id": "VOTING_USER_ID",
  "vote": "approve"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/VoteOnInviteRequest'
echo ""

echo "# Leave bubble"
echo 'grpcurl -plaintext -d '"'"'{
  "bubble_id": "BUBBLE_ID",
  "user_id": "USER_ID"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/LeaveBubble'
echo ""

echo "# Get user bubbles"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "USER_ID",
  "status": "active"
}'"'"' localhost:8000 hopen.bubble.v1.BubbleService/GetUserBubbles'
echo ""

# ============================================================================
echo -e "${CYAN}💬 REALTIME SERVICE - MESSAGING${NC}"
echo ""

echo "# Send message (requires authentication)"
echo 'grpcurl -plaintext -H "Authorization: Bearer YOUR_JWT_TOKEN" -d '"'"'{
  "bubble_id": "BUBBLE_ID",
  "content": "Hello everyone!",
  "message_type": "TEXT"
}'"'"' localhost:8000 hopen.realtime.v1.RealtimeService/SendMessage'
echo ""

echo "# Get chat messages (requires authentication)"
echo 'grpcurl -plaintext -H "Authorization: Bearer YOUR_JWT_TOKEN" -d '"'"'{
  "bubble_id": "BUBBLE_ID",
  "limit": 20
}'"'"' localhost:8000 hopen.realtime.v1.RealtimeService/GetChatMessages'
echo ""

# ============================================================================
echo -e "${CYAN}👁️ PRESENCE SERVICE${NC}"
echo ""

echo "# Update user presence"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "USER_ID",
  "is_present": true
}'"'"' localhost:8000 hopen.presence.v1.PresenceService/UpdateUserPresence'
echo ""

echo "# Get batch presence"
echo 'grpcurl -plaintext -d '"'"'{
  "user_ids": ["USER_ID_1", "USER_ID_2", "USER_ID_3"]
}'"'"' localhost:8000 hopen.presence.v1.PresenceService/GetBatchPresence'
echo ""

# ============================================================================
echo -e "${CYAN}🤝 USER SERVICE - FRIEND RELATIONSHIPS${NC}"
echo ""

echo "# Accept friend request relationship (from maybe_friend)"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "USER_ID",
  "other_user_id": "OTHER_USER_ID"
}'"'"' localhost:8000 hopen.user.v1.UserService/AcceptFriendRequestRelationship'
echo ""

echo "# Decline friend request relationship"
echo 'grpcurl -plaintext -d '"'"'{
  "user_id": "USER_ID",
  "other_user_id": "OTHER_USER_ID"
}'"'"' localhost:8000 hopen.user.v1.UserService/DeclineFriendRequestRelationship'
echo ""

echo "# Create friendships from bubble (expiration simulation)"
echo 'grpcurl -plaintext -d '"'"'{
  "bubble_id": "BUBBLE_ID",
  "members": [
    {"user_id": "USER_ID_1", "bubble_id": "BUBBLE_ID", "status": "active"},
    {"user_id": "USER_ID_2", "bubble_id": "BUBBLE_ID", "status": "active"}
  ]
}'"'"' localhost:8000 hopen.user.v1.UserService/CreateFriendshipsFromBubble'
echo ""

# ============================================================================
echo -e "${CYAN}🔍 UTILITY COMMANDS${NC}"
echo ""

echo "# List all available services"
echo 'grpcurl -plaintext localhost:8000 list'
echo ""

echo "# List methods for a specific service"
echo 'grpcurl -plaintext localhost:8000 list hopen.auth.v1.AuthService'
echo ""

echo "# Describe a specific method"
echo 'grpcurl -plaintext localhost:8000 describe hopen.auth.v1.AuthService.RegisterUser'
echo ""

echo "# Check Kong Gateway status"
echo 'curl http://localhost:8001/status'
echo ""

echo "# Check service health through Kong"
echo 'grpcurl -plaintext localhost:8000 grpc.health.v1.Health/Check'
echo ""

echo -e "\n${BLUE}💡 Tips:${NC}"
echo "1. Replace placeholder values (USER_ID, BUBBLE_ID, etc.) with actual IDs from responses"
echo "2. Use 'jq' to parse JSON responses: grpcurl ... | jq '.'"
echo "3. Save responses to files for ID extraction: grpcurl ... > response.json"
echo "4. Some endpoints require JWT authentication tokens"
echo "5. Check the comprehensive test plan for complete workflows"
echo ""
