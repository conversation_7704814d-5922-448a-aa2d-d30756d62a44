#!/bin/bash

# ============================================================================
# 🚀 QUICK BACKEND HEALTH CHECK
# ============================================================================
# 
# This script provides a fast health check of the Hopen backend services
# to verify they are running and responding correctly.
#
# Prerequisites:
# - Backend services running (docker-compose up -d)
# - grpcurl installed
# 
# Usage: ./quick_backend_check.sh
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
KONG_ENDPOINT="localhost:8000"
KONG_ADMIN="localhost:8001"
TEST_TIMEOUT=5

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Helper functions
print_header() {
    echo -e "\n${BLUE}═══ $1 ═══${NC}"
}

print_check() {
    echo -e "${YELLOW}Checking: $1${NC}"
    ((TOTAL_CHECKS++))
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    ((PASSED_CHECKS++))
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    ((FAILED_CHECKS++))
}

print_info() {
    echo -e "${CYAN}ℹ $1${NC}"
}

# Check if a service responds
check_service() {
    local service_name=$1
    local method=$2
    local data=${3:-'{}'}
    
    print_check "$service_name service"
    
    if grpcurl -plaintext -d "$data" -connect-timeout $TEST_TIMEOUT "$KONG_ENDPOINT" "$method" >/dev/null 2>&1; then
        print_success "$service_name is responding"
        return 0
    else
        print_error "$service_name is not responding"
        return 1
    fi
}

# Main health check
main() {
    echo -e "${BLUE}🚀 QUICK BACKEND HEALTH CHECK${NC}"
    echo -e "${BLUE}==============================${NC}\n"
    
    print_header "INFRASTRUCTURE CHECKS"
    
    # Check Kong Gateway
    print_check "Kong Gateway"
    if curl -f -s "$KONG_ADMIN/status" >/dev/null 2>&1; then
        print_success "Kong Gateway is accessible"
    else
        print_error "Kong Gateway is not accessible"
    fi
    
    # Check grpcurl
    print_check "grpcurl availability"
    if command -v grpcurl &> /dev/null; then
        print_success "grpcurl is available"
    else
        print_error "grpcurl is not installed"
    fi
    
    print_header "MICROSERVICE HEALTH CHECKS"
    
    # Check core services
    check_service "Auth" "hopen.auth.v1.AuthService/GetAuthStatus" '{"user_id":"health-check"}'
    check_service "User" "hopen.user.v1.UserService/CheckUsernameAvailability" '{"username":"health-check"}'
    check_service "Bubble" "hopen.bubble.v1.BubbleService/GetBubble" '{"bubble_id":"health-check"}'
    check_service "Realtime" "hopen.realtime.v1.RealtimeService/GetChatMessages" '{"bubble_id":"health-check","limit":1}'
    check_service "Presence" "hopen.presence.v1.PresenceService/GetBatchPresence" '{"user_ids":["health-check"]}'
    
    print_header "QUICK FUNCTIONALITY TEST"
    
    # Test service discovery
    print_check "Service discovery"
    if grpcurl -plaintext -connect-timeout $TEST_TIMEOUT "$KONG_ENDPOINT" list >/dev/null 2>&1; then
        print_success "Service discovery working"
    else
        print_error "Service discovery failed"
    fi
    
    # Test a simple registration (should fail gracefully with validation error)
    print_check "Auth service validation"
    if grpcurl -plaintext -d '{"username":"","email":"","password":""}' -connect-timeout $TEST_TIMEOUT "$KONG_ENDPOINT" "hopen.auth.v1.AuthService/RegisterUser" 2>&1 | grep -q "validation\|required\|invalid"; then
        print_success "Auth service validation working"
    else
        print_error "Auth service validation not working"
    fi
    
    print_header "SUMMARY"
    
    echo -e "Total Checks: $TOTAL_CHECKS"
    echo -e "${GREEN}Passed: $PASSED_CHECKS${NC}"
    echo -e "${RED}Failed: $FAILED_CHECKS${NC}"
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 All health checks passed! Backend is ready for testing.${NC}"
        echo -e "\n${CYAN}Next steps:${NC}"
        echo -e "  • Run comprehensive tests: ${YELLOW}./comprehensive_backend_test_plan.sh${NC}"
        echo -e "  • Manual testing: ${YELLOW}./manual_grpcurl_commands.sh${NC}"
        echo -e "  • View test documentation: ${YELLOW}cat README_COMPREHENSIVE_TEST_PLAN.md${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ Some health checks failed.${NC}"
        echo -e "\n${CYAN}Troubleshooting:${NC}"
        echo -e "  • Check services: ${YELLOW}docker-compose ps${NC}"
        echo -e "  • View logs: ${YELLOW}docker-compose logs <service-name>${NC}"
        echo -e "  • Restart services: ${YELLOW}docker-compose restart${NC}"
        exit 1
    fi
}

# Run the health check
main "$@"
