package tests

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestOryKratosOAuthIntegration tests the complete OAuth flow with <PERSON>y <PERSON>
func TestOryKratosOAuthIntegration(t *testing.T) {
	tests := []struct {
		name     string
		provider string
		expected string
	}{
		{
			name:     "Google OAuth Provider",
			provider: "google",
			expected: "google",
		},
		{
			name:     "Apple OAuth Provider",
			provider: "apple",
			expected: "apple",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test OAuth provider configuration
			testOAuthProviderConfig(t, tt.provider)
		})
	}
}

func testOAuthProviderConfig(t *testing.T, provider string) {
	// Mock Kratos server for testing
	kratosServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch {
		case strings.Contains(r.URL.Path, "/self-service/login/api"):
			// Mock login flow initialization
			response := map[string]interface{}{
				"id": "test-flow-id",
				"ui": map[string]interface{}{
					"nodes": []map[string]interface{}{
						{
							"type":  "input",
							"group": "oidc",
							"attributes": map[string]interface{}{
								"name":      "provider",
								"value":     provider,
								"node_type": "input",
							},
						},
					},
				},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)

		case strings.Contains(r.URL.Path, "/self-service/login") && r.Method == "POST":
			// Mock login flow submission
			var body map[string]interface{}
			json.NewDecoder(r.Body).Decode(&body)

			if body["provider"] == provider {
				// Simulate successful OAuth flow
				response := map[string]interface{}{
					"session": map[string]interface{}{
						"id": "test-session-id",
						"identity": map[string]interface{}{
							"id": "test-user-id",
							"traits": map[string]interface{}{
								"email":      "<EMAIL>",
								"first_name": "Test",
								"last_name":  "User",
							},
						},
					},
				}
				w.Header().Set("Content-Type", "application/json")
				json.NewEncoder(w).Encode(response)
			} else {
				// Simulate OAuth redirect
				response := map[string]interface{}{
					"redirect_browser_to": fmt.Sprintf("https://accounts.%s.com/oauth/authorize", provider),
				}
				w.WriteHeader(http.StatusUnprocessableEntity)
				w.Header().Set("Content-Type", "application/json")
				json.NewEncoder(w).Encode(response)
			}

		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer kratosServer.Close()

	// Test OAuth flow initialization
	t.Run("OAuth Flow Initialization", func(t *testing.T) {
		client := &http.Client{Timeout: 10 * time.Second}

		// Initialize login flow
		resp, err := client.Get(kratosServer.URL + "/self-service/login/api")
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var flowResponse map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&flowResponse)
		require.NoError(t, err)

		// Verify flow contains OAuth provider
		assert.NotEmpty(t, flowResponse["id"])
		assert.NotEmpty(t, flowResponse["ui"])
	})

	// Test OAuth provider submission
	t.Run("OAuth Provider Submission", func(t *testing.T) {
		client := &http.Client{Timeout: 10 * time.Second}

		// Submit OAuth provider
		payload := fmt.Sprintf(`{"method":"oidc","provider":"%s"}`, provider)
		resp, err := client.Post(
			kratosServer.URL+"/self-service/login?flow=test-flow-id",
			"application/json",
			strings.NewReader(payload),
		)
		require.NoError(t, err)
		defer resp.Body.Close()

		// Should get redirect for browser-based OAuth
		assert.Equal(t, http.StatusUnprocessableEntity, resp.StatusCode)

		var redirectResponse map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&redirectResponse)
		require.NoError(t, err)

		// Verify redirect URL contains provider
		redirectURL, ok := redirectResponse["redirect_browser_to"].(string)
		assert.True(t, ok)
		assert.Contains(t, redirectURL, provider)
	})
}

// TestOAuthProviderConfiguration tests Kratos OAuth provider configuration
func TestOAuthProviderConfiguration(t *testing.T) {
	// Test Google OAuth configuration
	t.Run("Google OAuth Configuration", func(t *testing.T) {
		config := map[string]interface{}{
			"id":         "google",
			"provider":   "google",
			"client_id":  "************-8555p6h5grihi9qmgrts661sro96kfd7.apps.googleusercontent.com",
			"issuer_url": "https://accounts.google.com",
			"scope":      []string{"openid", "profile", "email"},
		}

		// Verify required fields
		assert.Equal(t, "google", config["id"])
		assert.Equal(t, "google", config["provider"])
		assert.NotEmpty(t, config["client_id"])
		assert.Equal(t, "https://accounts.google.com", config["issuer_url"])

		scopes, ok := config["scope"].([]string)
		assert.True(t, ok)
		assert.Contains(t, scopes, "openid")
		assert.Contains(t, scopes, "email")
	})

	// Test Apple OAuth configuration
	t.Run("Apple OAuth Configuration", func(t *testing.T) {
		config := map[string]interface{}{
			"id":         "apple",
			"provider":   "apple",
			"issuer_url": "https://appleid.apple.com",
			"scope":      []string{"email"},
		}

		// Verify required fields
		assert.Equal(t, "apple", config["id"])
		assert.Equal(t, "apple", config["provider"])
		assert.Equal(t, "https://appleid.apple.com", config["issuer_url"])

		scopes, ok := config["scope"].([]string)
		assert.True(t, ok)
		assert.Contains(t, scopes, "email")
	})
}

// TestOAuthDataMapping tests the Jsonnet data mapping for OAuth providers
func TestOAuthDataMapping(t *testing.T) {
	t.Run("Google Data Mapping", func(t *testing.T) {
		// Expected mapping result for Google OAuth
		expected := map[string]interface{}{
			"identity": map[string]interface{}{
				"traits": map[string]interface{}{
					"email":      "<EMAIL>",
					"first_name": "John",
					"last_name":  "Doe",
				},
			},
		}

		// Verify mapping structure
		assert.Equal(t, "<EMAIL>", expected["identity"].(map[string]interface{})["traits"].(map[string]interface{})["email"])
		assert.Equal(t, "John", expected["identity"].(map[string]interface{})["traits"].(map[string]interface{})["first_name"])
		assert.Equal(t, "Doe", expected["identity"].(map[string]interface{})["traits"].(map[string]interface{})["last_name"])
	})

	t.Run("Apple Data Mapping", func(t *testing.T) {
		// Expected mapping result for Apple OAuth (only if email is verified)
		expected := map[string]interface{}{
			"identity": map[string]interface{}{
				"traits": map[string]interface{}{
					"email": "<EMAIL>",
				},
			},
		}

		// Verify mapping structure
		assert.Equal(t, "<EMAIL>", expected["identity"].(map[string]interface{})["traits"].(map[string]interface{})["email"])
	})
}

// TestOAuthSecurityValidation tests OAuth security measures
func TestOAuthSecurityValidation(t *testing.T) {
	t.Run("CSRF Protection", func(t *testing.T) {
		// Verify Apple provider uses POST method (exempt from CSRF)
		assert.Equal(t, "apple", "apple") // Apple provider ID must be "apple" for CSRF exemption
	})

	t.Run("Token Validation", func(t *testing.T) {
		// Test that invalid tokens are rejected
		invalidTokens := []string{
			"",
			"invalid-token",
			"expired-token",
		}

		for _, token := range invalidTokens {
			if token == "" {
				continue // Skip empty token test
			}
			t.Run(fmt.Sprintf("Invalid token: %s", token), func(t *testing.T) {
				// In a real implementation, these would be rejected by OAuth providers
				assert.NotEmpty(t, token) // Placeholder assertion
			})
		}
	})
}
