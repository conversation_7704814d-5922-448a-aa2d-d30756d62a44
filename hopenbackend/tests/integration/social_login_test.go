package integration

import (
	"testing"

	"github.com/stretchr/testify/assert"

	authv1 "hopenbackend/protos/gen"
)

// TestKratosOAuthIntegration tests the Kratos-based OAuth flow integration
func TestKratosOAuthIntegration(t *testing.T) {
	t.Run("Kratos_Login_Flow_Initiation", func(t *testing.T) {
		// Test that Kratos login flow can be initiated for OAuth providers
		// This test validates the gRPC method structure and request validation

		// Test Google provider flow initiation
		googleReq := &authv1.InitiateLoginFlowRequest{
			Provider: "google",
		}

		// Validate request structure
		assert.Equal(t, "google", googleReq.Provider)
		assert.NotNil(t, googleReq)

		// Test Apple provider flow initiation
		appleReq := &authv1.InitiateLoginFlowRequest{
			Provider: "apple",
		}

		assert.Equal(t, "apple", appleReq.Provider)
		assert.NotNil(t, appleReq)
	})

	t.Run("Kratos_Registration_Flow_Initiation", func(t *testing.T) {
		// Test that Kratos registration flow can be initiated for OAuth providers

		// Test Google provider registration flow
		googleReq := &authv1.InitiateRegistrationFlowRequest{
			Provider: "google",
		}

		assert.Equal(t, "google", googleReq.Provider)
		assert.NotNil(t, googleReq)

		// Test Apple provider registration flow
		appleReq := &authv1.InitiateRegistrationFlowRequest{
			Provider: "apple",
		}

		assert.Equal(t, "apple", appleReq.Provider)
		assert.NotNil(t, appleReq)
	})

	t.Run("Kratos_Flow_Completion", func(t *testing.T) {
		// Test Kratos flow completion request structure

		completeReq := &authv1.CompleteKratosFlowRequest{
			FlowId:       "test-flow-id-123",
			FlowType:     "login",
			SessionToken: "test-session-token",
		}

		assert.Equal(t, "test-flow-id-123", completeReq.FlowId)
		assert.Equal(t, "login", completeReq.FlowType)
		assert.Equal(t, "test-session-token", completeReq.SessionToken)

		// Test registration flow completion
		regCompleteReq := &authv1.CompleteKratosFlowRequest{
			FlowId:       "test-reg-flow-id-456",
			FlowType:     "registration",
			SessionToken: "test-reg-session-token",
		}

		assert.Equal(t, "test-reg-flow-id-456", regCompleteReq.FlowId)
		assert.Equal(t, "registration", regCompleteReq.FlowType)
		assert.Equal(t, "test-reg-session-token", regCompleteReq.SessionToken)
	})

	t.Run("Kratos_Response_Structure", func(t *testing.T) {
		// Test Kratos flow response structures

		// Test login flow response
		loginResp := &authv1.InitiateLoginFlowResponse{
			FlowId:   "flow-123",
			LoginUrl: "https://kratos.example.com/self-service/login/flows?id=flow-123",
		}

		assert.Equal(t, "flow-123", loginResp.FlowId)
		assert.Contains(t, loginResp.LoginUrl, "flow-123")

		// Test registration flow response
		regResp := &authv1.InitiateRegistrationFlowResponse{
			FlowId:          "reg-flow-456",
			RegistrationUrl: "https://kratos.example.com/self-service/registration/flows?id=reg-flow-456",
		}

		assert.Equal(t, "reg-flow-456", regResp.FlowId)
		assert.Contains(t, regResp.RegistrationUrl, "reg-flow-456")

		// Test completion response
		completeResp := &authv1.CompleteKratosFlowResponse{
			UserId:       "kratos-identity-789",
			Email:        "<EMAIL>",
			Username:     "testuser",
			FirstName:    "Test",
			LastName:     "User",
			SessionToken: "kratos-session-token",
		}

		assert.Equal(t, "kratos-identity-789", completeResp.UserId)
		assert.Equal(t, "<EMAIL>", completeResp.Email)
		assert.Equal(t, "testuser", completeResp.Username)
		assert.Equal(t, "Test", completeResp.FirstName)
		assert.Equal(t, "User", completeResp.LastName)
	})
}

// TestKratosSessionManagement tests Kratos session management
func TestKratosSessionManagement(t *testing.T) {
	t.Run("Kratos_Session_Token_Validation", func(t *testing.T) {
		// Test Kratos session token validation structure
		req := &authv1.ValidateSessionRequest{
			SessionToken: "kratos-session-token-abc123",
		}

		assert.True(t, len(req.SessionToken) > 0)
		assert.NotEmpty(t, req.SessionToken)
	})

	t.Run("Kratos_Session_Info_Structure", func(t *testing.T) {
		// Test Kratos session info structure
		sessionInfo := &authv1.SessionInfo{
			UserId:      "kratos-identity-123",
			SessionId:   "kratos-session-456",
			IsActive:    true,
			Permissions: []string{"user:read", "user:write"},
			Roles:       []string{"user"},
		}

		assert.Equal(t, "kratos-identity-123", sessionInfo.UserId)
		assert.Equal(t, "kratos-session-456", sessionInfo.SessionId)
		assert.True(t, sessionInfo.IsActive)
		assert.Contains(t, sessionInfo.Permissions, "user:read")
		assert.Contains(t, sessionInfo.Permissions, "user:write")
		assert.Contains(t, sessionInfo.Roles, "user")
	})
}

// TestKratosErrorHandling tests error scenarios in Kratos OAuth flows
func TestKratosErrorHandling(t *testing.T) {
	t.Run("Missing_Provider_In_Flow", func(t *testing.T) {
		req := &authv1.InitiateLoginFlowRequest{
			Provider: "",
		}

		// This should fail validation
		assert.Empty(t, req.Provider)
	})

	t.Run("Invalid_Provider_In_Flow", func(t *testing.T) {
		req := &authv1.InitiateLoginFlowRequest{
			Provider: "invalid-provider",
		}

		// This should fail with unsupported provider
		assert.Equal(t, "invalid-provider", req.Provider)
	})

	t.Run("Missing_Flow_ID_In_Completion", func(t *testing.T) {
		req := &authv1.CompleteKratosFlowRequest{
			FlowId:       "",
			FlowType:     "login",
			SessionToken: "valid-session-token",
		}

		// This should fail validation
		assert.Empty(t, req.FlowId)
		assert.NotEmpty(t, req.FlowType)
		assert.NotEmpty(t, req.SessionToken)
	})
}
