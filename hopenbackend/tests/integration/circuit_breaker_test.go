package integration

import (
	"context"
	"errors"
	"testing"
	"time"

	"go.uber.org/zap/zaptest"
	"hopenbackend/pkg/config"
)

func TestCircuitBreakerIntegration(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{
		Resilience: config.ResilienceConfig{
			CircuitBreakers: map[string]config.CircuitBreakerConfig{
				"test-service": {
					Name:             "test-service",
					MaxRequests:      10,
					Interval:         30 * time.Second,
					Timeout:          30 * time.Second,
					FailureThreshold: 3,
					SuccessThreshold: 2,
					Enabled:          true,
				},
			},
		},
	}

	manager := NewManager(logger, cfg)
	ctx := context.Background()

	// Test 1: Normal operation
	t.Run("Normal Operation", func(t *testing.T) {
		err := manager.ExecuteWithService(ctx, "test-service", func() error {
			return nil
		})
		if err != nil {
			t.<PERSON>("Expected no error, got %v", err)
		}
	})

	// Test 2: Circuit breaker opens after failures
	t.Run("Circuit Opens After Failures", func(t *testing.T) {
		// Cause failures to open circuit
		for i := 0; i < 4; i++ {
			manager.ExecuteWithService(ctx, "test-service", func() error {
				return errors.New("test failure")
			})
		}

		// Circuit should now be open
		err := manager.ExecuteWithService(ctx, "test-service", func() error {
			return nil
		})
		if err == nil {
			t.Error("Expected circuit breaker error, got nil")
		}

		var cbErr *CircuitBreakerError
		if !errors.As(err, &cbErr) {
			t.Errorf("Expected CircuitBreakerError, got %T", err)
		}
	})

	// Test 3: Health check
	t.Run("Health Check", func(t *testing.T) {
		health := manager.HealthCheck()
		if len(health) == 0 {
			t.Error("Expected health check results")
		}
	})

	// Test 4: Metrics collection
	t.Run("Metrics Collection", func(t *testing.T) {
		metrics := manager.GetAllServiceMetrics()
		if len(metrics) == 0 {
			t.Error("Expected metrics")
		}
	})

	// Test 5: Bulk operations
	t.Run("Bulk Operations", func(t *testing.T) {
		operations := map[string]func() error{
			"service1": func() error { return nil },
			"service2": func() error { return errors.New("service2 error") },
		}

		results := manager.BulkExecute(ctx, operations)
		if len(results) != 2 {
			t.Errorf("Expected 2 results, got %d", len(results))
		}
	})

	manager.Stop()
}

func TestAdaptiveCircuitBreakerIntegration(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{}
	manager := NewManager(logger, cfg)

	adaptiveConfig := AdaptiveConfig{
		Name:                  "adaptive-test",
		BaseFailureThreshold:  3,
		BaseSuccessThreshold:  2,
		BaseTimeout:           30 * time.Second,
		MaxRequests:           10,
		Interval:              60 * time.Second,
		AdaptationInterval:    5 * time.Minute,
		AdaptationSensitivity: 0.5,
		HealthCheckInterval:   15 * time.Second,
		HealthCheckTimeout:    5 * time.Second,
		MinTimeout:            5 * time.Second,
		MaxTimeout:            120 * time.Second,
		MinFailureThreshold:   2,
		MaxFailureThreshold:   10,
		IsFailure: func(err error) bool {
			return err != nil
		},
	}

	acb := manager.CreateAdaptiveBreaker("adaptive-test", adaptiveConfig)
	if acb == nil {
		t.Fatal("Failed to create adaptive circuit breaker")
	}

	ctx := context.Background()

	// Test adaptive behavior
	t.Run("Adaptive Behavior", func(t *testing.T) {
		// Execute some operations
		for i := 0; i < 5; i++ {
			acb.Execute(ctx, func() error {
				if i%2 == 0 {
					return nil
				}
				return errors.New("test failure")
			})
		}

		metrics := acb.GetAdaptiveMetrics()
		if metrics.Name != "adaptive-test" {
			t.Errorf("Expected name 'adaptive-test', got '%s'", metrics.Name)
		}
	})

	manager.Stop()
}

func TestCascadingCircuitBreakerIntegration(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{}
	manager := NewManager(logger, cfg)

	// Create dependencies first
	manager.GetOrCreate("dep1", DefaultConfig("dep1"))
	manager.GetOrCreate("dep2", DefaultConfig("dep2"))

	cascadingConfig := CascadingConfig{
		Name:                     "cascading-test",
		Dependencies:             []string{"dep1", "dep2"},
		FailureThreshold:         2,
		CascadeTimeout:           30 * time.Second,
		RecoveryTimeout:          60 * time.Second,
		HealthCheckInterval:      15 * time.Second,
		MaxCascadeDepth:          5,
		EnablePartialDegradation: true,
		CriticalDependencies:     []string{"dep1"},
		FailureRateThreshold:     0.5,
	}

	ccb := manager.CreateCascadingBreaker("cascading-test", cascadingConfig)
	if ccb == nil {
		t.Fatal("Failed to create cascading circuit breaker")
	}

	ctx := context.Background()

	// Test cascading behavior
	t.Run("Cascading Behavior", func(t *testing.T) {
		// Execute operation
		err := ccb.Execute(ctx, func() error {
			return nil
		}, func() error {
			return errors.New("fallback error")
		})

		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}

		metrics := ccb.GetCascadingMetrics()
		if metrics.Name != "cascading-test" {
			t.Errorf("Expected name 'cascading-test', got '%s'", metrics.Name)
		}
	})

	manager.Stop()
}

func TestBulkheadIntegration(t *testing.T) {
	logger := zaptest.NewLogger(t)
	cfg := &config.Config{}
	manager := NewManager(logger, cfg)

	bulkheadConfig := BulkheadConfig{
		Name:               "bulkhead-test",
		MaxConcurrentCalls: 2,
		MaxQueueSize:       5,
		CallTimeout:        30 * time.Second,
		QueueTimeout:       5 * time.Second,
	}

	bi := manager.CreateBulkhead("bulkhead-test", bulkheadConfig)
	if bi == nil {
		t.Fatal("Failed to create bulkhead isolator")
	}

	ctx := context.Background()

	// Test bulkhead isolation
	t.Run("Bulkhead Isolation", func(t *testing.T) {
		err := manager.ExecuteWithBulkhead(ctx, "bulkhead-test", "user1", func() error {
			return nil
		})

		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}

		metrics := bi.GetMetrics()
		if metrics.Name != "bulkhead-test" {
			t.Errorf("Expected name 'bulkhead-test', got '%s'", metrics.Name)
		}
	})

	manager.Stop()
}

func TestValidationIntegration(t *testing.T) {
	// Test basic config validation using the validation package
	t.Run("Basic Config Validation", func(t *testing.T) {
		validConfig := DefaultConfig("test")
		if validConfig.Name == "" {
			t.Error("Expected valid config name")
		}
		if validConfig.MaxRequests == 0 {
			t.Error("Expected valid max requests")
		}
		if validConfig.FailureThreshold == 0 {
			t.Error("Expected valid failure threshold")
		}
	})

	// Test adaptive config validation
	t.Run("Adaptive Config Validation", func(t *testing.T) {
		validConfig := DefaultAdaptiveConfig("test")
		if validConfig.Name == "" {
			t.Error("Expected valid config name")
		}
		if validConfig.BaseFailureThreshold == 0 {
			t.Error("Expected valid base failure threshold")
		}
	})

	// Test cascading config validation
	t.Run("Cascading Config Validation", func(t *testing.T) {
		validConfig := DefaultCascadingConfig("test")
		validConfig.Dependencies = []string{"dep1", "dep2"}
		validConfig.CriticalDependencies = []string{"dep1"}
		if validConfig.Name == "" {
			t.Error("Expected valid config name")
		}
		if len(validConfig.Dependencies) == 0 {
			t.Error("Expected dependencies")
		}
	})

	// Test bulkhead config validation
	t.Run("Bulkhead Config Validation", func(t *testing.T) {
		validConfig := DefaultBulkheadConfig("test")
		if validConfig.Name == "" {
			t.Error("Expected valid config name")
		}
		if validConfig.MaxConcurrentCalls == 0 {
			t.Error("Expected valid max concurrent calls")
		}
	})
}

func TestInterceptorIntegration(t *testing.T) {
	// Test that circuit breaker integrates with the unified interceptor
	t.Run("Circuit Breaker Integration", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		cfg := &config.Config{}
		manager := NewManager(logger, cfg)

		// Verify manager can be used with interceptors
		if manager == nil {
			t.Fatal("Failed to create circuit breaker manager")
		}

		// Test that manager has metrics collector
		if manager.metricsCollector == nil {
			t.Error("Expected metrics collector to be initialized")
		}

		manager.Stop()
	})
}
