package tests

import (
	"context"
	"testing"

	"hopenbackend/microservices/auth"
	authv1 "hopenbackend/protos/gen"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestOAuthImplementation(t *testing.T) {
	// Test OAuth provider initialization
	t.Run("OAuth Providers Initialization", func(t *testing.T) {
		// Test Google OAuth provider
		googleProvider := auth.NewGoogleOAuthProvider(
			"test-client-id",
			"test-client-secret",
			nil, // logger
		)

		assert.Equal(t, "google", googleProvider.GetProviderName())
		assert.Contains(t, googleProvider.GetScopes(), "openid")
		assert.Contains(t, googleProvider.GetScopes(), "profile")
		assert.Contains(t, googleProvider.GetScopes(), "email")
	})

	t.Run("Apple OAuth Provider", func(t *testing.T) {
		// Test Apple OAuth provider
		appleProvider := auth.NewAppleOAuthProvider(
			"test-client-id",
			"test-team-id",
			"test-key-id",
			[]byte("test-private-key"),
			nil, // logger
		)

		assert.Equal(t, "apple", appleProvider.GetProviderName())
		assert.Contains(t, appleProvider.GetScopes(), "name")
		assert.Contains(t, appleProvider.GetScopes(), "email")
	})

	t.Run("OAuth Token Validation Structure", func(t *testing.T) {
		ctx := context.Background()
		logger := zap.NewNop() // No-op logger for testing

		// Test Google token validation structure (will fail with invalid token, but tests the flow)
		googleProvider := auth.NewGoogleOAuthProvider(
			"test-client-id",
			"test-client-secret",
			logger,
		)

		// This should fail gracefully with invalid token
		_, err := googleProvider.ValidateToken(ctx, "invalid-token")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid Google token")

		// Test Apple token validation structure
		appleProvider := auth.NewAppleOAuthProvider(
			"test-client-id",
			"test-team-id",
			"test-key-id",
			[]byte("test-private-key"),
			logger,
		)

		// This should fail gracefully with invalid token
		_, err = appleProvider.ValidateToken(ctx, "invalid-token")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid Apple ID token")
	})
}

func TestOAuthUserHandling(t *testing.T) {
	t.Run("OAuth User Info Structure", func(t *testing.T) {
		// Test OAuthUserInfo structure
		userInfo := &auth.OAuthUserInfo{
			ID:            "oauth-user-123",
			Email:         "<EMAIL>",
			EmailVerified: true,
			FirstName:     "Test",
			LastName:      "User",
			Picture:       "https://example.com/avatar.jpg",
			Provider:      "google",
		}

		assert.Equal(t, "oauth-user-123", userInfo.ID)
		assert.Equal(t, "<EMAIL>", userInfo.Email)
		assert.True(t, userInfo.EmailVerified)
		assert.Equal(t, "google", userInfo.Provider)
	})
}

func TestSocialLoginEndpoint(t *testing.T) {
	t.Run("Social Login Request Structure", func(t *testing.T) {
		// Test social login request structure
		req := &authv1.SocialLoginRequest{
			Provider: "google",
			Token:    "test-oauth-token",
		}

		assert.Equal(t, "google", req.Provider)
		assert.Equal(t, "test-oauth-token", req.Token)

		// Test with Apple
		appleReq := &authv1.SocialLoginRequest{
			Provider: "apple",
			Token:    "test-apple-token",
		}

		assert.Equal(t, "apple", appleReq.Provider)
		assert.Equal(t, "test-apple-token", appleReq.Token)
	})
}

func TestOAuthRegistrationFlow(t *testing.T) {
	t.Run("OAuth Registration Request Generation", func(t *testing.T) {
		// Test OAuth user info to registration request conversion
		userInfo := &auth.OAuthUserInfo{
			ID:            "oauth-user-123",
			Email:         "<EMAIL>",
			EmailVerified: true,
			FirstName:     "John",
			LastName:      "Doe",
			Provider:      "google",
		}

		// Simulate username generation from email
		username := "john.doe" // extracted from email

		req := &authv1.RegisterUserRequest{
			Username:  username,
			Email:     userInfo.Email,
			Password:  "", // OAuth users don't have passwords
			FirstName: userInfo.FirstName,
			LastName:  userInfo.LastName,
		}

		assert.Equal(t, "john.doe", req.Username)
		assert.Equal(t, "<EMAIL>", req.Email)
		assert.Empty(t, req.Password) // OAuth users don't have passwords
		assert.Equal(t, "John", req.FirstName)
		assert.Equal(t, "Doe", req.LastName)
	})
}
