# 🧪 Complete Testing Guide for Hopen Backend

This guide provides a comprehensive overview of testing the Hopen backend, from quick health checks to detailed user story validation.

## 🚀 Quick Start

### 1. Health Check (30 seconds)
```bash
cd hopenbackend/tests
./quick_backend_check.sh
```
**Purpose**: Verify all services are running and responding correctly.

### 2. Comprehensive Testing (5-10 minutes)
```bash
./comprehensive_backend_test_plan.sh
```
**Purpose**: Validate complete user workflows against your user stories.

### 3. Manual Testing
```bash
./manual_grpcurl_commands.sh
```
**Purpose**: Reference for individual grpcurl commands for debugging.

## 📋 Testing Levels

### Level 1: Infrastructure Health
- **Script**: `quick_backend_check.sh`
- **Duration**: ~30 seconds
- **Purpose**: Verify services are running
- **When to use**: Before any development work, after deployments

### Level 2: User Story Validation
- **Script**: `comprehensive_backend_test_plan.sh`
- **Duration**: 5-10 minutes
- **Purpose**: End-to-end workflow testing
- **When to use**: Before releases, after major changes

### Level 3: Manual Testing & Debugging
- **Script**: `manual_grpcurl_commands.sh`
- **Duration**: As needed
- **Purpose**: Individual endpoint testing
- **When to use**: Debugging specific issues, exploring APIs

## 🎯 What Gets Tested

### Critical User Flows
1. **User Registration & Onboarding**
   - Multi-step signup process
   - Password breach checking (HIBP)
   - User profile creation

2. **Contact Relationship Management**
   - Send/accept/decline contact requests
   - Relationship status tracking
   - Contact list management

3. **Bubble Lifecycle Management**
   - Bubble creation between contacts
   - Invitation and voting systems
   - Member management
   - Countdown extension logic

4. **Critical Business Logic**
   - **Bubble Dissolution** (< 2 members) → contacts remain contacts
   - **Bubble Expiration** (timeout) → contacts become maybe_friends
   - Friend request acceptance/decline flows

5. **Real-time Features**
   - Message sending and retrieval
   - Presence updates
   - Batch presence checking

### Relationship State Transitions
The tests validate the complete relationship lifecycle:
```
none → contact_request_pending → contact → bubbler → maybe_friend → friend
```

## 🔧 Prerequisites

### Required Software
```bash
# Install grpcurl (macOS)
brew install grpcurl

# Install grpcurl (Linux)
go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest

# Verify installation
grpcurl --version
```

### Backend Services
```bash
# Start all services
cd hopenbackend
docker-compose up -d

# Verify services are running
docker-compose ps

# Check Kong Gateway
curl http://localhost:8001/status
```

## 📊 Understanding Test Results

### Success Output
```
🧪 COMPREHENSIVE BACKEND TEST PLAN FOR HOPEN
================================================

═══ CHECKING PREREQUISITES ═══
✓ Kong Gateway is accessible
✓ grpcurl is available

═══ PHASE 1: USER REGISTRATION AND SETUP ═══
✓ Register Alice
✓ Register Bob
...

═══ TEST RESULTS SUMMARY ═══
Total Tests: 25
Passed: 25
Failed: 0

🎉 All tests passed! Backend is ready.
```

### Failure Analysis
When tests fail, look for:
- **Service connectivity issues**: Check `docker-compose ps`
- **Authentication errors**: Some endpoints require JWT tokens
- **Validation errors**: Check request payload format
- **Business logic errors**: Review the specific test that failed

## 🐛 Troubleshooting

### Common Issues

1. **Kong Gateway Not Accessible**
   ```bash
   # Check Kong status
   docker-compose logs kong
   
   # Restart Kong
   docker-compose restart kong
   ```

2. **Services Not Responding**
   ```bash
   # Check all services
   docker-compose ps
   
   # Check specific service logs
   docker-compose logs auth
   docker-compose logs user
   docker-compose logs bubble
   ```

3. **grpcurl Not Found**
   ```bash
   # Install grpcurl
   brew install grpcurl  # macOS
   # or
   go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest
   ```

4. **Authentication Errors**
   - Some endpoints require JWT tokens
   - The comprehensive test plan handles this automatically
   - For manual testing, you'll need to capture tokens from auth responses

### Debug Commands
```bash
# List all available services
grpcurl -plaintext localhost:8000 list

# List methods for a service
grpcurl -plaintext localhost:8000 list hopen.auth.v1.AuthService

# Describe a method
grpcurl -plaintext localhost:8000 describe hopen.auth.v1.AuthService.RegisterUser

# Test with verbose output
grpcurl -plaintext -v -d '{}' localhost:8000 hopen.auth.v1.AuthService/GetAuthStatus
```

## 🔄 Integration with Development Workflow

### Before Starting Development
```bash
./quick_backend_check.sh
```

### Before Committing Changes
```bash
./comprehensive_backend_test_plan.sh
```

### Before Releases
```bash
# Run all test types
make test-all
./comprehensive_backend_test_plan.sh
```

### CI/CD Integration
```yaml
# Example GitHub Actions
- name: Backend Health Check
  run: |
    cd hopenbackend
    docker-compose up -d
    sleep 30
    ./tests/quick_backend_check.sh

- name: Comprehensive Backend Tests
  run: |
    cd hopenbackend/tests
    ./comprehensive_backend_test_plan.sh
```

## 📚 Related Documentation

- **[User Stories](../docs/features/user_stories.md)**: The requirements these tests validate
- **[API Documentation](../docs/backend/api/api_documentation.md)**: Complete API reference
- **[Kong Gateway Setup](../docs/backend/api/api_gateway.md)**: Gateway configuration
- **[Comprehensive Test Plan Details](./README_COMPREHENSIVE_TEST_PLAN.md)**: Detailed test documentation

## 🎯 Test Coverage

The comprehensive test plan covers:
- ✅ **User Registration**: Multi-step signup with validation
- ✅ **Contact Management**: Full request/accept/decline flow
- ✅ **Bubble Creation**: Contact-to-bubble workflow
- ✅ **Bubble Invitations**: Multi-step invite and voting system
- ✅ **Bubble Dissolution**: Critical business logic for < 2 members
- ✅ **Bubble Expiration**: Maybe-friend creation and friend requests
- ✅ **Relationship Transitions**: All state changes validated
- ✅ **Real-time Features**: Messaging and presence endpoints

## 💡 Tips for Effective Testing

1. **Start with health checks**: Always run `quick_backend_check.sh` first
2. **Use comprehensive tests regularly**: Run the full test plan before major changes
3. **Save test outputs**: Use `tee` to save results for analysis
4. **Test incrementally**: Use manual commands to debug specific issues
5. **Monitor logs**: Keep `docker-compose logs -f` running during testing
6. **Understand the business logic**: The tests validate specific user stories

---

This testing approach ensures your Hopen backend correctly implements the complex social dynamics and business rules that make the platform unique.
