package circuitbreaker

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// State represents the circuit breaker state
type State int

const (
	StateClosed State = iota
	StateHalfOpen
	StateOpen
)

func (s State) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateHalfOpen:
		return "HALF_OPEN"
	case StateOpen:
		return "OPEN"
	default:
		return "UNKNOWN"
	}
}

// Config holds circuit breaker configuration
type Config struct {
	Name             string
	MaxRequests      uint32
	Interval         time.Duration
	Timeout          time.Duration
	FailureThreshold uint32
	SuccessThreshold uint32
	OnStateChange    func(name string, from State, to State)
	IsFailure        func(err error) bool
}

// DefaultConfig returns a sensible default configuration
func DefaultConfig(name string) Config {
	return Config{
		Name:             name,
		MaxRequests:      10,
		Interval:         60 * time.Second,
		Timeout:          60 * time.Second,
		FailureThreshold: 5,
		SuccessThreshold: 3,
		IsFailure: func(err error) bool {
			return err != nil
		},
	}
}

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	config Config
	state  State
	mutex  sync.RWMutex
	logger *zap.Logger

	// Counters
	requests      uint32
	failures      uint32
	successes     uint32
	lastFailTime  time.Time
	lastStateTime time.Time

	// Metrics
	totalRequests  uint64
	totalFailures  uint64
	totalSuccesses uint64
}

// New creates a new circuit breaker
func New(config Config, logger *zap.Logger) *CircuitBreaker {
	if config.IsFailure == nil {
		config.IsFailure = func(err error) bool {
			return err != nil
		}
	}

	return &CircuitBreaker{
		config:        config,
		state:         StateClosed,
		logger:        logger.With(zap.String("circuit_breaker", config.Name)),
		lastStateTime: time.Now(),
	}
}

// Execute runs the given function with circuit breaker protection
func (cb *CircuitBreaker) Execute(ctx context.Context, fn func() error) error {
	// Check if we can proceed
	if !cb.canProceed() {
		cb.recordMetrics(false, true, 0) // blocked request
		return &CircuitBreakerError{
			Name:  cb.config.Name,
			State: cb.state,
		}
	}

	// Execute the function
	err := fn()

	// Record the result
	cb.recordResult(err)

	return err
}

// canProceed checks if a request should be allowed
func (cb *CircuitBreaker) canProceed() bool {
	cb.mutex.RLock()
	state := cb.state
	lastStateTime := cb.lastStateTime
	requests := cb.requests
	cb.mutex.RUnlock()

	switch state {
	case StateClosed:
		return true

	case StateOpen:
		// Check if timeout has passed to potentially transition to half-open
		if time.Since(lastStateTime) > cb.config.Timeout {
			// Attempt to transition to Half-Open. This must be under a full write lock.
			cb.mutex.Lock()
			// CRITICAL: We must re-check the state after acquiring the lock.
			// Another goroutine might have already transitioned it while we were waiting.
			if cb.state == StateOpen {
				cb.setState(StateHalfOpen)
				cb.resetCounters()
				// Allow the first request to go through immediately after transition.
				cb.mutex.Unlock()
				return true
			}
			// If the state is no longer Open, we fall through to the logic below.
			cb.mutex.Unlock()
			// Re-call canProceed to evaluate the new state correctly.
			return cb.canProceed()
		}
		return false

	case StateHalfOpen:
		// Allow limited requests in half-open state
		return requests < cb.config.MaxRequests

	default:
		return false
	}
}

// recordResult records the result of a request
func (cb *CircuitBreaker) recordResult(err error) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.requests++
	cb.totalRequests++

	isFailure := cb.config.IsFailure(err)

	if isFailure {
		cb.failures++
		cb.totalFailures++
		cb.lastFailTime = time.Now()

		// Check if we should open the circuit
		if cb.state == StateClosed && cb.failures >= cb.config.FailureThreshold {
			cb.setState(StateOpen)
		} else if cb.state == StateHalfOpen {
			// Any failure in half-open state opens the circuit
			cb.setState(StateOpen)
		}
	} else {
		cb.successes++
		cb.totalSuccesses++

		// Check if we should close the circuit from half-open
		if cb.state == StateHalfOpen && cb.successes >= cb.config.SuccessThreshold {
			cb.setState(StateClosed)
		}
	}
}

// setState changes the circuit breaker state
func (cb *CircuitBreaker) setState(newState State) {
	if cb.state == newState {
		return
	}

	oldState := cb.state
	cb.state = newState
	cb.lastStateTime = time.Now()

	if cb.config.OnStateChange != nil {
		cb.config.OnStateChange(cb.config.Name, oldState, newState)
	}

	cb.logger.Info("Circuit breaker state changed",
		zap.String("from", oldState.String()),
		zap.String("to", newState.String()),
		zap.Uint32("failures", cb.failures),
		zap.Uint32("successes", cb.successes))
}

// resetCounters resets the request counters
func (cb *CircuitBreaker) resetCounters() {
	cb.requests = 0
	cb.failures = 0
	cb.successes = 0
}

// recordMetrics records metrics for monitoring
func (cb *CircuitBreaker) recordMetrics(success, blocked bool, responseTime time.Duration) {
	// This can be extended to integrate with metrics systems
	if blocked {
		cb.logger.Debug("Circuit breaker blocked request",
			zap.String("name", cb.config.Name),
			zap.String("state", cb.state.String()))
	} else if success {
		cb.logger.Debug("Circuit breaker successful request",
			zap.String("name", cb.config.Name),
			zap.Duration("response_time", responseTime))
	} else {
		cb.logger.Debug("Circuit breaker failed request",
			zap.String("name", cb.config.Name),
			zap.Duration("response_time", responseTime))
	}
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() State {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// Metrics holds circuit breaker metrics
type Metrics struct {
	Name           string `json:"name"`
	State          State  `json:"state"`
	TotalRequests  uint64 `json:"total_requests"`
	TotalFailures  uint64 `json:"total_failures"`
	TotalSuccesses uint64 `json:"total_successes"`
}

// GetMetrics returns circuit breaker metrics
func (cb *CircuitBreaker) GetMetrics() Metrics {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return Metrics{
		Name:           cb.config.Name,
		State:          cb.state,
		TotalRequests:  cb.totalRequests,
		TotalFailures:  cb.totalFailures,
		TotalSuccesses: cb.totalSuccesses,
	}
}

// Reset resets the circuit breaker to closed state
func (cb *CircuitBreaker) Reset() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.setState(StateClosed)
	cb.resetCounters()
}

// CircuitBreakerError represents a circuit breaker error
type CircuitBreakerError struct {
	Name   string
	State  State
	Reason string
}

func (e *CircuitBreakerError) Error() string {
	if e.Reason != "" {
		return fmt.Sprintf("circuit breaker %s is %s: %s", e.Name, e.State.String(), e.Reason)
	}
	return fmt.Sprintf("circuit breaker %s is %s", e.Name, e.State.String())
}

// Close closes the circuit breaker (alias to Reset for compatibility)
func (cb *CircuitBreaker) Close() {
	cb.Reset()
}

// Common errors
var (
	ErrCircuitBreakerOpen = errors.New("circuit breaker is open")
	ErrTimeout            = errors.New("operation timed out")
)
