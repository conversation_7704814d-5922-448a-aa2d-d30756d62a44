package blacklist

import (
	"context"
	"time"

	"github.com/valkey-io/valkey-go"
	"go.uber.org/zap"
)

// Blacklist provides a mechanism for blacklisting JWTs
type Blacklist struct {
	client valkey.Client
	logger *zap.Logger
}

// New creates a new Blacklist service
func New(client valkey.Client, logger *zap.Logger) *Blacklist {
	return &Blacklist{
		client: client,
		logger: logger,
	}
}

// Add adds a token to the blacklist with the given expiration
func (b *Blacklist) Add(ctx context.Context, jti string, exp time.Duration) error {
	cmd := b.client.B().Set().Key(jti).Value("blacklisted").Ex(exp).Build()
	err := b.client.Do(ctx, cmd).Error()
	if err != nil {
		b.logger.Error("failed to add token to blacklist", zap.Error(err))
		return err
	}
	return nil
}

// IsBlacklisted checks if a token is in the blacklist
func (b *Blacklist) IsBlacklisted(ctx context.Context, jti string) (bool, error) {
	cmd := b.client.B().Get().Key(jti).Build()
	val, err := b.client.Do(ctx, cmd).ToString()
	if err != nil {
		if valkey.IsValkeyNil(err) {
			return false, nil
		}
		b.logger.Error("failed to check if token is blacklisted", zap.Error(err))
		return false, err
	}

	return val == "blacklisted", nil
}
