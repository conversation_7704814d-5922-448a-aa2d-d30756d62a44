package jwt

import (
	"context"
	"crypto/rsa"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/lestrrat-go/jwx/v2/jwk"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// Validator provides JWT validation
type Validator struct {
	logger    *zap.Logger
	config    *config.Config
	jwksCache *jwk.Cache
}

// NewValidator creates a new JWT validator
func NewValidator(logger *zap.Logger, cfg *config.Config) (*Validator, error) {
	// Create a JWK cache to store Oathkeeper's public keys
	jwksURL := cfg.Security.Auth.JWKSURL
	if jwksURL == "" {
		return nil, fmt.Errorf("jwks_url is not configured")
	}

	cache := jwk.NewCache(context.Background())
	err := cache.Register(jwksURL, jwk.WithRefreshInterval(15*time.Minute))
	if err != nil {
		return nil, fmt.Errorf("failed to register jwks url: %w", err)
	}

	// Fetch the JWKS once to ensure it's available on startup
	_, err = cache.Refresh(context.Background(), jwksURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch jwks: %w", err)
	}

	return &Validator{
		logger:    logger,
		config:    cfg,
		jwksCache: cache,
	}, nil
}

// Validate validates a JWT and returns the claims
func (v *Validator) Validate(ctx context.Context, tokenString string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		keySet, err := v.jwksCache.Get(ctx, v.config.Security.Auth.JWKSURL)
		if err != nil {
			return nil, fmt.Errorf("failed to get jwks: %w", err)
		}

		keyID, ok := token.Header["kid"].(string)
		if !ok {
			return nil, fmt.Errorf("kid header not found")
		}

		key, ok := keySet.LookupKeyID(keyID)
		if !ok {
			return nil, fmt.Errorf("key %s not found", keyID)
		}

		var pubkey interface{}
		if err := key.Raw(&pubkey); err != nil {
			return nil, fmt.Errorf("failed to get raw key: %w", err)
		}

		return pubkey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse jwt: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid claims")
	}

	// Validate issuer
	issuer := v.config.Security.Auth.JWTIssuer
	if !claims.VerifyIssuer(issuer, true) {
		return nil, fmt.Errorf("invalid issuer")
	}

	// Validate audience
	audience := v.config.Security.Auth.JWTAudience
	if !claims.VerifyAudience(audience, true) {
		return nil, fmt.Errorf("invalid audience")
	}

	return claims, nil
}

// AppleJWTValidator provides validation for Apple Sign-In JWT tokens
type AppleJWTValidator struct {
	logger    *zap.Logger
	jwksCache *jwk.Cache
	clientID  string
}

// AppleJWTClaims represents the claims structure for Apple ID tokens
type AppleJWTClaims struct {
	jwt.RegisteredClaims
	Email          string `json:"email,omitempty"`
	EmailVerified  string `json:"email_verified,omitempty"`
	Nonce          string `json:"nonce,omitempty"`
	NonceSupported bool   `json:"nonce_supported,omitempty"`
}

// NewAppleJWTValidator creates a new Apple JWT validator
func NewAppleJWTValidator(logger *zap.Logger, clientID string) (*AppleJWTValidator, error) {
	if clientID == "" {
		return nil, fmt.Errorf("client_id is required for Apple JWT validation")
	}

	// Apple's JWKS endpoint
	jwksURL := "https://appleid.apple.com/auth/keys"

	cache := jwk.NewCache(context.Background())
	err := cache.Register(jwksURL, jwk.WithRefreshInterval(15*time.Minute))
	if err != nil {
		return nil, fmt.Errorf("failed to register Apple JWKS URL: %w", err)
	}

	// Fetch the JWKS once to ensure it's available on startup
	_, err = cache.Refresh(context.Background(), jwksURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Apple JWKS: %w", err)
	}

	return &AppleJWTValidator{
		logger:    logger,
		jwksCache: cache,
		clientID:  clientID,
	}, nil
}

// ValidateAppleIDToken validates an Apple ID token and returns the claims
func (v *AppleJWTValidator) ValidateAppleIDToken(ctx context.Context, tokenString string) (*AppleJWTClaims, error) {
	v.logger.Debug("Validating Apple ID token", zap.String("token_prefix", tokenString[:min(10, len(tokenString))]))

	token, err := jwt.ParseWithClaims(tokenString, &AppleJWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify the signing method is RS256
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Get the key ID from the token header
		keyID, ok := token.Header["kid"].(string)
		if !ok {
			return nil, fmt.Errorf("kid header not found in Apple ID token")
		}

		// Fetch Apple's public keys
		keySet, err := v.jwksCache.Get(ctx, "https://appleid.apple.com/auth/keys")
		if err != nil {
			return nil, fmt.Errorf("failed to get Apple JWKS: %w", err)
		}

		// Find the matching key
		key, ok := keySet.LookupKeyID(keyID)
		if !ok {
			return nil, fmt.Errorf("Apple public key with ID %s not found", keyID)
		}

		// Extract the RSA public key
		var pubkey interface{}
		if err := key.Raw(&pubkey); err != nil {
			return nil, fmt.Errorf("failed to get raw Apple public key: %w", err)
		}

		// Ensure it's an RSA public key
		rsaPubKey, ok := pubkey.(*rsa.PublicKey)
		if !ok {
			return nil, fmt.Errorf("Apple public key is not an RSA key")
		}

		return rsaPubKey, nil
	})

	if err != nil {
		v.logger.Warn("Failed to parse Apple ID token", zap.Error(err))
		return nil, fmt.Errorf("failed to parse Apple ID token: %w", err)
	}

	if !token.Valid {
		v.logger.Warn("Apple ID token is invalid")
		return nil, fmt.Errorf("Apple ID token is invalid")
	}

	claims, ok := token.Claims.(*AppleJWTClaims)
	if !ok {
		v.logger.Warn("Invalid Apple ID token claims")
		return nil, fmt.Errorf("invalid Apple ID token claims")
	}

	// Validate Apple-specific claims
	if err := v.validateAppleClaims(claims); err != nil {
		v.logger.Warn("Apple ID token claims validation failed", zap.Error(err))
		return nil, fmt.Errorf("Apple ID token claims validation failed: %w", err)
	}

	v.logger.Info("Apple ID token validated successfully",
		zap.String("subject", claims.Subject),
		zap.String("email", claims.Email),
		zap.Time("expires_at", claims.ExpiresAt.Time))

	return claims, nil
}

// validateAppleClaims validates Apple-specific claims
func (v *AppleJWTValidator) validateAppleClaims(claims *AppleJWTClaims) error {
	// Validate issuer
	if claims.Issuer != "https://appleid.apple.com" {
		return fmt.Errorf("invalid issuer: expected 'https://appleid.apple.com', got '%s'", claims.Issuer)
	}

	// Validate audience (should match the client ID)
	if claims.Audience == nil || len(claims.Audience) == 0 {
		return fmt.Errorf("audience claim is missing")
	}

	// Check if our client ID is in the audience
	audienceValid := false
	for _, aud := range claims.Audience {
		if aud == v.clientID {
			audienceValid = true
			break
		}
	}
	if !audienceValid {
		return fmt.Errorf("invalid audience: client ID '%s' not found in audience %v", v.clientID, claims.Audience)
	}

	// Validate expiration
	if claims.ExpiresAt == nil {
		return fmt.Errorf("expiration claim is missing")
	}
	if time.Now().After(claims.ExpiresAt.Time) {
		return fmt.Errorf("token has expired at %v", claims.ExpiresAt.Time)
	}

	// Validate issued at time
	if claims.IssuedAt == nil {
		return fmt.Errorf("issued at claim is missing")
	}
	if time.Now().Before(claims.IssuedAt.Time.Add(-5 * time.Minute)) {
		return fmt.Errorf("token issued in the future: %v", claims.IssuedAt.Time)
	}

	// Validate subject (user ID)
	if claims.Subject == "" {
		return fmt.Errorf("subject claim is missing")
	}

	return nil
}

// Helper function for min
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
