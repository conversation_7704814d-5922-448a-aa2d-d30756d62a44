package jwt

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSessionTokenGeneration(t *testing.T) {
	// Create JWT generator
	generator := NewGenerator("test-api-key", "test-secret")
	require.NotNil(t, generator)

	// Test data
	userID := "user-123"
	sessionID := "session-456"
	email := "<EMAIL>"
	permissions := []string{"user:read", "user:write"}
	roles := []string{"user", "premium"}
	mqttBubbleIDs := []string{"bubble-1", "bubble-2", "bubble-3"}

	// Generate session token
	token, err := generator.GenerateSessionToken(userID, sessionID, email, permissions, roles, mqttBubbleIDs)
	require.NoError(t, err)
	require.NotEmpty(t, token)

	t.Logf("Generated token: %s", token)

	// Validate session token
	claims, err := generator.ValidateSessionToken(token)
	require.NoError(t, err)
	require.NotNil(t, claims)

	// Verify claims
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, sessionID, claims.SessionID)
	assert.Equal(t, email, claims.Email)
	assert.Equal(t, permissions, claims.Permissions)
	assert.Equal(t, roles, claims.Roles)
	assert.Equal(t, mqttBubbleIDs, claims.MQTTBubbleIDs)
	assert.Greater(t, claims.MQTTPermissionsUpdatedAt, int64(0))

	// Verify standard JWT claims
	assert.Equal(t, "hopen-auth", claims.Issuer)
	assert.Equal(t, userID, claims.Subject)
	assert.Contains(t, claims.Audience, "hopen-api")
	assert.Contains(t, claims.Audience, "hopen-mqtt")
	assert.Equal(t, sessionID, claims.ID)
}

func TestSessionTokenValidation(t *testing.T) {
	generator := NewGenerator("test-api-key", "test-secret")

	// Test invalid token
	_, err := generator.ValidateSessionToken("invalid-token")
	assert.Error(t, err)

	// Test token with wrong secret
	wrongGenerator := NewGenerator("test-api-key", "wrong-secret")
	token, err := generator.GenerateSessionToken("user-123", "session-456", "<EMAIL>", nil, nil, nil)
	require.NoError(t, err)

	_, err = wrongGenerator.ValidateSessionToken(token)
	assert.Error(t, err)
}

func TestSessionTokenExpiration(t *testing.T) {
	generator := NewGenerator("test-api-key", "test-secret")

	// Generate token
	token, err := generator.GenerateSessionToken("user-123", "session-456", "<EMAIL>", nil, nil, nil)
	require.NoError(t, err)

	// Validate immediately (should work)
	claims, err := generator.ValidateSessionToken(token)
	require.NoError(t, err)

	// Check expiration is set correctly (24 hours from now)
	expectedExpiry := time.Now().Add(24 * time.Hour)
	actualExpiry := claims.ExpiresAt.Time
	
	// Allow 1 minute tolerance for test execution time
	assert.WithinDuration(t, expectedExpiry, actualExpiry, time.Minute)
}

func TestMQTTBubbleIDsInToken(t *testing.T) {
	generator := NewGenerator("test-api-key", "test-secret")

	// Test with empty bubble IDs
	token1, err := generator.GenerateSessionToken("user-123", "session-456", "<EMAIL>", nil, nil, nil)
	require.NoError(t, err)

	claims1, err := generator.ValidateSessionToken(token1)
	require.NoError(t, err)
	assert.Empty(t, claims1.MQTTBubbleIDs)

	// Test with multiple bubble IDs
	bubbleIDs := []string{"bubble-1", "bubble-2", "bubble-3", "bubble-4", "bubble-5"}
	token2, err := generator.GenerateSessionToken("user-123", "session-456", "<EMAIL>", nil, nil, bubbleIDs)
	require.NoError(t, err)

	claims2, err := generator.ValidateSessionToken(token2)
	require.NoError(t, err)
	assert.Equal(t, bubbleIDs, claims2.MQTTBubbleIDs)
	assert.Len(t, claims2.MQTTBubbleIDs, 5)
}
