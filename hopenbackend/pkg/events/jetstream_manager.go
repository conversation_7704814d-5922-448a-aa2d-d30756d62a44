package events

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
)

// JetStreamInterface defines the interface for JetStream operations
type JetStreamInterface interface {
	AddStream(cfg *nats.StreamConfig, opts ...nats.JSOpt) (*nats.StreamInfo, error)
	UpdateStream(cfg *nats.StreamConfig, opts ...nats.JSOpt) (*nats.StreamInfo, error)
	DeleteStream(name string, opts ...nats.JSOpt) error
	StreamInfo(name string, opts ...nats.JSOpt) (*nats.StreamInfo, error)
	AddConsumer(stream string, cfg *nats.ConsumerConfig, opts ...nats.JSOpt) (*nats.ConsumerInfo, error)
	DeleteConsumer(stream, consumer string, opts ...nats.JSOpt) error
	ConsumerInfo(stream, consumer string, opts ...nats.JSOpt) (*nats.ConsumerInfo, error)
}

// JetStreamManager manages JetStream streams and consumers
type JetStreamManager struct {
	js     JetStreamInterface
	logger *zap.Logger
	config *StreamConfig
	retry  *RetryConfig
}

// RetryConfig defines retry behavior for JetStream operations
type RetryConfig struct {
	MaxAttempts int           `json:"max_attempts"`
	BaseDelay   time.Duration `json:"base_delay"`
	MaxDelay    time.Duration `json:"max_delay"`
	Multiplier  float64       `json:"multiplier"`
}

// StreamConfig holds configuration for all streams
type StreamConfig struct {
	// Core event streams
	RealtimeEvents StreamDefinition `json:"realtime_events"`
	BubbleEvents   StreamDefinition `json:"bubble_events"`
	UserEvents     StreamDefinition `json:"user_events"`
	OutboxEvents   StreamDefinition `json:"outbox_events"`

	// Specialized streams
	CallEvents         StreamDefinition `json:"call_events"`
	NotificationEvents StreamDefinition `json:"notification_events"`
	PresenceEvents     StreamDefinition `json:"presence_events"`
	MediaEvents        StreamDefinition `json:"media_events"`

	// Dead letter queue stream
	DLQEvents StreamDefinition `json:"dlq_events"`
}

// StreamDefinition defines a JetStream stream configuration
type StreamDefinition struct {
	Name        string               `json:"name"`
	Description string               `json:"description"`
	Subjects    []string             `json:"subjects"`
	Retention   nats.RetentionPolicy `json:"retention"`
	MaxAge      time.Duration        `json:"max_age"`
	MaxBytes    int64                `json:"max_bytes"`
	MaxMsgs     int64                `json:"max_msgs"`
	Storage     nats.StorageType     `json:"storage"`
	Replicas    int                  `json:"replicas"`
	Compression bool                 `json:"compression"`

	// Consumer configurations for this stream
	Consumers []ConsumerDefinition `json:"consumers"`
}

// ConsumerDefinition defines a JetStream consumer configuration
type ConsumerDefinition struct {
	Name          string             `json:"name"`
	Description   string             `json:"description"`
	DeliverPolicy nats.DeliverPolicy `json:"deliver_policy"`
	AckPolicy     nats.AckPolicy     `json:"ack_policy"`
	AckWait       time.Duration      `json:"ack_wait"`
	MaxDeliver    int                `json:"max_deliver"`
	FilterSubject string             `json:"filter_subject"`
	ReplayPolicy  nats.ReplayPolicy  `json:"replay_policy"`
	RateLimitBps  uint64             `json:"rate_limit_bps"`
	MaxAckPending int                `json:"max_ack_pending"`

	// Queue group for load balancing
	QueueGroup string `json:"queue_group"`
}

// NewJetStreamManager creates a new JetStream manager
func NewJetStreamManager(js JetStreamInterface, logger *zap.Logger) *JetStreamManager {
	return &JetStreamManager{
		js:     js,
		logger: logger,
		config: DefaultStreamConfig(),
		retry:  DefaultRetryConfig(),
	}
}

// NewJetStreamManagerWithConfig creates a new JetStream manager with custom configuration
func NewJetStreamManagerWithConfig(js JetStreamInterface, logger *zap.Logger, config *StreamConfig, retry *RetryConfig) *JetStreamManager {
	if retry == nil {
		retry = DefaultRetryConfig()
	}
	return &JetStreamManager{
		js:     js,
		logger: logger,
		config: config,
		retry:  retry,
	}
}

// NewJetStreamManagerFromNATS creates a new JetStream manager from a NATS JetStream context
func NewJetStreamManagerFromNATS(js nats.JetStreamContext, logger *zap.Logger) *JetStreamManager {
	return NewJetStreamManager(js, logger)
}

// NewJetStreamManagerFromNATSWithConfig creates a new JetStream manager from a NATS JetStream context with custom configuration
func NewJetStreamManagerFromNATSWithConfig(js nats.JetStreamContext, logger *zap.Logger, config *StreamConfig, retry *RetryConfig) *JetStreamManager {
	return NewJetStreamManagerWithConfig(js, logger, config, retry)
}

// DefaultRetryConfig returns the default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxAttempts: 3,
		BaseDelay:   100 * time.Millisecond,
		MaxDelay:    5 * time.Second,
		Multiplier:  2.0,
	}
}

// DefaultStreamConfig returns the default stream configuration for Hopen
func DefaultStreamConfig() *StreamConfig {
	return &StreamConfig{
		RealtimeEvents: StreamDefinition{
			Name:        "REALTIME_EVENTS",
			Description: "Real-time messaging and communication events",
			Subjects:    []string{"realtime.>", "message.>", "conversation.>"},
			Retention:   nats.LimitsPolicy,
			MaxAge:      7 * 24 * time.Hour,      // 7 days
			MaxBytes:    10 * 1024 * 1024 * 1024, // 10GB
			Storage:     nats.FileStorage,
			Replicas:    3, // High availability
			Compression: true,
			Consumers: []ConsumerDefinition{
				{
					Name:          "realtime-processor",
					Description:   "Processes real-time events for live updates",
					DeliverPolicy: nats.DeliverNewPolicy,
					AckPolicy:     nats.AckExplicitPolicy,
					AckWait:       30 * time.Second,
					MaxDeliver:    3,
					QueueGroup:    "realtime-workers",
				},
			},
		},

		BubbleEvents: StreamDefinition{
			Name:        "BUBBLE_EVENTS",
			Description: "Bubble lifecycle and membership events",
			Subjects:    []string{"bubble.>", "events.bubble.>"},
			Retention:   nats.WorkQueuePolicy,
			MaxAge:      24 * time.Hour,
			Storage:     nats.FileStorage,
			Replicas:    3,
			Compression: true,
			Consumers: []ConsumerDefinition{
				{
					Name:          "bubble-lifecycle",
					Description:   "Handles bubble creation, expiry, dissolution",
					DeliverPolicy: nats.DeliverAllPolicy,
					AckPolicy:     nats.AckExplicitPolicy,
					AckWait:       60 * time.Second,
					MaxDeliver:    5,
					QueueGroup:    "bubble-workers",
				},
			},
		},

		UserEvents: StreamDefinition{
			Name:        "USER_EVENTS",
			Description: "User lifecycle, presence, and relationship events",
			Subjects:    []string{"user.>", "presence.>", "relationship.>"},
			Retention:   nats.LimitsPolicy,
			MaxAge:      30 * 24 * time.Hour, // 30 days
			Storage:     nats.FileStorage,
			Replicas:    3,
			Compression: true,
			Consumers: []ConsumerDefinition{
				{
					Name:          "user-processor",
					Description:   "Processes user events for profile updates",
					DeliverPolicy: nats.DeliverNewPolicy,
					AckPolicy:     nats.AckExplicitPolicy,
					AckWait:       30 * time.Second,
					MaxDeliver:    3,
					QueueGroup:    "user-workers",
				},
			},
		},

		OutboxEvents: StreamDefinition{
			Name:        "OUTBOX_EVENTS",
			Description: "Outbox pattern events from all microservices",
			Subjects:    []string{"outbox.>"},
			Retention:   nats.WorkQueuePolicy,
			MaxAge:      7 * 24 * time.Hour, // 7 days
			Storage:     nats.FileStorage,
			Replicas:    3,
			Compression: true,
			Consumers: []ConsumerDefinition{
				{
					Name:          "outbox-processor",
					Description:   "Processes outbox events for reliable delivery",
					DeliverPolicy: nats.DeliverAllPolicy,
					AckPolicy:     nats.AckExplicitPolicy,
					AckWait:       30 * time.Second,
					MaxDeliver:    5,
					QueueGroup:    "outbox-workers",
				},
			},
		},

		CallEvents: StreamDefinition{
			Name:        "CALL_EVENTS",
			Description: "Call lifecycle, state changes, and WebRTC events",
			Subjects:    []string{"call.>", "webrtc.>", "livekit.>"},
			Retention:   nats.WorkQueuePolicy,
			MaxAge:      24 * time.Hour,         // 1 day retention for call events
			MaxBytes:    5 * 1024 * 1024 * 1024, // 5GB
			Storage:     nats.FileStorage,
			Replicas:    3, // High availability for critical call events
			Compression: true,
			Consumers: []ConsumerDefinition{
				{
					Name:          "call-lifecycle",
					Description:   "Handles call creation, termination, and state changes",
					DeliverPolicy: nats.DeliverAllPolicy,
					AckPolicy:     nats.AckExplicitPolicy,
					AckWait:       60 * time.Second, // Longer timeout for call processing
					MaxDeliver:    5,
					QueueGroup:    "call-workers",
					MaxAckPending: 1000,
				},
				{
					Name:          "call-analytics",
					Description:   "Processes call events for analytics and monitoring",
					DeliverPolicy: nats.DeliverNewPolicy,
					AckPolicy:     nats.AckExplicitPolicy,
					AckWait:       30 * time.Second,
					MaxDeliver:    3,
					QueueGroup:    "analytics-workers",
					MaxAckPending: 500,
				},
			},
		},

		NotificationEvents: StreamDefinition{
			Name:        "NOTIFICATION_EVENTS",
			Description: "Push notifications and alerts",
			Subjects:    []string{"notification.>", "alert.>"},
			Retention:   nats.LimitsPolicy,
			MaxAge:      7 * 24 * time.Hour,
			Storage:     nats.FileStorage,
			Replicas:    2,
			Compression: true,
		},

		PresenceEvents: StreamDefinition{
			Name:        "PRESENCE_EVENTS",
			Description: "User presence and activity events",
			Subjects:    []string{"presence.>", "activity.>"},
			Retention:   nats.LimitsPolicy,
			MaxAge:      24 * time.Hour,     // Short retention for presence
			Storage:     nats.MemoryStorage, // Fast access
			Replicas:    2,
			Compression: false, // Speed over space
		},

		MediaEvents: StreamDefinition{
			Name:        "MEDIA_EVENTS",
			Description: "Media upload, processing, and delivery events",
			Subjects:    []string{"media.>", "upload.>", "processing.>"},
			Retention:   nats.LimitsPolicy,
			MaxAge:      30 * 24 * time.Hour,
			Storage:     nats.FileStorage,
			Replicas:    2,
			Compression: true,
		},

		DLQEvents: StreamDefinition{
			Name:        "DLQ_EVENTS",
			Description: "Dead letter queue for failed message processing",
			Subjects:    []string{"dlq.>"},
			Retention:   nats.LimitsPolicy,
			MaxAge:      30 * 24 * time.Hour, // Long retention for debugging
			Storage:     nats.FileStorage,
			Replicas:    3,
			Compression: true,
		},
	}
}

// InitializeStreams creates or updates all streams and consumers
func (jsm *JetStreamManager) InitializeStreams(ctx context.Context) error {
	// Validate configuration first
	if err := jsm.validateConfiguration(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	streams, err := jsm.extractStreamDefinitions()
	if err != nil {
		return fmt.Errorf("failed to extract stream definitions: %w", err)
	}

	jsm.logger.Info("Starting JetStream initialization",
		zap.Int("total_streams", len(streams)))

	var errors []error
	successCount := 0

	for _, streamDef := range streams {
		// Check context cancellation before processing each stream
		select {
		case <-ctx.Done():
			return fmt.Errorf("initialization cancelled: %w", ctx.Err())
		default:
		}

		if err := jsm.createOrUpdateStream(ctx, streamDef); err != nil {
			jsm.logger.Error("Failed to create/update stream",
				zap.String("stream", streamDef.Name),
				zap.Error(err))
			errors = append(errors, fmt.Errorf("stream %s: %w", streamDef.Name, err))
			continue
		}

		// Create consumers for this stream
		consumerErrors := 0
		for _, consumerDef := range streamDef.Consumers {
			// Check context cancellation before processing each consumer
			select {
			case <-ctx.Done():
				return fmt.Errorf("initialization cancelled during consumer creation: %w", ctx.Err())
			default:
			}

			if err := jsm.createOrUpdateConsumer(ctx, streamDef.Name, consumerDef); err != nil {
				jsm.logger.Error("Failed to create/update consumer",
					zap.String("stream", streamDef.Name),
					zap.String("consumer", consumerDef.Name),
					zap.Error(err))
				errors = append(errors, fmt.Errorf("consumer %s for stream %s: %w",
					consumerDef.Name, streamDef.Name, err))
				consumerErrors++
			}
		}

		if consumerErrors == 0 {
			successCount++
			jsm.logger.Info("Successfully initialized stream",
				zap.String("stream", streamDef.Name),
				zap.Int("consumers", len(streamDef.Consumers)))
		}
	}

	jsm.logger.Info("JetStream initialization completed",
		zap.Int("successful_streams", successCount),
		zap.Int("total_streams", len(streams)),
		zap.Int("errors", len(errors)))

	// Return error only if all streams failed
	if len(errors) == len(streams) {
		return fmt.Errorf("all streams failed to initialize: %v", errors)
	}

	return nil
}

// validateConfiguration validates the JetStream manager configuration
func (jsm *JetStreamManager) validateConfiguration() error {
	if jsm.js == nil {
		return fmt.Errorf("JetStream context is nil")
	}

	if jsm.config == nil {
		return fmt.Errorf("stream configuration is nil")
	}

	if jsm.retry == nil {
		return fmt.Errorf("retry configuration is nil")
	}

	if jsm.retry.MaxAttempts <= 0 {
		return fmt.Errorf("retry max attempts must be positive")
	}

	if jsm.retry.BaseDelay <= 0 {
		return fmt.Errorf("retry base delay must be positive")
	}

	if jsm.retry.MaxDelay <= 0 {
		return fmt.Errorf("retry max delay must be positive")
	}

	if jsm.retry.Multiplier <= 1.0 {
		return fmt.Errorf("retry multiplier must be greater than 1.0")
	}

	return nil
}

// extractStreamDefinitions uses reflection to extract all StreamDefinition fields from the config
func (jsm *JetStreamManager) extractStreamDefinitions() ([]StreamDefinition, error) {
	if jsm.config == nil {
		return nil, fmt.Errorf("stream config is nil")
	}

	var streams []StreamDefinition
	configValue := reflect.ValueOf(jsm.config)
	if configValue.Kind() == reflect.Ptr {
		configValue = configValue.Elem()
	}

	if configValue.Kind() != reflect.Struct {
		return nil, fmt.Errorf("config is not a struct")
	}

	configType := configValue.Type()
	for i := 0; i < configValue.NumField(); i++ {
		field := configValue.Field(i)
		fieldType := configType.Field(i)

		// Skip unexported fields
		if !field.CanInterface() {
			continue
		}

		// Check if the field is of type StreamDefinition
		if field.Type() == reflect.TypeOf(StreamDefinition{}) {
			streamDef := field.Interface().(StreamDefinition)
			if streamDef.Name != "" { // Only include streams with names
				streams = append(streams, streamDef)
				jsm.logger.Debug("Found stream definition",
					zap.String("field", fieldType.Name),
					zap.String("stream", streamDef.Name))
			}
		}
	}

	if len(streams) == 0 {
		return nil, fmt.Errorf("no valid stream definitions found in config")
	}

	jsm.logger.Info("Extracted stream definitions using reflection",
		zap.Int("count", len(streams)))

	return streams, nil
}

// retryOperation executes an operation with exponential backoff retry logic
func (jsm *JetStreamManager) retryOperation(ctx context.Context, operation func() error, operationName string) error {
	var lastErr error

	for attempt := 1; attempt <= jsm.retry.MaxAttempts; attempt++ {
		select {
		case <-ctx.Done():
			return fmt.Errorf("operation %s cancelled: %w", operationName, ctx.Err())
		default:
		}

		err := operation()
		if err == nil {
			if attempt > 1 {
				jsm.logger.Info("Operation succeeded after retry",
					zap.String("operation", operationName),
					zap.Int("attempt", attempt))
			}
			return nil
		}

		lastErr = err
		if attempt == jsm.retry.MaxAttempts {
			break
		}

		// Calculate delay with exponential backoff
		delay := time.Duration(float64(jsm.retry.BaseDelay) *
			(jsm.retry.Multiplier * float64(attempt-1)))
		if delay > jsm.retry.MaxDelay {
			delay = jsm.retry.MaxDelay
		}

		jsm.logger.Warn("Operation failed, retrying",
			zap.String("operation", operationName),
			zap.Int("attempt", attempt),
			zap.Int("max_attempts", jsm.retry.MaxAttempts),
			zap.Duration("delay", delay),
			zap.Error(err))

		select {
		case <-ctx.Done():
			return fmt.Errorf("operation %s cancelled during retry: %w", operationName, ctx.Err())
		case <-time.After(delay):
		}
	}

	return fmt.Errorf("operation %s failed after %d attempts: %w",
		operationName, jsm.retry.MaxAttempts, lastErr)
}

// createOrUpdateStream creates or updates a JetStream stream
func (jsm *JetStreamManager) createOrUpdateStream(ctx context.Context, def StreamDefinition) error {
	return jsm.retryOperation(ctx, func() error {
		return jsm.doCreateOrUpdateStream(def)
	}, fmt.Sprintf("create/update stream %s", def.Name))
}

// doCreateOrUpdateStream performs the actual stream creation/update operation
func (jsm *JetStreamManager) doCreateOrUpdateStream(def StreamDefinition) error {
	config := &nats.StreamConfig{
		Name:        def.Name,
		Description: def.Description,
		Subjects:    def.Subjects,
		Retention:   def.Retention,
		MaxAge:      def.MaxAge,
		MaxBytes:    def.MaxBytes,
		MaxMsgs:     def.MaxMsgs,
		Storage:     def.Storage,
		Replicas:    def.Replicas,
	}

	// Set compression based on boolean flag
	if def.Compression {
		config.Compression = nats.S2Compression
	} else {
		config.Compression = nats.NoCompression
	}

	// Try to update first, then create if it doesn't exist
	_, err := jsm.js.UpdateStream(config)
	if err != nil {
		if err == nats.ErrStreamNotFound {
			_, err = jsm.js.AddStream(config)
			if err != nil {
				return fmt.Errorf("failed to create stream: %w", err)
			}
			jsm.logger.Info("Created JetStream stream", zap.String("stream", def.Name))
		} else {
			return fmt.Errorf("failed to update stream: %w", err)
		}
	} else {
		jsm.logger.Info("Updated JetStream stream", zap.String("stream", def.Name))
	}

	return nil
}

// createOrUpdateConsumer creates or updates a JetStream consumer
func (jsm *JetStreamManager) createOrUpdateConsumer(ctx context.Context, streamName string, def ConsumerDefinition) error {
	return jsm.retryOperation(ctx, func() error {
		return jsm.doCreateOrUpdateConsumer(streamName, def)
	}, fmt.Sprintf("create/update consumer %s for stream %s", def.Name, streamName))
}

// doCreateOrUpdateConsumer performs the actual consumer creation/update operation
func (jsm *JetStreamManager) doCreateOrUpdateConsumer(streamName string, def ConsumerDefinition) error {
	config := &nats.ConsumerConfig{
		Name:          def.Name,
		Description:   def.Description,
		DeliverPolicy: def.DeliverPolicy,
		AckPolicy:     def.AckPolicy,
		AckWait:       def.AckWait,
		MaxDeliver:    def.MaxDeliver,
		FilterSubject: def.FilterSubject,
		ReplayPolicy:  def.ReplayPolicy,
		RateLimit:     def.RateLimitBps,
		MaxAckPending: def.MaxAckPending,
	}

	// Check if consumer already exists
	existingConsumer, err := jsm.js.ConsumerInfo(streamName, def.Name)
	if err != nil {
		// Consumer doesn't exist, create it
		if err == nats.ErrConsumerNotFound {
			_, err = jsm.js.AddConsumer(streamName, config)
			if err != nil {
				return fmt.Errorf("failed to create consumer %s: %w", def.Name, err)
			}
			jsm.logger.Info("Created JetStream consumer",
				zap.String("stream", streamName),
				zap.String("consumer", def.Name))
			return nil
		}
		return fmt.Errorf("failed to check consumer existence: %w", err)
	}

	// Consumer exists, check if update is needed
	if jsm.consumerConfigChanged(&existingConsumer.Config, config) {
		// Delete and recreate consumer if configuration changed
		// Note: NATS JetStream doesn't support direct consumer updates for all fields
		err = jsm.js.DeleteConsumer(streamName, def.Name)
		if err != nil {
			return fmt.Errorf("failed to delete existing consumer %s for update: %w", def.Name, err)
		}

		_, err = jsm.js.AddConsumer(streamName, config)
		if err != nil {
			return fmt.Errorf("failed to recreate consumer %s: %w", def.Name, err)
		}
		jsm.logger.Info("Updated JetStream consumer",
			zap.String("stream", streamName),
			zap.String("consumer", def.Name))
	} else {
		jsm.logger.Debug("JetStream consumer configuration unchanged",
			zap.String("stream", streamName),
			zap.String("consumer", def.Name))
	}

	return nil
}

// consumerConfigChanged compares two consumer configurations to determine if an update is needed
func (jsm *JetStreamManager) consumerConfigChanged(existing, desired *nats.ConsumerConfig) bool {
	// Compare key configuration fields that affect consumer behavior
	if existing.DeliverPolicy != desired.DeliverPolicy ||
		existing.AckPolicy != desired.AckPolicy ||
		existing.AckWait != desired.AckWait ||
		existing.MaxDeliver != desired.MaxDeliver ||
		existing.FilterSubject != desired.FilterSubject ||
		existing.ReplayPolicy != desired.ReplayPolicy ||
		existing.RateLimit != desired.RateLimit ||
		existing.MaxAckPending != desired.MaxAckPending ||
		existing.Description != desired.Description {
		return true
	}

	return false
}

// GetStreamInfo returns information about a specific stream
func (jsm *JetStreamManager) GetStreamInfo(streamName string) (*nats.StreamInfo, error) {
	return jsm.js.StreamInfo(streamName)
}

// GetConsumerInfo returns information about a specific consumer
func (jsm *JetStreamManager) GetConsumerInfo(streamName, consumerName string) (*nats.ConsumerInfo, error) {
	return jsm.js.ConsumerInfo(streamName, consumerName)
}

// DeleteStream deletes a stream (use with caution)
func (jsm *JetStreamManager) DeleteStream(streamName string) error {
	return jsm.js.DeleteStream(streamName)
}

// DeleteConsumer deletes a consumer
func (jsm *JetStreamManager) DeleteConsumer(streamName, consumerName string) error {
	return jsm.js.DeleteConsumer(streamName, consumerName)
}

// Note: Additional utility methods like ListStreams, ListConsumers, PurgeStream, etc.
// can be added by extending the JetStreamInterface as needed
