package events

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"go.uber.org/zap"
)

// JetStreamConsumer provides enhanced JetStream consumer functionality
type JetStreamConsumer struct {
	js           nats.JetStreamContext
	logger       *zap.Logger
	config       *ConsumerConfig
	subscriptions map[string]*nats.Subscription
	mu           sync.RWMutex
	ctx          context.Context
	cancel       context.CancelFunc
}

// ConsumerConfig holds configuration for JetStream consumers
type ConsumerConfig struct {
	// Consumer behavior
	MaxConcurrent     int           `json:"max_concurrent"`
	AckWait          time.Duration `json:"ack_wait"`
	MaxDeliver       int           `json:"max_deliver"`
	BackoffStrategy  string        `json:"backoff_strategy"`
	
	// Error handling
	EnableDLQ        bool          `json:"enable_dlq"`
	DLQSubject       string        `json:"dlq_subject"`
	MaxRetries       int           `json:"max_retries"`
	RetryBackoff     time.Duration `json:"retry_backoff"`
	
	// Monitoring
	EnableMetrics    bool          `json:"enable_metrics"`
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	
	// Flow control
	MaxAckPending    int           `json:"max_ack_pending"`
	RateLimitBps     uint64        `json:"rate_limit_bps"`
}

// DefaultConsumerConfig returns default consumer configuration
func DefaultConsumerConfig() *ConsumerConfig {
	return &ConsumerConfig{
		MaxConcurrent:     10,
		AckWait:          30 * time.Second,
		MaxDeliver:       3,
		BackoffStrategy:  "exponential",
		EnableDLQ:        true,
		DLQSubject:       "dlq.>",
		MaxRetries:       3,
		RetryBackoff:     time.Second,
		EnableMetrics:    true,
		HeartbeatInterval: 30 * time.Second,
		MaxAckPending:    100,
		RateLimitBps:     0, // No rate limit by default
	}
}

// MessageHandler defines the interface for handling JetStream messages
type MessageHandler interface {
	Handle(ctx context.Context, msg *nats.Msg) error
	GetSubject() string
	GetConsumerName() string
}

// Metrics for JetStream consumers
var (
	jetStreamMessagesProcessed = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "jetstream_messages_processed_total",
			Help: "Total number of JetStream messages processed",
		},
		[]string{"stream", "consumer", "status"},
	)

	jetStreamProcessingDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "jetstream_processing_duration_seconds",
			Help:    "Time spent processing JetStream messages",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"stream", "consumer"},
	)

	jetStreamAckLatency = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "jetstream_ack_latency_seconds",
			Help:    "Latency between message delivery and acknowledgment",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"stream", "consumer"},
	)

	jetStreamActiveConsumers = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "jetstream_active_consumers",
			Help: "Number of active JetStream consumers",
		},
		[]string{"stream"},
	)
)

// NewJetStreamConsumer creates a new enhanced JetStream consumer
func NewJetStreamConsumer(js nats.JetStreamContext, logger *zap.Logger, config *ConsumerConfig) *JetStreamConsumer {
	if config == nil {
		config = DefaultConsumerConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &JetStreamConsumer{
		js:            js,
		logger:        logger,
		config:        config,
		subscriptions: make(map[string]*nats.Subscription),
		ctx:           ctx,
		cancel:        cancel,
	}
}

// Subscribe creates a durable pull consumer with enhanced error handling
func (jsc *JetStreamConsumer) Subscribe(streamName string, handler MessageHandler) error {
	jsc.mu.Lock()
	defer jsc.mu.Unlock()

	consumerName := handler.GetConsumerName()
	subject := handler.GetSubject()

	// Create consumer configuration
	consumerConfig := &nats.ConsumerConfig{
		Name:          consumerName,
		DeliverPolicy: nats.DeliverNewPolicy,
		AckPolicy:     nats.AckExplicitPolicy,
		AckWait:       jsc.config.AckWait,
		MaxDeliver:    jsc.config.MaxDeliver,
		FilterSubject: subject,
		MaxAckPending: jsc.config.MaxAckPending,
		RateLimit:     jsc.config.RateLimitBps,
	}

	// Create or get existing consumer
	_, err := jsc.js.AddConsumer(streamName, consumerConfig)
	if err != nil && err != nats.ErrConsumerNameAlreadyInUse {
		return fmt.Errorf("failed to create consumer %s: %w", consumerName, err)
	}

	// Create pull subscription
	sub, err := jsc.js.PullSubscribe(subject, consumerName, nats.Bind(streamName, consumerName))
	if err != nil {
		return fmt.Errorf("failed to create pull subscription: %w", err)
	}

	// Store subscription
	subscriptionKey := fmt.Sprintf("%s:%s", streamName, consumerName)
	jsc.subscriptions[subscriptionKey] = sub

	// Start message processing goroutine
	go jsc.processMessages(streamName, consumerName, sub, handler)

	// Update metrics
	if jsc.config.EnableMetrics {
		jetStreamActiveConsumers.WithLabelValues(streamName).Inc()
	}

	jsc.logger.Info("JetStream consumer subscribed successfully",
		zap.String("stream", streamName),
		zap.String("consumer", consumerName),
		zap.String("subject", subject))

	return nil
}

// processMessages handles message processing with concurrency control
func (jsc *JetStreamConsumer) processMessages(streamName, consumerName string, sub *nats.Subscription, handler MessageHandler) {
	defer func() {
		if jsc.config.EnableMetrics {
			jetStreamActiveConsumers.WithLabelValues(streamName).Dec()
		}
	}()

	// Create semaphore for concurrency control
	semaphore := make(chan struct{}, jsc.config.MaxConcurrent)

	for {
		select {
		case <-jsc.ctx.Done():
			jsc.logger.Info("Stopping message processing",
				zap.String("stream", streamName),
				zap.String("consumer", consumerName))
			return
		default:
			// Fetch messages in batches
			msgs, err := sub.Fetch(jsc.config.MaxConcurrent, nats.MaxWait(5*time.Second))
			if err != nil {
				if err == nats.ErrTimeout {
					continue // No messages available, continue polling
				}
				jsc.logger.Error("Failed to fetch messages",
					zap.String("stream", streamName),
					zap.String("consumer", consumerName),
					zap.Error(err))
				time.Sleep(time.Second)
				continue
			}

			// Process each message concurrently
			for _, msg := range msgs {
				select {
				case semaphore <- struct{}{}:
					go jsc.handleMessage(streamName, consumerName, msg, handler, semaphore)
				case <-jsc.ctx.Done():
					return
				}
			}
		}
	}
}

// handleMessage processes a single message with error handling and metrics
func (jsc *JetStreamConsumer) handleMessage(streamName, consumerName string, msg *nats.Msg, handler MessageHandler, semaphore chan struct{}) {
	defer func() { <-semaphore }()

	start := time.Now()
	var status string

	defer func() {
		if jsc.config.EnableMetrics {
			duration := time.Since(start)
			jetStreamMessagesProcessed.WithLabelValues(streamName, consumerName, status).Inc()
			jetStreamProcessingDuration.WithLabelValues(streamName, consumerName).Observe(duration.Seconds())
		}
	}()

	// Create context with timeout
	ctx, cancel := context.WithTimeout(jsc.ctx, jsc.config.AckWait)
	defer cancel()

	// Process message with retries
	var lastErr error
	for attempt := 0; attempt <= jsc.config.MaxRetries; attempt++ {
		if attempt > 0 {
			// Exponential backoff
			backoff := time.Duration(attempt) * jsc.config.RetryBackoff
			time.Sleep(backoff)
		}

		err := handler.Handle(ctx, msg)
		if err == nil {
			// Success - acknowledge message
			if ackErr := msg.Ack(); ackErr != nil {
				jsc.logger.Error("Failed to acknowledge message",
					zap.String("stream", streamName),
					zap.String("consumer", consumerName),
					zap.Error(ackErr))
			} else {
				if jsc.config.EnableMetrics {
					ackLatency := time.Since(start)
					jetStreamAckLatency.WithLabelValues(streamName, consumerName).Observe(ackLatency.Seconds())
				}
			}
			status = "success"
			return
		}

		lastErr = err
		jsc.logger.Warn("Message processing failed",
			zap.String("stream", streamName),
			zap.String("consumer", consumerName),
			zap.Int("attempt", attempt+1),
			zap.Error(err))
	}

	// All retries exhausted - handle failure
	status = "failed"
	jsc.logger.Error("Message processing failed after all retries",
		zap.String("stream", streamName),
		zap.String("consumer", consumerName),
		zap.Error(lastErr))

	// Send to DLQ if enabled
	if jsc.config.EnableDLQ {
		jsc.sendToDLQ(msg, lastErr)
	}

	// Negative acknowledge to trigger redelivery or move to DLQ
	if nakErr := msg.Nak(); nakErr != nil {
		jsc.logger.Error("Failed to negative acknowledge message",
			zap.String("stream", streamName),
			zap.String("consumer", consumerName),
			zap.Error(nakErr))
	}
}

// sendToDLQ sends failed messages to dead letter queue
func (jsc *JetStreamConsumer) sendToDLQ(msg *nats.Msg, processingErr error) {
	dlqSubject := fmt.Sprintf("dlq.%s", msg.Subject)
	
	// Create DLQ message with error information
	dlqMsg := &nats.Msg{
		Subject: dlqSubject,
		Data:    msg.Data,
		Header:  make(nats.Header),
	}
	
	// Copy original headers
	for key, values := range msg.Header {
		dlqMsg.Header[key] = values
	}
	
	// Add DLQ-specific headers
	dlqMsg.Header.Set("DLQ-Original-Subject", msg.Subject)
	dlqMsg.Header.Set("DLQ-Error", processingErr.Error())
	dlqMsg.Header.Set("DLQ-Timestamp", time.Now().Format(time.RFC3339))
	
	// Publish to DLQ
	if _, err := jsc.js.PublishMsg(dlqMsg); err != nil {
		jsc.logger.Error("Failed to send message to DLQ",
			zap.String("original_subject", msg.Subject),
			zap.String("dlq_subject", dlqSubject),
			zap.Error(err))
	} else {
		jsc.logger.Info("Message sent to DLQ",
			zap.String("original_subject", msg.Subject),
			zap.String("dlq_subject", dlqSubject))
	}
}

// Unsubscribe removes a subscription
func (jsc *JetStreamConsumer) Unsubscribe(streamName, consumerName string) error {
	jsc.mu.Lock()
	defer jsc.mu.Unlock()

	subscriptionKey := fmt.Sprintf("%s:%s", streamName, consumerName)
	sub, exists := jsc.subscriptions[subscriptionKey]
	if !exists {
		return fmt.Errorf("subscription not found: %s", subscriptionKey)
	}

	if err := sub.Unsubscribe(); err != nil {
		return fmt.Errorf("failed to unsubscribe: %w", err)
	}

	delete(jsc.subscriptions, subscriptionKey)

	jsc.logger.Info("JetStream consumer unsubscribed",
		zap.String("stream", streamName),
		zap.String("consumer", consumerName))

	return nil
}

// Close stops all consumers and cleans up resources
func (jsc *JetStreamConsumer) Close() error {
	jsc.cancel()

	jsc.mu.Lock()
	defer jsc.mu.Unlock()

	var errors []error
	for key, sub := range jsc.subscriptions {
		if err := sub.Unsubscribe(); err != nil {
			errors = append(errors, fmt.Errorf("failed to unsubscribe %s: %w", key, err))
		}
	}

	jsc.subscriptions = make(map[string]*nats.Subscription)

	if len(errors) > 0 {
		return fmt.Errorf("errors during close: %v", errors)
	}

	jsc.logger.Info("JetStream consumer closed successfully")
	return nil
}
