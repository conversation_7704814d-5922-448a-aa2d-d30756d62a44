package outbox

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gocql/gocql"
	"go.uber.org/zap"

	"hopenbackend/pkg/database"
)

// ScyllaDBRepository implements the Repository interface using ScyllaDB
type ScyllaDBRepository struct {
	client *database.ScyllaDBClient
	logger *zap.Logger
}

// NewScyllaDBRepository creates a new ScyllaDB outbox repository
func NewScyllaDBRepository(client *database.ScyllaDBClient, logger *zap.Logger) Repository {
	return &ScyllaDBRepository{
		client: client,
		logger: logger,
	}
}

// CreateOutboxMessage creates a new outbox message within a batch (for atomic operations)
func (r *ScyllaDBRepository) CreateOutboxMessage(ctx context.Context, q database.Querier, message *OutboxMessage) error {
	// For ScyllaDB, we need to handle this differently since gocql.Batch doesn't implement database.Querier
	// This method is primarily for PostgreSQL compatibility
	return fmt.Errorf("ScyllaDB outbox should use CreateOutboxMessageInBatch for atomic operations")

	return nil
}

// CreateOutboxMessageInBatch creates a new outbox message within a ScyllaDB batch
func (r *ScyllaDBRepository) CreateOutboxMessageInBatch(ctx context.Context, batch *gocql.Batch, message *OutboxMessage) error {
	if message.ID == "" {
		message.ID = gocql.TimeUUID().String()
	}

	// Set default values
	message.CreatedAt = time.Now()
	message.Status = string(StatusPending)
	if message.MaxRetries == 0 {
		message.MaxRetries = 3
	}

	// Create time bucket (hourly partitioning)
	timeBucket := message.CreatedAt.UTC().Format("2006-01-02-15")

	// Marshal event data and metadata
	eventData, err := json.Marshal(message.EventData)
	if err != nil {
		return fmt.Errorf("failed to marshal event data: %w", err)
	}

	var eventMetadata []byte
	if message.EventMetadata != nil {
		eventMetadata, err = json.Marshal(message.EventMetadata)
		if err != nil {
			return fmt.Errorf("failed to marshal event metadata: %w", err)
		}
	}

	// Parse aggregate ID as UUID
	aggregateUUID, err := gocql.ParseUUID(message.AggregateID)
	if err != nil {
		return fmt.Errorf("invalid aggregate ID format: %w", err)
	}

	// Parse event ID as TIMEUUID
	eventUUID, err := gocql.ParseUUID(message.ID)
	if err != nil {
		return fmt.Errorf("invalid event ID format: %w", err)
	}

	// Add to batch
	batch.Query(`
		INSERT INTO outbox_events (
			time_bucket, event_id, aggregate_id, aggregate_type, event_type,
			event_data, event_metadata, status, retry_count, max_retries,
			created_at, processed_at, next_retry_at, error_message
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		timeBucket, eventUUID, aggregateUUID, message.AggregateType, message.EventType,
		string(eventData), string(eventMetadata), message.Status, message.RetryCount, message.MaxRetries,
		message.CreatedAt, nil, message.NextRetryAt, message.ErrorMessage)

	return nil
}

// CreateOutboxMessageWithOwnTransaction creates a new outbox message with its own batch
func (r *ScyllaDBRepository) CreateOutboxMessageWithOwnTransaction(ctx context.Context, message *OutboxMessage) error {
	// Create a new batch for this operation
	batch := r.client.NewBatch(gocql.LoggedBatch)

	// Add the outbox message to the batch
	if err := r.CreateOutboxMessageInBatch(ctx, batch, message); err != nil {
		return err
	}

	// Execute the batch
	if err := r.client.Session.ExecuteBatch(batch); err != nil {
		r.logger.Error("Failed to execute outbox message batch", zap.Error(err))
		return fmt.Errorf("failed to create outbox message: %w", err)
	}

	return nil
}

// GetPendingMessages retrieves pending messages for processing
func (r *ScyllaDBRepository) GetPendingMessages(ctx context.Context, limit int) ([]*OutboxMessage, error) {
	// Query pending events from the last few hours to ensure we don't miss any
	now := time.Now().UTC()
	timeBuckets := []string{
		now.Format("2006-01-02-15"),                     // Current hour
		now.Add(-1 * time.Hour).Format("2006-01-02-15"), // Previous hour
		now.Add(-2 * time.Hour).Format("2006-01-02-15"), // Two hours ago
	}

	var allMessages []*OutboxMessage

	for _, bucket := range timeBuckets {
		query := `
			SELECT time_bucket, event_id, aggregate_id, aggregate_type, event_type,
				   event_data, event_metadata, status, retry_count, max_retries,
				   created_at, processed_at, next_retry_at, error_message
			FROM outbox_events_by_status
			WHERE status = ? AND time_bucket = ?
			LIMIT ?`

		iter := r.client.Session.Query(query, StatusPending, bucket, limit).Iter()

		var timeBucket, eventData, eventMetadata, status, eventType, aggregateType, errorMessage string
		var eventID, aggregateID gocql.UUID
		var retryCount, maxRetries int
		var createdAt time.Time
		var processedAtPtr, nextRetryAtPtr *time.Time

		for iter.Scan(&timeBucket, &eventID, &aggregateID, &aggregateType, &eventType,
			&eventData, &eventMetadata, &status, &retryCount, &maxRetries,
			&createdAt, &processedAtPtr, &nextRetryAtPtr, &errorMessage) {

			var errorMessagePtr *string
			if errorMessage != "" {
				errorMessagePtr = &errorMessage
			}

			message := &OutboxMessage{
				ID:            eventID.String(),
				AggregateID:   aggregateID.String(),
				AggregateType: aggregateType,
				EventType:     eventType,
				Status:        status,
				RetryCount:    retryCount,
				MaxRetries:    maxRetries,
				CreatedAt:     createdAt,
				ErrorMessage:  errorMessagePtr,
			}

			if processedAtPtr != nil {
				message.PublishedAt = processedAtPtr
			}
			if nextRetryAtPtr != nil {
				message.NextRetryAt = nextRetryAtPtr
			}

			// Unmarshal event data
			if err := json.Unmarshal([]byte(eventData), &message.EventData); err != nil {
				r.logger.Error("Failed to unmarshal event data", zap.Error(err))
				continue
			}

			// Unmarshal event metadata if present
			if eventMetadata != "" {
				if err := json.Unmarshal([]byte(eventMetadata), &message.EventMetadata); err != nil {
					r.logger.Error("Failed to unmarshal event metadata", zap.Error(err))
					continue
				}
			}

			allMessages = append(allMessages, message)

			// Stop if we've reached the limit
			if len(allMessages) >= limit {
				break
			}
		}

		if err := iter.Close(); err != nil {
			r.logger.Error("Failed to iterate pending messages", zap.Error(err))
		}

		// Stop if we've reached the limit
		if len(allMessages) >= limit {
			break
		}
	}

	return allMessages, nil
}

// MarkMessageAsProcessing marks a message as being processed
func (r *ScyllaDBRepository) MarkMessageAsProcessing(ctx context.Context, messageID string) error {
	return r.updateMessageStatus(ctx, messageID, StatusProcessing, "")
}

// MarkMessageAsPublished marks a message as successfully published
func (r *ScyllaDBRepository) MarkMessageAsPublished(ctx context.Context, messageID string) error {
	return r.updateMessageStatus(ctx, messageID, StatusPublished, "")
}

// MarkMessageAsFailed marks a message as failed and schedules retry
func (r *ScyllaDBRepository) MarkMessageAsFailed(ctx context.Context, messageID string, errorMessage string) error {
	return r.updateMessageStatus(ctx, messageID, StatusFailed, errorMessage)
}

// updateMessageStatus is a helper method to update message status
func (r *ScyllaDBRepository) updateMessageStatus(ctx context.Context, messageID string, status OutboxStatus, errorMessage string) error {
	eventUUID, err := gocql.ParseUUID(messageID)
	if err != nil {
		return fmt.Errorf("invalid message ID format: %w", err)
	}

	// First, find the time bucket for this event
	timeBucket, err := r.findTimeBucketForEvent(ctx, eventUUID)
	if err != nil {
		return fmt.Errorf("failed to find time bucket for event: %w", err)
	}

	// Calculate next retry time for failed messages
	var nextRetryAt *time.Time
	if status == StatusFailed {
		retryTime := time.Now().Add(5 * time.Minute) // Retry in 5 minutes
		nextRetryAt = &retryTime
	}

	// Update the message status
	query := `
		UPDATE outbox_events 
		SET status = ?, processed_at = ?, error_message = ?, next_retry_at = ?
		WHERE time_bucket = ? AND event_id = ?`

	var processedAt *time.Time
	if status == StatusPublished {
		now := time.Now()
		processedAt = &now
	}

	if err := r.client.Session.Query(query, string(status), processedAt, errorMessage, nextRetryAt, timeBucket, eventUUID).Exec(); err != nil {
		return fmt.Errorf("failed to update message status: %w", err)
	}

	return nil
}

// findTimeBucketForEvent finds the time bucket for a given event ID
func (r *ScyllaDBRepository) findTimeBucketForEvent(ctx context.Context, eventID gocql.UUID) (string, error) {
	// Extract timestamp from TIMEUUID
	timestamp := eventID.Time()
	return timestamp.UTC().Format("2006-01-02-15"), nil
}

// GetFailedMessages retrieves failed messages for retry
func (r *ScyllaDBRepository) GetFailedMessages(ctx context.Context, limit int) ([]*OutboxMessage, error) {
	// Similar to GetPendingMessages but for failed status
	// Implementation would be similar, querying for StatusFailed
	return []*OutboxMessage{}, nil // Placeholder
}

// CleanupOldMessages removes old published messages (handled by TTL in ScyllaDB)
func (r *ScyllaDBRepository) CleanupOldMessages(ctx context.Context, olderThan time.Duration) error {
	// ScyllaDB handles cleanup automatically via TTL
	// This method is kept for interface compatibility
	r.logger.Info("ScyllaDB outbox cleanup handled automatically via TTL")
	return nil
}

// EventExists checks if an outbox event exists for a given aggregate and event type
func (r *ScyllaDBRepository) EventExists(ctx context.Context, aggregateID, eventType string) (bool, error) {
	aggregateUUID, err := gocql.ParseUUID(aggregateID)
	if err != nil {
		return false, fmt.Errorf("invalid aggregate ID format: %w", err)
	}

	// Check recent time buckets for the event
	now := time.Now().UTC()
	timeBuckets := []string{
		now.Format("2006-01-02-15"),
		now.Add(-1 * time.Hour).Format("2006-01-02-15"),
		now.Add(-2 * time.Hour).Format("2006-01-02-15"),
	}

	for _, bucket := range timeBuckets {
		query := `
			SELECT COUNT(*) 
			FROM outbox_events 
			WHERE time_bucket = ? AND aggregate_id = ? AND event_type = ?`

		var count int
		if err := r.client.Session.Query(query, bucket, aggregateUUID, eventType).Scan(&count); err != nil {
			continue // Try next bucket
		}

		if count > 0 {
			return true, nil
		}
	}

	return false, nil
}
