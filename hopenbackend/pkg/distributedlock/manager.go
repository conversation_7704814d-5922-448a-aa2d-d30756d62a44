package distributedlock

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/valkey-io/valkey-go"
	"go.uber.org/zap"
)

// LockManager provides distributed locking using Valkey (Redis-compatible)
// Implements the Redlock algorithm for high availability
type LockManager struct {
	client valkey.Client
	logger *zap.Logger
}

// Lock represents a distributed lock
type Lock struct {
	key        string
	value      string
	ttl        time.Duration
	client     valkey.Client
	logger     *zap.Logger
	acquired   bool
	acquiredAt time.Time
}

// LockOptions configures lock behavior
type LockOptions struct {
	TTL            time.Duration // Lock expiration time
	RetryDelay     time.Duration // Delay between retry attempts
	MaxRetries     int           // Maximum number of retry attempts
	AcquireTimeout time.Duration // Total timeout for acquiring lock
}

// DefaultLockOptions returns sensible defaults for most use cases
func DefaultLockOptions() *LockOptions {
	return &LockOptions{
		TTL:            10 * time.Second,
		RetryDelay:     100 * time.Millisecond,
		MaxRetries:     3,
		AcquireTimeout: 5 * time.Second,
	}
}

// NewLockManager creates a new distributed lock manager
func NewLockManager(client valkey.Client, logger *zap.Logger) *LockManager {
	return &LockManager{
		client: client,
		logger: logger,
	}
}

// AcquireLock attempts to acquire a distributed lock
func (lm *LockManager) AcquireLock(ctx context.Context, key string, opts *LockOptions) (*Lock, error) {
	if opts == nil {
		opts = DefaultLockOptions()
	}

	// Generate unique value for this lock attempt
	value, err := generateLockValue()
	if err != nil {
		return nil, fmt.Errorf("failed to generate lock value: %w", err)
	}

	lock := &Lock{
		key:    key,
		value:  value,
		ttl:    opts.TTL,
		client: lm.client,
		logger: lm.logger,
	}

	// Create context with timeout for the entire acquisition process
	acquireCtx, cancel := context.WithTimeout(ctx, opts.AcquireTimeout)
	defer cancel()

	// Attempt to acquire lock with retries
	for attempt := 0; attempt <= opts.MaxRetries; attempt++ {
		if attempt > 0 {
			// Wait before retry
			select {
			case <-acquireCtx.Done():
				return nil, fmt.Errorf("lock acquisition timeout: %w", acquireCtx.Err())
			case <-time.After(opts.RetryDelay):
			}
		}

		acquired, err := lm.tryAcquire(acquireCtx, lock)
		if err != nil {
			lm.logger.Warn("Lock acquisition attempt failed",
				zap.String("key", key),
				zap.Int("attempt", attempt+1),
				zap.Error(err))
			continue
		}

		if acquired {
			lock.acquired = true
			lock.acquiredAt = time.Now()
			lm.logger.Info("Lock acquired successfully",
				zap.String("key", key),
				zap.String("value", value),
				zap.Duration("ttl", opts.TTL),
				zap.Int("attempt", attempt+1))
			return lock, nil
		}

		lm.logger.Debug("Lock acquisition failed, retrying",
			zap.String("key", key),
			zap.Int("attempt", attempt+1),
			zap.Int("max_retries", opts.MaxRetries))
	}

	return nil, fmt.Errorf("failed to acquire lock after %d attempts", opts.MaxRetries+1)
}

// tryAcquire attempts to acquire the lock once
func (lm *LockManager) tryAcquire(ctx context.Context, lock *Lock) (bool, error) {
	// Use SET with NX (only if not exists) and EX (expiration)
	cmd := lm.client.B().Set().Key(lock.key).Value(lock.value).Nx().Ex(lock.ttl).Build()
	result, err := lm.client.Do(ctx, cmd).ToString()
	if err != nil {
		if valkey.IsValkeyNil(err) {
			// Key already exists, lock not acquired
			return false, nil
		}
		return false, fmt.Errorf("failed to execute SET command: %w", err)
	}

	// "OK" means the key was set successfully
	return result == "OK", nil
}

// Release releases the distributed lock
func (l *Lock) Release(ctx context.Context) error {
	if !l.acquired {
		return fmt.Errorf("lock not acquired, cannot release")
	}

	// Use Lua script to ensure we only delete our own lock
	// This prevents accidentally deleting a lock that was acquired by another process
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`

	cmd := l.client.B().Eval().Script(luaScript).Numkeys(1).Key(l.key).Arg(l.value).Build()
	result, err := l.client.Do(ctx, cmd).AsInt64()
	if err != nil {
		l.logger.Error("Failed to release lock",
			zap.String("key", l.key),
			zap.Error(err))
		return fmt.Errorf("failed to release lock: %w", err)
	}

	if result == 1 {
		l.acquired = false
		l.logger.Info("Lock released successfully",
			zap.String("key", l.key),
			zap.String("value", l.value),
			zap.Duration("held_for", time.Since(l.acquiredAt)))
		return nil
	}

	l.logger.Warn("Lock was not released (may have expired or been taken by another process)",
		zap.String("key", l.key),
		zap.String("value", l.value))
	return fmt.Errorf("lock was not released (may have expired)")
}

// Extend extends the lock TTL (useful for long-running operations)
func (l *Lock) Extend(ctx context.Context, additionalTTL time.Duration) error {
	if !l.acquired {
		return fmt.Errorf("lock not acquired, cannot extend")
	}

	// Use Lua script to extend TTL only if we own the lock
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("EXPIRE", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	newTTLSeconds := int64(additionalTTL.Seconds())
	cmd := l.client.B().Eval().Script(luaScript).Numkeys(1).Key(l.key).Arg(l.value).Arg(fmt.Sprintf("%d", newTTLSeconds)).Build()
	result, err := l.client.Do(ctx, cmd).AsInt64()
	if err != nil {
		return fmt.Errorf("failed to extend lock: %w", err)
	}

	if result == 1 {
		l.logger.Info("Lock extended successfully",
			zap.String("key", l.key),
			zap.Duration("additional_ttl", additionalTTL))
		return nil
	}

	return fmt.Errorf("failed to extend lock (may have expired or been taken by another process)")
}

// IsAcquired returns whether the lock is currently acquired
func (l *Lock) IsAcquired() bool {
	return l.acquired
}

// Key returns the lock key
func (l *Lock) Key() string {
	return l.key
}

// Value returns the lock value
func (l *Lock) Value() string {
	return l.value
}

// AcquiredAt returns when the lock was acquired
func (l *Lock) AcquiredAt() time.Time {
	return l.acquiredAt
}

// generateLockValue generates a cryptographically secure random value for the lock
func generateLockValue() (string, error) {
	bytes := make([]byte, 16) // 128 bits of randomness
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// WithLock executes a function while holding a distributed lock
func (lm *LockManager) WithLock(ctx context.Context, key string, opts *LockOptions, fn func(ctx context.Context) error) error {
	lock, err := lm.AcquireLock(ctx, key, opts)
	if err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}
	defer func() {
		if releaseErr := lock.Release(ctx); releaseErr != nil {
			lm.logger.Error("Failed to release lock in defer",
				zap.String("key", key),
				zap.Error(releaseErr))
		}
	}()

	return fn(ctx)
}
