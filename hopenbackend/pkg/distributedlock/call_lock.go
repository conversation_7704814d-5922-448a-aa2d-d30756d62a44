package distributedlock

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// CallLockManager provides specialized locking for call operations
type CallLockManager struct {
	lockManager *LockManager
	logger      *zap.Logger
}

// NewCallLockManager creates a new call-specific lock manager
func NewCallLockManager(lock<PERSON>anager *LockManager, logger *zap.Logger) *CallLockManager {
	return &CallLockManager{
		lockManager: lockManager,
		logger:      logger,
	}
}

// CallLockOptions defines options for call-related locks
type CallLockOptions struct {
	TTL            time.Duration // How long to hold the lock
	RetryDelay     time.Duration // Delay between retries
	MaxRetries     int           // Maximum retry attempts
	AcquireTimeout time.Duration // Total timeout for acquiring lock
}

// DefaultCallLockOptions returns sensible defaults for call operations
func DefaultCallLockOptions() *CallLockOptions {
	return &CallLockOptions{
		TTL:            15 * time.Second, // Longer TTL for call operations
		RetryDelay:     50 * time.Millisecond,
		MaxRetries:     5,
		AcquireTimeout: 3 * time.Second,
	}
}

// AcquireCallCreationLock acquires a lock for call creation in a specific bubble
func (clm *CallLockManager) AcquireCallCreationLock(ctx context.Context, bubbleID string, opts *CallLockOptions) (*Lock, error) {
	if opts == nil {
		opts = DefaultCallLockOptions()
	}

	lockKey := fmt.Sprintf("call:creation:bubble:%s", bubbleID)

	lockOpts := &LockOptions{
		TTL:            opts.TTL,
		RetryDelay:     opts.RetryDelay,
		MaxRetries:     opts.MaxRetries,
		AcquireTimeout: opts.AcquireTimeout,
	}

	clm.logger.Info("Attempting to acquire call creation lock",
		zap.String("bubble_id", bubbleID),
		zap.String("lock_key", lockKey),
		zap.Duration("ttl", opts.TTL))

	return clm.lockManager.AcquireLock(ctx, lockKey, lockOpts)
}

// AcquireConversationCallLock acquires a lock for call creation in a conversation
func (clm *CallLockManager) AcquireConversationCallLock(ctx context.Context, conversationID string, opts *CallLockOptions) (*Lock, error) {
	if opts == nil {
		opts = DefaultCallLockOptions()
	}

	lockKey := fmt.Sprintf("call:creation:conversation:%s", conversationID)

	lockOpts := &LockOptions{
		TTL:            opts.TTL,
		RetryDelay:     opts.RetryDelay,
		MaxRetries:     opts.MaxRetries,
		AcquireTimeout: opts.AcquireTimeout,
	}

	clm.logger.Info("Attempting to acquire conversation call lock",
		zap.String("conversation_id", conversationID),
		zap.String("lock_key", lockKey),
		zap.Duration("ttl", opts.TTL))

	return clm.lockManager.AcquireLock(ctx, lockKey, lockOpts)
}

// AcquireLiveKitRoomLock acquires a lock for LiveKit room operations
func (clm *CallLockManager) AcquireLiveKitRoomLock(ctx context.Context, roomName string, opts *CallLockOptions) (*Lock, error) {
	if opts == nil {
		opts = DefaultCallLockOptions()
	}

	lockKey := fmt.Sprintf("livekit:room:%s", roomName)

	lockOpts := &LockOptions{
		TTL:            opts.TTL,
		RetryDelay:     opts.RetryDelay,
		MaxRetries:     opts.MaxRetries,
		AcquireTimeout: opts.AcquireTimeout,
	}

	clm.logger.Info("Attempting to acquire LiveKit room lock",
		zap.String("room_name", roomName),
		zap.String("lock_key", lockKey),
		zap.Duration("ttl", opts.TTL))

	return clm.lockManager.AcquireLock(ctx, lockKey, lockOpts)
}

// WithCallCreationLock executes a function while holding a call creation lock
func (clm *CallLockManager) WithCallCreationLock(ctx context.Context, bubbleID string, opts *CallLockOptions, fn func(ctx context.Context) error) error {
	if opts == nil {
		opts = DefaultCallLockOptions()
	}

	lockKey := fmt.Sprintf("call:creation:bubble:%s", bubbleID)

	lockOpts := &LockOptions{
		TTL:            opts.TTL,
		RetryDelay:     opts.RetryDelay,
		MaxRetries:     opts.MaxRetries,
		AcquireTimeout: opts.AcquireTimeout,
	}

	clm.logger.Info("Executing function with call creation lock",
		zap.String("bubble_id", bubbleID),
		zap.String("lock_key", lockKey))

	return clm.lockManager.WithLock(ctx, lockKey, lockOpts, fn)
}

// WithConversationCallLock executes a function while holding a conversation call lock
func (clm *CallLockManager) WithConversationCallLock(ctx context.Context, conversationID string, opts *CallLockOptions, fn func(ctx context.Context) error) error {
	if opts == nil {
		opts = DefaultCallLockOptions()
	}

	lockKey := fmt.Sprintf("call:creation:conversation:%s", conversationID)

	lockOpts := &LockOptions{
		TTL:            opts.TTL,
		RetryDelay:     opts.RetryDelay,
		MaxRetries:     opts.MaxRetries,
		AcquireTimeout: opts.AcquireTimeout,
	}

	clm.logger.Info("Executing function with conversation call lock",
		zap.String("conversation_id", conversationID),
		zap.String("lock_key", lockKey))

	return clm.lockManager.WithLock(ctx, lockKey, lockOpts, fn)
}

// WithLiveKitRoomLock executes a function while holding a LiveKit room lock
func (clm *CallLockManager) WithLiveKitRoomLock(ctx context.Context, roomName string, opts *CallLockOptions, fn func(ctx context.Context) error) error {
	if opts == nil {
		opts = DefaultCallLockOptions()
	}

	lockKey := fmt.Sprintf("livekit:room:%s", roomName)

	lockOpts := &LockOptions{
		TTL:            opts.TTL,
		RetryDelay:     opts.RetryDelay,
		MaxRetries:     opts.MaxRetries,
		AcquireTimeout: opts.AcquireTimeout,
	}

	clm.logger.Info("Executing function with LiveKit room lock",
		zap.String("room_name", roomName),
		zap.String("lock_key", lockKey))

	return clm.lockManager.WithLock(ctx, lockKey, lockOpts, fn)
}

// AcquireCallJoinLock acquires a distributed lock for call join operations
func (clm *CallLockManager) AcquireCallJoinLock(ctx context.Context, callID string, opts *CallLockOptions) (*Lock, error) {
	if opts == nil {
		opts = DefaultCallLockOptions()
	}

	lockKey := fmt.Sprintf("call:join:%s", callID)

	lockOpts := &LockOptions{
		TTL:            opts.TTL,
		RetryDelay:     opts.RetryDelay,
		MaxRetries:     opts.MaxRetries,
		AcquireTimeout: opts.AcquireTimeout,
	}

	clm.logger.Info("Attempting to acquire call join lock",
		zap.String("call_id", callID),
		zap.String("lock_key", lockKey),
		zap.Duration("ttl", opts.TTL))

	return clm.lockManager.AcquireLock(ctx, lockKey, lockOpts)
}

// WithCallJoinLock executes a function while holding a call join lock
func (clm *CallLockManager) WithCallJoinLock(ctx context.Context, callID string, opts *CallLockOptions, fn func(context.Context) error) error {
	if opts == nil {
		opts = DefaultCallLockOptions()
	}

	lockKey := fmt.Sprintf("call:join:%s", callID)

	lockOpts := &LockOptions{
		TTL:            opts.TTL,
		RetryDelay:     opts.RetryDelay,
		MaxRetries:     opts.MaxRetries,
		AcquireTimeout: opts.AcquireTimeout,
	}

	clm.logger.Info("Executing function with call join lock",
		zap.String("call_id", callID),
		zap.String("lock_key", lockKey))

	return clm.lockManager.WithLock(ctx, lockKey, lockOpts, fn)
}
