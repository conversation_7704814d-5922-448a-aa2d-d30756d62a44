package database

import (
	"context"
	"errors"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"

	apperrors "hopenbackend/pkg/errors"
)

// PostgreSQLErrorTranslator implements ErrorTranslator for PostgreSQL-specific errors
type PostgreSQLErrorTranslator struct{}

// NewPostgreSQLErrorTranslator creates a new PostgreSQL error translator
func NewPostgreSQLErrorTranslator() *PostgreSQLErrorTranslator {
	return &PostgreSQLErrorTranslator{}
}

func (t *PostgreSQLErrorTranslator) TranslateError(ctx context.Context, err error, resource string) error {
	if err == nil {
		return nil
	}

	// Handle no rows found
	if err == pgx.ErrNoRows {
		return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeNotFound, resource+" not found").WithCause(err)
	}

	// Handle connection and network errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		if pgErr.Code == "08006" || pgErr.Code == "08001" {
			return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeServiceUnavailable, "database connection unavailable").WithCause(err)
		}
	} else if strings.Contains(err.Error(), "connection") {
		return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeServiceUnavailable, "database connection unavailable").WithCause(err)
	}

	// Handle PostgreSQL specific errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		switch pgErr.Code {
		case "23505": // Unique violation
			return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeAlreadyExists, resource+" already exists").
				WithCause(err).
				WithMetadata("constraint_detail", pgErr.Detail)
		case "23503": // Foreign key violation
			code := apperrors.ErrorCodeConflict
			if strings.Contains(pgErr.Message, "user_id") {
				code = apperrors.ErrorCodeUserNotFound
			}
			return apperrors.NewAppErrorFromContext(ctx, code,
				fmt.Sprintf("foreign key constraint violation for %s", resource)).
				WithCause(err).
				WithMetadata("constraint_detail", pgErr.Detail)
		case "23514": // Check constraint violation
			return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeInvalidInput,
				fmt.Sprintf("check constraint violation for %s", resource)).
				WithCause(err).
				WithMetadata("constraint_detail", pgErr.Detail)
		case "23502": // Not null constraint violation
			return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeMissingField,
				fmt.Sprintf("required field cannot be null for %s", resource)).
				WithCause(err).
				WithMetadata("column_name", pgErr.ColumnName)
		case "P0001": // Custom trigger error
			return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeForbidden,
				fmt.Sprintf("operation forbidden for %s: %s", resource, pgErr.Message)).
				WithCause(err)
		case "55P03": // Lock not available (timeout)
			return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeResourceExhausted, "database lock timeout").WithCause(err)
		case "XX000": // Internal error (could be serialization)
			return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeInvalidInput, "invalid data serialization").WithCause(err)
		default:
			return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeDatabaseError,
				fmt.Sprintf("database operation failed for %s", resource)).
				WithCause(err).
				WithMetadata("pg_error_code", pgErr.Code).
				WithMetadata("pg_error_message", pgErr.Message)
		}
	}

	// Return generic database error for everything else
	return apperrors.NewAppErrorFromContext(ctx, apperrors.ErrorCodeDatabaseError,
		fmt.Sprintf("database operation failed for %s", resource)).
		WithCause(err)
}

// HandlePgxError provides backward compatibility - translates PostgreSQL errors into structured error types
// Deprecated: Use PostgreSQLErrorTranslator.TranslateError instead
func HandlePgxError(err error, resourceName string) error {
	if err == nil {
		return nil
	}

	// Handle no rows found
	if err == pgx.ErrNoRows {
		return apperrors.NewNotFoundError(resourceName, "record not found")
	}

	// Handle PostgreSQL specific errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		switch pgErr.Code {
		case "23505": // Unique violation
			return apperrors.NewDuplicateError(resourceName, pgErr.Detail)
		case "23503": // Foreign key violation
			return apperrors.NewConflictError(resourceName, fmt.Sprintf("foreign key constraint violation: %s", pgErr.Detail))
		case "23514": // Check constraint violation
			return apperrors.NewValidationError("", fmt.Sprintf("check constraint violation: %s", pgErr.Detail))
		case "23502": // Not null constraint violation
			return apperrors.NewValidationError(pgErr.ColumnName, "cannot be null")
		case "P0001": // Custom trigger error
			return apperrors.NewForbiddenError(resourceName, pgErr.Message)
		default:
			return fmt.Errorf("database operation failed on %s: %w", resourceName, err)
		}
	}

	// Return generic error for everything else
	return fmt.Errorf("database operation failed on %s: %w", resourceName, err)
}

// HandlePgxErrorWithContext provides additional context for error handling
// Deprecated: Use PostgreSQLErrorTranslator.TranslateError instead
func HandlePgxErrorWithContext(err error, resourceName, operation string) error {
	if err == nil {
		return nil
	}

	wrappedErr := HandlePgxError(err, resourceName)

	// Add operation context
	switch e := wrappedErr.(type) {
	case *apperrors.NotFoundError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *apperrors.DuplicateError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *apperrors.ValidationError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *apperrors.ConflictError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *apperrors.ForbiddenError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	case *apperrors.UnauthorizedError:
		e.Message = fmt.Sprintf("%s during %s", e.Message, operation)
	}

	return wrappedErr
}
