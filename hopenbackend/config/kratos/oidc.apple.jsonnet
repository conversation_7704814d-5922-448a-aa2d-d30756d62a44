local claims = {
  email_verified: false,
} + std.extVar('claims');

// Helper function to generate username from name or email
// Note: <PERSON><PERSON><PERSON> will handle username uniqueness validation and conflicts
local generateUsername(firstName, lastName, email) =
  local baseName = if firstName != null && lastName != null then
    std.asciiLower(std.strReplace(std.strReplace(firstName + lastName, ' ', ''), '-', ''))
  else if email != null then
    std.asciiLower(std.split(email, '@')[0])
  else
    'user';

  // Remove non-alphanumeric characters and limit length
  local cleanName = std.substr(
    std.join('', std.filter(function(c)
      (std.codepoint(c) >= 48 && std.codepoint(c) <= 57) ||
      (std.codepoint(c) >= 97 && std.codepoint(c) <= 122),
      std.stringChars(baseName))),
    0,
    20
  );

  // Ensure minimum length and valid format
  if std.length(cleanName) >= 3 then
    cleanName
  else
    'user' + std.toString(std.floor(std.random() * 10000));

// Extract name from Apple's complex name object
local extractFirstName(name) = 
  if name != null then
    if std.objectHas(name, 'firstName') && name.firstName != null then
      name.firstName
    else if std.objectHas(name, 'givenName') && name.givenName != null then
      name.givenName
    else null
  else null;

local extractLastName(name) = 
  if name != null then
    if std.objectHas(name, 'lastName') && name.lastName != null then
      name.lastName
    else if std.objectHas(name, 'familyName') && name.familyName != null then
      name.familyName
    else null
  else null;

// Apple doesn't provide profile pictures, but we set oauth_provider for tracking
{
  identity: {
    traits: {
      email: if claims.email_verified then claims.email else null,
      username: generateUsername(extractFirstName(claims.name), extractLastName(claims.name), claims.email),
      first_name: extractFirstName(claims.name),
      last_name: extractLastName(claims.name),
      profile_picture: null,  // Apple doesn't provide profile pictures
      birthday: null,  // Apple doesn't provide birthday in standard claims
      oauth_provider: 'apple',
    },
  },
}
