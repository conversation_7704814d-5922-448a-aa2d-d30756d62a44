local claims = std.extVar('claims');

// Helper function to generate username from name or email
// Note: <PERSON><PERSON><PERSON> will handle username uniqueness validation and conflicts
local generateUsername(firstName, lastName, email) =
  local baseName = if firstName != null && lastName != null then
    std.asciiLower(std.strReplace(std.strReplace(firstName + lastName, ' ', ''), '-', ''))
  else if email != null then
    std.asciiLower(std.split(email, '@')[0])
  else
    'user';

  // Remove non-alphanumeric characters and limit length
  local cleanName = std.substr(
    std.join('', std.filter(function(c)
      (std.codepoint(c) >= 48 && std.codepoint(c) <= 57) ||
      (std.codepoint(c) >= 97 && std.codepoint(c) <= 122),
      std.stringChars(baseName))),
    0,
    20
  );

  // Ensure minimum length and valid format
  if std.length(cleanName) >= 3 then
    cleanName
  else
    'user' + std.toString(std.floor(std.random() * 10000));

// Helper function to format birthday from Google's format
local formatBirthday(birthday) = 
  if birthday != null && std.objectHas(birthday, 'year') && std.objectHas(birthday, 'month') && std.objectHas(birthday, 'day') then
    std.toString(birthday.year) + '-' + 
    (if birthday.month < 10 then '0' else '') + std.toString(birthday.month) + '-' +
    (if birthday.day < 10 then '0' else '') + std.toString(birthday.day)
  else null;

// Extract profile picture URL
local getProfilePicture(picture) = 
  if picture != null && std.isString(picture) then
    // Google profile pictures often have size parameters, get high-res version
    if std.findSubstr('=s', picture) != [] then
      std.split(picture, '=s')[0] + '=s1440'  // 1440x1440 for high quality
    else
      picture
  else null;

{
  identity: {
    traits: {
      email: claims.email,
      username: generateUsername(claims.given_name, claims.family_name, claims.email),
      first_name: claims.given_name,
      last_name: claims.family_name,
      profile_picture: getProfilePicture(claims.picture),
      birthday: formatBirthday(claims.birthday),
      oauth_provider: 'google',
    },
  },
}
