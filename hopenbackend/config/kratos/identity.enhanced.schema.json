{"$id": "https://schemas.hopen.app/identity.enhanced.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "Hopen Enhanced Identity Schema", "type": "object", "properties": {"traits": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User's email address", "ory.sh/kratos": {"credentials": {"password": {"identifier": true}}, "verification": {"via": "email"}}}, "username": {"type": "string", "minLength": 3, "maxLength": 64, "pattern": "^[a-zA-Z0-9_]+$", "title": "Username", "description": "Unique username for the user", "ory.sh/kratos": {"credentials": {"password": {"identifier": true}}}}, "first_name": {"type": "string", "minLength": 1, "maxLength": 100, "title": "First Name", "description": "User's first name"}, "last_name": {"type": "string", "minLength": 1, "maxLength": 100, "title": "Last Name", "description": "User's last name"}, "profile_picture": {"type": "string", "format": "uri", "title": "Profile Picture", "description": "URL to user's profile picture"}, "birthday": {"type": "string", "format": "date", "title": "Birthday", "description": "User's birthday in YYYY-MM-DD format"}, "oauth_provider": {"type": "string", "enum": ["google", "apple", "password"], "title": "OAuth Provider", "description": "The OAuth provider used for registration"}}, "required": ["email"], "additionalProperties": false}}, "required": ["traits"], "additionalProperties": false}