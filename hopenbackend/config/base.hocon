# EMQX v5.8 Base Configuration for Hopen Backend
# Runtime-configurable settings (can be overridden via Dashboard/API)

node {
  name = "emqx@emqx"
  cookie = "hopen-emqx-secure-cookie-2024"
  data_dir = "/opt/emqx/data"
}

# Dashboard configuration
dashboard {
  default_username = "admin"
  default_password = "hopen123"
  listeners {
    http {
      bind = 18083
    }
  }
}

# MQTT Listeners
listeners {
  tcp {
    default {
      bind = "0.0.0.0:1883"
      max_connections = 10000
      max_conn_rate = 1000
    }
  }
}

# Cluster configuration (single node for development)
cluster {
  name = "hopen_cluster"
  discovery_strategy = "static"
  static {
    seeds = ["emqx@emqx"]
  }
}

# ExHook Configuration - gRPC integration with auth service
exhook {
  servers = [
    {
      name = "hopen_auth_hook"
      url = "http://auth:4000"
      request_timeout = "5s"
      failed_action = "deny"
      ssl {
        enable = false
      }
      auto_reconnect = "60s"
      pool_size = 8
    }
  ]
}

# MQTT Protocol Configuration
mqtt {
  max_packet_size = "1MB"
  max_clientid_len = 65535
  max_topic_levels = 128
  max_qos_allowed = 2
  max_topic_alias = 65535
  retain_available = true
  wildcard_subscription = true
  shared_subscription = true
  exclusive_subscription = false
  max_subscriptions = 10
  upgrade_qos = false
  max_inflight = 32
  retry_interval = "30s"
  max_awaiting_rel = 100
  await_rel_timeout = "300s"
  session_expiry_interval = "2h"
  max_mqueue_len = 1000
  mqueue_priorities = "disabled"
  mqueue_default_priority = "lowest"
  mqueue_store_qos0 = true
  use_username_as_clientid = false
  peer_cert_as_username = "disabled"
  peer_cert_as_clientid = "disabled"
  keepalive_multiplier = 1.5
}

# Session Management (optimized for mobile clients)
session {
  max_subscriptions = 10
  upgrade_qos = false
  max_inflight = 32
  retry_interval = "30s"
  max_awaiting_rel = 100
  await_rel_timeout = "300s"
  session_expiry_interval = "2h"
  max_mqueue_len = 1000
  mqueue_priorities = "disabled"
  mqueue_default_priority = "lowest"
  mqueue_store_qos0 = true
}

# Basic logging
log {
  console_handler {
    enable = true
    level = "info"
    formatter = "text"
  }
}

# Zone configuration for mobile optimization
zones {
  default {
    mqtt {
      max_packet_size = "1MB"
      max_clientid_len = 65535
      max_topic_levels = 128
      max_qos_allowed = 2
      max_topic_alias = 65535
      retain_available = true
      wildcard_subscription = true
      shared_subscription = true
      exclusive_subscription = false
      max_subscriptions = 10
      upgrade_qos = false
      max_inflight = 32
      retry_interval = "30s"
      max_awaiting_rel = 100
      await_rel_timeout = "300s"
      session_expiry_interval = "2h"
      max_mqueue_len = 1000
      mqueue_priorities = "disabled"
      mqueue_default_priority = "lowest"
      mqueue_store_qos0 = true
      use_username_as_clientid = false
      peer_cert_as_username = "disabled"
      peer_cert_as_clientid = "disabled"
      keepalive_multiplier = 1.5
    }
  }
}
