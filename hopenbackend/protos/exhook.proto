syntax = "proto3";

package emqx.exhook.v2;

option go_package = "hopenbackend/protos/gen;gen";

import "google/protobuf/empty.proto";

// ExHook service for EMQX integration
service HookProvider {
  // Called when the provider is loaded
  rpc OnProviderLoaded(ProviderLoadedRequest) returns (LoadedResponse);

  // Called when a client connects to EMQX
  rpc OnClientConnect(ClientConnectRequest) returns (LoadedResponse);
  
  // Called when a client authenticates
  rpc OnClientAuthenticate(ClientAuthenticateRequest) returns (LoadedResponse);
  
  // Called when a client disconnects
  rpc OnClientDisconnect(ClientDisconnectRequest) returns (EmptySuccess);
  
  // Called when a client subscribes to a topic
  rpc OnClientSubscribe(ClientSubscribeRequest) returns (LoadedResponse);
  
  // Called when a client unsubscribes from a topic
  rpc OnClientUnsubscribe(ClientUnsubscribeRequest) returns (EmptySuccess);
  
  // Called when a message is published
  rpc OnMessagePublish(MessagePublishRequest) returns (LoadedResponse);
  
  // Called when a message is delivered
  rpc OnMessageDelivered(MessageDeliveredRequest) returns (EmptySuccess);
  
  // Called when a message is acknowledged
  rpc OnMessageAcked(MessageAckedRequest) returns (EmptySuccess);
  
  // Called when a message is dropped
  rpc OnMessageDropped(MessageDroppedRequest) returns (EmptySuccess);
}

// Client information
message ClientInfo {
  string node = 1;
  string clientid = 2;
  string username = 3;
  string password = 4;
  string peerhost = 5;
  uint32 peerport = 6;
  string sockhost = 7;
  uint32 sockport = 8;
  string protocol = 9;
  string mountpoint = 10;
  bool is_superuser = 11;
  bool anonymous = 12;
  string cn = 13;
  string dn = 14;
  uint64 connected_at = 15;
  uint32 keepalive = 16;
  uint32 expiry_interval = 17;
}

// EmqxMessage information
message EmqxMessage {
  string id = 1;
  uint32 qos = 2;
  string from = 3;
  string topic = 4;
  bytes payload = 5;
  uint64 timestamp = 6;
  map<string, string> headers = 7;
}

// Property information
message Property {
  string name = 1;
  string value = 2;
}

// Topic filter
message TopicFilter {
  string name = 1;
  uint32 qos = 2;
}

// Request messages
message ClientConnectRequest {
  ClientInfo conninfo = 1;
  repeated Property props = 2;
}

message ClientAuthenticateRequest {
  ClientInfo clientinfo = 1;
  bool result = 2;
}

message ClientDisconnectRequest {
  ClientInfo clientinfo = 1;
  string reason = 2;
}

message ClientSubscribeRequest {
  ClientInfo clientinfo = 1;
  repeated Property props = 2;
  repeated TopicFilter topicfilters = 3;
}

message ClientUnsubscribeRequest {
  ClientInfo clientinfo = 1;
  repeated Property props = 2;
  repeated TopicFilter topicfilters = 3;
}

message MessagePublishRequest {
  EmqxMessage message = 1;
  ClientInfo clientinfo = 2;
}

message MessageDeliveredRequest {
  EmqxMessage message = 1;
  ClientInfo clientinfo = 2;
}

message MessageAckedRequest {
  EmqxMessage message = 1;
  ClientInfo clientinfo = 2;
}

message MessageDroppedRequest {
  EmqxMessage message = 1;
  ClientInfo clientinfo = 2;
  string reason = 3;
}

// Response messages
message LoadedResponse {
  ResponsedType type = 1;
  
  enum ResponsedType {
    CONTINUE = 0;
    IGNORE = 1;
    STOP_AND_RETURN = 2;
  }
}

message EmptySuccess {
  // Empty response for successful operations
}

// Provider loaded request
message ProviderLoadedRequest {
  BrokerInfo broker = 1;
  MetaInfo meta = 2;
}

// Broker information
message BrokerInfo {
  string version = 1;
  string sysdescr = 2;
  string uptime = 3;
  string datetime = 4;
}

// Meta information
message MetaInfo {
  string node = 1;
  string version = 2;
  string cluster_name = 3;
  string sysdescr = 4;
}
