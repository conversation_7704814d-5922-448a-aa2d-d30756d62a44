// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.0
// source: exhook.proto

package gen

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	HookProvider_OnProviderLoaded_FullMethodName     = "/emqx.exhook.v2.HookProvider/OnProviderLoaded"
	HookProvider_OnClientConnect_FullMethodName      = "/emqx.exhook.v2.HookProvider/OnClientConnect"
	HookProvider_OnClientAuthenticate_FullMethodName = "/emqx.exhook.v2.HookProvider/OnClientAuthenticate"
	HookProvider_OnClientDisconnect_FullMethodName   = "/emqx.exhook.v2.HookProvider/OnClientDisconnect"
	HookProvider_OnClientSubscribe_FullMethodName    = "/emqx.exhook.v2.HookProvider/OnClientSubscribe"
	HookProvider_OnClientUnsubscribe_FullMethodName  = "/emqx.exhook.v2.HookProvider/OnClientUnsubscribe"
	HookProvider_OnMessagePublish_FullMethodName     = "/emqx.exhook.v2.HookProvider/OnMessagePublish"
	HookProvider_OnMessageDelivered_FullMethodName   = "/emqx.exhook.v2.HookProvider/OnMessageDelivered"
	HookProvider_OnMessageAcked_FullMethodName       = "/emqx.exhook.v2.HookProvider/OnMessageAcked"
	HookProvider_OnMessageDropped_FullMethodName     = "/emqx.exhook.v2.HookProvider/OnMessageDropped"
)

// HookProviderClient is the client API for HookProvider service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ExHook service for EMQX integration
type HookProviderClient interface {
	// Called when the provider is loaded
	OnProviderLoaded(ctx context.Context, in *ProviderLoadedRequest, opts ...grpc.CallOption) (*LoadedResponse, error)
	// Called when a client connects to EMQX
	OnClientConnect(ctx context.Context, in *ClientConnectRequest, opts ...grpc.CallOption) (*LoadedResponse, error)
	// Called when a client authenticates
	OnClientAuthenticate(ctx context.Context, in *ClientAuthenticateRequest, opts ...grpc.CallOption) (*LoadedResponse, error)
	// Called when a client disconnects
	OnClientDisconnect(ctx context.Context, in *ClientDisconnectRequest, opts ...grpc.CallOption) (*EmptySuccess, error)
	// Called when a client subscribes to a topic
	OnClientSubscribe(ctx context.Context, in *ClientSubscribeRequest, opts ...grpc.CallOption) (*LoadedResponse, error)
	// Called when a client unsubscribes from a topic
	OnClientUnsubscribe(ctx context.Context, in *ClientUnsubscribeRequest, opts ...grpc.CallOption) (*EmptySuccess, error)
	// Called when a message is published
	OnMessagePublish(ctx context.Context, in *MessagePublishRequest, opts ...grpc.CallOption) (*LoadedResponse, error)
	// Called when a message is delivered
	OnMessageDelivered(ctx context.Context, in *MessageDeliveredRequest, opts ...grpc.CallOption) (*EmptySuccess, error)
	// Called when a message is acknowledged
	OnMessageAcked(ctx context.Context, in *MessageAckedRequest, opts ...grpc.CallOption) (*EmptySuccess, error)
	// Called when a message is dropped
	OnMessageDropped(ctx context.Context, in *MessageDroppedRequest, opts ...grpc.CallOption) (*EmptySuccess, error)
}

type hookProviderClient struct {
	cc grpc.ClientConnInterface
}

func NewHookProviderClient(cc grpc.ClientConnInterface) HookProviderClient {
	return &hookProviderClient{cc}
}

func (c *hookProviderClient) OnProviderLoaded(ctx context.Context, in *ProviderLoadedRequest, opts ...grpc.CallOption) (*LoadedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoadedResponse)
	err := c.cc.Invoke(ctx, HookProvider_OnProviderLoaded_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnClientConnect(ctx context.Context, in *ClientConnectRequest, opts ...grpc.CallOption) (*LoadedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoadedResponse)
	err := c.cc.Invoke(ctx, HookProvider_OnClientConnect_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnClientAuthenticate(ctx context.Context, in *ClientAuthenticateRequest, opts ...grpc.CallOption) (*LoadedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoadedResponse)
	err := c.cc.Invoke(ctx, HookProvider_OnClientAuthenticate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnClientDisconnect(ctx context.Context, in *ClientDisconnectRequest, opts ...grpc.CallOption) (*EmptySuccess, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptySuccess)
	err := c.cc.Invoke(ctx, HookProvider_OnClientDisconnect_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnClientSubscribe(ctx context.Context, in *ClientSubscribeRequest, opts ...grpc.CallOption) (*LoadedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoadedResponse)
	err := c.cc.Invoke(ctx, HookProvider_OnClientSubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnClientUnsubscribe(ctx context.Context, in *ClientUnsubscribeRequest, opts ...grpc.CallOption) (*EmptySuccess, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptySuccess)
	err := c.cc.Invoke(ctx, HookProvider_OnClientUnsubscribe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnMessagePublish(ctx context.Context, in *MessagePublishRequest, opts ...grpc.CallOption) (*LoadedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoadedResponse)
	err := c.cc.Invoke(ctx, HookProvider_OnMessagePublish_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnMessageDelivered(ctx context.Context, in *MessageDeliveredRequest, opts ...grpc.CallOption) (*EmptySuccess, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptySuccess)
	err := c.cc.Invoke(ctx, HookProvider_OnMessageDelivered_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnMessageAcked(ctx context.Context, in *MessageAckedRequest, opts ...grpc.CallOption) (*EmptySuccess, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptySuccess)
	err := c.cc.Invoke(ctx, HookProvider_OnMessageAcked_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookProviderClient) OnMessageDropped(ctx context.Context, in *MessageDroppedRequest, opts ...grpc.CallOption) (*EmptySuccess, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptySuccess)
	err := c.cc.Invoke(ctx, HookProvider_OnMessageDropped_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HookProviderServer is the server API for HookProvider service.
// All implementations must embed UnimplementedHookProviderServer
// for forward compatibility.
//
// ExHook service for EMQX integration
type HookProviderServer interface {
	// Called when the provider is loaded
	OnProviderLoaded(context.Context, *ProviderLoadedRequest) (*LoadedResponse, error)
	// Called when a client connects to EMQX
	OnClientConnect(context.Context, *ClientConnectRequest) (*LoadedResponse, error)
	// Called when a client authenticates
	OnClientAuthenticate(context.Context, *ClientAuthenticateRequest) (*LoadedResponse, error)
	// Called when a client disconnects
	OnClientDisconnect(context.Context, *ClientDisconnectRequest) (*EmptySuccess, error)
	// Called when a client subscribes to a topic
	OnClientSubscribe(context.Context, *ClientSubscribeRequest) (*LoadedResponse, error)
	// Called when a client unsubscribes from a topic
	OnClientUnsubscribe(context.Context, *ClientUnsubscribeRequest) (*EmptySuccess, error)
	// Called when a message is published
	OnMessagePublish(context.Context, *MessagePublishRequest) (*LoadedResponse, error)
	// Called when a message is delivered
	OnMessageDelivered(context.Context, *MessageDeliveredRequest) (*EmptySuccess, error)
	// Called when a message is acknowledged
	OnMessageAcked(context.Context, *MessageAckedRequest) (*EmptySuccess, error)
	// Called when a message is dropped
	OnMessageDropped(context.Context, *MessageDroppedRequest) (*EmptySuccess, error)
	mustEmbedUnimplementedHookProviderServer()
}

// UnimplementedHookProviderServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedHookProviderServer struct{}

func (UnimplementedHookProviderServer) OnProviderLoaded(context.Context, *ProviderLoadedRequest) (*LoadedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnProviderLoaded not implemented")
}
func (UnimplementedHookProviderServer) OnClientConnect(context.Context, *ClientConnectRequest) (*LoadedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnClientConnect not implemented")
}
func (UnimplementedHookProviderServer) OnClientAuthenticate(context.Context, *ClientAuthenticateRequest) (*LoadedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnClientAuthenticate not implemented")
}
func (UnimplementedHookProviderServer) OnClientDisconnect(context.Context, *ClientDisconnectRequest) (*EmptySuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnClientDisconnect not implemented")
}
func (UnimplementedHookProviderServer) OnClientSubscribe(context.Context, *ClientSubscribeRequest) (*LoadedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnClientSubscribe not implemented")
}
func (UnimplementedHookProviderServer) OnClientUnsubscribe(context.Context, *ClientUnsubscribeRequest) (*EmptySuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnClientUnsubscribe not implemented")
}
func (UnimplementedHookProviderServer) OnMessagePublish(context.Context, *MessagePublishRequest) (*LoadedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnMessagePublish not implemented")
}
func (UnimplementedHookProviderServer) OnMessageDelivered(context.Context, *MessageDeliveredRequest) (*EmptySuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnMessageDelivered not implemented")
}
func (UnimplementedHookProviderServer) OnMessageAcked(context.Context, *MessageAckedRequest) (*EmptySuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnMessageAcked not implemented")
}
func (UnimplementedHookProviderServer) OnMessageDropped(context.Context, *MessageDroppedRequest) (*EmptySuccess, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnMessageDropped not implemented")
}
func (UnimplementedHookProviderServer) mustEmbedUnimplementedHookProviderServer() {}
func (UnimplementedHookProviderServer) testEmbeddedByValue()                      {}

// UnsafeHookProviderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HookProviderServer will
// result in compilation errors.
type UnsafeHookProviderServer interface {
	mustEmbedUnimplementedHookProviderServer()
}

func RegisterHookProviderServer(s grpc.ServiceRegistrar, srv HookProviderServer) {
	// If the following call pancis, it indicates UnimplementedHookProviderServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&HookProvider_ServiceDesc, srv)
}

func _HookProvider_OnProviderLoaded_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProviderLoadedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnProviderLoaded(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnProviderLoaded_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnProviderLoaded(ctx, req.(*ProviderLoadedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnClientConnect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientConnectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnClientConnect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnClientConnect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnClientConnect(ctx, req.(*ClientConnectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnClientAuthenticate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientAuthenticateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnClientAuthenticate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnClientAuthenticate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnClientAuthenticate(ctx, req.(*ClientAuthenticateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnClientDisconnect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientDisconnectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnClientDisconnect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnClientDisconnect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnClientDisconnect(ctx, req.(*ClientDisconnectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnClientSubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientSubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnClientSubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnClientSubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnClientSubscribe(ctx, req.(*ClientSubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnClientUnsubscribe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClientUnsubscribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnClientUnsubscribe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnClientUnsubscribe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnClientUnsubscribe(ctx, req.(*ClientUnsubscribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnMessagePublish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessagePublishRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnMessagePublish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnMessagePublish_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnMessagePublish(ctx, req.(*MessagePublishRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnMessageDelivered_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageDeliveredRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnMessageDelivered(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnMessageDelivered_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnMessageDelivered(ctx, req.(*MessageDeliveredRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnMessageAcked_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageAckedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnMessageAcked(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnMessageAcked_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnMessageAcked(ctx, req.(*MessageAckedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookProvider_OnMessageDropped_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageDroppedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookProviderServer).OnMessageDropped(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookProvider_OnMessageDropped_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookProviderServer).OnMessageDropped(ctx, req.(*MessageDroppedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HookProvider_ServiceDesc is the grpc.ServiceDesc for HookProvider service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HookProvider_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "emqx.exhook.v2.HookProvider",
	HandlerType: (*HookProviderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OnProviderLoaded",
			Handler:    _HookProvider_OnProviderLoaded_Handler,
		},
		{
			MethodName: "OnClientConnect",
			Handler:    _HookProvider_OnClientConnect_Handler,
		},
		{
			MethodName: "OnClientAuthenticate",
			Handler:    _HookProvider_OnClientAuthenticate_Handler,
		},
		{
			MethodName: "OnClientDisconnect",
			Handler:    _HookProvider_OnClientDisconnect_Handler,
		},
		{
			MethodName: "OnClientSubscribe",
			Handler:    _HookProvider_OnClientSubscribe_Handler,
		},
		{
			MethodName: "OnClientUnsubscribe",
			Handler:    _HookProvider_OnClientUnsubscribe_Handler,
		},
		{
			MethodName: "OnMessagePublish",
			Handler:    _HookProvider_OnMessagePublish_Handler,
		},
		{
			MethodName: "OnMessageDelivered",
			Handler:    _HookProvider_OnMessageDelivered_Handler,
		},
		{
			MethodName: "OnMessageAcked",
			Handler:    _HookProvider_OnMessageAcked_Handler,
		},
		{
			MethodName: "OnMessageDropped",
			Handler:    _HookProvider_OnMessageDropped_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "exhook.proto",
}
