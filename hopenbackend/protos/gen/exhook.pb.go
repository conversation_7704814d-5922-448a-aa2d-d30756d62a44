// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.0
// source: exhook.proto

package gen

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoadedResponse_ResponsedType int32

const (
	LoadedResponse_CONTINUE        LoadedResponse_ResponsedType = 0
	LoadedResponse_IGNORE          LoadedResponse_ResponsedType = 1
	LoadedResponse_STOP_AND_RETURN LoadedResponse_ResponsedType = 2
)

// Enum value maps for LoadedResponse_ResponsedType.
var (
	LoadedResponse_ResponsedType_name = map[int32]string{
		0: "CONTINUE",
		1: "IGNORE",
		2: "STOP_AND_RETURN",
	}
	LoadedResponse_ResponsedType_value = map[string]int32{
		"CONTINUE":        0,
		"IGNORE":          1,
		"STOP_AND_RETURN": 2,
	}
)

func (x LoadedResponse_ResponsedType) Enum() *LoadedResponse_ResponsedType {
	p := new(LoadedResponse_ResponsedType)
	*p = x
	return p
}

func (x LoadedResponse_ResponsedType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoadedResponse_ResponsedType) Descriptor() protoreflect.EnumDescriptor {
	return file_exhook_proto_enumTypes[0].Descriptor()
}

func (LoadedResponse_ResponsedType) Type() protoreflect.EnumType {
	return &file_exhook_proto_enumTypes[0]
}

func (x LoadedResponse_ResponsedType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoadedResponse_ResponsedType.Descriptor instead.
func (LoadedResponse_ResponsedType) EnumDescriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{13, 0}
}

// Client information
type ClientInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Node           string                 `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	Clientid       string                 `protobuf:"bytes,2,opt,name=clientid,proto3" json:"clientid,omitempty"`
	Username       string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password       string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	Peerhost       string                 `protobuf:"bytes,5,opt,name=peerhost,proto3" json:"peerhost,omitempty"`
	Peerport       uint32                 `protobuf:"varint,6,opt,name=peerport,proto3" json:"peerport,omitempty"`
	Sockhost       string                 `protobuf:"bytes,7,opt,name=sockhost,proto3" json:"sockhost,omitempty"`
	Sockport       uint32                 `protobuf:"varint,8,opt,name=sockport,proto3" json:"sockport,omitempty"`
	Protocol       string                 `protobuf:"bytes,9,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Mountpoint     string                 `protobuf:"bytes,10,opt,name=mountpoint,proto3" json:"mountpoint,omitempty"`
	IsSuperuser    bool                   `protobuf:"varint,11,opt,name=is_superuser,json=isSuperuser,proto3" json:"is_superuser,omitempty"`
	Anonymous      bool                   `protobuf:"varint,12,opt,name=anonymous,proto3" json:"anonymous,omitempty"`
	Cn             string                 `protobuf:"bytes,13,opt,name=cn,proto3" json:"cn,omitempty"`
	Dn             string                 `protobuf:"bytes,14,opt,name=dn,proto3" json:"dn,omitempty"`
	ConnectedAt    uint64                 `protobuf:"varint,15,opt,name=connected_at,json=connectedAt,proto3" json:"connected_at,omitempty"`
	Keepalive      uint32                 `protobuf:"varint,16,opt,name=keepalive,proto3" json:"keepalive,omitempty"`
	ExpiryInterval uint32                 `protobuf:"varint,17,opt,name=expiry_interval,json=expiryInterval,proto3" json:"expiry_interval,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ClientInfo) Reset() {
	*x = ClientInfo{}
	mi := &file_exhook_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientInfo) ProtoMessage() {}

func (x *ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientInfo.ProtoReflect.Descriptor instead.
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{0}
}

func (x *ClientInfo) GetNode() string {
	if x != nil {
		return x.Node
	}
	return ""
}

func (x *ClientInfo) GetClientid() string {
	if x != nil {
		return x.Clientid
	}
	return ""
}

func (x *ClientInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ClientInfo) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ClientInfo) GetPeerhost() string {
	if x != nil {
		return x.Peerhost
	}
	return ""
}

func (x *ClientInfo) GetPeerport() uint32 {
	if x != nil {
		return x.Peerport
	}
	return 0
}

func (x *ClientInfo) GetSockhost() string {
	if x != nil {
		return x.Sockhost
	}
	return ""
}

func (x *ClientInfo) GetSockport() uint32 {
	if x != nil {
		return x.Sockport
	}
	return 0
}

func (x *ClientInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *ClientInfo) GetMountpoint() string {
	if x != nil {
		return x.Mountpoint
	}
	return ""
}

func (x *ClientInfo) GetIsSuperuser() bool {
	if x != nil {
		return x.IsSuperuser
	}
	return false
}

func (x *ClientInfo) GetAnonymous() bool {
	if x != nil {
		return x.Anonymous
	}
	return false
}

func (x *ClientInfo) GetCn() string {
	if x != nil {
		return x.Cn
	}
	return ""
}

func (x *ClientInfo) GetDn() string {
	if x != nil {
		return x.Dn
	}
	return ""
}

func (x *ClientInfo) GetConnectedAt() uint64 {
	if x != nil {
		return x.ConnectedAt
	}
	return 0
}

func (x *ClientInfo) GetKeepalive() uint32 {
	if x != nil {
		return x.Keepalive
	}
	return 0
}

func (x *ClientInfo) GetExpiryInterval() uint32 {
	if x != nil {
		return x.ExpiryInterval
	}
	return 0
}

// EmqxMessage information
type EmqxMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Qos           uint32                 `protobuf:"varint,2,opt,name=qos,proto3" json:"qos,omitempty"`
	From          string                 `protobuf:"bytes,3,opt,name=from,proto3" json:"from,omitempty"`
	Topic         string                 `protobuf:"bytes,4,opt,name=topic,proto3" json:"topic,omitempty"`
	Payload       []byte                 `protobuf:"bytes,5,opt,name=payload,proto3" json:"payload,omitempty"`
	Timestamp     uint64                 `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Headers       map[string]string      `protobuf:"bytes,7,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmqxMessage) Reset() {
	*x = EmqxMessage{}
	mi := &file_exhook_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmqxMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmqxMessage) ProtoMessage() {}

func (x *EmqxMessage) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmqxMessage.ProtoReflect.Descriptor instead.
func (*EmqxMessage) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{1}
}

func (x *EmqxMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EmqxMessage) GetQos() uint32 {
	if x != nil {
		return x.Qos
	}
	return 0
}

func (x *EmqxMessage) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *EmqxMessage) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *EmqxMessage) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *EmqxMessage) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *EmqxMessage) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

// Property information
type Property struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Property) Reset() {
	*x = Property{}
	mi := &file_exhook_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Property) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Property) ProtoMessage() {}

func (x *Property) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Property.ProtoReflect.Descriptor instead.
func (*Property) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{2}
}

func (x *Property) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Property) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// Topic filter
type TopicFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Qos           uint32                 `protobuf:"varint,2,opt,name=qos,proto3" json:"qos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TopicFilter) Reset() {
	*x = TopicFilter{}
	mi := &file_exhook_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TopicFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicFilter) ProtoMessage() {}

func (x *TopicFilter) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicFilter.ProtoReflect.Descriptor instead.
func (*TopicFilter) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{3}
}

func (x *TopicFilter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TopicFilter) GetQos() uint32 {
	if x != nil {
		return x.Qos
	}
	return 0
}

// Request messages
type ClientConnectRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Conninfo      *ClientInfo            `protobuf:"bytes,1,opt,name=conninfo,proto3" json:"conninfo,omitempty"`
	Props         []*Property            `protobuf:"bytes,2,rep,name=props,proto3" json:"props,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientConnectRequest) Reset() {
	*x = ClientConnectRequest{}
	mi := &file_exhook_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientConnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConnectRequest) ProtoMessage() {}

func (x *ClientConnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConnectRequest.ProtoReflect.Descriptor instead.
func (*ClientConnectRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{4}
}

func (x *ClientConnectRequest) GetConninfo() *ClientInfo {
	if x != nil {
		return x.Conninfo
	}
	return nil
}

func (x *ClientConnectRequest) GetProps() []*Property {
	if x != nil {
		return x.Props
	}
	return nil
}

type ClientAuthenticateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Clientinfo    *ClientInfo            `protobuf:"bytes,1,opt,name=clientinfo,proto3" json:"clientinfo,omitempty"`
	Result        bool                   `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientAuthenticateRequest) Reset() {
	*x = ClientAuthenticateRequest{}
	mi := &file_exhook_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientAuthenticateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientAuthenticateRequest) ProtoMessage() {}

func (x *ClientAuthenticateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientAuthenticateRequest.ProtoReflect.Descriptor instead.
func (*ClientAuthenticateRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{5}
}

func (x *ClientAuthenticateRequest) GetClientinfo() *ClientInfo {
	if x != nil {
		return x.Clientinfo
	}
	return nil
}

func (x *ClientAuthenticateRequest) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

type ClientDisconnectRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Clientinfo    *ClientInfo            `protobuf:"bytes,1,opt,name=clientinfo,proto3" json:"clientinfo,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientDisconnectRequest) Reset() {
	*x = ClientDisconnectRequest{}
	mi := &file_exhook_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientDisconnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientDisconnectRequest) ProtoMessage() {}

func (x *ClientDisconnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientDisconnectRequest.ProtoReflect.Descriptor instead.
func (*ClientDisconnectRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{6}
}

func (x *ClientDisconnectRequest) GetClientinfo() *ClientInfo {
	if x != nil {
		return x.Clientinfo
	}
	return nil
}

func (x *ClientDisconnectRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ClientSubscribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Clientinfo    *ClientInfo            `protobuf:"bytes,1,opt,name=clientinfo,proto3" json:"clientinfo,omitempty"`
	Props         []*Property            `protobuf:"bytes,2,rep,name=props,proto3" json:"props,omitempty"`
	Topicfilters  []*TopicFilter         `protobuf:"bytes,3,rep,name=topicfilters,proto3" json:"topicfilters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientSubscribeRequest) Reset() {
	*x = ClientSubscribeRequest{}
	mi := &file_exhook_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientSubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientSubscribeRequest) ProtoMessage() {}

func (x *ClientSubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientSubscribeRequest.ProtoReflect.Descriptor instead.
func (*ClientSubscribeRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{7}
}

func (x *ClientSubscribeRequest) GetClientinfo() *ClientInfo {
	if x != nil {
		return x.Clientinfo
	}
	return nil
}

func (x *ClientSubscribeRequest) GetProps() []*Property {
	if x != nil {
		return x.Props
	}
	return nil
}

func (x *ClientSubscribeRequest) GetTopicfilters() []*TopicFilter {
	if x != nil {
		return x.Topicfilters
	}
	return nil
}

type ClientUnsubscribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Clientinfo    *ClientInfo            `protobuf:"bytes,1,opt,name=clientinfo,proto3" json:"clientinfo,omitempty"`
	Props         []*Property            `protobuf:"bytes,2,rep,name=props,proto3" json:"props,omitempty"`
	Topicfilters  []*TopicFilter         `protobuf:"bytes,3,rep,name=topicfilters,proto3" json:"topicfilters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientUnsubscribeRequest) Reset() {
	*x = ClientUnsubscribeRequest{}
	mi := &file_exhook_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientUnsubscribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientUnsubscribeRequest) ProtoMessage() {}

func (x *ClientUnsubscribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientUnsubscribeRequest.ProtoReflect.Descriptor instead.
func (*ClientUnsubscribeRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{8}
}

func (x *ClientUnsubscribeRequest) GetClientinfo() *ClientInfo {
	if x != nil {
		return x.Clientinfo
	}
	return nil
}

func (x *ClientUnsubscribeRequest) GetProps() []*Property {
	if x != nil {
		return x.Props
	}
	return nil
}

func (x *ClientUnsubscribeRequest) GetTopicfilters() []*TopicFilter {
	if x != nil {
		return x.Topicfilters
	}
	return nil
}

type MessagePublishRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *EmqxMessage           `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Clientinfo    *ClientInfo            `protobuf:"bytes,2,opt,name=clientinfo,proto3" json:"clientinfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessagePublishRequest) Reset() {
	*x = MessagePublishRequest{}
	mi := &file_exhook_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessagePublishRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessagePublishRequest) ProtoMessage() {}

func (x *MessagePublishRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessagePublishRequest.ProtoReflect.Descriptor instead.
func (*MessagePublishRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{9}
}

func (x *MessagePublishRequest) GetMessage() *EmqxMessage {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *MessagePublishRequest) GetClientinfo() *ClientInfo {
	if x != nil {
		return x.Clientinfo
	}
	return nil
}

type MessageDeliveredRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *EmqxMessage           `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Clientinfo    *ClientInfo            `protobuf:"bytes,2,opt,name=clientinfo,proto3" json:"clientinfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageDeliveredRequest) Reset() {
	*x = MessageDeliveredRequest{}
	mi := &file_exhook_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageDeliveredRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageDeliveredRequest) ProtoMessage() {}

func (x *MessageDeliveredRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageDeliveredRequest.ProtoReflect.Descriptor instead.
func (*MessageDeliveredRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{10}
}

func (x *MessageDeliveredRequest) GetMessage() *EmqxMessage {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *MessageDeliveredRequest) GetClientinfo() *ClientInfo {
	if x != nil {
		return x.Clientinfo
	}
	return nil
}

type MessageAckedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *EmqxMessage           `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Clientinfo    *ClientInfo            `protobuf:"bytes,2,opt,name=clientinfo,proto3" json:"clientinfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageAckedRequest) Reset() {
	*x = MessageAckedRequest{}
	mi := &file_exhook_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageAckedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageAckedRequest) ProtoMessage() {}

func (x *MessageAckedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageAckedRequest.ProtoReflect.Descriptor instead.
func (*MessageAckedRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{11}
}

func (x *MessageAckedRequest) GetMessage() *EmqxMessage {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *MessageAckedRequest) GetClientinfo() *ClientInfo {
	if x != nil {
		return x.Clientinfo
	}
	return nil
}

type MessageDroppedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *EmqxMessage           `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Clientinfo    *ClientInfo            `protobuf:"bytes,2,opt,name=clientinfo,proto3" json:"clientinfo,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageDroppedRequest) Reset() {
	*x = MessageDroppedRequest{}
	mi := &file_exhook_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageDroppedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageDroppedRequest) ProtoMessage() {}

func (x *MessageDroppedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageDroppedRequest.ProtoReflect.Descriptor instead.
func (*MessageDroppedRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{12}
}

func (x *MessageDroppedRequest) GetMessage() *EmqxMessage {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *MessageDroppedRequest) GetClientinfo() *ClientInfo {
	if x != nil {
		return x.Clientinfo
	}
	return nil
}

func (x *MessageDroppedRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Response messages
type LoadedResponse struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Type          LoadedResponse_ResponsedType `protobuf:"varint,1,opt,name=type,proto3,enum=emqx.exhook.v2.LoadedResponse_ResponsedType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadedResponse) Reset() {
	*x = LoadedResponse{}
	mi := &file_exhook_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadedResponse) ProtoMessage() {}

func (x *LoadedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadedResponse.ProtoReflect.Descriptor instead.
func (*LoadedResponse) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{13}
}

func (x *LoadedResponse) GetType() LoadedResponse_ResponsedType {
	if x != nil {
		return x.Type
	}
	return LoadedResponse_CONTINUE
}

type EmptySuccess struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptySuccess) Reset() {
	*x = EmptySuccess{}
	mi := &file_exhook_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptySuccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptySuccess) ProtoMessage() {}

func (x *EmptySuccess) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptySuccess.ProtoReflect.Descriptor instead.
func (*EmptySuccess) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{14}
}

// Provider loaded request
type ProviderLoadedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Broker        *BrokerInfo            `protobuf:"bytes,1,opt,name=broker,proto3" json:"broker,omitempty"`
	Meta          *MetaInfo              `protobuf:"bytes,2,opt,name=meta,proto3" json:"meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProviderLoadedRequest) Reset() {
	*x = ProviderLoadedRequest{}
	mi := &file_exhook_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProviderLoadedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProviderLoadedRequest) ProtoMessage() {}

func (x *ProviderLoadedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProviderLoadedRequest.ProtoReflect.Descriptor instead.
func (*ProviderLoadedRequest) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{15}
}

func (x *ProviderLoadedRequest) GetBroker() *BrokerInfo {
	if x != nil {
		return x.Broker
	}
	return nil
}

func (x *ProviderLoadedRequest) GetMeta() *MetaInfo {
	if x != nil {
		return x.Meta
	}
	return nil
}

// Broker information
type BrokerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       string                 `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Sysdescr      string                 `protobuf:"bytes,2,opt,name=sysdescr,proto3" json:"sysdescr,omitempty"`
	Uptime        string                 `protobuf:"bytes,3,opt,name=uptime,proto3" json:"uptime,omitempty"`
	Datetime      string                 `protobuf:"bytes,4,opt,name=datetime,proto3" json:"datetime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BrokerInfo) Reset() {
	*x = BrokerInfo{}
	mi := &file_exhook_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BrokerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrokerInfo) ProtoMessage() {}

func (x *BrokerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrokerInfo.ProtoReflect.Descriptor instead.
func (*BrokerInfo) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{16}
}

func (x *BrokerInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BrokerInfo) GetSysdescr() string {
	if x != nil {
		return x.Sysdescr
	}
	return ""
}

func (x *BrokerInfo) GetUptime() string {
	if x != nil {
		return x.Uptime
	}
	return ""
}

func (x *BrokerInfo) GetDatetime() string {
	if x != nil {
		return x.Datetime
	}
	return ""
}

// Meta information
type MetaInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          string                 `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	ClusterName   string                 `protobuf:"bytes,3,opt,name=cluster_name,json=clusterName,proto3" json:"cluster_name,omitempty"`
	Sysdescr      string                 `protobuf:"bytes,4,opt,name=sysdescr,proto3" json:"sysdescr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetaInfo) Reset() {
	*x = MetaInfo{}
	mi := &file_exhook_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaInfo) ProtoMessage() {}

func (x *MetaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_exhook_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaInfo.ProtoReflect.Descriptor instead.
func (*MetaInfo) Descriptor() ([]byte, []int) {
	return file_exhook_proto_rawDescGZIP(), []int{17}
}

func (x *MetaInfo) GetNode() string {
	if x != nil {
		return x.Node
	}
	return ""
}

func (x *MetaInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *MetaInfo) GetClusterName() string {
	if x != nil {
		return x.ClusterName
	}
	return ""
}

func (x *MetaInfo) GetSysdescr() string {
	if x != nil {
		return x.Sysdescr
	}
	return ""
}

var File_exhook_proto protoreflect.FileDescriptor

const file_exhook_proto_rawDesc = "" +
	"\n" +
	"\fexhook.proto\x12\x0eemqx.exhook.v2\x1a\x1bgoogle/protobuf/empty.proto\"\xeb\x03\n" +
	"\n" +
	"ClientInfo\x12\x12\n" +
	"\x04node\x18\x01 \x01(\tR\x04node\x12\x1a\n" +
	"\bclientid\x18\x02 \x01(\tR\bclientid\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x1a\n" +
	"\bpeerhost\x18\x05 \x01(\tR\bpeerhost\x12\x1a\n" +
	"\bpeerport\x18\x06 \x01(\rR\bpeerport\x12\x1a\n" +
	"\bsockhost\x18\a \x01(\tR\bsockhost\x12\x1a\n" +
	"\bsockport\x18\b \x01(\rR\bsockport\x12\x1a\n" +
	"\bprotocol\x18\t \x01(\tR\bprotocol\x12\x1e\n" +
	"\n" +
	"mountpoint\x18\n" +
	" \x01(\tR\n" +
	"mountpoint\x12!\n" +
	"\fis_superuser\x18\v \x01(\bR\visSuperuser\x12\x1c\n" +
	"\tanonymous\x18\f \x01(\bR\tanonymous\x12\x0e\n" +
	"\x02cn\x18\r \x01(\tR\x02cn\x12\x0e\n" +
	"\x02dn\x18\x0e \x01(\tR\x02dn\x12!\n" +
	"\fconnected_at\x18\x0f \x01(\x04R\vconnectedAt\x12\x1c\n" +
	"\tkeepalive\x18\x10 \x01(\rR\tkeepalive\x12'\n" +
	"\x0fexpiry_interval\x18\x11 \x01(\rR\x0eexpiryInterval\"\x91\x02\n" +
	"\vEmqxMessage\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x10\n" +
	"\x03qos\x18\x02 \x01(\rR\x03qos\x12\x12\n" +
	"\x04from\x18\x03 \x01(\tR\x04from\x12\x14\n" +
	"\x05topic\x18\x04 \x01(\tR\x05topic\x12\x18\n" +
	"\apayload\x18\x05 \x01(\fR\apayload\x12\x1c\n" +
	"\ttimestamp\x18\x06 \x01(\x04R\ttimestamp\x12B\n" +
	"\aheaders\x18\a \x03(\v2(.emqx.exhook.v2.EmqxMessage.HeadersEntryR\aheaders\x1a:\n" +
	"\fHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"4\n" +
	"\bProperty\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\"3\n" +
	"\vTopicFilter\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x10\n" +
	"\x03qos\x18\x02 \x01(\rR\x03qos\"~\n" +
	"\x14ClientConnectRequest\x126\n" +
	"\bconninfo\x18\x01 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\bconninfo\x12.\n" +
	"\x05props\x18\x02 \x03(\v2\x18.emqx.exhook.v2.PropertyR\x05props\"o\n" +
	"\x19ClientAuthenticateRequest\x12:\n" +
	"\n" +
	"clientinfo\x18\x01 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\n" +
	"clientinfo\x12\x16\n" +
	"\x06result\x18\x02 \x01(\bR\x06result\"m\n" +
	"\x17ClientDisconnectRequest\x12:\n" +
	"\n" +
	"clientinfo\x18\x01 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\n" +
	"clientinfo\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\"\xc5\x01\n" +
	"\x16ClientSubscribeRequest\x12:\n" +
	"\n" +
	"clientinfo\x18\x01 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\n" +
	"clientinfo\x12.\n" +
	"\x05props\x18\x02 \x03(\v2\x18.emqx.exhook.v2.PropertyR\x05props\x12?\n" +
	"\ftopicfilters\x18\x03 \x03(\v2\x1b.emqx.exhook.v2.TopicFilterR\ftopicfilters\"\xc7\x01\n" +
	"\x18ClientUnsubscribeRequest\x12:\n" +
	"\n" +
	"clientinfo\x18\x01 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\n" +
	"clientinfo\x12.\n" +
	"\x05props\x18\x02 \x03(\v2\x18.emqx.exhook.v2.PropertyR\x05props\x12?\n" +
	"\ftopicfilters\x18\x03 \x03(\v2\x1b.emqx.exhook.v2.TopicFilterR\ftopicfilters\"\x8a\x01\n" +
	"\x15MessagePublishRequest\x125\n" +
	"\amessage\x18\x01 \x01(\v2\x1b.emqx.exhook.v2.EmqxMessageR\amessage\x12:\n" +
	"\n" +
	"clientinfo\x18\x02 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\n" +
	"clientinfo\"\x8c\x01\n" +
	"\x17MessageDeliveredRequest\x125\n" +
	"\amessage\x18\x01 \x01(\v2\x1b.emqx.exhook.v2.EmqxMessageR\amessage\x12:\n" +
	"\n" +
	"clientinfo\x18\x02 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\n" +
	"clientinfo\"\x88\x01\n" +
	"\x13MessageAckedRequest\x125\n" +
	"\amessage\x18\x01 \x01(\v2\x1b.emqx.exhook.v2.EmqxMessageR\amessage\x12:\n" +
	"\n" +
	"clientinfo\x18\x02 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\n" +
	"clientinfo\"\xa2\x01\n" +
	"\x15MessageDroppedRequest\x125\n" +
	"\amessage\x18\x01 \x01(\v2\x1b.emqx.exhook.v2.EmqxMessageR\amessage\x12:\n" +
	"\n" +
	"clientinfo\x18\x02 \x01(\v2\x1a.emqx.exhook.v2.ClientInfoR\n" +
	"clientinfo\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"\x92\x01\n" +
	"\x0eLoadedResponse\x12@\n" +
	"\x04type\x18\x01 \x01(\x0e2,.emqx.exhook.v2.LoadedResponse.ResponsedTypeR\x04type\">\n" +
	"\rResponsedType\x12\f\n" +
	"\bCONTINUE\x10\x00\x12\n" +
	"\n" +
	"\x06IGNORE\x10\x01\x12\x13\n" +
	"\x0fSTOP_AND_RETURN\x10\x02\"\x0e\n" +
	"\fEmptySuccess\"y\n" +
	"\x15ProviderLoadedRequest\x122\n" +
	"\x06broker\x18\x01 \x01(\v2\x1a.emqx.exhook.v2.BrokerInfoR\x06broker\x12,\n" +
	"\x04meta\x18\x02 \x01(\v2\x18.emqx.exhook.v2.MetaInfoR\x04meta\"v\n" +
	"\n" +
	"BrokerInfo\x12\x18\n" +
	"\aversion\x18\x01 \x01(\tR\aversion\x12\x1a\n" +
	"\bsysdescr\x18\x02 \x01(\tR\bsysdescr\x12\x16\n" +
	"\x06uptime\x18\x03 \x01(\tR\x06uptime\x12\x1a\n" +
	"\bdatetime\x18\x04 \x01(\tR\bdatetime\"w\n" +
	"\bMetaInfo\x12\x12\n" +
	"\x04node\x18\x01 \x01(\tR\x04node\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12!\n" +
	"\fcluster_name\x18\x03 \x01(\tR\vclusterName\x12\x1a\n" +
	"\bsysdescr\x18\x04 \x01(\tR\bsysdescr2\xa4\a\n" +
	"\fHookProvider\x12Y\n" +
	"\x10OnProviderLoaded\x12%.emqx.exhook.v2.ProviderLoadedRequest\x1a\x1e.emqx.exhook.v2.LoadedResponse\x12W\n" +
	"\x0fOnClientConnect\x12$.emqx.exhook.v2.ClientConnectRequest\x1a\x1e.emqx.exhook.v2.LoadedResponse\x12a\n" +
	"\x14OnClientAuthenticate\x12).emqx.exhook.v2.ClientAuthenticateRequest\x1a\x1e.emqx.exhook.v2.LoadedResponse\x12[\n" +
	"\x12OnClientDisconnect\x12'.emqx.exhook.v2.ClientDisconnectRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\x12[\n" +
	"\x11OnClientSubscribe\x12&.emqx.exhook.v2.ClientSubscribeRequest\x1a\x1e.emqx.exhook.v2.LoadedResponse\x12]\n" +
	"\x13OnClientUnsubscribe\x12(.emqx.exhook.v2.ClientUnsubscribeRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\x12Y\n" +
	"\x10OnMessagePublish\x12%.emqx.exhook.v2.MessagePublishRequest\x1a\x1e.emqx.exhook.v2.LoadedResponse\x12[\n" +
	"\x12OnMessageDelivered\x12'.emqx.exhook.v2.MessageDeliveredRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\x12S\n" +
	"\x0eOnMessageAcked\x12#.emqx.exhook.v2.MessageAckedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccess\x12W\n" +
	"\x10OnMessageDropped\x12%.emqx.exhook.v2.MessageDroppedRequest\x1a\x1c.emqx.exhook.v2.EmptySuccessB\x1dZ\x1bhopenbackend/protos/gen;genb\x06proto3"

var (
	file_exhook_proto_rawDescOnce sync.Once
	file_exhook_proto_rawDescData []byte
)

func file_exhook_proto_rawDescGZIP() []byte {
	file_exhook_proto_rawDescOnce.Do(func() {
		file_exhook_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_exhook_proto_rawDesc), len(file_exhook_proto_rawDesc)))
	})
	return file_exhook_proto_rawDescData
}

var file_exhook_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_exhook_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_exhook_proto_goTypes = []any{
	(LoadedResponse_ResponsedType)(0), // 0: emqx.exhook.v2.LoadedResponse.ResponsedType
	(*ClientInfo)(nil),                // 1: emqx.exhook.v2.ClientInfo
	(*EmqxMessage)(nil),               // 2: emqx.exhook.v2.EmqxMessage
	(*Property)(nil),                  // 3: emqx.exhook.v2.Property
	(*TopicFilter)(nil),               // 4: emqx.exhook.v2.TopicFilter
	(*ClientConnectRequest)(nil),      // 5: emqx.exhook.v2.ClientConnectRequest
	(*ClientAuthenticateRequest)(nil), // 6: emqx.exhook.v2.ClientAuthenticateRequest
	(*ClientDisconnectRequest)(nil),   // 7: emqx.exhook.v2.ClientDisconnectRequest
	(*ClientSubscribeRequest)(nil),    // 8: emqx.exhook.v2.ClientSubscribeRequest
	(*ClientUnsubscribeRequest)(nil),  // 9: emqx.exhook.v2.ClientUnsubscribeRequest
	(*MessagePublishRequest)(nil),     // 10: emqx.exhook.v2.MessagePublishRequest
	(*MessageDeliveredRequest)(nil),   // 11: emqx.exhook.v2.MessageDeliveredRequest
	(*MessageAckedRequest)(nil),       // 12: emqx.exhook.v2.MessageAckedRequest
	(*MessageDroppedRequest)(nil),     // 13: emqx.exhook.v2.MessageDroppedRequest
	(*LoadedResponse)(nil),            // 14: emqx.exhook.v2.LoadedResponse
	(*EmptySuccess)(nil),              // 15: emqx.exhook.v2.EmptySuccess
	(*ProviderLoadedRequest)(nil),     // 16: emqx.exhook.v2.ProviderLoadedRequest
	(*BrokerInfo)(nil),                // 17: emqx.exhook.v2.BrokerInfo
	(*MetaInfo)(nil),                  // 18: emqx.exhook.v2.MetaInfo
	nil,                               // 19: emqx.exhook.v2.EmqxMessage.HeadersEntry
}
var file_exhook_proto_depIdxs = []int32{
	19, // 0: emqx.exhook.v2.EmqxMessage.headers:type_name -> emqx.exhook.v2.EmqxMessage.HeadersEntry
	1,  // 1: emqx.exhook.v2.ClientConnectRequest.conninfo:type_name -> emqx.exhook.v2.ClientInfo
	3,  // 2: emqx.exhook.v2.ClientConnectRequest.props:type_name -> emqx.exhook.v2.Property
	1,  // 3: emqx.exhook.v2.ClientAuthenticateRequest.clientinfo:type_name -> emqx.exhook.v2.ClientInfo
	1,  // 4: emqx.exhook.v2.ClientDisconnectRequest.clientinfo:type_name -> emqx.exhook.v2.ClientInfo
	1,  // 5: emqx.exhook.v2.ClientSubscribeRequest.clientinfo:type_name -> emqx.exhook.v2.ClientInfo
	3,  // 6: emqx.exhook.v2.ClientSubscribeRequest.props:type_name -> emqx.exhook.v2.Property
	4,  // 7: emqx.exhook.v2.ClientSubscribeRequest.topicfilters:type_name -> emqx.exhook.v2.TopicFilter
	1,  // 8: emqx.exhook.v2.ClientUnsubscribeRequest.clientinfo:type_name -> emqx.exhook.v2.ClientInfo
	3,  // 9: emqx.exhook.v2.ClientUnsubscribeRequest.props:type_name -> emqx.exhook.v2.Property
	4,  // 10: emqx.exhook.v2.ClientUnsubscribeRequest.topicfilters:type_name -> emqx.exhook.v2.TopicFilter
	2,  // 11: emqx.exhook.v2.MessagePublishRequest.message:type_name -> emqx.exhook.v2.EmqxMessage
	1,  // 12: emqx.exhook.v2.MessagePublishRequest.clientinfo:type_name -> emqx.exhook.v2.ClientInfo
	2,  // 13: emqx.exhook.v2.MessageDeliveredRequest.message:type_name -> emqx.exhook.v2.EmqxMessage
	1,  // 14: emqx.exhook.v2.MessageDeliveredRequest.clientinfo:type_name -> emqx.exhook.v2.ClientInfo
	2,  // 15: emqx.exhook.v2.MessageAckedRequest.message:type_name -> emqx.exhook.v2.EmqxMessage
	1,  // 16: emqx.exhook.v2.MessageAckedRequest.clientinfo:type_name -> emqx.exhook.v2.ClientInfo
	2,  // 17: emqx.exhook.v2.MessageDroppedRequest.message:type_name -> emqx.exhook.v2.EmqxMessage
	1,  // 18: emqx.exhook.v2.MessageDroppedRequest.clientinfo:type_name -> emqx.exhook.v2.ClientInfo
	0,  // 19: emqx.exhook.v2.LoadedResponse.type:type_name -> emqx.exhook.v2.LoadedResponse.ResponsedType
	17, // 20: emqx.exhook.v2.ProviderLoadedRequest.broker:type_name -> emqx.exhook.v2.BrokerInfo
	18, // 21: emqx.exhook.v2.ProviderLoadedRequest.meta:type_name -> emqx.exhook.v2.MetaInfo
	16, // 22: emqx.exhook.v2.HookProvider.OnProviderLoaded:input_type -> emqx.exhook.v2.ProviderLoadedRequest
	5,  // 23: emqx.exhook.v2.HookProvider.OnClientConnect:input_type -> emqx.exhook.v2.ClientConnectRequest
	6,  // 24: emqx.exhook.v2.HookProvider.OnClientAuthenticate:input_type -> emqx.exhook.v2.ClientAuthenticateRequest
	7,  // 25: emqx.exhook.v2.HookProvider.OnClientDisconnect:input_type -> emqx.exhook.v2.ClientDisconnectRequest
	8,  // 26: emqx.exhook.v2.HookProvider.OnClientSubscribe:input_type -> emqx.exhook.v2.ClientSubscribeRequest
	9,  // 27: emqx.exhook.v2.HookProvider.OnClientUnsubscribe:input_type -> emqx.exhook.v2.ClientUnsubscribeRequest
	10, // 28: emqx.exhook.v2.HookProvider.OnMessagePublish:input_type -> emqx.exhook.v2.MessagePublishRequest
	11, // 29: emqx.exhook.v2.HookProvider.OnMessageDelivered:input_type -> emqx.exhook.v2.MessageDeliveredRequest
	12, // 30: emqx.exhook.v2.HookProvider.OnMessageAcked:input_type -> emqx.exhook.v2.MessageAckedRequest
	13, // 31: emqx.exhook.v2.HookProvider.OnMessageDropped:input_type -> emqx.exhook.v2.MessageDroppedRequest
	14, // 32: emqx.exhook.v2.HookProvider.OnProviderLoaded:output_type -> emqx.exhook.v2.LoadedResponse
	14, // 33: emqx.exhook.v2.HookProvider.OnClientConnect:output_type -> emqx.exhook.v2.LoadedResponse
	14, // 34: emqx.exhook.v2.HookProvider.OnClientAuthenticate:output_type -> emqx.exhook.v2.LoadedResponse
	15, // 35: emqx.exhook.v2.HookProvider.OnClientDisconnect:output_type -> emqx.exhook.v2.EmptySuccess
	14, // 36: emqx.exhook.v2.HookProvider.OnClientSubscribe:output_type -> emqx.exhook.v2.LoadedResponse
	15, // 37: emqx.exhook.v2.HookProvider.OnClientUnsubscribe:output_type -> emqx.exhook.v2.EmptySuccess
	14, // 38: emqx.exhook.v2.HookProvider.OnMessagePublish:output_type -> emqx.exhook.v2.LoadedResponse
	15, // 39: emqx.exhook.v2.HookProvider.OnMessageDelivered:output_type -> emqx.exhook.v2.EmptySuccess
	15, // 40: emqx.exhook.v2.HookProvider.OnMessageAcked:output_type -> emqx.exhook.v2.EmptySuccess
	15, // 41: emqx.exhook.v2.HookProvider.OnMessageDropped:output_type -> emqx.exhook.v2.EmptySuccess
	32, // [32:42] is the sub-list for method output_type
	22, // [22:32] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_exhook_proto_init() }
func file_exhook_proto_init() {
	if File_exhook_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_exhook_proto_rawDesc), len(file_exhook_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_exhook_proto_goTypes,
		DependencyIndexes: file_exhook_proto_depIdxs,
		EnumInfos:         file_exhook_proto_enumTypes,
		MessageInfos:      file_exhook_proto_msgTypes,
	}.Build()
	File_exhook_proto = out.File
	file_exhook_proto_goTypes = nil
	file_exhook_proto_depIdxs = nil
}
