#!/bin/bash

# MQTT Integration Test Suite for EMQX 5.8.1 + Hopen Backend
# Tests MQTT functionality, ExHook integration, and realtime messaging

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
EMQX_HOST="localhost"
EMQX_PORT="1883"
EMQX_API_PORT="18083"
AUTH_SERVICE_PORT="4000"
REALTIME_SERVICE_PORT="4008"

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
print_status() { echo -e "${GREEN}[OK]${NC} $1"; }
print_warn()   { echo -e "${YELLOW}[WARN]${NC} $1"; }
print_error()  { echo -e "${RED}[ERROR]${NC} $1"; }
print_info()   { echo -e "${BLUE}[INFO]${NC} $1"; }

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TESTS_TOTAL++))
    print_info "Running test: $test_name"
    
    if eval "$test_command"; then
        print_status "✅ $test_name"
        ((TESTS_PASSED++))
        return 0
    else
        print_error "❌ $test_name"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Test 1: EMQX Service Health Check
test_emqx_health() {
    print_info "Testing EMQX 5.8.1 health..."
    
    # Check if EMQX is running
    if ! docker-compose ps emqx | grep -q "Up"; then
        print_error "EMQX container is not running"
        return 1
    fi
    
    # Check EMQX status via API (without auth for basic status)
    if ! curl -s --connect-timeout 5 "http://$EMQX_HOST:$EMQX_API_PORT/api/v5/status" | grep -q "emqx is running"; then
        print_error "EMQX API is not responding correctly"
        return 1
    fi
    
    print_status "EMQX 5.8.1 is healthy and running"
    return 0
}

# Test 2: MQTT Connection Test
test_mqtt_connection() {
    print_info "Testing basic MQTT connection..."
    
    # Test basic connection without authentication
    local test_topic="test/connection/$(date +%s)"
    local test_message="Hello EMQX 5.8.1"
    
    # Use gtimeout to prevent hanging (macOS coreutils)
    if gtimeout 10s mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "$test_topic" -m "$test_message" -q 1; then
        print_status "Basic MQTT connection successful"
        return 0
    else
        print_error "Failed to connect to MQTT broker"
        return 1
    fi
}

# Test 3: MQTT Publish/Subscribe Test
test_mqtt_pubsub() {
    print_info "Testing MQTT publish/subscribe functionality..."
    
    local test_topic="test/pubsub/$(date +%s)"
    local test_message="PubSub test message"
    local received_file="/tmp/mqtt_test_received.txt"
    
    # Start subscriber in background
    gtimeout 15s mosquitto_sub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "$test_topic" -q 1 > "$received_file" &
    local sub_pid=$!
    
    # Wait a moment for subscriber to connect
    sleep 2
    
    # Publish message
    if mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "$test_topic" -m "$test_message" -q 1; then
        # Wait for message to be received
        sleep 3
        
        # Check if message was received
        if [[ -f "$received_file" ]] && grep -q "$test_message" "$received_file"; then
            print_status "MQTT publish/subscribe working correctly"
            kill $sub_pid 2>/dev/null || true
            rm -f "$received_file"
            return 0
        else
            print_error "Message was not received by subscriber"
            kill $sub_pid 2>/dev/null || true
            rm -f "$received_file"
            return 1
        fi
    else
        print_error "Failed to publish MQTT message"
        kill $sub_pid 2>/dev/null || true
        rm -f "$received_file"
        return 1
    fi
}

# Test 4: Auth Service Health Check
test_auth_service() {
    print_info "Testing Auth service health..."
    
    # Check if auth service is responding
    if grpcurl -plaintext -connect-timeout 5 "localhost:$AUTH_SERVICE_PORT" list > /dev/null 2>&1; then
        print_status "Auth service is healthy and responding"
        return 0
    else
        print_error "Auth service is not responding"
        return 1
    fi
}

# Test 5: Realtime Service Health Check
test_realtime_service() {
    print_info "Testing Realtime service health..."
    
    # Check if realtime service is responding
    if grpcurl -plaintext -connect-timeout 5 "localhost:$REALTIME_SERVICE_PORT" list > /dev/null 2>&1; then
        print_status "Realtime service is healthy and responding"
        return 0
    else
        print_error "Realtime service is not responding"
        return 1
    fi
}

# Test 6: MQTT QoS Levels Test
test_mqtt_qos() {
    print_info "Testing MQTT QoS levels..."
    
    local test_topic="test/qos/$(date +%s)"
    local success=true
    
    # Test QoS 0
    if ! mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "${test_topic}/qos0" -m "QoS 0 message" -q 0; then
        print_error "QoS 0 publish failed"
        success=false
    fi
    
    # Test QoS 1
    if ! mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "${test_topic}/qos1" -m "QoS 1 message" -q 1; then
        print_error "QoS 1 publish failed"
        success=false
    fi
    
    # Test QoS 2
    if ! mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "${test_topic}/qos2" -m "QoS 2 message" -q 2; then
        print_error "QoS 2 publish failed"
        success=false
    fi
    
    if $success; then
        print_status "All MQTT QoS levels working correctly"
        return 0
    else
        return 1
    fi
}

# Test 7: MQTT Retained Messages Test
test_mqtt_retained() {
    print_info "Testing MQTT retained messages..."
    
    local test_topic="test/retained/$(date +%s)"
    local test_message="Retained message test"
    local received_file="/tmp/mqtt_retained_test.txt"
    
    # Publish retained message
    if mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "$test_topic" -m "$test_message" -q 1 -r; then
        # Wait a moment
        sleep 2
        
        # Subscribe to get retained message
        gtimeout 10s mosquitto_sub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "$test_topic" -q 1 -C 1 > "$received_file"
        
        if [[ -f "$received_file" ]] && grep -q "$test_message" "$received_file"; then
            print_status "MQTT retained messages working correctly"
            rm -f "$received_file"
            return 0
        else
            print_error "Retained message was not received"
            rm -f "$received_file"
            return 1
        fi
    else
        print_error "Failed to publish retained message"
        return 1
    fi
}

# Test 8: MQTT Wildcard Subscriptions Test
test_mqtt_wildcards() {
    print_info "Testing MQTT wildcard subscriptions..."
    
    local base_topic="test/wildcards/$(date +%s)"
    local received_file="/tmp/mqtt_wildcard_test.txt"
    
    # Start wildcard subscriber
    gtimeout 15s mosquitto_sub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "${base_topic}/+/data" -q 1 > "$received_file" &
    local sub_pid=$!
    
    # Wait for subscriber to connect
    sleep 2
    
    # Publish to multiple matching topics
    mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "${base_topic}/sensor1/data" -m "sensor1 data" -q 1
    mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "${base_topic}/sensor2/data" -m "sensor2 data" -q 1
    
    # Wait for messages
    sleep 3
    
    # Check if both messages were received
    if [[ -f "$received_file" ]] && grep -q "sensor1 data" "$received_file" && grep -q "sensor2 data" "$received_file"; then
        print_status "MQTT wildcard subscriptions working correctly"
        kill $sub_pid 2>/dev/null || true
        rm -f "$received_file"
        return 0
    else
        print_error "Wildcard subscription test failed"
        kill $sub_pid 2>/dev/null || true
        rm -f "$received_file"
        return 1
    fi
}

# Main test execution
main() {
    echo "🧪 MQTT Integration Test Suite for EMQX 5.8.1 + Hopen Backend"
    echo "=============================================================="
    echo ""
    
    # Run all tests
    run_test "EMQX Health Check" "test_emqx_health"
    run_test "MQTT Connection Test" "test_mqtt_connection"
    run_test "MQTT Publish/Subscribe Test" "test_mqtt_pubsub"
    run_test "Auth Service Health Check" "test_auth_service"
    run_test "Realtime Service Health Check" "test_realtime_service"
    run_test "MQTT QoS Levels Test" "test_mqtt_qos"
    run_test "MQTT Retained Messages Test" "test_mqtt_retained"
    run_test "MQTT Wildcard Subscriptions Test" "test_mqtt_wildcards"
    
    # Print summary
    echo ""
    echo "=============================================================="
    echo "🏁 Test Summary:"
    echo "   Total Tests: $TESTS_TOTAL"
    echo "   Passed: $TESTS_PASSED"
    echo "   Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        print_status "🎉 All MQTT integration tests passed!"
        echo ""
        print_status "✅ EMQX 5.8.1 is working perfectly with Hopen Backend"
        print_status "✅ MQTT functionality is fully operational"
        print_status "✅ Auth and Realtime services are healthy"
        print_status "✅ System is ready for production use"
        exit 0
    else
        print_error "❌ Some tests failed. Please check the logs above."
        exit 1
    fi
}

# Check dependencies
if ! command -v mosquitto_pub &> /dev/null; then
    print_error "mosquitto_pub not found. Please install mosquitto clients."
    exit 1
fi

if ! command -v grpcurl &> /dev/null; then
    print_error "grpcurl not found. Please install grpcurl for gRPC testing."
    exit 1
fi

# Run main function
main "$@"
