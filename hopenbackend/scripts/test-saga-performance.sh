#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to print section headers
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to measure saga performance
test_saga_performance() {
    print_header "🚀 SAGA PERFORMANCE ANALYSIS"
    
    echo "📊 Testing account creation saga performance..."
    echo ""
    
    # Test multiple registrations to get average performance
    TOTAL_TESTS=5
    TOTAL_TIME=0
    SUCCESSFUL_TESTS=0
    
    for i in $(seq 1 $TOTAL_TESTS); do
        echo "🔄 Test $i/$TOTAL_TESTS"
        
        # Generate unique credentials
        TIMESTAMP=$(date +%s%N)
        UNIQUE_EMAIL="perf_test_${TIMESTAMP}@example.com"
        UNIQUE_USERNAME="perf_user_${TIMESTAMP}"
        
        echo "   📝 Email: $UNIQUE_EMAIL"
        echo "   👤 Username: $UNIQUE_USERNAME"
        
        # Measure execution time
        START_TIME=$(date +%s.%N)
        
        REGISTER_RESULT=$(grpcurl -plaintext -d "{
            \"username\": \"$UNIQUE_USERNAME\", 
            \"email\": \"$UNIQUE_EMAIL\", 
            \"password\": \"SecurePass789#\", 
            \"first_name\": \"Performance\", 
            \"last_name\": \"Test\"
        }" localhost:4000 hopen.auth.v1.AuthService/RegisterUser 2>&1)
        
        END_TIME=$(date +%s.%N)
        DURATION=$(echo "$END_TIME - $START_TIME" | bc)
        
        echo "   ⏱️  Duration: ${DURATION}s"
        
        if echo "$REGISTER_RESULT" | grep -q "ERROR"; then
            echo "   ❌ Registration failed:"
            echo "   $REGISTER_RESULT"
            echo ""
        else
            echo "   ✅ Registration successful"
            TOTAL_TIME=$(echo "$TOTAL_TIME + $DURATION" | bc)
            SUCCESSFUL_TESTS=$((SUCCESSFUL_TESTS + 1))
            echo ""
        fi
        
        # Small delay between tests
        sleep 1
    done
    
    # Calculate statistics
    if [ $SUCCESSFUL_TESTS -gt 0 ]; then
        AVERAGE_TIME=$(echo "scale=3; $TOTAL_TIME / $SUCCESSFUL_TESTS" | bc)
        echo "📈 PERFORMANCE STATISTICS:"
        echo "   🎯 Total tests: $TOTAL_TESTS"
        echo "   ✅ Successful tests: $SUCCESSFUL_TESTS"
        echo "   ⏱️  Total time: ${TOTAL_TIME}s"
        echo "   📊 Average time: ${AVERAGE_TIME}s"
        echo ""
        
        # Performance analysis
        if (( $(echo "$AVERAGE_TIME < 2.0" | bc -l) )); then
            echo -e "${GREEN}🚀 EXCELLENT: Saga performance is excellent (< 2s)${NC}"
        elif (( $(echo "$AVERAGE_TIME < 5.0" | bc -l) )); then
            echo -e "${YELLOW}⚡ GOOD: Saga performance is acceptable (< 5s)${NC}"
        elif (( $(echo "$AVERAGE_TIME < 10.0" | bc -l) )); then
            echo -e "${YELLOW}⚠️  MODERATE: Saga performance is moderate (< 10s)${NC}"
        else
            echo -e "${RED}🐌 SLOW: Saga performance needs optimization (> 10s)${NC}"
        fi
    else
        echo -e "${RED}❌ No successful tests to analyze${NC}"
    fi
}

# Function to analyze saga steps
analyze_saga_steps() {
    print_header "🔍 SAGA STEP ANALYSIS"
    
    echo "📋 Analyzing saga implementation steps..."
    echo ""
    
    echo "🔄 SAGA STEPS IDENTIFIED:"
    echo "   1️⃣  CheckUserExistence - Validates username/email uniqueness"
    echo "   2️⃣  CreateOryIdentity - Creates identity in Kratos"
    echo "   3️⃣  CreateUserProfile - Creates user profile in user service"
    echo "   4️⃣  AssignDefaultRole - Assigns default user role"
    echo ""
    
    echo "⚡ PERFORMANCE CHARACTERISTICS:"
    echo "   🔗 Network calls: 3-4 external service calls"
    echo "   💾 Database operations: Multiple writes with transactions"
    echo "   🔄 Retry mechanisms: Exponential backoff on failures"
    echo "   🛡️  Idempotency: Each step is idempotent with keys"
    echo "   📝 Persistence: State persisted after each step"
    echo ""
    
    echo "🎯 POTENTIAL BOTTLENECKS:"
    echo "   1. Network latency between services"
    echo "   2. Database transaction overhead"
    echo "   3. Idempotency key generation and validation"
    echo "   4. State persistence after each step"
    echo "   5. Retry mechanisms on service failures"
    echo ""
}

# Function to check service health
check_service_health() {
    print_header "🏥 SERVICE HEALTH CHECK"
    
    echo "🔍 Checking service availability..."
    
    # Check auth service
    AUTH_HEALTH=$(grpcurl -plaintext localhost:4000 grpc.health.v1.Health/Check 2>&1)
    if echo "$AUTH_HEALTH" | grep -q "SERVING"; then
        print_result 0 "Auth service is healthy"
    else
        print_result 1 "Auth service is unhealthy"
    fi
    
    # Check NATS connectivity
    NATS_HEALTH=$(curl -s http://localhost:8222/healthz 2>/dev/null)
    if [ "$NATS_HEALTH" = "ok" ]; then
        print_result 0 "NATS is healthy"
    else
        print_result 1 "NATS is unhealthy"
    fi
    
    # Check service discovery
    NATS_CONNECTIONS=$(curl -s http://localhost:8222/connz | jq '.connections | length' 2>/dev/null)
    if [ "$NATS_CONNECTIONS" -gt 0 ]; then
        print_result 0 "Service discovery active ($NATS_CONNECTIONS connections)"
    else
        print_result 1 "Service discovery inactive"
    fi
    
    echo ""
}

# Main execution
main() {
    echo -e "${BLUE}🎯 HOPEN SAGA PERFORMANCE ANALYZER${NC}"
    echo -e "${BLUE}===================================${NC}"
    echo ""
    
    check_service_health
    analyze_saga_steps
    test_saga_performance
    
    echo -e "${BLUE}=== 🎉 ANALYSIS COMPLETE ===${NC}"
}

# Check if bc is available for calculations
if ! command -v bc &> /dev/null; then
    echo -e "${RED}❌ Error: 'bc' calculator is required but not installed${NC}"
    echo "Please install bc: brew install bc (macOS) or apt-get install bc (Ubuntu)"
    exit 1
fi

# Run main function
main
