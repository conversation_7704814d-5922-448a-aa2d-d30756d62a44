#!/bin/bash

# ExHook Integration Test for EMQX 5.8.1 + Auth Service
# Tests the gRPC ExHook integration between EMQX and Auth service

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
EMQX_HOST="localhost"
EMQX_PORT="1883"
AUTH_SERVICE_PORT="4000"

# Helper functions
print_status() { echo -e "${GREEN}[OK]${NC} $1"; }
print_warn()   { echo -e "${YELLOW}[WARN]${NC} $1"; }
print_error()  { echo -e "${RED}[ERROR]${NC} $1"; }
print_info()   { echo -e "${BLUE}[INFO]${NC} $1"; }

echo "🔗 ExHook Integration Test for EMQX 5.8.1 + Auth Service"
echo "========================================================"
echo ""

# Test 1: Check ExHook configuration in EMQX
print_info "Checking ExHook configuration..."

# Check if ExHook is configured
if docker-compose exec -T emqx emqx ctl exhook list 2>/dev/null | grep -q "hopen_auth_hook"; then
    print_status "ExHook 'hopen_auth_hook' is configured"
else
    print_warn "ExHook may not be properly configured or enabled"
fi

# Test 2: Check Auth service ExHook endpoints
print_info "Checking Auth service ExHook gRPC endpoints..."

if grpcurl -plaintext localhost:$AUTH_SERVICE_PORT list | grep -q "exhook"; then
    print_status "Auth service has ExHook gRPC endpoints"
    
    # List available ExHook methods
    print_info "Available ExHook methods:"
    grpcurl -plaintext localhost:$AUTH_SERVICE_PORT list exhook.HookProvider | sed 's/^/  - /'
else
    print_error "Auth service does not expose ExHook gRPC endpoints"
fi

# Test 3: Test ExHook client connection hook
print_info "Testing ExHook client connection hook..."

# Monitor auth service logs for ExHook calls
print_info "Monitoring auth service logs for ExHook calls..."
echo "Starting MQTT client to trigger ExHook calls..."

# Start log monitoring in background
docker-compose logs -f auth 2>/dev/null | grep -i "exhook\|mqtt\|client" &
LOG_PID=$!

# Give log monitoring a moment to start
sleep 2

# Connect an MQTT client to trigger ExHook
print_info "Connecting MQTT client to trigger ExHook..."
mosquitto_pub -h "$EMQX_HOST" -p "$EMQX_PORT" -t "test/exhook/$(date +%s)" -m "ExHook test message" -q 1 -i "exhook_test_client" || true

# Wait for logs
sleep 3

# Stop log monitoring
kill $LOG_PID 2>/dev/null || true

# Test 4: Check EMQX ExHook status
print_info "Checking EMQX ExHook status..."

# Try to get ExHook status from EMQX
if docker-compose exec -T emqx emqx ctl exhook list 2>/dev/null; then
    print_status "ExHook status retrieved successfully"
else
    print_warn "Could not retrieve ExHook status"
fi

# Test 5: Verify ExHook server connectivity
print_info "Testing ExHook server connectivity from EMQX container..."

# Test if EMQX can reach auth service
if docker-compose exec -T emqx nc -z auth 4000 2>/dev/null; then
    print_status "EMQX can reach auth service on port 4000"
else
    print_error "EMQX cannot reach auth service on port 4000"
fi

# Test 6: Check ExHook configuration in EMQX config
print_info "Checking ExHook configuration in EMQX..."

if docker-compose exec -T emqx cat /opt/emqx/etc/emqx.conf | grep -A 10 -B 2 "exhook" 2>/dev/null; then
    print_status "ExHook configuration found in EMQX config"
else
    print_warn "ExHook configuration not found in main config"
fi

# Check if ExHook is in the loaded configuration
if docker-compose exec -T emqx emqx ctl conf show | grep -i "exhook" 2>/dev/null; then
    print_status "ExHook is in the loaded EMQX configuration"
else
    print_warn "ExHook is not in the loaded EMQX configuration"
fi

echo ""
echo "========================================================"
print_info "ExHook Integration Test Summary:"
echo ""
print_info "If ExHook is working properly, you should see:"
print_info "1. ✅ ExHook configured in EMQX"
print_info "2. ✅ Auth service exposing ExHook gRPC endpoints"
print_info "3. ✅ Network connectivity between EMQX and Auth service"
print_info "4. 📋 ExHook calls in auth service logs when MQTT clients connect"
echo ""
print_info "Note: ExHook integration requires proper authentication setup."
print_info "The current test uses anonymous MQTT connections which may not trigger all ExHook events."
echo ""
