package auth

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pb "hopenbackend/protos/gen"
)

// ExHookService implements the EMQX ExHook gRPC interface
type ExHookService struct {
	pb.UnimplementedHookProviderServer
	authService *Service
	logger      *zap.Logger
}

// NewExHookService creates a new ExHook service instance
func NewExHookService(authService *Service, logger *zap.Logger) *ExHookService {
	return &ExHookService{
		authService: authService,
		logger:      logger,
	}
}

// OnProviderLoaded handles provider loaded events
func (e *ExHookService) OnProviderLoaded(ctx context.Context, req *pb.ProviderLoadedRequest) (*pb.LoadedResponse, error) {
	e.logger.Info("ExHook: Provider loaded",
		zap.String("broker_version", req.Broker.Version),
		zap.String("node", req.Meta.Node),
		zap.String("cluster_name", req.Meta.ClusterName))

	return &pb.LoadedResponse{
		Type: pb.LoadedResponse_CONTINUE,
	}, nil
}

// OnClientConnect handles client connection events
func (e *ExHookService) OnClientConnect(ctx context.Context, req *pb.ClientConnectRequest) (*pb.LoadedResponse, error) {
	e.logger.Debug("ExHook: Client connecting",
		zap.String("client_id", req.Conninfo.Clientid),
		zap.String("username", req.Conninfo.Username),
		zap.String("peer_host", req.Conninfo.Peerhost))

	// Allow connection to proceed - authentication happens in OnClientAuthenticate
	return &pb.LoadedResponse{
		Type: pb.LoadedResponse_CONTINUE,
	}, nil
}

// OnClientAuthenticate handles client authentication
func (e *ExHookService) OnClientAuthenticate(ctx context.Context, req *pb.ClientAuthenticateRequest) (*pb.LoadedResponse, error) {
	clientInfo := req.Clientinfo

	e.logger.Info("ExHook: Authenticating client",
		zap.String("client_id", clientInfo.Clientid),
		zap.String("username", clientInfo.Username),
		zap.String("peer_host", clientInfo.Peerhost))

	// Skip authentication for anonymous clients or empty username
	if clientInfo.Anonymous || clientInfo.Username == "" {
		e.logger.Warn("ExHook: Rejecting anonymous or empty username client",
			zap.String("client_id", clientInfo.Clientid),
			zap.Bool("anonymous", clientInfo.Anonymous))
		return &pb.LoadedResponse{
			Type: pb.LoadedResponse_STOP_AND_RETURN,
		}, status.Error(codes.Unauthenticated, "authentication required")
	}

	// Call the existing ValidateMqttConnection method
	validateReq := &pb.ValidateMqttConnectionRequest{
		UserId:   clientInfo.Username,
		ClientId: clientInfo.Clientid,
		Username: clientInfo.Username,
		Password: clientInfo.Password,
	}

	validateResp, err := e.authService.ValidateMqttConnection(ctx, validateReq)
	if err != nil {
		e.logger.Error("ExHook: Authentication failed",
			zap.String("client_id", clientInfo.Clientid),
			zap.String("username", clientInfo.Username),
			zap.Error(err))
		return &pb.LoadedResponse{
			Type: pb.LoadedResponse_STOP_AND_RETURN,
		}, err
	}

	if !validateResp.IsValid {
		e.logger.Warn("ExHook: Invalid credentials",
			zap.String("client_id", clientInfo.Clientid),
			zap.String("username", clientInfo.Username))
		return &pb.LoadedResponse{
			Type: pb.LoadedResponse_STOP_AND_RETURN,
		}, status.Error(codes.Unauthenticated, "invalid credentials")
	}

	e.logger.Info("ExHook: Client authenticated successfully",
		zap.String("client_id", clientInfo.Clientid),
		zap.String("username", clientInfo.Username),
		zap.Int("allowed_topics", len(validateResp.AllowedTopics)))

	return &pb.LoadedResponse{
		Type: pb.LoadedResponse_CONTINUE,
	}, nil
}

// OnClientDisconnect handles client disconnection events
func (e *ExHookService) OnClientDisconnect(ctx context.Context, req *pb.ClientDisconnectRequest) (*pb.EmptySuccess, error) {
	e.logger.Debug("ExHook: Client disconnected",
		zap.String("client_id", req.Clientinfo.Clientid),
		zap.String("username", req.Clientinfo.Username),
		zap.String("reason", req.Reason))

	return &pb.EmptySuccess{}, nil
}

// OnClientSubscribe handles client subscription events
func (e *ExHookService) OnClientSubscribe(ctx context.Context, req *pb.ClientSubscribeRequest) (*pb.LoadedResponse, error) {
	clientInfo := req.Clientinfo

	e.logger.Debug("ExHook: Client subscribing",
		zap.String("client_id", clientInfo.Clientid),
		zap.String("username", clientInfo.Username),
		zap.Int("topic_count", len(req.Topicfilters)))

	// Check permissions for each topic
	for _, topicFilter := range req.Topicfilters {
		if !e.isTopicAllowed(ctx, clientInfo.Username, topicFilter.Name, "subscribe") {
			e.logger.Warn("ExHook: Subscription denied",
				zap.String("client_id", clientInfo.Clientid),
				zap.String("username", clientInfo.Username),
				zap.String("topic", topicFilter.Name))
			return &pb.LoadedResponse{
				Type: pb.LoadedResponse_STOP_AND_RETURN,
			}, status.Error(codes.PermissionDenied, "subscription not allowed")
		}
	}

	e.logger.Debug("ExHook: Subscription allowed",
		zap.String("client_id", clientInfo.Clientid),
		zap.String("username", clientInfo.Username))

	return &pb.LoadedResponse{
		Type: pb.LoadedResponse_CONTINUE,
	}, nil
}

// OnClientUnsubscribe handles client unsubscription events
func (e *ExHookService) OnClientUnsubscribe(ctx context.Context, req *pb.ClientUnsubscribeRequest) (*pb.EmptySuccess, error) {
	e.logger.Debug("ExHook: Client unsubscribing",
		zap.String("client_id", req.Clientinfo.Clientid),
		zap.String("username", req.Clientinfo.Username),
		zap.Int("topic_count", len(req.Topicfilters)))

	return &pb.EmptySuccess{}, nil
}

// OnMessagePublish handles message publication events
func (e *ExHookService) OnMessagePublish(ctx context.Context, req *pb.MessagePublishRequest) (*pb.LoadedResponse, error) {
	message := req.Message
	clientInfo := req.Clientinfo

	e.logger.Debug("ExHook: Message publish",
		zap.String("client_id", clientInfo.Clientid),
		zap.String("username", clientInfo.Username),
		zap.String("topic", message.Topic),
		zap.Uint32("qos", message.Qos))

	// Check publish permissions
	if !e.isTopicAllowed(ctx, clientInfo.Username, message.Topic, "publish") {
		e.logger.Warn("ExHook: Publish denied",
			zap.String("client_id", clientInfo.Clientid),
			zap.String("username", clientInfo.Username),
			zap.String("topic", message.Topic))
		return &pb.LoadedResponse{
			Type: pb.LoadedResponse_STOP_AND_RETURN,
		}, status.Error(codes.PermissionDenied, "publish not allowed")
	}

	return &pb.LoadedResponse{
		Type: pb.LoadedResponse_CONTINUE,
	}, nil
}

// OnMessageDelivered handles message delivery events
func (e *ExHookService) OnMessageDelivered(ctx context.Context, req *pb.MessageDeliveredRequest) (*pb.EmptySuccess, error) {
	return &pb.EmptySuccess{}, nil
}

// OnMessageAcked handles message acknowledgment events
func (e *ExHookService) OnMessageAcked(ctx context.Context, req *pb.MessageAckedRequest) (*pb.EmptySuccess, error) {
	return &pb.EmptySuccess{}, nil
}

// OnMessageDropped handles message drop events
func (e *ExHookService) OnMessageDropped(ctx context.Context, req *pb.MessageDroppedRequest) (*pb.EmptySuccess, error) {
	e.logger.Debug("ExHook: Message dropped",
		zap.String("topic", req.Message.Topic),
		zap.String("reason", req.Reason))
	return &pb.EmptySuccess{}, nil
}

// isTopicAllowed checks if a user can perform an action on a topic
func (e *ExHookService) isTopicAllowed(ctx context.Context, username, topic, action string) bool {
	// Use the existing MQTT permission checking logic from the auth service
	return e.authService.CheckMQTTTopicPermission(ctx, username, topic, action)
}
