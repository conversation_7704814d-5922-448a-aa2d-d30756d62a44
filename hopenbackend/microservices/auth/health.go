package auth

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"hopenbackend/pkg/monitoring"
)

// HIBPConfig holds configuration for Have I Been Pwned API integration
type HIBPConfig struct {
	BaseURL   string
	APIKey    string
	UserAgent string
	Timeout   time.Duration
}

// NewHIBPHealthCheck creates a new health check for the HIBP API
func NewHIBPHealthCheck(config HIBPConfig) *monitoring.HealthCheck {
	return &monitoring.HealthCheck{
		Name:        "hibp",
		Description: "Have I Been Pwned API connectivity",
		Checker: func(ctx context.Context) (monitoring.HealthStatus, error) {
			// Use a known hash prefix for health check (doesn't reveal any sensitive data)
			// This is the prefix for "password" - a commonly breached password
			healthCheckURL := fmt.Sprintf("%s/range/5E884", config.BaseURL)

			req, err := http.NewRequestWithContext(ctx, "GET", healthCheckURL, nil)
			if err != nil {
				return monitoring.HealthStatusUnhealthy, fmt.Errorf("failed to create health check request: %w", err)
			}

			// Set required headers
			req.Header.Set("User-Agent", config.UserAgent)
			req.Header.Set("Accept", "text/plain")

			// Perform the request
			resp, err := http.DefaultClient.Do(req)
			if err != nil {
				return monitoring.HealthStatusUnhealthy, fmt.Errorf("health check request failed: %w", err)
			}
			defer resp.Body.Close()

			// Check if response is successful
			if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNotFound {
				// Both 200 and 404 are valid responses for HIBP API
				return monitoring.HealthStatusHealthy, nil
			} else {
				return monitoring.HealthStatusUnhealthy, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
			}
		},
	}
}
