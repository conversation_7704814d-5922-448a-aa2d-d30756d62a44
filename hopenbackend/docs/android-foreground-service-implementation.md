# Android Foreground Service Implementation Guide

## Overview
Implementation guide for Android foreground service to maintain VoIP calls in background.

## 1. Android Manifest Configuration

```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- VoIP Permissions -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    
    <!-- Audio Permissions -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <application>
        <!-- Foreground Service Declaration -->
        <service
            android:name=".VoIPForegroundService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall" />
            
        <!-- Notification Channel for API 26+ -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="call_service_channel" />
    </application>
</manifest>
```

## 2. VoIP Foreground Service Implementation

```kotlin
// android/app/src/main/kotlin/com/hopen/app/VoIPForegroundService.kt
package com.hopen.app

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat

class VoIPForegroundService : Service() {
    companion object {
        const val CHANNEL_ID = "call_service_channel"
        const val NOTIFICATION_ID = 1001
        
        const val ACTION_START_CALL = "START_CALL"
        const val ACTION_END_CALL = "END_CALL"
        const val ACTION_UPDATE_CALL = "UPDATE_CALL"
        
        const val EXTRA_CALL_ID = "call_id"
        const val EXTRA_CALLER_NAME = "caller_name"
        const val EXTRA_CALL_DURATION = "call_duration"
    }

    private var isCallActive = false
    private var callId: String? = null
    private var callerName: String? = null

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_CALL -> startCallService(intent)
            ACTION_UPDATE_CALL -> updateCallNotification(intent)
            ACTION_END_CALL -> stopCallService()
        }
        return START_STICKY // Restart if killed
    }

    private fun startCallService(intent: Intent) {
        callId = intent.getStringExtra(EXTRA_CALL_ID)
        callerName = intent.getStringExtra(EXTRA_CALLER_NAME) ?: "Unknown"
        isCallActive = true

        val notification = createCallNotification(
            title = "Ongoing Call",
            content = "Call with $callerName",
            isOngoing = true
        )

        startForeground(NOTIFICATION_ID, notification)
    }

    private fun updateCallNotification(intent: Intent) {
        if (!isCallActive) return

        val duration = intent.getStringExtra(EXTRA_CALL_DURATION) ?: "00:00"
        val notification = createCallNotification(
            title = "Call in Progress",
            content = "Call with $callerName • $duration",
            isOngoing = true
        )

        NotificationManagerCompat.from(this)
            .notify(NOTIFICATION_ID, notification)
    }

    private fun stopCallService() {
        isCallActive = false
        stopForeground(true)
        stopSelf()
    }

    private fun createCallNotification(
        title: String,
        content: String,
        isOngoing: Boolean
    ): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra("open_call", true)
            callId?.let { putExtra("call_id", it) }
        }

        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_call)
            .setContentIntent(pendingIntent)
            .setOngoing(isOngoing)
            .setCategory(NotificationCompat.CATEGORY_CALL)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setColorized(true)
            .setColor(0xFF4CAF50.toInt())
            .build()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Call Service",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for ongoing calls"
                setSound(null, null)
                enableVibration(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null
}
```

## 3. Platform Channel Handler

```kotlin
// android/app/src/main/kotlin/com/hopen/app/CallPlatformChannel.kt
package com.hopen.app

import android.content.Context
import android.content.Intent
import io.flutter.plugin.common.MethodChannel

class CallPlatformChannel(private val context: Context) {
    companion object {
        const val CHANNEL_NAME = "com.hopen.call/foreground_service"
    }

    fun handleMethodCall(call: MethodChannel.Call, result: MethodChannel.Result) {
        when (call.method) {
            "startService" -> startForegroundService(call, result)
            "updateService" -> updateForegroundService(call, result)
            "stopService" -> stopForegroundService(result)
            else -> result.notImplemented()
        }
    }

    private fun startForegroundService(call: MethodChannel.Call, result: MethodChannel.Result) {
        try {
            val callId = call.argument<String>("callId")
            val callerName = call.argument<String>("callerName")

            val intent = Intent(context, VoIPForegroundService::class.java).apply {
                action = VoIPForegroundService.ACTION_START_CALL
                putExtra(VoIPForegroundService.EXTRA_CALL_ID, callId)
                putExtra(VoIPForegroundService.EXTRA_CALLER_NAME, callerName)
            }

            context.startForegroundService(intent)
            result.success(true)
        } catch (e: Exception) {
            result.error("SERVICE_ERROR", "Failed to start foreground service", e.message)
        }
    }

    private fun updateForegroundService(call: MethodChannel.Call, result: MethodChannel.Result) {
        try {
            val duration = call.argument<String>("duration")

            val intent = Intent(context, VoIPForegroundService::class.java).apply {
                action = VoIPForegroundService.ACTION_UPDATE_CALL
                putExtra(VoIPForegroundService.EXTRA_CALL_DURATION, duration)
            }

            context.startService(intent)
            result.success(true)
        } catch (e: Exception) {
            result.error("UPDATE_ERROR", "Failed to update service", e.message)
        }
    }

    private fun stopForegroundService(result: MethodChannel.Result) {
        try {
            val intent = Intent(context, VoIPForegroundService::class.java).apply {
                action = VoIPForegroundService.ACTION_END_CALL
            }

            context.startService(intent)
            result.success(true)
        } catch (e: Exception) {
            result.error("STOP_ERROR", "Failed to stop service", e.message)
        }
    }
}
```

## 4. Audio Session Management

```kotlin
// android/app/src/main/kotlin/com/hopen/app/AudioSessionManager.kt
package com.hopen.app

import android.content.Context
import android.media.AudioManager
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.os.Build
import io.flutter.plugin.common.MethodChannel

class AudioSessionManager(private val context: Context) {
    companion object {
        const val CHANNEL_NAME = "com.hopen.call/audio_session"
    }

    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private var audioFocusRequest: AudioFocusRequest? = null

    fun handleMethodCall(call: MethodChannel.Call, result: MethodChannel.Result) {
        when (call.method) {
            "configureVoIPAudio" -> configureVoIPAudio(result)
            "enableBackgroundAudio" -> enableBackgroundAudio(result)
            "restoreForegroundAudio" -> restoreForegroundAudio(result)
            else -> result.notImplemented()
        }
    }

    private fun configureVoIPAudio(result: MethodChannel.Result) {
        try {
            // Set audio mode for VoIP
            audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
            
            // Request audio focus
            requestAudioFocus()
            
            result.success(true)
        } catch (e: Exception) {
            result.error("AUDIO_ERROR", "Failed to configure VoIP audio", e.message)
        }
    }

    private fun enableBackgroundAudio(result: MethodChannel.Result) {
        try {
            // Maintain audio focus for background
            if (audioFocusRequest == null) {
                requestAudioFocus()
            }
            result.success(true)
        } catch (e: Exception) {
            result.error("BACKGROUND_AUDIO_ERROR", "Failed to enable background audio", e.message)
        }
    }

    private fun restoreForegroundAudio(result: MethodChannel.Result) {
        try {
            // Restore normal audio mode
            audioManager.mode = AudioManager.MODE_NORMAL
            abandonAudioFocus()
            result.success(true)
        } catch (e: Exception) {
            result.error("RESTORE_ERROR", "Failed to restore audio", e.message)
        }
    }

    private fun requestAudioFocus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build()

            audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                .setAudioAttributes(audioAttributes)
                .setAcceptsDelayedFocusGain(true)
                .build()

            audioManager.requestAudioFocus(audioFocusRequest!!)
        } else {
            @Suppress("DEPRECATION")
            audioManager.requestAudioFocus(
                null,
                AudioManager.STREAM_VOICE_CALL,
                AudioManager.AUDIOFOCUS_GAIN
            )
        }
    }

    private fun abandonAudioFocus() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            audioFocusRequest?.let { audioManager.abandonAudioFocusRequest(it) }
        } else {
            @Suppress("DEPRECATION")
            audioManager.abandonAudioFocus(null)
        }
        audioFocusRequest = null
    }
}
```

## 5. MainActivity Integration

```kotlin
// android/app/src/main/kotlin/com/hopen/app/MainActivity.kt
class MainActivity: FlutterActivity() {
    private lateinit var callPlatformChannel: CallPlatformChannel
    private lateinit var audioSessionManager: AudioSessionManager

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        callPlatformChannel = CallPlatformChannel(this)
        audioSessionManager = AudioSessionManager(this)

        // Register platform channels
        MethodChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            CallPlatformChannel.CHANNEL_NAME
        ).setMethodCallHandler { call, result ->
            callPlatformChannel.handleMethodCall(call, result)
        }

        MethodChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            AudioSessionManager.CHANNEL_NAME
        ).setMethodCallHandler { call, result ->
            audioSessionManager.handleMethodCall(call, result)
        }
    }
}
```

## Testing Checklist

### Functional Testing
- [ ] Foreground service starts when call begins
- [ ] Notification shows correct call information
- [ ] Service persists during app backgrounding
- [ ] Audio focus maintained during background
- [ ] Service stops when call ends
- [ ] Notification updates with call duration

### Edge Case Testing
- [ ] App killed during call (service continues)
- [ ] Low memory conditions
- [ ] Battery optimization interference
- [ ] Multiple call scenarios
- [ ] Network disconnection during background

### Performance Testing
- [ ] Memory usage during long calls
- [ ] Battery consumption analysis
- [ ] CPU usage monitoring
- [ ] Audio quality in background

## Production Considerations

### Battery Optimization
- Handle manufacturer-specific battery optimization
- Provide user guidance for whitelisting app
- Implement intelligent service management

### Permissions
- Request permissions at appropriate times
- Handle permission denials gracefully
- Provide clear explanations for VoIP permissions

### Error Handling
- Service crash recovery
- Audio focus conflicts
- Notification channel issues
- Platform version compatibility
