# Call Concurrency Implementation

## Overview

This document describes the implementation of distributed locking for handling simultaneous call initiation in the Hopen backend call service. The solution prevents race conditions when multiple users attempt to start a call in the same bubble simultaneously.

## Problem Statement

**Scenario**: Two members in the same bubble tap the "Call" button at almost the same instant.

**Potential Issues**:
- Two separate call instances being created for the same bubble
- One user's request failing without a clear reason
- Database constraint violations
- Inconsistent call state

## Solution Architecture

### 1. Multi-Layer Protection

The implementation uses a defense-in-depth approach with multiple layers of protection:

1. **Database Constraints** (Safety Net)
2. **Distributed Locking** (Primary Protection)
3. **Idempotency Handling** (Request Deduplication)
4. **Graceful Error Handling** (User Experience)

### 2. Database Constraints

**File**: `hopenbackend/migrations/postgresql/004_supporting_features_schema.up.sql`

```sql
-- Unique constraint to prevent multiple active calls per bubble
CREATE UNIQUE INDEX idx_calls_unique_active_bubble
ON calls (bubble_id)
WHERE status IN ('initiated', 'ringing', 'connected') AND bubble_id IS NOT NULL;

-- Unique constraint to prevent multiple active calls per conversation
CREATE UNIQUE INDEX idx_calls_unique_active_conversation
ON calls (conversation_id)
WHERE status IN ('initiated', 'ringing', 'connected') AND conversation_id IS NOT NULL;
```

**Benefits**:
- Prevents duplicate active calls at the database level
- Acts as a final safety net if distributed locking fails
- Ensures data integrity regardless of application logic

### 3. Distributed Locking Implementation

#### Lock Manager (`pkg/distributedlock/manager.go`)

- Implements the **Redlock algorithm** for distributed locking
- Uses Valkey (Redis-compatible) for lock storage
- Provides atomic lock acquisition and release operations
- Includes retry logic and timeout handling

#### Call Lock Manager (`pkg/distributedlock/call_lock.go`)

- Call-specific wrapper around the generic lock manager
- Provides specialized methods for call creation scenarios
- Handles different lock types (bubble calls, conversation calls, LiveKit rooms)

#### Key Features:

```go
// Lock acquisition with retry and timeout
func (clm *CallLockManager) AcquireCallCreationLock(ctx context.Context, bubbleID string, opts *CallLockOptions) (*Lock, error)

// High-level wrapper for call creation with automatic lock management
func (clm *CallLockManager) WithCallCreationLock(ctx context.Context, bubbleID string, opts *CallLockOptions, fn func(context.Context) error) error
```

### 4. Service Implementation

#### Updated StartCall Method

**File**: `hopenbackend/microservices/call/service.go`

The `StartCall` method now follows this flow:

1. **Input Validation**: Validate user ID and bubble ID
2. **Lock Acquisition**: Acquire distributed lock for the bubble
3. **Existing Call Check**: Check if an active call already exists
4. **Decision Logic**:
   - If call exists: Convert to "join existing call" operation
   - If no call exists: Create new call
5. **Lock Release**: Automatic lock release via defer

#### Race Condition Handling

```go
func (s *Service) startCallWithLock(ctx context.Context, req *callv1.StartCallRequest, lockOpts *distributedlock.CallLockOptions) (*callv1.StartCallResponse, error) {
    return s.callLockManager.WithCallCreationLock(ctx, req.BubbleId, lockOpts, func(lockCtx context.Context) error {
        // Check for existing active call
        existingCall, err := s.getActiveCallForBubble(lockCtx, req.BubbleId)
        if err != nil && !errors.Is(err, pgx.ErrNoRows) {
            return err
        }

        if existingCall != nil {
            // Join existing call
            response, err = s.handleExistingCall(lockCtx, req, existingCall)
            return err
        }

        // Create new call
        response, err = s.createNewCall(lockCtx, req)
        return err
    })
}
```

### 5. Lock Acquisition Failure Handling

When lock acquisition fails, the service:

1. **Checks for Existing Call**: Queries the database to see if a call was created by another request
2. **Graceful Fallback**: If a call exists, converts the request to a join operation
3. **Clear Error Messages**: Provides user-friendly error messages for race conditions

### 6. Configuration

#### Lock Settings

```go
// Default lock options
lockOpts := distributedlock.DefaultCallLockOptions()
lockOpts.TTL = 15 * time.Second  // Lock timeout
lockOpts.RetryAttempts = 6       // Number of retry attempts
lockOpts.RetryDelay = 250 * time.Millisecond  // Delay between retries
```

#### Service Dependencies

The call service now requires a `CallLockManager` dependency:

```go
type Dependencies struct {
    // ... other dependencies
    CallLockManager *distributedlock.CallLockManager
}
```

### 7. Error Handling and User Experience

#### Success Scenarios

1. **First Request**: Creates new call, returns call info with access token
2. **Subsequent Requests**: Join existing call, returns same call info

#### Error Scenarios

1. **Lock Timeout**: Returns "Call creation in progress" message
2. **Database Error**: Returns appropriate error with retry suggestion
3. **Service Unavailable**: Returns service unavailable error

### 8. Performance Considerations

#### Lock Efficiency

- **Short Lock Duration**: Locks are held only during critical sections
- **Automatic Cleanup**: Locks have TTL to prevent deadlocks
- **Minimal Overhead**: Lock operations are optimized for low latency

#### Database Performance

- **Indexed Queries**: All active call queries use optimized indexes
- **Partial Indexes**: Constraints only apply to active calls
- **Efficient Lookups**: O(1) lookup time for existing call checks

### 9. Monitoring and Observability

#### Metrics to Monitor

- Lock acquisition success/failure rates
- Lock acquisition duration
- Concurrent call creation attempts
- Database constraint violation rates

#### Logging

- Lock acquisition and release events
- Race condition detection and handling
- Error scenarios with context

### 10. Testing Strategy

#### Unit Tests

- Mock distributed lock manager for isolated testing
- Test race condition scenarios
- Verify error handling paths

#### Integration Tests

- Real Valkey instance for lock testing
- Concurrent request simulation
- End-to-end call creation flow

#### Load Tests

- High concurrency scenarios
- Lock contention under load
- Performance degradation thresholds

## Benefits

1. **Race Condition Prevention**: Eliminates duplicate call creation
2. **Improved User Experience**: Clear error messages and graceful handling
3. **Data Integrity**: Multiple layers of protection
4. **Scalability**: Distributed solution works across multiple service instances
5. **Reliability**: Automatic cleanup and timeout handling
6. **Observability**: Comprehensive logging and monitoring

## Future Enhancements

1. **Circuit Breaker**: Add circuit breaker pattern for lock service failures
2. **Metrics Collection**: Implement detailed metrics for monitoring
3. **Lock Optimization**: Fine-tune lock parameters based on production data
4. **Fallback Strategies**: Additional fallback mechanisms for edge cases
