# Atomic Call Join Implementation

## Overview

This document describes the implementation of atomic call join operations to handle the "joining a call as it ends" scenario. The solution ensures that users cannot join calls that have already ended, providing clear error messages and preventing confusing UI states.

## Problem Statement

**Scenario**: A user tries to join an active call just as the last remaining participant hangs up, causing the call to terminate on the backend.

**Issue**: The joining user's app might optimistically navigate to the BubbleCallPage for a call that no longer exists, leading to connection failure and confusing UI state.

**Solution**: Implement atomic call state validation using distributed locking to ensure call existence before allowing joins.

## Architecture

### Components

1. **Distributed Lock Manager** (`pkg/distributedlock/manager.go`)
   - Provides atomic operations using Valkey (Redis-compatible)
   - Implements Redlock algorithm for distributed coordination

2. **Call Lock Manager** (`pkg/distributedlock/call_lock.go`)
   - Specialized wrapper for call-specific operations
   - Provides `WithCallJoinLock` method for atomic join operations

3. **Enhanced JoinCall Service** (`microservices/call/service.go`)
   - Atomic validation of call state before allowing joins
   - Clear error responses for ended/non-existent calls

### Flow Diagram

```
User Request → JoinCall → Acquire Lock → Validate Call State → Generate Token → Release Lock → Response
                    ↓
                Lock Failed → Return Error
                    ↓
                Call Not Found → Return "CALL_NOT_FOUND"
                    ↓
                Call Ended → Return "This call has already ended"
```

## Implementation Details

### 1. Atomic Call Join Process

```go
func (s *Service) joinCallWithLock(ctx context.Context, req *callv1.JoinCallRequest, lockOpts *distributedlock.CallLockOptions) (*callv1.JoinCallResponse, error) {
    return s.callLockManager.WithCallJoinLock(ctx, req.CallId, lockOpts, func(lockCtx context.Context) error {
        // Step 1: Atomically verify call exists and is active
        call, err := s.getActiveCallByID(lockCtx, req.CallId)
        if err != nil {
            if errors.Is(err, pgx.ErrNoRows) {
                return "CALL_NOT_FOUND: This call has already ended"
            }
            return err
        }
        
        // Step 2: Authorization check
        err = s.ketoClient.AuthorizeCallJoin(lockCtx, req.UserId, call.BubbleID)
        if err != nil {
            return "FORBIDDEN: User not authorized to join this call"
        }
        
        // Step 3: Generate access token and complete join
        return s.completeCallJoin(lockCtx, req, call)
    })
}
```

### 2. Call State Validation

The `getActiveCallByID` method performs atomic validation:

```sql
SELECT call_id, bubble_id, conversation_id, initiator_id, call_type, status, 
       started_at, ended_at, duration, participants, livekit_room
FROM calls 
WHERE call_id = $1 
  AND status IN ('initiated', 'ringing', 'connected')
  AND (ended_at IS NULL OR ended_at > NOW())
```

This query ensures:
- Call exists in database
- Call is in an active state
- Call has not ended (ended_at is NULL or in the future)

### 3. Error Response Format

The implementation returns structured error responses:

```json
{
  "call_info": null,
  "api_response": {
    "success": false,
    "message": "This call has already ended",
    "error_code": "CALL_NOT_FOUND",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "access_token": "",
  "livekit_url": ""
}
```

## Error Codes

| Error Code | Scenario | Message |
|------------|----------|---------|
| `CALL_NOT_FOUND` | Call doesn't exist or has ended | "This call has already ended" |
| `FORBIDDEN` | User not authorized | "User not authorized to join this call" |
| `SERVICE_UNAVAILABLE` | Lock manager unavailable | "Call locking service not available" |
| `CALL_JOIN_FAILED` | Lock acquisition failed | "Failed to join call due to concurrent access" |

## Frontend Integration

The frontend should handle these error codes appropriately:

```dart
try {
  final response = await callRepository.joinCall(
    bubbleId: bubbleId,
    callId: callId,
    withVideo: withVideo,
    withScreenShare: withScreenShare,
  );
  
  // Handle successful join
  navigateToCallPage(response);
  
} catch (e) {
  if (e.errorCode == 'CALL_NOT_FOUND') {
    // Show toast: "This call has already ended"
    showErrorToast("This call has already ended");
    // Don't navigate to call page
    return;
  }
  
  // Handle other errors
  showErrorToast("Failed to join call: ${e.message}");
}
```

## Performance Considerations

### Lock Configuration

```go
lockOpts := distributedlock.DefaultCallLockOptions()
lockOpts.TTL = 10 * time.Second  // Shorter TTL for join operations
lockOpts.RetryDelay = 50 * time.Millisecond
lockOpts.MaxRetries = 5
lockOpts.AcquireTimeout = 3 * time.Second
```

### Database Optimization

- Uses existing database indexes on `call_id` and `status`
- Query is optimized for fast execution
- No additional table scans required

## Testing

### Test Scenarios

1. **Join Non-Existent Call**
   - Verify `CALL_NOT_FOUND` error returned
   - Ensure no gRPC error thrown

2. **Join Call That Just Ended**
   - Simulate call ending during join process
   - Verify atomic validation catches the state change

3. **Concurrent Join Attempts**
   - Multiple users joining same call simultaneously
   - Verify proper lock coordination

4. **Authorization Failure**
   - User without permission tries to join
   - Verify `FORBIDDEN` error returned

### Running Tests

```bash
cd hopenbackend
go test ./microservices/call/... -v -run TestAtomicCallJoin
```

## Monitoring and Observability

### Metrics to Track

- Call join success/failure rates
- Lock acquisition times
- Error code distribution
- Call state validation performance

### Logging

The implementation includes comprehensive logging:

```go
s.logger.Warn("Call not found or has ended",
    zap.String("call_id", req.CallId),
    zap.String("user_id", req.UserId))
```

## Security Considerations

1. **Authorization**: Every join attempt is validated against Ory Keto
2. **Rate Limiting**: Existing rate limiting applies to join operations
3. **Input Validation**: All inputs are validated before processing
4. **Audit Trail**: All join attempts are logged for security monitoring

## Future Enhancements

1. **Call State Caching**: Cache active call states in Valkey for faster validation
2. **Predictive Join**: Detect when calls are about to end and prevent new joins
3. **Graceful Degradation**: Fallback mechanisms when distributed locking fails
4. **Real-time Notifications**: Notify users when calls end while they're joining

## Conclusion

This implementation provides robust protection against the "joining a call as it ends" scenario through:

- Atomic call state validation using distributed locking
- Clear error messages for different failure scenarios
- Proper integration with existing authorization and rate limiting
- Comprehensive testing and monitoring capabilities

The solution ensures users receive clear feedback when calls are no longer available, preventing confusing UI states and improving overall user experience.
