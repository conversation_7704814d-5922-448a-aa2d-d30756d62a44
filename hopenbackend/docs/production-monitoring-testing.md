# Production Monitoring & Testing Guide

## Overview
Comprehensive monitoring and testing strategy for VoIP call system in production.

## 1. Backend Monitoring

### Metrics Collection
```go
// hopenbackend/pkg/monitoring/call_metrics.go
package monitoring

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    // Call lifecycle metrics
    CallsInitiated = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hopen_calls_initiated_total",
            Help: "Total number of calls initiated",
        },
        []string{"bubble_id", "call_type", "user_id"},
    )

    CallsConnected = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hopen_calls_connected_total",
            Help: "Total number of calls successfully connected",
        },
        []string{"bubble_id", "call_type"},
    )

    CallsEnded = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hopen_calls_ended_total",
            Help: "Total number of calls ended",
        },
        []string{"bubble_id", "call_type", "end_reason"},
    )

    CallDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "hopen_call_duration_seconds",
            Help: "Duration of calls in seconds",
            Buckets: []float64{5, 10, 30, 60, 300, 600, 1800, 3600},
        },
        []string{"call_type"},
    )

    // Edge case metrics
    ConcurrentCallAttempts = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hopen_concurrent_call_attempts_total",
            Help: "Total number of concurrent call attempts detected",
        },
        []string{"bubble_id", "resolution"},
    )

    StaleCallsDetected = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hopen_stale_calls_detected_total",
            Help: "Total number of stale calls detected and cleaned up",
        },
        []string{"cleanup_reason"},
    )

    BubbleLifecycleCallTerminations = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hopen_bubble_lifecycle_call_terminations_total",
            Help: "Total number of calls terminated due to bubble lifecycle events",
        },
        []string{"lifecycle_event", "termination_success"},
    )

    // Performance metrics
    CallSetupLatency = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "hopen_call_setup_latency_seconds",
            Help: "Time taken to set up a call",
            Buckets: []float64{0.1, 0.5, 1, 2, 5, 10},
        },
        []string{"call_type"},
    )

    LiveKitOperationLatency = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "hopen_livekit_operation_latency_seconds",
            Help: "Latency of LiveKit operations",
            Buckets: []float64{0.05, 0.1, 0.25, 0.5, 1, 2},
        },
        []string{"operation"},
    )

    // Error metrics
    CallErrors = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hopen_call_errors_total",
            Help: "Total number of call errors",
        },
        []string{"error_type", "operation"},
    )

    JetStreamEventProcessingErrors = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hopen_jetstream_event_processing_errors_total",
            Help: "Total number of JetStream event processing errors",
        },
        []string{"event_type", "error_category"},
    )
)
```

### Alerting Rules
```yaml
# hopenbackend/monitoring/alerts/call-system.yml
groups:
  - name: call-system
    rules:
      # High error rate
      - alert: HighCallErrorRate
        expr: rate(hopen_call_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High call error rate detected"
          description: "Call error rate is {{ $value }} errors/sec"

      # Call setup latency
      - alert: HighCallSetupLatency
        expr: histogram_quantile(0.95, rate(hopen_call_setup_latency_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High call setup latency"
          description: "95th percentile call setup latency is {{ $value }}s"

      # Stale call detection
      - alert: HighStaleCallRate
        expr: rate(hopen_stale_calls_detected_total[10m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High rate of stale calls detected"
          description: "Stale call detection rate is {{ $value }} calls/sec"

      # JetStream processing issues
      - alert: JetStreamProcessingErrors
        expr: rate(hopen_jetstream_event_processing_errors_total[5m]) > 0.01
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "JetStream event processing errors"
          description: "JetStream error rate is {{ $value }} errors/sec"

      # LiveKit connectivity
      - alert: LiveKitHighLatency
        expr: histogram_quantile(0.95, rate(hopen_livekit_operation_latency_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High LiveKit operation latency"
          description: "95th percentile LiveKit latency is {{ $value }}s"
```

## 2. Frontend Monitoring

### Flutter Performance Monitoring
```dart
// hopen/lib/monitoring/call_performance_monitor.dart
import 'package:flutter/foundation.dart';
import 'package:firebase_performance/firebase_performance.dart';

class CallPerformanceMonitor {
  static final FirebasePerformance _performance = FirebasePerformance.instance;
  
  static final Map<String, Trace> _activeTraces = {};

  /// Start monitoring call initiation
  static Future<void> startCallInitiation(String callId) async {
    final trace = _performance.newTrace('call_initiation');
    trace.putAttribute('call_id', callId);
    await trace.start();
    _activeTraces['call_initiation_$callId'] = trace;
  }

  /// Stop monitoring call initiation
  static Future<void> stopCallInitiation(String callId, {bool success = true}) async {
    final trace = _activeTraces.remove('call_initiation_$callId');
    if (trace != null) {
      trace.putAttribute('success', success.toString());
      await trace.stop();
    }
  }

  /// Monitor call duration
  static Future<void> startCallDuration(String callId) async {
    final trace = _performance.newTrace('call_duration');
    trace.putAttribute('call_id', callId);
    await trace.start();
    _activeTraces['call_duration_$callId'] = trace;
  }

  /// Stop monitoring call duration
  static Future<void> stopCallDuration(String callId, Duration duration) async {
    final trace = _activeTraces.remove('call_duration_$callId');
    if (trace != null) {
      trace.putAttribute('duration_seconds', duration.inSeconds.toString());
      await trace.stop();
    }
  }

  /// Monitor platform channel operations
  static Future<T> monitorPlatformOperation<T>(
    String operation,
    Future<T> Function() operation_func,
  ) async {
    final trace = _performance.newTrace('platform_channel_$operation');
    await trace.start();
    
    try {
      final result = await operation_func();
      trace.putAttribute('success', 'true');
      return result;
    } catch (e) {
      trace.putAttribute('success', 'false');
      trace.putAttribute('error', e.toString());
      rethrow;
    } finally {
      await trace.stop();
    }
  }

  /// Log custom metrics
  static void logCustomMetric(String name, double value, {Map<String, String>? attributes}) {
    if (kDebugMode) {
      debugPrint('Custom Metric: $name = $value, attributes: $attributes');
    }
    
    // In production, send to analytics service
    // FirebaseAnalytics.instance.logEvent(
    //   name: 'custom_metric',
    //   parameters: {
    //     'metric_name': name,
    //     'metric_value': value,
    //     ...?attributes,
    //   },
    // );
  }
}
```

## 3. Comprehensive Testing Strategy

### Backend Integration Tests
```go
// hopenbackend/microservices/call/integration_test.go
package call_test

import (
    "context"
    "testing"
    "time"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
)

type CallIntegrationTestSuite struct {
    suite.Suite
    callService *CallService
    testBubbleID string
    testUserID string
}

func (suite *CallIntegrationTestSuite) TestConcurrentCallInitiation() {
    // Test concurrent call initiation edge case
    ctx := context.Background()
    
    // Start two concurrent calls
    call1Ch := make(chan error, 1)
    call2Ch := make(chan error, 1)
    
    go func() {
        _, err := suite.callService.InitiateCall(ctx, &InitiateCallRequest{
            BubbleId: suite.testBubbleID,
            UserId: suite.testUserID,
            WithVideo: true,
        })
        call1Ch <- err
    }()
    
    go func() {
        _, err := suite.callService.InitiateCall(ctx, &InitiateCallRequest{
            BubbleId: suite.testBubbleID,
            UserId: suite.testUserID,
            WithVideo: false,
        })
        call2Ch <- err
    }()
    
    // Wait for both calls to complete
    err1 := <-call1Ch
    err2 := <-call2Ch
    
    // One should succeed, one should fail
    assert.True(suite.T(), (err1 == nil) != (err2 == nil), "Exactly one call should succeed")
}

func (suite *CallIntegrationTestSuite) TestBubbleLifecycleCallTermination() {
    // Test bubble lifecycle event during call
    ctx := context.Background()
    
    // Start a call
    callResp, err := suite.callService.InitiateCall(ctx, &InitiateCallRequest{
        BubbleId: suite.testBubbleID,
        UserId: suite.testUserID,
        WithVideo: true,
    })
    assert.NoError(suite.T(), err)
    
    // Simulate bubble expiration event
    err = suite.callService.handleBubbleLifecycleEnd(ctx, BubbleLifecycleEvent{
        BubbleID: suite.testBubbleID,
        EventType: "bubble.expired",
        Timestamp: time.Now(),
    })
    assert.NoError(suite.T(), err)
    
    // Verify call was terminated
    time.Sleep(100 * time.Millisecond) // Allow async processing
    
    callStatus, err := suite.callService.GetCallStatus(ctx, &GetCallStatusRequest{
        CallId: callResp.CallId,
    })
    assert.NoError(suite.T(), err)
    assert.Equal(suite.T(), "ended", callStatus.Status)
}

func (suite *CallIntegrationTestSuite) TestStaleCallCleanup() {
    // Test stale call detection and cleanup
    ctx := context.Background()
    
    // Create a stale call entry
    staleCallID := "stale-call-" + time.Now().Format("20060102150405")
    
    // Insert stale call directly into Valkey
    err := suite.callService.valkeyClient.Set(ctx, 
        "call:"+staleCallID, 
        `{"id":"`+staleCallID+`","bubble_id":"`+suite.testBubbleID+`","status":"active","created_at":"`+time.Now().Add(-2*time.Hour).Format(time.RFC3339)+`"}`,
        time.Hour,
    ).Err()
    assert.NoError(suite.T(), err)
    
    // Run garbage collection
    err = suite.callService.runStaleCallGarbageCollection(ctx)
    assert.NoError(suite.T(), err)
    
    // Verify stale call was cleaned up
    exists := suite.callService.valkeyClient.Exists(ctx, "call:"+staleCallID).Val()
    assert.Equal(suite.T(), int64(0), exists)
}

func TestCallIntegrationSuite(t *testing.T) {
    suite.Run(t, new(CallIntegrationTestSuite))
}
```

### Frontend Widget Tests
```dart
// hopen/test/widget/call_provider_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:riverpod/riverpod.dart';

import 'package:hopen/statefulbusinesslogic/riverpod/call/call_provider.dart';

class MockCallRepository extends Mock implements CallRepository {}
class MockToastService extends Mock implements ToastService {}
class MockPlatformCallHandler extends Mock implements PlatformCallHandler {}

void main() {
  group('CallProvider Edge Cases', () {
    late ProviderContainer container;
    late MockCallRepository mockCallRepo;
    late MockToastService mockToastService;
    late MockPlatformCallHandler mockPlatformHandler;

    setUp(() {
      mockCallRepo = MockCallRepository();
      mockToastService = MockToastService();
      mockPlatformHandler = MockPlatformCallHandler();
      
      container = ProviderContainer(
        overrides: [
          callRepositoryModuleProvider.overrideWithValue(mockCallRepo),
          toastServiceModuleProvider.overrideWithValue(mockToastService),
          platformCallHandlerModuleProvider.overrideWithValue(mockPlatformHandler),
        ],
      );
    });

    testWidgets('handles concurrent call initiation', (tester) async {
      // Setup mock responses
      when(mockCallRepo.initiateCall(any)).thenAnswer((_) async => 
        CallInitiationResult(callId: 'test-call-1', success: true));
      
      final provider = container.read(callControllerProvider.notifier);
      
      // Initiate two calls concurrently
      final future1 = provider.initiateCall(
        targetId: 'user1',
        isGroup: false,
        withVideo: true,
      );
      
      final future2 = provider.initiateCall(
        targetId: 'user2', 
        isGroup: false,
        withVideo: false,
      );
      
      await Future.wait([future1, future2]);
      
      // Verify only one call was actually initiated
      verify(mockCallRepo.initiateCall(any)).called(1);
    });

    testWidgets('handles push notification direct join', (tester) async {
      when(mockCallRepo.joinCall(any)).thenAnswer((_) async => 
        CallJoinResult(success: true, callId: 'incoming-call-1'));
      
      final provider = container.read(callControllerProvider.notifier);
      
      await provider.handleIncomingCallFromPush(
        payload: {
          'call_id': 'incoming-call-1',
          'bubble_id': 'bubble-1',
          'caller_id': 'caller-1',
          'caller_name': 'John Doe',
          'is_video': true,
        },
        shouldJoinDirectly: true,
      );
      
      // Verify call was joined
      verify(mockCallRepo.joinCall(any)).called(1);
      
      // Verify state is updated
      final state = container.read(callControllerProvider);
      expect(state.value, isA<CallDialing>());
    });

    testWidgets('handles background call validation', (tester) async {
      when(mockCallRepo.validateCall(any)).thenAnswer((_) async => false);
      
      final provider = container.read(callControllerProvider.notifier);
      
      // Set up active call state
      container.read(callControllerProvider.notifier).state = 
        AsyncData(CallActive(callId: 'test-call', bubbleId: 'bubble-1'));
      
      // Simulate app returning from background
      await provider.validateCallAfterBackground('bubble-1');
      
      // Verify call was validated
      verify(mockCallRepo.validateCall('test-call')).called(1);
      
      // Verify warning toast was shown
      verify(mockToastService.showWarningToast(any)).called(1);
    });
  });
}
```

## 4. Load Testing

### Backend Load Tests
```bash
#!/bin/bash
# hopenbackend/scripts/load-test-calls.sh

# Test concurrent call initiation
echo "Testing concurrent call initiation..."
for i in {1..50}; do
  grpcurl -plaintext -d '{
    "bubble_id": "load-test-bubble-'$i'",
    "user_id": "load-test-user-'$i'",
    "with_video": true
  }' localhost:50051 call.v1.CallService/InitiateCall &
done
wait

# Test call lifecycle under load
echo "Testing call lifecycle under load..."
for i in {1..20}; do
  (
    # Initiate call
    CALL_ID=$(grpcurl -plaintext -d '{
      "bubble_id": "lifecycle-test-'$i'",
      "user_id": "user-'$i'",
      "with_video": false
    }' localhost:50051 call.v1.CallService/InitiateCall | jq -r '.call_id')
    
    # Wait random time
    sleep $((RANDOM % 10 + 5))
    
    # End call
    grpcurl -plaintext -d '{
      "call_id": "'$CALL_ID'"
    }' localhost:50051 call.v1.CallService/EndCall
  ) &
done
wait

echo "Load testing completed"
```

## 5. Production Deployment Checklist

### Pre-deployment
- [ ] All unit tests passing
- [ ] Integration tests passing
- [ ] Load tests completed successfully
- [ ] Security audit completed
- [ ] Performance benchmarks met
- [ ] Monitoring dashboards configured
- [ ] Alerting rules tested
- [ ] Rollback plan prepared

### Platform-specific
- [ ] Android: Foreground service tested on multiple devices
- [ ] Android: Battery optimization handling verified
- [ ] iOS: CallKit integration tested with TestFlight
- [ ] iOS: VoIP push notifications working
- [ ] Both: Audio quality verified in various scenarios
- [ ] Both: Background call handling tested

### Infrastructure
- [ ] NATS JetStream cluster configured
- [ ] Valkey cluster configured with persistence
- [ ] LiveKit cluster scaled appropriately
- [ ] Database connection pooling optimized
- [ ] CDN configured for media delivery
- [ ] Load balancers configured

### Monitoring
- [ ] Prometheus metrics collection active
- [ ] Grafana dashboards deployed
- [ ] Alert manager configured
- [ ] Log aggregation working
- [ ] Error tracking active
- [ ] Performance monitoring enabled

This comprehensive monitoring and testing strategy ensures production readiness with robust observability and quality assurance.
