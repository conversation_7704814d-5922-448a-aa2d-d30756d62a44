# Call Platform Integration Guide

## Overview

This document provides implementation guidance for the platform-specific integrations required to complete the call edge case handling, particularly for VoIP background execution and push notification handling.

## Flutter Platform Channels

### 1. Android Foreground Service Integration

**Channel**: `com.hopen.call/foreground_service`

**Required Methods**:
```dart
// Start foreground service for active call
await platform.invokeMethod('startService', {
  'title': 'Ongoing Call',
  'body': 'Tap to return to call',
  'channelId': 'call_service',
  'importance': 'high',
  'callId': callId,
  'bubbleId': bubbleId,
});

// Update service notification
await platform.invokeMethod('updateService', {
  'title': 'Call in Progress',
  'body': 'Tap to return to call',
});

// Stop foreground service
await platform.invokeMethod('stopService');
```

**Android Implementation Requirements**:
- Extend `ForegroundService` class
- Implement `FOREGROUND_SERVICE` permission
- Create notification channel for call service
- Handle service lifecycle (start/stop/update)
- Integrate with Flutter activity for call return

### 2. iOS CallKit Integration

**Channel**: `com.hopen.call/callkit`

**Required Methods**:
```dart
// Configure CallKit provider
await platform.invokeMethod('configureProvider', {
  'localizedName': 'Hopen',
  'maximumCallGroups': 1,
  'maximumCallsPerCallGroup': 1,
  'supportsVideo': true,
});

// Report incoming call
await platform.invokeMethod('reportIncomingCall', {
  'callId': callId,
  'callerName': callerName,
  'hasVideo': isVideo,
});

// Report call ended
await platform.invokeMethod('reportCallEnded', {
  'callId': callId,
  'reason': 'remoteEnded', // or 'failed', 'unanswered'
});
```

**iOS Implementation Requirements**:
- Import `CallKit` framework
- Implement `CXProviderDelegate`
- Configure `Info.plist` for VoIP background modes
- Handle CallKit actions (answer, end, mute, etc.)
- Integrate with Flutter for call state synchronization

### 3. Audio Session Management

**Android Channel**: `com.hopen.call/audio_session`
**iOS Channel**: `com.hopen.call/audio_session`

**Android Methods**:
```dart
// Configure audio for VoIP
await platform.invokeMethod('configureVoIPAudio', {
  'mode': 'MODE_IN_COMMUNICATION',
  'enableSpeaker': false,
  'enableBluetooth': true,
});

// Enable background audio
await platform.invokeMethod('enableBackgroundAudio');

// Restore foreground audio
await platform.invokeMethod('restoreForegroundAudio');
```

**iOS Methods**:
```dart
// Configure audio session
await platform.invokeMethod('configureAudioSession', {
  'category': 'playAndRecord',
  'mode': 'voiceChat',
  'options': ['allowBluetooth', 'allowBluetoothA2DP'],
});

// Enable background audio
await platform.invokeMethod('enableBackgroundAudio');

// Restore foreground audio
await platform.invokeMethod('restoreForegroundAudio');
```

## Push Notification Integration

### 1. Firebase Cloud Messaging Setup

**Payload Structure**:
```json
{
  "notification": {
    "title": "Incoming Call",
    "body": "John Doe is calling..."
  },
  "data": {
    "type": "incoming_call",
    "call_id": "uuid-call-id",
    "bubble_id": "uuid-bubble-id",
    "caller_id": "uuid-caller-id",
    "caller_name": "John Doe",
    "is_video": "true",
    "call_type": "video",
    "action": "incoming_call",
    "priority": "high",
    "ttl": "30"
  },
  "android": {
    "priority": "high",
    "ttl": "30s",
    "notification": {
      "channel_id": "call_notifications",
      "priority": "high",
      "visibility": "public"
    }
  },
  "apns": {
    "headers": {
      "apns-priority": "10",
      "apns-expiration": "30"
    },
    "payload": {
      "aps": {
        "alert": {
          "title": "Incoming Call",
          "body": "John Doe is calling..."
        },
        "sound": "call_ringtone.caf",
        "category": "CALL_CATEGORY"
      }
    }
  }
}
```

### 2. Flutter Integration

**Message Handling**:
```dart
// Handle foreground messages
FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  if (message.data['type'] == 'incoming_call') {
    ref.read(callControllerProvider.notifier).handleIncomingCallFromPush(
      payload: message.data,
      shouldJoinDirectly: false,
    );
  }
});

// Handle background/terminated app messages
FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
  if (message.data['type'] == 'incoming_call') {
    ref.read(callControllerProvider.notifier).handleIncomingCallFromPush(
      payload: message.data,
      shouldJoinDirectly: true,
    );
  }
});
```

## Backend Event Publishing

### 1. Enhanced Call Events

**Call Started Event with Push Notification**:
```go
// Publish call started event
callEvent := events.CallStartedEvent{
    BaseEvent: events.BaseEvent{
        ID:        uuid.New().String(),
        Type:      string(events.EventTypeCallStarted),
        Timestamp: time.Now().Unix(),
        Source:    "call-service",
    },
    Call: struct {
        ID       string `json:"id"`
        CallerID string `json:"caller_id"`
        CalleeID string `json:"callee_id"`
        CallType string `json:"call_type"`
        BubbleID string `json:"bubble_id,omitempty"`
    }{
        ID:       callID,
        CallerID: req.UserId,
        CalleeID: targetUserID,
        CallType: req.CallType.String(),
        BubbleID: req.BubbleId,
    },
}

// Publish push notification event
pushEvent := events.CallPushNotificationEvent{
    BaseEvent: events.BaseEvent{
        ID:        uuid.New().String(),
        Type:      string(events.EventTypeCallPushNotification),
        Timestamp: time.Now().Unix(),
        Source:    "call-service",
    },
    CallID:     callID,
    BubbleID:   req.BubbleId,
    CallerID:   req.UserId,
    CallerName: callerName, // From user service
    IsVideo:    req.CallType == callv1.CallType_VIDEO,
    CallType:   req.CallType.String(),
    Action:     "incoming_call",
    Priority:   "high",
    TTL:        30,
}
```

### 2. Notification Service Integration

**NATS to FCM Bridge**:
```go
// Subscribe to push notification events
func (ns *NotificationService) subscribeToPushEvents() {
    ns.subscriber.SubscribeToEvent(events.EventTypeCallPushNotification, 
        ns.handleCallPushNotification)
}

func (ns *NotificationService) handleCallPushNotification(data []byte) error {
    var event events.CallPushNotificationEvent
    if err := events.DeserializeEvent(data, &event); err != nil {
        return err
    }

    // Send FCM notification
    return ns.fcmClient.SendNotification(ctx, &fcm.Message{
        Token: userFCMToken,
        Notification: &fcm.Notification{
            Title: "Incoming Call",
            Body:  fmt.Sprintf("%s is calling...", event.CallerName),
        },
        Data: map[string]string{
            "type":        "incoming_call",
            "call_id":     event.CallID,
            "bubble_id":   event.BubbleID,
            "caller_id":   event.CallerID,
            "caller_name": event.CallerName,
            "is_video":    strconv.FormatBool(event.IsVideo),
            "call_type":   event.CallType,
            "action":      event.Action,
            "priority":    event.Priority,
            "ttl":         strconv.Itoa(event.TTL),
        },
        Android: &fcm.AndroidConfig{
            Priority: "high",
            TTL:      time.Duration(event.TTL) * time.Second,
        },
        APNS: &fcm.APNSConfig{
            Headers: map[string]string{
                "apns-priority":    "10",
                "apns-expiration":  strconv.FormatInt(time.Now().Add(time.Duration(event.TTL)*time.Second).Unix(), 10),
            },
        },
    })
}
```

## Implementation Checklist

### Android
- [ ] Implement foreground service for VoIP
- [ ] Configure audio session for background calls
- [ ] Set up notification channels for call notifications
- [ ] Implement platform channel handlers
- [ ] Add required permissions to AndroidManifest.xml
- [ ] Test background call functionality

### iOS
- [ ] Implement CallKit integration
- [ ] Configure VoIP background modes in Info.plist
- [ ] Set up audio session for background calls
- [ ] Implement platform channel handlers
- [ ] Add VoIP push notification entitlements
- [ ] Test CallKit integration and background audio

### Flutter
- [ ] Implement platform channel communication
- [ ] Handle push notification payloads
- [ ] Integrate with call state management
- [ ] Test foreground/background transitions
- [ ] Verify call recovery mechanisms
- [ ] Test push notification direct join

### Backend
- [ ] Enhance call events with push notification data
- [ ] Implement notification service integration
- [ ] Set up NATS to FCM bridge
- [ ] Test event publishing and delivery
- [ ] Monitor push notification success rates
- [ ] Implement retry logic for failed notifications

## Testing Strategy

### Unit Tests
- Platform channel method calls
- Push notification payload parsing
- Audio session state management
- Call state transitions

### Integration Tests
- End-to-end call flow with push notifications
- Background/foreground transitions during calls
- CallKit integration on iOS
- Foreground service behavior on Android

### Manual Testing
- Incoming call notifications (app closed/background/foreground)
- Background call quality and connection stability
- Platform-specific UI integration (CallKit, notifications)
- Audio routing and session management

---

**Status**: Implementation guide complete
**Next Steps**: Platform-specific native code implementation
**Priority**: High (required for production VoIP experience)
