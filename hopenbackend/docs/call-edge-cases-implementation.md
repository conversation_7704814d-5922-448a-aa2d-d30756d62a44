# Call Edge Cases Implementation

This document describes the implementation of two critical edge cases in the Hopen call system:

1. **User Kicked from Bubble During a Call** - Forceful removal of kicked users from active calls
2. **Stale UI State** - Proactive validation to prevent UI/backend state mismatches

## Edge Case 1: User Kicked from <PERSON><PERSON><PERSON> During a Call

### Problem Statement
When a user is an active participant in a bubble call while other members vote to kick them out, the kicked user could remain in the call, able to communicate with members of a bubble they no longer belong to. This is a significant social and privacy issue.

### Solution Architecture

#### Backend Implementation

**Event-Driven Architecture:**
- Bubble Service publishes `bubble.member_left` events when users are kicked
- Call Service subscribes to these events and handles forceful removal
- LiveKit integration for immediate participant disconnection

**Key Components:**

1. **Event Subscription** (`microservices/call/service.go`)
   ```go
   func (s *Service) subscribeToBubbleEvents() {
       _, err := s.natsConn.Subscribe("bubble.member_left", s.handleBubbleMemberLeftEvent)
   }
   ```

2. **Event Handler**
   ```go
   func (s *Service) handleBubbleMemberLeftEvent(msg *nats.Msg) {
       var event BubbleMemberLeftEvent
       // Only handle kicked users (not voluntary leaves)
       if event.Reason == "kicked" {
           s.handleUserKickedFromBubble(ctx, event.BubbleID, event.UserID)
       }
   }
   ```

3. **Forceful Removal Logic**
   ```go
   func (s *Service) handleUserKickedFromBubble(ctx context.Context, bubbleID, userID string) error {
       // 1. Find active calls in the bubble
       // 2. Check if kicked user is a participant
       // 3. Remove from LiveKit room
       // 4. Update database
       // 5. Publish removal event
   }
   ```

**Event Flow:**
```
Bubble Service (Kick Vote) → NATS Event → Call Service → LiveKit Removal → Database Update → MQTT Notification
```

#### Frontend Integration

The frontend receives MQTT notifications about call removal and updates the UI accordingly.

### Testing

Comprehensive test coverage includes:
- Kicked user removal from active calls
- Voluntary leave events (should not trigger removal)
- Error handling for edge cases

## Edge Case 2: Stale UI State

### Problem Statement
User's UI indicates an active call ("Join Call" is visible), but the call has already ended due to delayed or missed MQTT messages from brief network issues.

### Solution Architecture

#### Backend Implementation

**New gRPC Method:**
```protobuf
rpc ValidateCallState(ValidateCallStateRequest) returns (ValidateCallStateResponse);

message ValidateCallStateRequest {
  string bubble_id = 1;
  string user_id = 2;
}

message ValidateCallStateResponse {
  bool has_active_call = 1;
  CallInfo call_info = 2;
  hopen.common.v1.ApiResponse api_response = 3;
}
```

**Implementation** (`microservices/call/service.go`):
```go
func (s *Service) ValidateCallState(ctx context.Context, req *callv1.ValidateCallStateRequest) (*callv1.ValidateCallStateResponse, error) {
    // 1. Validate input parameters
    // 2. Check for active call in bubble
    // 3. Verify user authorization
    // 4. Return call state with detailed info
}
```

#### Frontend Implementation

**Proactive Validation** (`call_provider.dart`):

1. **App Lifecycle Integration:**
   - Detects foreground/background state changes
   - Triggers immediate validation when app resumes
   - Adjusts validation frequency based on app state

2. **Periodic Validation:**
   - Foreground: Every 30 seconds
   - Background: Every 2 minutes
   - Configurable and can be disabled

3. **Bubble Registration:**
   ```dart
   void registerBubbleForValidation(String bubbleId)
   void unregisterBubbleFromValidation(String bubbleId)
   ```

4. **Validation Logic:**
   ```dart
   Future<CallStateValidationResult> validateBubbleCallState(String bubbleId, String userId)
   ```

5. **Stale State Handling:**
   ```dart
   void _handleStaleUIState(String bubbleId) {
       // Update UI state to reflect actual call state
       // Show user-friendly notification
       // Clean up resources
   }
   ```

**Validation Flow:**
```
App Lifecycle Change → Immediate Validation → Backend Check → UI State Update → User Notification
```

### Key Features

#### Intelligent Validation
- **Context-Aware:** Only validates bubbles that are actively monitored
- **Efficient:** Concurrent validation of multiple bubbles
- **Resilient:** Graceful error handling and fallback

#### User Experience
- **Seamless:** Automatic state synchronization without user intervention
- **Informative:** Clear notifications when state mismatches are detected
- **Non-Intrusive:** Subtle UI updates that don't disrupt user workflow

#### Performance Optimized
- **Adaptive Frequency:** Reduces validation in background to save battery
- **Minimal Network:** Only validates when necessary
- **Resource Conscious:** Proper cleanup and timer management

## Integration Points

### NATS/JetStream Events
- `bubble.member_left` - Bubble member removal events
- `call.participant_removed` - Call participant removal notifications

### MQTT Notifications
- Real-time updates to clients about call state changes
- Fallback mechanism when MQTT messages are missed

### Database Consistency
- Atomic operations ensure data integrity
- Proper cleanup of participant records
- Audit trail for security and debugging

## Security Considerations

### Authorization
- User authorization verified before call state validation
- Kicked users cannot access call information after removal
- Proper permission checks for all operations

### Privacy
- Immediate disconnection prevents unauthorized communication
- Event logs for audit and compliance
- Secure event transmission via NATS

## Monitoring and Observability

### Logging
- Comprehensive logging for all edge case scenarios
- Structured logs for easy monitoring and alerting
- Performance metrics for validation operations

### Metrics
- Validation success/failure rates
- Response times for state validation
- Event processing latency

### Alerts
- Failed forceful removals
- High validation failure rates
- Event processing delays

## Configuration

### Backend Configuration
```yaml
call_service:
  validation:
    enabled: true
    timeout: 10s
  events:
    bubble_subscription: "bubble.member_left"
```

### Frontend Configuration
```dart
// Validation intervals
const foregroundValidationInterval = Duration(seconds: 30);
const backgroundValidationInterval = Duration(minutes: 2);

// Enable/disable validation
callController.setValidationEnabled(true);
```

## Best Practices

1. **Event Handling:** Always handle events asynchronously to prevent blocking
2. **Error Recovery:** Implement retry logic with exponential backoff
3. **Resource Management:** Proper cleanup of timers and subscriptions
4. **User Feedback:** Provide clear, actionable feedback to users
5. **Testing:** Comprehensive test coverage for all edge cases

## Future Enhancements

1. **Predictive Validation:** Use ML to predict when validation is most needed
2. **Smart Intervals:** Dynamic adjustment of validation frequency based on network conditions
3. **Enhanced Notifications:** Rich notifications with call context and actions
4. **Cross-Platform Sync:** Synchronize validation state across multiple devices

This implementation ensures robust handling of edge cases while maintaining excellent user experience and system reliability.
