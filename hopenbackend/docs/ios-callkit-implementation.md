# iOS CallKit Integration Implementation Guide

## Overview
Implementation guide for iOS CallKit integration to provide native VoIP experience.

## 1. iOS Configuration

### Info.plist Configuration
```xml
<!-- ios/Runner/Info.plist -->
<dict>
    <!-- VoIP Background Mode -->
    <key>UIBackgroundModes</key>
    <array>
        <string>voip</string>
        <string>audio</string>
    </array>
    
    <!-- Microphone Permission -->
    <key>NSMicrophoneUsageDescription</key>
    <string><PERSON><PERSON> needs microphone access for voice calls</string>
    
    <!-- CallKit Support -->
    <key>CXCallDirectoryExtensionSupported</key>
    <true/>
</dict>
```

### Entitlements
```xml
<!-- ios/Runner/Runner.entitlements -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.developer.pushkit.unrestricted-voip</key>
    <true/>
</dict>
</plist>
```

## 2. CallKit Manager Implementation

```swift
// ios/Runner/CallKitManager.swift
import Foundation
import CallKit
import AVFoundation

class CallKitManager: NSObject {
    static let shared = CallKitManager()
    
    private let callController = CXCallController()
    private let provider: CXProvider
    
    private var activeCallUUIDs: Set<UUID> = []
    private var callIdToUUID: [String: UUID] = [:]
    
    override init() {
        let configuration = CXProviderConfiguration(localizedName: "Hopen")
        configuration.supportsVideo = true
        configuration.maximumCallGroups = 1
        configuration.maximumCallsPerCallGroup = 1
        configuration.supportedHandleTypes = [.generic]
        configuration.iconTemplateImageData = UIImage(named: "CallKitIcon")?.pngData()
        
        provider = CXProvider(configuration: configuration)
        
        super.init()
        
        provider.setDelegate(self, queue: nil)
    }
    
    // MARK: - Incoming Call
    func reportIncomingCall(
        callId: String,
        callerName: String,
        hasVideo: Bool,
        completion: @escaping (Error?) -> Void
    ) {
        let uuid = UUID()
        callIdToUUID[callId] = uuid
        activeCallUUIDs.insert(uuid)
        
        let update = CXCallUpdate()
        update.remoteHandle = CXHandle(type: .generic, value: callerName)
        update.hasVideo = hasVideo
        update.localizedCallerName = callerName
        
        provider.reportNewIncomingCall(with: uuid, update: update) { error in
            if let error = error {
                self.activeCallUUIDs.remove(uuid)
                self.callIdToUUID.removeValue(forKey: callId)
            }
            completion(error)
        }
    }
    
    // MARK: - Outgoing Call
    func startOutgoingCall(
        callId: String,
        callerName: String,
        hasVideo: Bool,
        completion: @escaping (Error?) -> Void
    ) {
        let uuid = UUID()
        callIdToUUID[callId] = uuid
        activeCallUUIDs.insert(uuid)
        
        let handle = CXHandle(type: .generic, value: callerName)
        let startCallAction = CXStartCallAction(call: uuid, handle: handle)
        startCallAction.isVideo = hasVideo
        
        let transaction = CXTransaction(action: startCallAction)
        
        callController.request(transaction) { error in
            if let error = error {
                self.activeCallUUIDs.remove(uuid)
                self.callIdToUUID.removeValue(forKey: callId)
            }
            completion(error)
        }
    }
    
    // MARK: - Call Management
    func endCall(callId: String, completion: @escaping (Error?) -> Void) {
        guard let uuid = callIdToUUID[callId] else {
            completion(NSError(domain: "CallKit", code: -1, userInfo: [NSLocalizedDescriptionKey: "Call not found"]))
            return
        }
        
        let endCallAction = CXEndCallAction(call: uuid)
        let transaction = CXTransaction(action: endCallAction)
        
        callController.request(transaction) { error in
            if error == nil {
                self.activeCallUUIDs.remove(uuid)
                self.callIdToUUID.removeValue(forKey: callId)
            }
            completion(error)
        }
    }
    
    func updateCall(callId: String, callerName: String? = nil, hasVideo: Bool? = nil) {
        guard let uuid = callIdToUUID[callId] else { return }
        
        let update = CXCallUpdate()
        if let callerName = callerName {
            update.localizedCallerName = callerName
            update.remoteHandle = CXHandle(type: .generic, value: callerName)
        }
        if let hasVideo = hasVideo {
            update.hasVideo = hasVideo
        }
        
        provider.reportCall(with: uuid, updated: update)
    }
    
    func reportCallConnected(callId: String) {
        guard let uuid = callIdToUUID[callId] else { return }
        provider.reportOutgoingCall(with: uuid, connectedAt: Date())
    }
    
    func reportCallEnded(callId: String, reason: CXCallEndedReason) {
        guard let uuid = callIdToUUID[callId] else { return }
        
        provider.reportCall(with: uuid, endedAt: Date(), reason: reason)
        activeCallUUIDs.remove(uuid)
        callIdToUUID.removeValue(forKey: callId)
    }
}

// MARK: - CXProviderDelegate
extension CallKitManager: CXProviderDelegate {
    func providerDidReset(_ provider: CXProvider) {
        // Clean up all calls
        activeCallUUIDs.removeAll()
        callIdToUUID.removeAll()
        
        // Notify Flutter
        CallPlatformChannel.shared.notifyCallKitReset()
    }
    
    func provider(_ provider: CXProvider, perform action: CXStartCallAction) {
        // Configure audio session
        configureAudioSession()
        
        // Notify Flutter to start call
        if let callId = getCallId(for: action.callUUID) {
            CallPlatformChannel.shared.notifyStartCall(callId: callId)
        }
        
        action.fulfill()
    }
    
    func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) {
        // Configure audio session
        configureAudioSession()
        
        // Notify Flutter to answer call
        if let callId = getCallId(for: action.callUUID) {
            CallPlatformChannel.shared.notifyAnswerCall(callId: callId)
        }
        
        action.fulfill()
    }
    
    func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
        // Notify Flutter to end call
        if let callId = getCallId(for: action.callUUID) {
            CallPlatformChannel.shared.notifyEndCall(callId: callId)
        }
        
        activeCallUUIDs.remove(action.callUUID)
        callIdToUUID = callIdToUUID.filter { $0.value != action.callUUID }
        
        action.fulfill()
    }
    
    func provider(_ provider: CXProvider, perform action: CXSetMutedCallAction) {
        // Notify Flutter about mute state change
        if let callId = getCallId(for: action.callUUID) {
            CallPlatformChannel.shared.notifyMuteChanged(callId: callId, isMuted: action.isMuted)
        }
        
        action.fulfill()
    }
    
    func provider(_ provider: CXProvider, didActivate audioSession: AVAudioSession) {
        // Audio session activated - start audio
        CallPlatformChannel.shared.notifyAudioSessionActivated()
    }
    
    func provider(_ provider: CXProvider, didDeactivate audioSession: AVAudioSession) {
        // Audio session deactivated - stop audio
        CallPlatformChannel.shared.notifyAudioSessionDeactivated()
    }
    
    private func getCallId(for uuid: UUID) -> String? {
        return callIdToUUID.first { $0.value == uuid }?.key
    }
    
    private func configureAudioSession() {
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.playAndRecord, mode: .voiceChat, options: [])
            try audioSession.setActive(true)
        } catch {
            print("Failed to configure audio session: \(error)")
        }
    }
}
```

## 3. Platform Channel Handler

```swift
// ios/Runner/CallPlatformChannel.swift
import Foundation
import Flutter
import CallKit

class CallPlatformChannel: NSObject {
    static let shared = CallPlatformChannel()
    
    private var methodChannel: FlutterMethodChannel?
    
    func setup(with binaryMessenger: FlutterBinaryMessenger) {
        methodChannel = FlutterMethodChannel(
            name: "com.hopen.call/callkit",
            binaryMessenger: binaryMessenger
        )
        
        methodChannel?.setMethodCallHandler { [weak self] call, result in
            self?.handleMethodCall(call: call, result: result)
        }
    }
    
    private func handleMethodCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "reportIncomingCall":
            reportIncomingCall(call: call, result: result)
        case "startOutgoingCall":
            startOutgoingCall(call: call, result: result)
        case "endCall":
            endCall(call: call, result: result)
        case "updateCall":
            updateCall(call: call, result: result)
        case "reportCallConnected":
            reportCallConnected(call: call, result: result)
        case "reportCallEnded":
            reportCallEnded(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func reportIncomingCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let callId = args["callId"] as? String,
              let callerName = args["callerName"] as? String,
              let hasVideo = args["hasVideo"] as? Bool else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing required arguments", details: nil))
            return
        }
        
        CallKitManager.shared.reportIncomingCall(
            callId: callId,
            callerName: callerName,
            hasVideo: hasVideo
        ) { error in
            if let error = error {
                result(FlutterError(code: "CALLKIT_ERROR", message: error.localizedDescription, details: nil))
            } else {
                result(true)
            }
        }
    }
    
    private func startOutgoingCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let callId = args["callId"] as? String,
              let callerName = args["callerName"] as? String,
              let hasVideo = args["hasVideo"] as? Bool else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing required arguments", details: nil))
            return
        }
        
        CallKitManager.shared.startOutgoingCall(
            callId: callId,
            callerName: callerName,
            hasVideo: hasVideo
        ) { error in
            if let error = error {
                result(FlutterError(code: "CALLKIT_ERROR", message: error.localizedDescription, details: nil))
            } else {
                result(true)
            }
        }
    }
    
    private func endCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let callId = args["callId"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing callId", details: nil))
            return
        }
        
        CallKitManager.shared.endCall(callId: callId) { error in
            if let error = error {
                result(FlutterError(code: "CALLKIT_ERROR", message: error.localizedDescription, details: nil))
            } else {
                result(true)
            }
        }
    }
    
    // MARK: - Notifications to Flutter
    func notifyCallKitReset() {
        methodChannel?.invokeMethod("onCallKitReset", arguments: nil)
    }
    
    func notifyStartCall(callId: String) {
        methodChannel?.invokeMethod("onStartCall", arguments: ["callId": callId])
    }
    
    func notifyAnswerCall(callId: String) {
        methodChannel?.invokeMethod("onAnswerCall", arguments: ["callId": callId])
    }
    
    func notifyEndCall(callId: String) {
        methodChannel?.invokeMethod("onEndCall", arguments: ["callId": callId])
    }
    
    func notifyMuteChanged(callId: String, isMuted: Bool) {
        methodChannel?.invokeMethod("onMuteChanged", arguments: [
            "callId": callId,
            "isMuted": isMuted
        ])
    }
    
    func notifyAudioSessionActivated() {
        methodChannel?.invokeMethod("onAudioSessionActivated", arguments: nil)
    }
    
    func notifyAudioSessionDeactivated() {
        methodChannel?.invokeMethod("onAudioSessionDeactivated", arguments: nil)
    }
}
```

## 4. AppDelegate Integration

```swift
// ios/Runner/AppDelegate.swift
import UIKit
import Flutter
import PushKit

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate, PKPushRegistryDelegate {
    private var pushRegistry: PKPushRegistry?
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        
        // Setup platform channels
        if let controller = window?.rootViewController as? FlutterViewController {
            CallPlatformChannel.shared.setup(with: controller.binaryMessenger)
        }
        
        // Setup VoIP push notifications
        setupVoIPPush()
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func setupVoIPPush() {
        pushRegistry = PKPushRegistry(queue: DispatchQueue.main)
        pushRegistry?.delegate = self
        pushRegistry?.desiredPushTypes = [.voIP]
    }
    
    // MARK: - PKPushRegistryDelegate
    func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
        if type == .voIP {
            let token = pushCredentials.token.map { String(format: "%02.2hhx", $0) }.joined()
            // Send token to Flutter
            if let controller = window?.rootViewController as? FlutterViewController {
                let channel = FlutterMethodChannel(
                    name: "com.hopen.call/voip_push",
                    binaryMessenger: controller.binaryMessenger
                )
                channel.invokeMethod("onVoIPTokenReceived", arguments: ["token": token])
            }
        }
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
        if type == .voIP {
            handleVoIPPush(payload: payload, completion: completion)
        }
    }
    
    private func handleVoIPPush(payload: PKPushPayload, completion: @escaping () -> Void) {
        guard let callId = payload.dictionaryPayload["call_id"] as? String,
              let callerName = payload.dictionaryPayload["caller_name"] as? String,
              let hasVideo = payload.dictionaryPayload["has_video"] as? Bool else {
            completion()
            return
        }
        
        // Report incoming call to CallKit
        CallKitManager.shared.reportIncomingCall(
            callId: callId,
            callerName: callerName,
            hasVideo: hasVideo
        ) { error in
            if let error = error {
                print("Failed to report incoming call: \(error)")
            }
            completion()
        }
        
        // Notify Flutter about incoming call
        if let controller = window?.rootViewController as? FlutterViewController {
            let channel = FlutterMethodChannel(
                name: "com.hopen.call/voip_push",
                binaryMessenger: controller.binaryMessenger
            )
            channel.invokeMethod("onIncomingCall", arguments: payload.dictionaryPayload)
        }
    }
}
```

## Testing Checklist

### CallKit Integration
- [ ] Incoming calls show in native UI
- [ ] Outgoing calls integrate with system
- [ ] Call controls work (mute, speaker, end)
- [ ] Multiple call handling
- [ ] Call history integration
- [ ] Siri integration works

### VoIP Push Notifications
- [ ] VoIP push token registration
- [ ] Incoming call notifications when app is killed
- [ ] Background call handling
- [ ] Push payload processing
- [ ] CallKit integration from push

### Audio Session Management
- [ ] Audio routing (speaker, bluetooth, earpiece)
- [ ] Audio interruption handling
- [ ] Background audio continuation
- [ ] Audio quality optimization

## Production Considerations

### App Store Review
- Ensure VoIP functionality is primary feature
- Provide clear VoIP usage description
- Test with TestFlight for VoIP capabilities
- Document CallKit integration properly

### Performance
- Optimize for battery usage
- Handle memory pressure during calls
- Efficient audio processing
- Network optimization for VoIP

### Error Handling
- CallKit permission denials
- Audio session conflicts
- VoIP push failures
- Network connectivity issues
