# Call Edge Cases Implementation with JetStream

## Overview

This document describes the implementation of two critical call edge cases using NATS JetStream for reliable event processing:

1. **Bubble Lifecycle Events During a Call** - Handling bubble expiration/dissolution during active calls
2. **Connection Loss During Call Setup** - Managing stale calls and frontend timeouts

## Architecture

### JetStream Integration

The implementation leverages NATS JetStream for guaranteed event delivery and durable consumers:

```go
// JetStream initialization in call service
func (s *Service) initializeJetStream() error {
    js, err := s.natsConn.JetStream()
    if err != nil {
        s.logger.Warn("JetStream not available, falling back to regular NATS", zap.Error(err))
        return nil
    }
    s.js = js

    // Initialize event publisher with JetStream
    s.eventPublisher = events.NewNATSPublisherWithJetStream(s.natsConn, js, s.logger)

    // Initialize event subscriber with durable consumers
    subscriberConfig := events.DefaultSubscriberConfig("call-service")
    subscriberConfig.UseJetStream = true
    subscriberConfig.EnableDurable = true
    subscriberConfig.EnableDLQ = true
    subscriberConfig.AckWait = 30 * time.Second
    subscriberConfig.MaxDeliver = 3
    
    s.eventSubscriber = events.NewNATSSubscriberWithJetStream(s.natsConn, js, s.logger, subscriberConfig)
    
    return nil
}
```

### Event Types

New event types added to the schema:

```go
// Bubble lifecycle events
EventTypeBubbleExpired    EventType = "bubble.expired"
EventTypeBubbleDissolved  EventType = "bubble.dissolved"
EventTypeBubbleMemberLeft EventType = "bubble.member_left"

// Event structures
type BubbleExpiredEvent struct {
    BaseEvent
    Bubble struct {
        ID   string `json:"id"`
        Name string `json:"name"`
    } `json:"bubble"`
}

type BubbleDissolvedEvent struct {
    BaseEvent
    Bubble struct {
        ID   string `json:"id"`
        Name string `json:"name"`
    } `json:"bubble"`
    Reason string `json:"reason"` // "below_minimum_members", "manual_dissolution"
}

type BubbleMemberLeftEvent struct {
    BaseEvent
    Bubble struct {
        ID   string `json:"id"`
        Name string `json:"name"`
    } `json:"bubble"`
    UserID string `json:"user_id"`
    Reason string `json:"reason"` // "kicked", "left", "expired"
}
```

## Edge Case 1: Bubble Lifecycle Events During a Call

### Problem
When a bubble expires (90-day timer) or dissolves (drops below 2 members) during an active call, the call could continue in a "zombie" bubble state.

### Solution

#### Backend Implementation

1. **Event Subscription with JetStream**:
```go
func (s *Service) subscribeToBubbleEvents() {
    // Subscribe to bubble lifecycle events using JetStream with durable consumer
    err := s.eventSubscriber.SubscribeToEvent(events.EventTypeBubbleExpired, s.handleBubbleExpiredEventBytes)
    err = s.eventSubscriber.SubscribeToEvent(events.EventTypeBubbleDissolved, s.handleBubbleDissolvedEventBytes)
}
```

2. **Event Handlers**:
```go
func (s *Service) handleBubbleLifecycleEnd(ctx context.Context, bubbleID, reason string) error {
    // Find any active calls in this bubble
    activeCall, err := s.getActiveCallForBubble(ctx, bubbleID)
    if err != nil {
        if errors.Is(err, pgx.ErrNoRows) {
            return nil // No active call
        }
        return fmt.Errorf("failed to check for active calls: %w", err)
    }

    // Terminate the call for all participants
    return s.terminateCallForBubbleLifecycleEnd(ctx, activeCall.CallID, reason)
}
```

3. **Call Termination**:
```go
func (s *Service) terminateCallForBubbleLifecycleEnd(ctx context.Context, callID, reason string) error {
    // Get participants before terminating
    participants, err := s.getCallParticipantsWithDetails(ctx, callID)
    
    // End call in LiveKit
    if s.callManager != nil {
        roomName := s.generateRoomName(callID)
        adminToken, _ := s.sharedComponents.GenerateAdminToken()
        s.callManager.EndCall(ctx, roomName, adminToken)
    }

    // Update database
    s.updateCallEndInDatabase(ctx, callID, "system")

    // Publish events for all participants
    for _, participant := range participants {
        s.publishCallEvent(participant.UserId, callID, "call", "bubble_lifecycle_ended", map[string]any{
            "reason":     reason,
            "call_id":    callID,
            "user_id":    participant.UserId,
            "auto_ended": true,
        })
    }
    
    return nil
}
```

#### Frontend Implementation

1. **Event Handling**:
```dart
void _handleCallOutcome(String callId, String outcome) {
    switch (outcome.toLowerCase()) {
        case 'bubble_lifecycle_ended':
            _handleBubbleLifecycleEnded(callId);
            break;
        // ... other cases
    }
}

void _handleBubbleLifecycleEnded(String callId) {
    // End the call state
    state = AsyncData(CallEnded(
        callId: callId,
        reason: 'Bubble no longer exists',
        duration: duration,
    ));
    
    // Show informative message
    ref.read(toastServiceProvider).showInfoToast(
        'Call ended because the bubble has expired or been dissolved.',
    );
    
    // UI should navigate to friends choice dialog
}
```

## Edge Case 2: Connection Loss During Call Setup

### Problem
When a user initiates a call but loses internet connection before backend confirmation, the UI might hang or the backend might create orphaned calls.

### Solution

#### Backend Implementation - Garbage Collection

1. **Stale Call Detection**:
```go
func (s *Service) startStaleCallGarbageCollection() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-ticker.C:
            ctx := context.Background()
            if err := s.cleanupStaleCalls(ctx); err != nil {
                s.logger.Error("Failed to cleanup stale calls", zap.Error(err))
            }
        }
    }
}
```

2. **Stale Call Cleanup**:
```go
func (s *Service) cleanupStaleCalls(ctx context.Context) error {
    // Find calls in 'initiated'/'ringing' state for >45 seconds without participants
    staleThreshold := time.Now().Add(-45 * time.Second)
    
    query := `
        SELECT c.call_id, c.bubble_id, c.initiator_id, c.started_at
        FROM calls c
        LEFT JOIN call_participants cp ON c.call_id = cp.call_id AND cp.joined_at IS NOT NULL
        WHERE c.status IN ('initiated', 'ringing')
          AND c.started_at < $1
          AND cp.call_id IS NULL
        GROUP BY c.call_id, c.bubble_id, c.initiator_id, c.started_at
    `
    
    // Process and terminate each stale call
    for _, call := range staleCalls {
        s.terminateStaleCall(ctx, call.CallID, call.InitiatorID, call.StartedAt)
    }
}
```

3. **Stale Call Termination**:
```go
func (s *Service) terminateStaleCall(ctx context.Context, callID, initiatorID string, startedAt time.Time) error {
    // End in LiveKit and update database
    // Publish stale call events
    s.publishCallEvent(initiatorID, callID, "call", "stale_call_terminated", map[string]any{
        "call_id":     callID,
        "user_id":     initiatorID,
        "age_seconds": int(time.Since(startedAt).Seconds()),
        "reason":      "connection_timeout",
    })
}
```

#### Frontend Implementation - Timeout Handling

1. **Call Initiation with Timeout**:
```dart
Future<void> initiateCall({
    required String targetId,
    required bool isGroup,
    bool withVideo = false,
    bool withScreenShare = false,
    String? targetName,
}) async {
    Timer? timeoutTimer;
    
    try {
        // Set up 30-second timeout timer
        timeoutTimer = Timer(const Duration(seconds: 30), () {
            LoggingService.warning('CallController: Call initiation timeout');
            
            // Revert to idle state
            state = const AsyncData(CallIdle());
            
            // Show timeout toast
            ref.read(toastServiceProvider).showErrorToast(
                'Call initiation timed out. Please check your connection and try again.',
            );
        });

        // Attempt call initiation
        final result = await callRepo.initiateCall(
            targetId: targetId,
            isGroup: isGroup,
            withVideo: withVideo,
            withScreenShare: withScreenShare,
        );

        // Cancel timeout on success
        timeoutTimer?.cancel();
        
        // Process successful result...
        
    } on Exception catch (e) {
        timeoutTimer?.cancel();
        
        // Handle connection/timeout errors specifically
        final errorMessage = e.toString().toLowerCase();
        if (errorMessage.contains('timeout') || errorMessage.contains('connection')) {
            state = const AsyncData(CallIdle());
            ref.read(toastServiceProvider).showErrorToast(
                'Connection timeout. Please check your network and try again.',
            );
        }
    }
}
```

2. **Stale Call Event Handling**:
```dart
void _handleStaleCallTerminated(String callId) {
    LoggingService.info('CallController: Stale call terminated for call $callId');
    
    // Revert to idle state
    state = const AsyncData(CallIdle());
    
    // Show timeout message
    ref.read(toastServiceProvider).showWarningToast(
        'Call setup timed out. Please try again.',
    );
}
```

## Benefits

### Reliability
- **JetStream Durable Consumers**: Events are not lost even if the call service restarts
- **Explicit Acknowledgments**: Events are redelivered if processing fails
- **Dead Letter Queues**: Failed events are captured for investigation

### Performance
- **Efficient Garbage Collection**: Runs every 30 seconds with optimized queries
- **Atomic Operations**: Database operations use transactions for consistency
- **Timeout Handling**: Frontend prevents hanging UI states

### User Experience
- **Clear Error Messages**: Users receive specific feedback about call failures
- **Automatic Recovery**: System automatically cleans up stale resources
- **Graceful Degradation**: Fallback to regular NATS if JetStream unavailable

## Testing

The implementation includes comprehensive test coverage for:
- Event subscription and handling
- Garbage collection mechanisms
- Timeout scenarios
- Error recovery paths

## Monitoring

Key metrics to monitor:
- Stale call cleanup frequency
- Event processing latency
- JetStream consumer lag
- Call setup timeout rates
