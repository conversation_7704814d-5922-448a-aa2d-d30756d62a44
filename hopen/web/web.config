<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <staticContent>
      <!-- WebAssembly MIME type -->
      <mimeMap fileExtension=".wasm" mimeType="application/wasm" />
      <!-- <PERSON><PERSON> compiled JavaScript -->
      <mimeMap fileExtension=".dart.js" mimeType="application/javascript" />
      <!-- Source maps -->
      <mimeMap fileExtension=".js.map" mimeType="application/json" />
    </staticContent>
    
    <httpProtocol>
      <customHeaders>
        <!-- Cross-Origin headers for WebAssembly -->
        <add name="Cross-Origin-Embedder-Policy" value="require-corp" />
        <add name="Cross-Origin-Opener-Policy" value="same-origin" />
        <!-- Security headers -->
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="DENY" />
        <add name="X-XSS-Protection" value="1; mode=block" />
      </customHeaders>
    </httpProtocol>
    
    <httpCompression>
      <dynamicTypes>
        <add mimeType="application/wasm" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
      </dynamicTypes>
    </httpCompression>
  </system.webServer>
</configuration>
