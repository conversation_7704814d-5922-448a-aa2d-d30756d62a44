# WebAssembly MIME type configuration
AddType application/wasm .wasm

# Enable compression for WebAssembly files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/wasm
</IfModule>

# Cache control for WebAssembly files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType application/wasm "access plus 1 year"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    # Cross-Origin Embedder Policy for SharedArrayBuffer support
    Header always set Cross-Origin-Embedder-Policy "require-corp"
    Header always set Cross-Origin-Opener-Policy "same-origin"
    
    # Additional security headers
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "DENY"
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
