# Development Environment Configuration
# This file contains all configuration for the development environment

environment:
  name: "development"
  flavor: "dev"

# Network Configuration
network:
  # Host IP configuration for development
  host_ip:
    # Default host for development (auto-detected based on platform)
    default: "localhost"
    # Android emulator host IP
    emulator_host: "********"
    # Docker host IP for physical devices
    docker_host: "*********"
    # Web platform host
    web_host: "localhost"

# gRPC Configuration (Kong Gateway)
grpc:
  port: 8000  # Kong Gateway HTTP port (8443 for HTTPS)
  use_tls: false  # Set to true for production with port 8443
  timeout_seconds: 30
  file_upload_timeout_minutes: 5

# MinIO Configuration
minio:
  endpoint: ""  # Will use host_ip if empty
  port: 9000
  access_key: "minioadmin"
  secret_key: "minioadmin"
  use_ssl: false
  default_bucket: "hopen-dev"
  region: "us-east-1"
  connection_timeout_seconds: 30

# MQTT Configuration
mqtt:
  port: 1883
  use_ssl: false
  username: "dev_user"
  password: "dev_password"
  client_id_prefix: "hopen_dev"
  keep_alive_seconds: 60
  connection_timeout_seconds: 30
  auto_reconnect: true

# Cache Configuration
cache:
  # Default TTL for cached data
  default_ttl_minutes: 30
  # Stale period for stale-while-revalidate
  stale_period_minutes: 5
  # Maximum cache size
  max_cache_objects: 1000
  # Profile picture cache settings
  profile_pictures:
    max_objects: 200
    stale_period_days: 7

# Logging Configuration
logging:
  level: "debug"
  enable_console: true
  enable_file: true
  max_file_size_mb: 10
  max_files: 5

# Feature Flags
features:
  enable_analytics: false
  enable_crash_reporting: false
  enable_performance_monitoring: false
  enable_debug_tools: true
  enable_mock_data: true

# Development-specific settings
development:
  # Enable hot reload
  hot_reload: true
  # Show debug banners
  debug_banner: true
  # Enable inspector
  inspector: true
  # Mock external services
  mock_services: false
