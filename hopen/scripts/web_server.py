#!/usr/bin/env python3
"""
Simple HTTP server for Flutter web development with proper WebAssembly MIME type support.
This server ensures that .wasm files are served with the correct application/wasm MIME type.
"""

import http.server
import socketserver
import mimetypes
import os
import sys
from pathlib import Path

class WebAssemblyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler that properly serves WebAssembly files."""
    
    def __init__(self, *args, **kwargs):
        # Add WebAssembly MIME type
        mimetypes.add_type('application/wasm', '.wasm')
        mimetypes.add_type('application/javascript', '.dart.js')
        mimetypes.add_type('application/json', '.js.map')
        super().__init__(*args, **kwargs)
    
    def end_headers(self):
        """Add security headers for WebAssembly support."""
        # Cross-Origin headers required for SharedArrayBuffer and WebAssembly
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        
        # Security headers
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        
        # Cache control for static assets
        if self.path.endswith(('.wasm', '.js', '.css')):
            self.send_header('Cache-Control', 'public, max-age=31536000')  # 1 year
        elif self.path.endswith('.html'):
            self.send_header('Cache-Control', 'no-cache')
        
        super().end_headers()
    
    def log_message(self, format, *args):
        """Custom log format."""
        print(f"[{self.log_date_time_string()}] {format % args}")

def main():
    """Start the web server."""
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8080
    host = '127.0.0.1'
    
    # Change to the web build directory
    web_dir = Path(__file__).parent.parent / 'build' / 'web'
    if not web_dir.exists():
        print(f"Error: Web build directory not found at {web_dir}")
        print("Please run 'flutter build web --wasm' first.")
        sys.exit(1)
    
    os.chdir(web_dir)
    
    with socketserver.TCPServer((host, port), WebAssemblyHTTPRequestHandler) as httpd:
        print(f"🚀 Flutter Web Server with WebAssembly support")
        print(f"📍 Serving at http://{host}:{port}")
        print(f"📁 Directory: {web_dir}")
        print(f"🔧 WebAssembly MIME type: application/wasm")
        print(f"🛡️  Security headers enabled")
        print(f"⏹️  Press Ctrl+C to stop")
        print()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")

if __name__ == '__main__':
    main()
