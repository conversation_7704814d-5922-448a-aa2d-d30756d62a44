name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        api-level: [29]
        target: [default]
        arch: [x86_64]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.0'
        channel: 'stable'

    - name: Cache Flutter dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.pub-cache
          ${{ runner.workspace }}/hopen/.dart_tool
        key: ${{ runner.os }}-flutter-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-

    - name: Install Flutter dependencies
      working-directory: ./hopen
      run: flutter pub get

    - name: Verify Flutter installation
      working-directory: ./hopen
      run: |
        flutter doctor -v
        flutter --version

    - name: Enable KVM group perms
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm

    - name: Setup Android SDK
      uses: android-actions/setup-android@v3

    - name: AVD cache
      uses: actions/cache@v3
      id: avd-cache
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: avd-${{ matrix.api-level }}

    - name: Create AVD and generate snapshot for caching
      if: steps.avd-cache.outputs.cache-hit != 'true'
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: ${{ matrix.arch }}
        force-avd-creation: false
        emulator-options: -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: false
        script: echo "Generated AVD snapshot for caching."

    - name: Run E2E tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: ${{ matrix.arch }}
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: |
          cd hopen && flutter test test/end_to_end_tests/ --reporter=json > test_results.json
          # The job will now correctly fail if tests fail, and the JSON report will still be available for upload.

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: |
          hopen/test_results.json
          hopen/test/screenshots/

    - name: Parse test results
      if: always()
      working-directory: ./hopen
      run: |
        if [ -f test_results.json ]; then
          echo "📊 Test Results Summary:"
          echo "========================"
          
          # Count total tests
          TOTAL_TESTS=$(jq '[.[] | select(.type == "testDone")] | length' test_results.json 2>/dev/null || echo "0")
          
          # Count passed tests
          PASSED_TESTS=$(jq '[.[] | select(.type == "testDone" and .result == "success")] | length' test_results.json 2>/dev/null || echo "0")
          
          # Count failed tests
          FAILED_TESTS=$(jq '[.[] | select(.type == "testDone" and .result == "error")] | length' test_results.json 2>/dev/null || echo "0")
          
          echo "✅ Total Tests: $TOTAL_TESTS"
          echo "✅ Passed: $PASSED_TESTS"
          echo "❌ Failed: $FAILED_TESTS"
          
          if [ "$FAILED_TESTS" -gt 0 ]; then
            echo ""
            echo "💥 Failed Tests:"
            jq -r '.[] | select(.type == "testDone" and .result == "error") | "  - " + .test' test_results.json 2>/dev/null || echo "Unable to parse failed tests"
          fi
          
          # Calculate success rate
          if [ "$TOTAL_TESTS" -gt 0 ]; then
            SUCCESS_RATE=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l 2>/dev/null || echo "0")
            echo "📈 Success Rate: ${SUCCESS_RATE}%"
          fi
        else
          echo "❌ No test results file found"
        fi

    - name: Comment PR with test results
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const path = './hopen/test_results.json';
          
          let comment = '## 🧪 E2E Test Results\n\n';
          
          if (fs.existsSync(path)) {
            try {
              const results = JSON.parse(fs.readFileSync(path, 'utf8'));
              const tests = results.filter(r => r.type === 'testDone');
              const passed = tests.filter(t => t.result === 'success').length;
              const failed = tests.filter(t => t.result === 'error').length;
              const total = tests.length;
              
              comment += `📊 **Summary:**\n`;
              comment += `- ✅ Passed: ${passed}\n`;
              comment += `- ❌ Failed: ${failed}\n`;
              comment += `- 📈 Success Rate: ${total > 0 ? Math.round(passed * 100 / total) : 0}%\n\n`;
              
              if (failed > 0) {
                comment += `💥 **Failed Tests:**\n`;
                const failedTests = tests.filter(t => t.result === 'error');
                failedTests.forEach(test => {
                  comment += `- ${test.test}\n`;
                });
              } else {
                comment += `🎉 All tests passed!`;
              }
            } catch (error) {
              comment += `❌ Error parsing test results: ${error.message}`;
            }
          } else {
            comment += `❌ No test results found`;
          }
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  # Job for iOS testing (if needed in the future)
  e2e-tests-ios:
    runs-on: macos-latest
    if: false  # Disabled for now, enable when iOS testing is needed
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.0'
        channel: 'stable'

    - name: Install Flutter dependencies
      working-directory: ./hopen
      run: flutter pub get

    - name: Start iOS Simulator
      run: |
        xcrun simctl list devices
        xcrun simctl boot "iPhone 14" || true

    - name: Run E2E tests on iOS
      working-directory: ./hopen
      run: |
        flutter test test/end_to_end_tests/ --verbose 