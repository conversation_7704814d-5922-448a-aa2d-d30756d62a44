PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - audioplayers_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - background_fetch (1.3.8):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - firebase_core (3.15.1):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_messaging (15.2.9):
    - Firebase/Messaging (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_webrtc (0.12.6):
    - Flutter
    - WebRTC-SDK (= 125.6422.06)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PostHog (3.29.0)
  - posthog_flutter (0.0.1):
    - Flutter
    - FlutterMacOS
    - PostHog (~> 3.22)
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - record_ios (1.1.0):
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.50.3):
    - sqlite3/common (= 3.50.3)
  - sqlite3/common (3.50.3)
  - sqlite3/dbstatvtab (3.50.3):
    - sqlite3/common
  - sqlite3/fts5 (3.50.3):
    - sqlite3/common
  - sqlite3/math (3.50.3):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.50.3):
    - sqlite3/common
  - sqlite3/rtree (3.50.3):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.50.3)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - SwiftyGif (5.4.5)
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - WebRTC-SDK (125.6422.06)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - workmanager_apple (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/darwin`)
  - background_fetch (from `.symlinks/plugins/background_fetch/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - posthog_flutter (from `.symlinks/plugins/posthog_flutter/ios`)
  - record_ios (from `.symlinks/plugins/record_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/darwin`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - workmanager_apple (from `.symlinks/plugins/workmanager_apple/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - libwebp
    - Mantle
    - nanopb
    - PostHog
    - PromisesObjC
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder
    - sqlite3
    - SwiftyGif
    - WebRTC-SDK

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/darwin"
  background_fetch:
    :path: ".symlinks/plugins/background_fetch/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  posthog_flutter:
    :path: ".symlinks/plugins/posthog_flutter/ios"
  record_ios:
    :path: ".symlinks/plugins/record_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/darwin"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"
  workmanager_apple:
    :path: ".symlinks/plugins/workmanager_apple/ios"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  audioplayers_darwin: 4f9ca89d92d3d21cec7ec580e78ca888e5fb68bd
  background_fetch: 851122c99dc3f25a011a6aebec5379ccdf4ab5eb
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_core: ece862f94b2bc72ee0edbeec7ab5c7cb09fe1ab5
  firebase_messaging: e1a5fae495603115be1d0183bc849da748734e2b
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  Flutter: cabc95a1d2626b1b06e7179b784ebcf0c0cde467
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  flutter_webrtc: 57f32415b8744e806f9c2a96ccdb60c6a627ba33
  google_sign_in_ios: b48bb9af78576358a168361173155596c845f0b9
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PostHog: 9e88bab08337ad61e71337b9d02b7182555f0ff3
  posthog_flutter: 631ab870f7daf1ed190deeb2414eb3e347445f37
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  record_ios: f75fa1d57f840012775c0e93a38a7f3ceea1a374
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  sqlite3: 83105acd294c9137c026e2da1931c30b4588ab81
  sqlite3_flutter_libs: ce0522d143cee6ef5e16587acfce8f476316e005
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  WebRTC-SDK: 79942c006ea64f6fb48d7da8a4786dfc820bc1db
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  workmanager_apple: f540d652595dfe5c8b8200c4c85ba622d6fb5c5b

PODFILE CHECKSUM: 4092cc1ed8df3b3f82eb6996998058116981621b

COCOAPODS: 1.16.2
