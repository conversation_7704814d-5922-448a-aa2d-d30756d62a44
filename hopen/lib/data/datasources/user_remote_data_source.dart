// GetIt removed - using pure Riverpod
import 'package:fixnum/fixnum.dart';

import '../../generated/grpc/bubble.pb.dart' as bubble_grpc;
import '../../generated/grpc/common.pb.dart' as common;
import '../../generated/grpc/google/protobuf/timestamp.pb.dart' as timestamp_proto;
import '../../generated/grpc/search.pb.dart' as search_proto;
import '../../generated/grpc/social_analytics.pb.dart' as social_analytics_grpc;
import '../../generated/grpc/sync.pb.dart' as sync_proto;
import '../../generated/grpc/user.pb.dart' as user_proto;
import '../../repositories/resilience/resilience_repository.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../exceptions/api_exceptions.dart';

import '../services/api/grpc_client_service.dart';
import 'base_remote_data_source.dart';

/// Remote data source for user-related operations
abstract class UserRemoteDataSource {
  /// Get current user profile
  Future<common.User> getCurrentUserProfile();

  /// Get user profile by ID
  Future<common.User> getUserProfile(String userId);

  /// Get enhanced user profile by ID
  Future<common.User> getEnhancedUserProfile(String userId);

  /// Get multiple users by IDs
  Future<List<common.User>> getUsersBatch(List<String> userIds);

  /// Get all users (admin function)
  Future<List<common.User>> getAllUsers();

  /// Create a new user
  Future<common.User> createUser(common.User user);

  /// Update user
  Future<common.User> updateUser(common.User user);

  /// Update user profile
  Future<common.User> updateUserProfile(user_proto.UpdateUserRequest request);

  /// Delete user account (soft delete)
  Future<void> deleteUser(String userId);

  /// Deactivate user account
  Future<void> deactivateUser(String userId, String reason);

  /// Search users
  Future<List<common.User>> searchUsers({
    String query = '',
    int page = 1,
    int pageSize = 20,
  });

  /// Get user statistics
  Future<user_proto.UserStats> getUserStats(String userId);

  /// Block user
  Future<void> blockUser(String userId, String blockedUserId, String reason);

  /// Unblock user
  Future<void> unblockUser(String userId, String blockedUserId);

  /// Get blocked users
  Future<List<common.User>> getBlockedUsers(
    String userId, {
    int page = 1,
    int pageSize = 20,
  });

  /// Submit user report
  Future<String> submitUserReport({
    required String reporterId,
    required String reportedUserId,
    required String reason,
    required String description,
    required String category,
    Map<String, String>? evidence,
  });

  /// Submit feedback
  Future<String> submitFeedback({
    required String userId,
    required String title,
    required String description,
    required String category,
    int rating = 5,
    String? userEmail,
  });

  /// Submit bug report
  Future<String> submitBugReport({
    required String userId,
    required String title,
    required String description,
    String? userEmail,
    String? deviceInfo,
    String? appVersion,
    String? osVersion,
    Map<String, String>? additionalInfo,
  });

  /// Report user
  Future<String> reportUser({
    required String reporterId,
    required String reportedUserId,
    required String reason,
    required String description,
    required String category,
  });

  /// Get user language preference
  Future<String> getUserLanguagePreference(String userId);

  /// Update user language preference
  Future<void> updateUserLanguagePreference({
    required String userId,
    required String languagePreference,
  });

  // ================================================================
  // Contacts (relationships under user microservice)
  // ================================================================
  Future<List<common.Contact>> getContacts();
  Future<void> sendContactRequest(String contactUserId);
  Future<void> acceptContactRequest(String contactRequestId);
  Future<void> rejectContactRequest(String contactRequestId);
  Future<void> removeContact(String contactId);
  Future<List<sync_proto.ContactRequest>> getPendingReceivedRequests();
  Future<List<sync_proto.ContactRequest>> getPendingSentRequests();
  Future<sync_proto.ContactRequest> getContactRequest(String requestId);
  Future<List<sync_proto.ContactRequest>> getContactRequestHistory();
  Future<void> cancelContactRequest(String requestId);
  Future<List<common.User>> getSuggestedContacts();
  Future<List<common.User>> getMutualContacts(String userId);

  // ================================================================
  // Enriched Relationship Methods (return relationship objects with embedded user data)
  // ================================================================
  Future<List<common.Contact>> getContactsEnriched();
  Future<List<common.Friendship>> getFriendsEnriched();
  Future<List<common.Friendship>> getMaybeFriendsEnriched();
  Future<List<common.Friendship>> getRecentlyActiveFriendsEnriched();

  /// Get relationship(s) between a user and a target user
  Future<user_proto.GetUserRelationshipsResponse> getRelationshipsWithUser({
    required String userId,
    required String targetUserId,
  });

  /// Get unified relationship status between two users
  ///
  /// This method provides comprehensive relationship status including primary
  /// relationship type, blocking status, pending requests, and bubble membership.
  /// Replaces multiple separate calls with a single efficient backend lookup.
  Future<user_proto.GetUserRelationshipStatusResponse> getUserRelationshipStatus({
    required String userId,
    required String targetUserId,
  });







  /// Get mutual friends between two users
  Future<List<common.User>> getMutualFriends(String userId, String otherUserId);



  /// Get recently active users
  Future<List<common.User>> getRecentlyActiveUsers(String userId);

  /// Get sorted contacts with server-side sorting and filtering
  Future<List<common.User>> getSortedContacts({
    required String userId,
    required String sortBy,
    required bool ascending,
    int page = 1,
    int pageSize = 20,
    String? filterQuery,
    List<String>? relationshipTypes,
  });

  // DEPRECATED METHODS REMOVED
  // All friend filtering methods have been replaced with the unified getFriends method
  // that accepts a FriendFilter parameter. This provides better type safety,
  // more flexible filtering options, and cleaner API design.

  // ================================================================
  // Bubble Operations
  // ================================================================
  /// Get bubble members
  Future<List<common.User>> getBubbleMembers(String bubbleId);

  /// Select friends for bubble
  Future<void> selectFriends(String bubbleId, List<String> selectedUserIds);



  // ================================================================
  // Friend Request Operations (for bubble expiry auto-generation)
  // ================================================================
  /// Get all pending requests (friend, bubble join, bubble invite)
  Future<List<dynamic>> getPendingRequests();

  /// Get pending friend requests (incoming, outgoing, or both)
  Future<List<user_proto.PendingFriendRequest>> getPendingFriendRequests({
    required String userId,
    String direction = 'both', // 'incoming', 'outgoing', 'both'
    int page = 1,
    int pageSize = 20,
  });

  /// Send friend request (auto-generated only)
  Future<String> sendFriendRequest({
    required String fromUserId,
    required String toUserId,
    String? message,
  });

  /// Accept friend request
  Future<void> acceptFriendRequest({
    required String userId,
    required String requesterId,
  });

  /// Decline friend request
  Future<void> declineFriendRequest({
    required String userId,
    required String requesterId,
    String? reason,
  });

  /// Get maybe_friend relationships (from bubble expiry)
  Future<List<user_proto.UserRelationship>> getMaybeFriendRelationships({
    required String userId,
    int page = 1,
    int pageSize = 20,
  });

  /// Accept friend request (between users who are already maybe_friends)
  Future<void> acceptFriendRequestRelationship({
    required String userId,
    required String otherUserId,
  });

  /// Decline friend request (between users who are already maybe_friends)
  Future<void> declineFriendRequestRelationship({
    required String userId,
    required String otherUserId,
  });

  /// Handle bubble-related requests (accept/decline)
  Future<void> handleBubbleRequest(String requestId, String action);

  /// Remove a friend (unfriend)
  Future<void> removeFriend(String friendId);

  /// Create friendships from bubble expiry (auto-generate friend requests)
  Future<void> createFriendshipsFromBubble(String bubbleId);

  /// Check if user is friend
  Future<bool> isFriend(String userId, String otherUserId);

  /// Get friend request history with pagination and filtering
  Future<user_proto.GetFriendRequestHistoryResponse> getFriendRequestHistory({
    required String userId,
    int page = 0,
    int pageSize = 20,
    String direction = 'both',
    List<String> statuses = const ['pending', 'accepted', 'declined', 'expired'],
  });

  /// Get former bubble members for "add friend after bubble" flow
  Future<user_proto.GetFormerBubbleMembersResponse> getFormerBubbleMembers({
    required String bubbleId,
    required String requestingUserId,
    int page = 0,
    int pageSize = 20,
    DateTime? since,
  });

  // ================================================================
  // Content Report Services
  // ================================================================

  /// Report content (messages, bubbles, media, etc.)
  Future<user_proto.ReportContentResponse> reportContent(
    user_proto.ReportContentRequest request,
  );

  /// Get content report by ID
  Future<user_proto.GetContentReportResponse> getContentReport(
    user_proto.GetContentReportRequest request,
  );

  /// List content reports with filtering
  Future<user_proto.ListContentReportsResponse> listContentReports(
    user_proto.ListContentReportsRequest request,
  );

  /// Update content report status
  Future<user_proto.UpdateContentReportStatusResponse> updateContentReportStatus(
    user_proto.UpdateContentReportStatusRequest request,
  );

  /// Delete content report
  Future<user_proto.DeleteContentReportResponse> deleteContentReport(
    user_proto.DeleteContentReportRequest request,
  );

  // ================================================================
  // User Location Services
  // ================================================================

  /// Update user location
  Future<void> updateUserLocation({
    required String userId,
    required double latitude,
    required double longitude,
    double? accuracy,
    String? address,
  });

  /// Get user location
  Future<user_proto.GetUserLocationResponse> getUserLocation(String userId);

  /// Update location privacy settings
  Future<void> updateLocationPrivacy({
    required String userId,
    required bool shareLocation,
    required String privacyLevel, // 'public', 'friends', 'private'
  });
}

/// Implementation of UserRemoteDataSource using gRPC client service
class UserRemoteDataSourceImpl extends BaseRemoteDataSource
    implements UserRemoteDataSource {
  UserRemoteDataSourceImpl(
    this._grpcClientService,
    this._getCurrentUserId,
    ResilienceRepository resilienceRepository,
  ) : super(resilienceRepository);

  final GrpcClientService _grpcClientService;
  final Future<String> Function() _getCurrentUserId;

  // Current user ID for operations requiring authentication context
  Future<String> get _currentUserId async {
    final userId = await _getCurrentUserId();
    if (userId.isEmpty) {
      throw const AuthenticationException(
        message: 'User must be authenticated to perform user operations',
      );
    }
    return userId;
  }

  @override
  Future<common.User> getCurrentUserProfile() => executeGrpcCallWithRetry(
    operationName: 'getCurrentUserProfile',
    grpcCall: () async {
      final request = user_proto.GetUserRequest();
      final response = await _grpcClientService.userService.getUser(request, options: _grpcClientService.createCallOptions());
      return response.user;
    },
  );

  @override
  Future<common.User> getUserProfile(String userId) => executeGrpcCallWithRetry(
    operationName: 'getUserProfile',
    grpcCall: () async {
      final request = user_proto.GetUserRequest()..userId = userId;
      final response = await _grpcClientService.userService.getUser(request, options: _grpcClientService.createCallOptions());
      return response.user;
    },
  );

  @override
  Future<common.User> getEnhancedUserProfile(String userId) => executeGrpcCallWithRetry(
    operationName: 'getEnhancedUserProfile',
    grpcCall: () async {
      final request = social_analytics_grpc.GetEnhancedProfileRequest()..userId = userId;
      final response = await _grpcClientService.socialAnalyticsService.getEnhancedProfile(request, options: _grpcClientService.createCallOptions());
      return common.User()
        ..id = userId
        ..username = response.profile.username
        ..email = response.profile.email
        ..firstName = response.profile.firstName
        ..lastName = response.profile.lastName
        ..avatarUrl = response.profile.avatarUrl
        ..bubbleId = response.profile.bubbleId
        ..isOnline = response.profile.isOnline;
    },
  );

  @override
  Future<List<common.User>> getUsersBatch(List<String> userIds) =>
      executeGrpcCallWithRetry(
        operationName: 'getUsersBatch',
        grpcCall: () async {
          final request = user_proto.GetUsersBatchRequest()
            ..userIds.addAll(userIds);
          final response = await _grpcClientService.userService.getUsersBatch(
            request,
            options: _grpcClientService.createCallOptions(),
          );
          return response.users;
        },
        metadata: {'userIds': userIds.join(',')},
      );

  @override
  Future<List<common.User>> getAllUsers() =>
      executeGrpcCallWithRetry(
        operationName: 'getAllUsers',
        grpcCall: () async {
          // ✅ OPTIMIZED: Use search with empty query but with proper pagination
          // This is the most efficient approach since there's no dedicated getAllUsers endpoint
          final request = user_proto.SearchUsersRequest()
            ..query = '' // Empty query returns all users
            ..page = 0
            ..pageSize = 1000; // Large page size for bulk operations

          final response = await _grpcClientService.userService.searchUsers(
            request,
            options: _grpcClientService.createCallOptions(),
          );
          return response.users;
        },
        metadata: {},
      );

  @override
  Future<common.User> createUser(common.User user) =>
      executeGrpcCallWithRetry(
        operationName: 'createUser',
        grpcCall: () async {
          final request = user_proto.CreateUserRequest();
          // Copy user fields to request
          if (user.hasEmail()) {
            request.email = user.email;
          }
          if (user.hasUsername()) {
            request.username = user.username;
          }
          if (user.hasFirstName()) {
            request.firstName = user.firstName;
          }
          if (user.hasLastName()) {
            request.lastName = user.lastName;
          }

          final response = await _grpcClientService.userService.createUser(
            request,
            options: _grpcClientService.createCallOptions(),
          );
          return response.user;
        },
        metadata: {'userId': user.id},
      );

  @override
  Future<common.User> updateUser(common.User user) =>
      executeGrpcCallWithRetry(
        operationName: 'updateUser',
        grpcCall: () async {
          final request = user_proto.UpdateUserRequest()
            ..userId = user.id;
          // Copy user fields to request
          if (user.hasUsername()) {
            request.username = user.username;
          }
          if (user.hasFirstName()) {
            request.firstName = user.firstName;
          }
          if (user.hasLastName()) {
            request.lastName = user.lastName;
          }
          if (user.hasAvatarUrl()) {
            request.avatarUrl = user.avatarUrl;
          }

          final response = await _grpcClientService.userService.updateUser(
            request,
            options: _grpcClientService.createCallOptions(),
          );
          return response.user;
        },
        metadata: {'userId': user.id},
      );

  @override
  Future<common.User> updateUserProfile(user_proto.UpdateUserRequest request) =>
      executeGrpcCallWithRetry(
        operationName: 'updateUserProfile',
        grpcCall: () async {
          final response = await _grpcClientService.userService.updateUser(
            request,
            options: _grpcClientService.createCallOptions(),
          );
          return response.user;
        },
      );

  @override
  Future<void> deleteUser(String userId) => executeGrpcCallWithRetry(
    operationName: 'deleteUser',
    grpcCall: () async {
      final request = user_proto.DeleteUserRequest()..userId = userId;
      await _grpcClientService.userService.deleteUser(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
    metadata: {'userId': userId},
  );

  @override
  Future<void> deactivateUser(String userId, String reason) => executeGrpcCallWithRetry(
    operationName: 'deactivateUser',
    grpcCall: () async {
      final request = user_proto.DeactivateUserRequest()
        ..userId = userId
        ..deactivationReason = reason
        ..deactivationType = 'user_requested'
        ..preserveData = true;
      await _grpcClientService.userService.deactivateUser(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
    metadata: {'userId': userId},
  );

  @override
  Future<List<common.User>> searchUsers({
    String query = '',
    int page = 1,
    int pageSize = 20,
  }) => executeGrpcCallWithRetry(
    operationName: 'searchUsers',
    grpcCall: () async {
      final request = user_proto.SearchUsersRequest()
        ..query = query
        ..page = page
        ..pageSize = pageSize;
      final response = await _grpcClientService.userService.searchUsers(request, options: _grpcClientService.createCallOptions());
      return response.users;
    },
  );

  @override
  Future<user_proto.UserStats> getUserStats(String userId) => executeGrpcCallWithRetry(
    operationName: 'getUserStats',
    grpcCall: () async {
      final request = user_proto.GetUserStatsRequest(userId: userId);
      final response = await _grpcClientService.userService.getUserStats(request, options: _grpcClientService.createCallOptions());
      return response.stats;
    },
  );

  @override
  Future<void> blockUser(String userId, String blockedUserId, String reason) =>
      executeGrpcCallWithRetry(
        operationName: 'blockUser',
        grpcCall: () async {
          final request = user_proto.BlockUserRequest(
            userId: userId,
            blockedUserId: blockedUserId,
            reason: reason,
          );
          await _grpcClientService.userService.blockUser(request, options: _grpcClientService.createCallOptions());
        },
      );

  @override
  Future<void> unblockUser(String userId, String blockedUserId) =>
      executeGrpcCallWithRetry(
        operationName: 'unblockUser',
        grpcCall: () async {
          final request = user_proto.UnblockUserRequest(
            userId: userId,
            blockedUserId: blockedUserId,
          );
          await _grpcClientService.userService.unblockUser(request, options: _grpcClientService.createCallOptions());
        },
      );

  @override
  Future<List<common.User>> getBlockedUsers(
    String userId, {
    int page = 1,
    int pageSize = 20,
  }) => executeGrpcCallWithRetry(
    operationName: 'getBlockedUsers',
    grpcCall: () async {
      final request = user_proto.GetUserBlockedUsersRequest()
        ..userId = userId
        ..page = page
        ..pageSize = pageSize;
      final response = await _grpcClientService.userService.getUserBlockedUsers(request, options: _grpcClientService.createCallOptions());
      return response.blockedUsers;
    },
  );

  @override
  Future<String> submitUserReport({
    required String reporterId,
    required String reportedUserId,
    required String reason,
    required String description,
    required String category,
    Map<String, String>? evidence,
  }) => executeGrpcCallWithRetry(
    operationName: 'submitUserReport',
    grpcCall: () async {
      final request = user_proto.ReportUserRequest(
        reporterId: reporterId,
        reportedUserId: reportedUserId,
        reason: reason,
        description: description,
        category: category,
      );
      final response = await _grpcClientService.userService.reportUser(request, options: _grpcClientService.createCallOptions());
      return response.reportId;
    },
  );

  @override
  Future<String> submitFeedback({
    required String userId,
    required String title,
    required String description,
    required String category,
    int rating = 5,
    String? userEmail,
  }) => executeGrpcCallWithRetry(
    operationName: 'submitFeedback',
    grpcCall: () async {
      final request = user_proto.SubmitFeedbackRequest(
        userId: userId,
        title: title,
        description: description,
        category: category,
        rating: rating,
        userEmail: userEmail ?? '',
      );
      final response = await _grpcClientService.userService.submitFeedback(request, options: _grpcClientService.createCallOptions());
      return response.feedbackId;
    },
  );

  @override
  Future<String> submitBugReport({
    required String userId,
    required String title,
    required String description,
    String? userEmail,
    String? deviceInfo,
    String? appVersion,
    String? osVersion,
    Map<String, String>? additionalInfo,
  }) => executeGrpcCallWithRetry(
    operationName: 'submitBugReport',
    grpcCall: () async {
      final request = user_proto.SubmitBugReportRequest(
        userId: userId,
        title: title,
        description: description,
        userEmail: userEmail ?? '',
        deviceInfo: deviceInfo ?? '',
        appVersion: appVersion ?? '',
        osVersion: osVersion ?? '',
        additionalInfo: (additionalInfo ?? {}).entries,
      );
      final response = await _grpcClientService.userService.submitBugReport(request, options: _grpcClientService.createCallOptions());
      return response.bugReportId;
    },
  );

  @override
  Future<String> reportUser({
    required String reporterId,
    required String reportedUserId,
    required String reason,
    required String description,
    required String category,
  }) => executeGrpcCallWithRetry(
    operationName: 'reportUser',
    grpcCall: () async {
      final request = user_proto.ReportUserRequest(
        reporterId: reporterId,
        reportedUserId: reportedUserId,
        reason: reason,
        description: description,
        category: category,
      );
      final response = await _grpcClientService.userService.reportUser(request, options: _grpcClientService.createCallOptions());
      return response.reportId;
    },
  );

  @override
  Future<String> getUserLanguagePreference(String userId) => executeGrpcCallWithRetry(
    operationName: 'getUserLanguagePreference',
    grpcCall: () async {
      final request =
          user_proto.GetUserLanguagePreferenceRequest()..userId = userId;
      final response = await _grpcClientService.userService
          .getUserLanguagePreference(request, options: _grpcClientService.createCallOptions());
      return response.languagePreference;
    },
  );

  @override
  Future<void> updateUserLanguagePreference({
    required String userId,
    required String languagePreference,
  }) => executeGrpcCallWithRetry(
    operationName: 'updateUserLanguagePreference',
    grpcCall: () async {
      final request =
          user_proto.UpdateUserLanguagePreferenceRequest()
            ..userId = userId
            ..languagePreference = languagePreference;
      await _grpcClientService.userService.updateUserLanguagePreference(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
  );

  // ================================================================
  // Contacts (relationships under user microservice)
  // ================================================================
  @override
  Future<List<common.Contact>> getContacts() => executeGrpcCallWithRetry(
    operationName: 'getContacts',
    grpcCall: () async {
      final request = user_proto.GetContactsRequest();
      final response = await _grpcClientService.userService.getContacts(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response.contacts
          .map(
            (user) =>
                common.Contact()
                  ..id = user.id
                  ..userId = user.id
                  ..contactUserId =
                      user
                          .id // Fixed field name
                  ..status = common.ContactStatus.CONTACT_STATUS_ACCEPTED,
          )
          .toList();
    },
  );

  @override
  Future<void> sendContactRequest(String contactUserId) => executeGrpcCallWithRetry(
    operationName: 'sendContactRequest',
    grpcCall: () async {
      final request =
          user_proto.SendContactRequestRequest()..targetUserId = contactUserId;
      await _grpcClientService.userService.sendContactRequest(request, options: _grpcClientService.createCallOptions());
    },
  );

  @override
  Future<void> acceptContactRequest(String contactRequestId) => executeGrpcCallWithRetry(
    operationName: 'acceptContactRequest',
    grpcCall: () async {
      final request =
          user_proto.AcceptContactRequestRequest()
            ..requesterId = contactRequestId; // Using requesterId field
      await _grpcClientService.userService.acceptContactRequest(request, options: _grpcClientService.createCallOptions());
    },
  );

  @override
  Future<void> rejectContactRequest(String contactRequestId) => executeGrpcCallWithRetry(
    operationName: 'rejectContactRequest',
    grpcCall: () async {
      final request =
          user_proto.DeclineContactRequestRequest()
            ..requesterId = contactRequestId; // Using requesterId field
      await _grpcClientService.userService.declineContactRequest(request, options: _grpcClientService.createCallOptions());
    },
  );

  @override
  Future<void> removeContact(String contactId) => executeGrpcCallWithRetry(
    operationName: 'removeContact',
    grpcCall: () async {
      final request = user_proto.RemoveContactRequest()..contactId = contactId;
      await _grpcClientService.userService.removeContact(request, options: _grpcClientService.createCallOptions());
    },
  );

  @override
  Future<List<sync_proto.ContactRequest>> getPendingReceivedRequests() =>
      executeGrpcCallWithRetry(
        operationName: 'getPendingReceivedRequests',
        grpcCall: () async {
          // Use User Service for contact requests, not Sync Service
          final request = user_proto.GetPendingContactRequestsRequest()
            ..direction = 'incoming'
            ..page = 0
            ..pageSize = 100;
          final response = await _grpcClientService.userService.getPendingContactRequests(
            request,
            options: _grpcClientService.createCallOptions(),
          );

          // Convert user service response to sync service format
          return response.requests.map((req) => sync_proto.ContactRequest()
            ..id = req.requestId
            ..fromUserId = req.fromUserId
            ..toUserId = req.toUserId
            ..message = req.message
            ..status = common.ContactStatus.CONTACT_STATUS_PENDING // Default status for pending requests
            ..createdAt = req.createdAt
            ..updatedAt = req.createdAt // Use createdAt as updatedAt since expiresAt doesn't exist
          ).toList();
        },
      );

  @override
  Future<List<sync_proto.ContactRequest>> getPendingSentRequests() =>
      executeGrpcCallWithRetry(
        operationName: 'getPendingSentRequests',
        grpcCall: () async {
          // Use User Service for contact requests, not Sync Service
          final request = user_proto.GetPendingContactRequestsRequest()
            ..direction = 'outgoing'
            ..page = 0
            ..pageSize = 100;
          final response = await _grpcClientService.userService.getPendingContactRequests(
            request,
            options: _grpcClientService.createCallOptions(),
          );

          // Convert user service response to sync service format
          return response.requests.map((req) => sync_proto.ContactRequest()
            ..id = req.requestId
            ..fromUserId = req.fromUserId
            ..toUserId = req.toUserId
            ..message = req.message
            ..status = common.ContactStatus.CONTACT_STATUS_PENDING // Default status for pending requests
            ..createdAt = req.createdAt
            ..updatedAt = req.createdAt // Use createdAt as updatedAt since expiresAt doesn't exist
          ).toList();
        },
      );

  @override
  Future<sync_proto.ContactRequest> getContactRequest(String requestId) =>
      executeGrpcCallWithRetry(
        operationName: 'getContactRequest',
        grpcCall: () async {
          // Use the new dedicated backend endpoint for efficient single-entity fetching
          final currentUserId = await _getCurrentUserId();

          final request = user_proto.GetContactRequestRequest()
            ..requestId = requestId
            ..userId = currentUserId;

          final response = await _grpcClientService.userService.getContactRequest(
            request,
            options: _grpcClientService.createCallOptions(),
          );

          // Convert user service response to sync service format
          final req = response.request;
          return sync_proto.ContactRequest()
            ..id = req.requestId
            ..fromUserId = req.fromUserId
            ..toUserId = req.toUserId
            ..message = req.message
            ..status = common.ContactStatus.CONTACT_STATUS_PENDING
            ..createdAt = req.createdAt
            ..updatedAt = req.createdAt;
        },
      );

  @override
  Future<List<sync_proto.ContactRequest>>
  getContactRequestHistory() => executeGrpcCallWithRetry(
    operationName: 'getContactRequestHistory',
    grpcCall: () async {
      // Get current user ID
      final currentUserId = await _getCurrentUserId();

      // Use the user service to get contact request history
      final request = user_proto.GetPendingContactRequestsRequest()
        ..userId = currentUserId
        ..direction = 'both' // Get both incoming and outgoing
        ..page = 0
        ..pageSize = 100; // Get a large number to include history

      final response = await _grpcClientService.userService.getPendingContactRequests(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      // Convert user service contact requests to sync service format
      final allContactRequests = <sync_proto.ContactRequest>[];

      for (final pendingRequest in response.requests) {
        final contactRequest = sync_proto.ContactRequest()
          ..id = pendingRequest.requestId
          ..fromUserId = pendingRequest.fromUserId
          ..toUserId = pendingRequest.toUserId
          ..message = pendingRequest.message
          ..status = common.ContactStatus.CONTACT_STATUS_PENDING // Default status since PendingContactRequest doesn't have status
          ..createdAt = pendingRequest.createdAt
          ..updatedAt = pendingRequest.createdAt; // Use createdAt as updatedAt since expiresAt doesn't exist

        allContactRequests.add(contactRequest);
      }

      return allContactRequests;
    },
  );





  @override
  Future<void> cancelContactRequest(String requestId) => executeGrpcCallWithRetry(
    operationName: 'cancelContactRequest',
    grpcCall: () async {
      final request =
          user_proto.DeclineContactRequestRequest()
            ..requesterId = requestId;
      await _grpcClientService.userService.declineContactRequest(request, options: _grpcClientService.createCallOptions());
    },
  );

  @override
  Future<List<common.User>> getSuggestedContacts() => executeGrpcCallWithRetry(
    operationName: 'getSuggestedContacts',
    grpcCall: () async {
      // ✅ OPTIMIZED: Use dedicated search service for user suggestions instead of generic search
      final currentUserId = await _currentUserId;

      final request = search_proto.GetUserSuggestionsRequest()
        ..userId = currentUserId
        ..limit = 20;

      final response = await _grpcClientService.searchService.getUserSuggestions(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      // Convert UserSuggestion to User objects
      return response.suggestions.map((suggestion) => common.User()
        ..id = suggestion.userId
        ..username = suggestion.username
        ..firstName = suggestion.firstName
        ..lastName = suggestion.lastName
        ..avatarUrl = suggestion.avatarUrl
      ).toList();
    },
  );

  @override
  Future<List<common.User>> getMutualContacts(String userId) => executeGrpcCallWithRetry(
    operationName: 'getMutualContacts',
    grpcCall: () async {
      // Assuming the first user is the currently authenticated one.
      // This needs to be fetched from an auth service.
      final currentUser = await getCurrentUserProfile();
      final req = social_analytics_grpc.GetMutualContactsRequest()
        ..userId = currentUser.id
        ..targetUserId = userId;
      final resp = await _grpcClientService.socialAnalyticsService.getMutualContacts(req, options: _grpcClientService.createCallOptions());
      final ids = resp.mutualContacts.toList();
      if (ids.isEmpty) {
        return [];
      }
      final batchRequest =
          user_proto.GetUsersBatchRequest()..userIds.addAll(ids);
      final batchResponse = await _grpcClientService.userService.getUsersBatch(
        batchRequest,
        options: _grpcClientService.createCallOptions(),
      );
      return batchResponse.users;
    },
  );

  // ================================================================
  // Enriched Relationship Methods (return relationship objects with embedded user data)
  // ================================================================
  @override
  Future<List<common.Contact>> getContactsEnriched() => executeGrpcCallWithRetry(
    operationName: 'getContactsEnriched',
    grpcCall: () async {
      final request = user_proto.GetContactsRequest();
      final response = await _grpcClientService.userService.getContacts(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response.contacts;
    },
  );

  @override
  Future<List<common.Friendship>> getFriendsEnriched() => executeGrpcCallWithRetry(
    operationName: 'getFriendsEnriched',
    grpcCall: () async {
      final request = user_proto.GetFriendsRequest();
      final response = await _grpcClientService.userService.getFriends(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response.friends;
    },
  );

  @override
  Future<List<common.Friendship>> getMaybeFriendsEnriched() => executeGrpcCallWithRetry(
    operationName: 'getMaybeFriendsEnriched',
    grpcCall: () async {
      final request = user_proto.GetMaybeFriendsRequest();
      final response = await _grpcClientService.userService.getMaybeFriends(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response.maybeFriends;
    },
  );

  @override
  Future<List<common.Friendship>> getRecentlyActiveFriendsEnriched() => executeGrpcCallWithRetry(
    operationName: 'getRecentlyActiveFriendsEnriched',
    grpcCall: () async {
      final request = user_proto.GetRecentlyActiveFriendsRequest();
      final response = await _grpcClientService.userService.getRecentlyActiveFriends(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response.friends;
    },
  );

  // ================================================================








  @override
  Future<List<common.User>> getMutualFriends(
    String userId,
    String otherUserId,
  ) => executeGrpcCallWithRetry(
    operationName: 'getMutualFriends',
    grpcCall: () async {
      // Assuming the first user is the currently authenticated one.
      final currentUser = await getCurrentUserProfile();
      final req = social_analytics_grpc.GetMutualFriendsAnalyticsRequest()
        ..userId = currentUser.id
        ..targetUserId = otherUserId;
      final resp = await _grpcClientService.socialAnalyticsService
          .getMutualFriendsAnalytics(req, options: _grpcClientService.createCallOptions());
      final friendIds = resp.mutualFriends.toList();
      if (friendIds.isEmpty) {
        return [];
      }
      final batchRequest =
          user_proto.GetUsersBatchRequest()..userIds.addAll(friendIds);
      final batchResponse = await _grpcClientService.userService.getUsersBatch(
        batchRequest,
        options: _grpcClientService.createCallOptions(),
      );
      return batchResponse.users;
    },
  );



  @override
  Future<List<common.User>> getRecentlyActiveUsers(String userId) =>
      executeGrpcCallWithRetry(
        operationName: 'getRecentlyActiveUsers',
        grpcCall: () async {
          // Use the new efficient server-side endpoint for recently active friends
          final request = user_proto.GetRecentlyActiveFriendsRequest()
            ..userId = userId
            ..limit = 20
            ..hoursWindow = 24;

          final response = await _grpcClientService.userService.getRecentlyActiveFriends(
            request,
            options: _grpcClientService.createCallOptions(),
          );
          return response.friends.cast<common.User>().toList();
        },
      );

  @override
  Future<List<common.User>> getSortedContacts({
    required String userId,
    required String sortBy,
    required bool ascending,
    int page = 1,
    int pageSize = 20,
    String? filterQuery,
    List<String>? relationshipTypes,
  }) => executeGrpcCallWithRetry(
        operationName: 'getSortedContacts',
        grpcCall: () async {
          // Convert sort by string to enum
          user_proto.UserSortBy sortByEnum;
          switch (sortBy.toLowerCase()) {
            case 'name':
              sortByEnum = user_proto.UserSortBy.USER_SORT_BY_NAME;
              break;
            case 'username':
              sortByEnum = user_proto.UserSortBy.USER_SORT_BY_USERNAME;
              break;
            case 'last_active':
              sortByEnum = user_proto.UserSortBy.USER_SORT_BY_LAST_ACTIVE;
              break;
            case 'relationship_type':
              sortByEnum = user_proto.UserSortBy.USER_SORT_BY_RELATIONSHIP_TYPE;
              break;
            case 'created_at':
              sortByEnum = user_proto.UserSortBy.USER_SORT_BY_CREATED_AT;
              break;
            default:
              sortByEnum = user_proto.UserSortBy.USER_SORT_BY_NAME;
          }

          final request = user_proto.GetSortedContactsRequest()
            ..userId = userId
            ..sortBy = sortByEnum
            ..ascending = ascending
            ..page = page
            ..pageSize = pageSize;

          if (filterQuery != null && filterQuery.isNotEmpty) {
            request.filterQuery = filterQuery;
          }

          if (relationshipTypes != null && relationshipTypes.isNotEmpty) {
            request.relationshipTypes.addAll(relationshipTypes);
          }

          final response = await _grpcClientService.userService.getSortedContacts(request);
          return response.contacts.cast<common.User>().toList();
        },
      );

  // DEPRECATED METHODS REMOVED
  // All deprecated friend filtering methods have been removed.
  // Use the unified getFriends method with appropriate FriendFilter instead.

  // ================================================================
  // Bubble Operations
  // ================================================================
  @override
  Future<List<common.User>> getBubbleMembers(
    String bubbleId,
  ) => executeGrpcCallWithRetry(
    operationName: 'getBubbleMembers',
    grpcCall: () async {
      final req = bubble_grpc.GetBubbleMembersRequest()..bubbleId = bubbleId;
      final resp = await _grpcClientService.bubbleService.getBubbleMembers(req, options: _grpcClientService.createCallOptions());
      final userIds = resp.members.map((m) => m.userId).toList();
      if (userIds.isEmpty) {
        return <common.User>[];
      }
      final batchReq =
          user_proto.GetUsersBatchRequest()..userIds.addAll(userIds);
      final batchResp = await _grpcClientService.userService.getUsersBatch(
        batchReq,
        options: _grpcClientService.createCallOptions(),
      );
      return batchResp.users;
    },
  );

  @override
  Future<void> selectFriends(String bubbleId, List<String> selectedUserIds) =>
      executeGrpcCallWithRetry(
        operationName: 'selectFriends',
        grpcCall: () async {
          final currentUserId = await _currentUserId;
          final req =
              bubble_grpc.SendBubbleInviteRequest()
                ..bubbleId = bubbleId
                ..inviterId = currentUserId
                ..inviteeIds.addAll(selectedUserIds);
          await _grpcClientService.bubbleService.sendBubbleInvite(req, options: _grpcClientService.createCallOptions());
        },
      );



  // ================================================================
  // Friend Request Operations (for bubble expiry auto-generation)
  // ================================================================
  @override
  Future<List<dynamic>> getPendingRequests() => executeGrpcCallWithRetry(
    operationName: 'getPendingRequests',
    grpcCall: () async {
      // Get all pending requests from User Service
      final currentUserId = await _currentUserId;
      final contactRequest = user_proto.GetPendingContactRequestsRequest()
        ..userId = currentUserId
        ..direction = 'both'
        ..page = 0
        ..pageSize = 100;
      final contactResponse = await _grpcClientService.userService.getPendingContactRequests(
        contactRequest,
        options: _grpcClientService.createCallOptions(),
      );

      final friendRequest = user_proto.GetPendingFriendRequestsRequest()
        ..userId = currentUserId
        ..direction = 'both'
        ..page = 0
        ..pageSize = 100;
      final friendResponse = await _grpcClientService.userService.getPendingFriendRequests(
        friendRequest,
        options: _grpcClientService.createCallOptions(),
      );

      final allRequests = <dynamic>[
        ...contactResponse.requests,
        ...friendResponse.requests,
      ];
      return allRequests;
    },
  );

  @override
  Future<void> handleBubbleRequest(String requestId, String action) =>
      executeGrpcCallWithRetry(
        operationName: 'handleBubbleRequest',
        grpcCall: () async {
          if (action == 'accept') {
            final req =
                bubble_grpc.AcceptBubbleRequestRequest()..requestId = requestId;
            await _grpcClientService.bubbleService.acceptBubbleRequest(req, options: _grpcClientService.createCallOptions());
          } else if (action == 'decline' || action == 'reject') {
            final req =
                bubble_grpc.RejectBubbleRequestRequest()..requestId = requestId;
            await _grpcClientService.bubbleService.rejectBubbleRequest(req, options: _grpcClientService.createCallOptions());
          } else {
            throw ValidationException(message: 'Invalid action: $action');
          }
        },
      );

  @override
  Future<void> removeFriend(String friendId) => executeGrpcCallWithRetry(
    operationName: 'removeFriend',
    grpcCall: () async {
      final currentUser = await getCurrentUserProfile();
      final request = user_proto.RemoveFriendRequest()
        ..userId = currentUser.id
        ..friendId = friendId;
      await _grpcClientService.userService.removeFriend(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
  );
  
  @override
  Future<void> createFriendshipsFromBubble(String bubbleId) => executeGrpcCallWithRetry(
    operationName: 'createFriendshipsFromBubble',
    grpcCall: () async {
      final request = user_proto.CreateFriendshipsFromBubbleRequest()
        ..bubbleId = bubbleId;
      await _grpcClientService.userService.createFriendshipsFromBubble(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
  );

  @override
  Future<List<user_proto.PendingFriendRequest>> getPendingFriendRequests({
    required String userId,
    String direction = 'both',
    int page = 1,
    int pageSize = 20,
  }) => executeGrpcCallWithRetry(
    operationName: 'getPendingFriendRequests',
    grpcCall: () async {
      final request = user_proto.GetPendingFriendRequestsRequest()
        ..userId = userId
        ..direction = direction
        ..page = page
        ..pageSize = pageSize;
      final response = await _grpcClientService.userService.getPendingFriendRequests(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response.requests;
    },
  );

  @override
  Future<String> sendFriendRequest({
    required String fromUserId,
    required String toUserId,
    String? message,
  }) => executeGrpcCallWithRetry(
    operationName: 'sendFriendRequest',
    grpcCall: () async {
      final request = user_proto.SendFriendRequestRequest()
        ..userId = fromUserId
        ..targetUserId = toUserId
        ..message = message ?? '';
      final response = await _grpcClientService.userService.sendFriendRequest(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response.requestId;
    },
  );

  @override
  Future<void> acceptFriendRequest({
    required String userId,
    required String requesterId,
  }) => executeGrpcCallWithRetry(
    operationName: 'acceptFriendRequest',
    grpcCall: () async {
      final request = user_proto.AcceptFriendRequestRequest()
        ..userId = userId
        ..requesterId = requesterId;
      await _grpcClientService.userService.acceptFriendRequest(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
  );

  @override
  Future<void> declineFriendRequest({
    required String userId,
    required String requesterId,
    String? reason,
  }) => executeGrpcCallWithRetry(
    operationName: 'declineFriendRequest',
    grpcCall: () async {
      final request = user_proto.DeclineFriendRequestRequest()
        ..userId = userId
        ..requesterId = requesterId
        ..reason = reason ?? '';
      await _grpcClientService.userService.declineFriendRequest(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
  );

  @override
  Future<List<user_proto.UserRelationship>> getMaybeFriendRelationships({
    required String userId,
    int page = 1,
    int pageSize = 20,
  }) => executeGrpcCallWithRetry(
    operationName: 'getMaybeFriendRelationships',
    grpcCall: () async {
      // ✅ OPTIMIZED: Use dedicated GetMaybeFriends endpoint instead of filtering all relationships
      final request = user_proto.GetMaybeFriendsRequest()
        ..userId = userId
        ..page = page
        ..pageSize = pageSize;

      final response = await _grpcClientService.userService.getMaybeFriends(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      // Convert MaybeFriend objects to UserRelationship format for compatibility
      return response.maybeFriends.map((maybeFriend) =>
        user_proto.UserRelationship()
          ..fromUserId = userId
          ..toUserId = maybeFriend.id
          ..relationshipType = 'maybefriend'
          ..createdAt = maybeFriend.createdAt
          ..updatedAt = maybeFriend.updatedAt
      ).toList();
    },
  );

  @override
  Future<void> acceptFriendRequestRelationship({
    required String userId,
    required String otherUserId,
  }) => executeGrpcCallWithRetry(
    operationName: 'acceptFriendRequestRelationship',
    grpcCall: () async {
      final request = user_proto.AcceptFriendRequestRequest()
        ..userId = userId
        ..requesterId = otherUserId;

      await _grpcClientService.userService.acceptFriendRequest(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
  );

  @override
  Future<void> declineFriendRequestRelationship({
    required String userId,
    required String otherUserId,
  }) => executeGrpcCallWithRetry(
    operationName: 'declineFriendRequestRelationship',
    grpcCall: () async {
      final request = user_proto.DeclineFriendRequestRelationshipRequest()
        ..userId = userId
        ..otherUserId = otherUserId;

      await _grpcClientService.userService.declineFriendRequestRelationship(
        request,
        options: _grpcClientService.createCallOptions(),
      );
    },
  );

  @override
  Future<bool> isFriend(String userId, String otherUserId) => executeGrpcCallWithRetry(
    operationName: 'isFriend',
    grpcCall: () async {
      final request = user_proto.GetUserRelationshipsRequest()
        ..userId = userId
        ..targetUserId = otherUserId;
      final response = await _grpcClientService.userService.getUserRelationships(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response.relationships.any((rel) =>
        rel.relationshipType == 'friend' && rel.status == 'active');
    },
  );

  /// Get relationship(s) between a user and a target user (backend lookup)
  @override
  Future<user_proto.GetUserRelationshipsResponse> getRelationshipsWithUser({
    required String userId,
    required String targetUserId,
  }) => executeGrpcCallWithRetry(
    operationName: 'getRelationshipsWithUser',
    grpcCall: () async {
      final request = user_proto.GetUserRelationshipsRequest()
        ..userId = userId
        ..targetUserId = targetUserId;
      final response = await _grpcClientService.userService.getUserRelationships(
        request,
        options: _grpcClientService.createCallOptions(),
      );
      return response;
    },
  );

  /// Get unified relationship status between two users (backend lookup)
  @override
  Future<user_proto.GetUserRelationshipStatusResponse> getUserRelationshipStatus({
    required String userId,
    required String targetUserId,
  }) => executeGrpcCallWithRetry(
    operationName: 'getUserRelationshipStatus',
    grpcCall: () async {
      LoggingService.info('🔍 UserRemoteDataSource: Getting relationship status between $userId and $targetUserId');

      final request = user_proto.GetUserRelationshipStatusRequest()
        ..userId = userId
        ..targetUserId = targetUserId;

      final response = await _grpcClientService.userService.getUserRelationshipStatus(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      LoggingService.success('✅ UserRemoteDataSource: Successfully retrieved relationship status');
      return response;
    },
  );

  @override
  Future<user_proto.GetFriendRequestHistoryResponse> getFriendRequestHistory({
    required String userId,
    int page = 0,
    int pageSize = 20,
    String direction = 'both',
    List<String> statuses = const ['pending', 'accepted', 'declined', 'expired'],
  }) => executeGrpcCallWithRetry(
    operationName: 'getFriendRequestHistory',
    grpcCall: () async {
      LoggingService.info('🔍 UserRemoteDataSource: Getting friend request history');

      final request = user_proto.GetFriendRequestHistoryRequest()
        ..userId = userId
        ..page = page
        ..pageSize = pageSize
        ..direction = direction
        ..statuses.addAll(statuses);

      final response = await _grpcClientService.userService.getFriendRequestHistory(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      LoggingService.success(
        '🔍 UserRemoteDataSource: Retrieved friend request history - Total: ${response.totalCount}, Page: ${response.page}, Requests: ${response.requests.length}',
      );
      return response;
    },
  );

  @override
  Future<user_proto.GetFormerBubbleMembersResponse> getFormerBubbleMembers({
    required String bubbleId,
    required String requestingUserId,
    int page = 0,
    int pageSize = 20,
    DateTime? since,
  }) => executeGrpcCallWithRetry(
    operationName: 'getFormerBubbleMembers',
    grpcCall: () async {
      LoggingService.info('🔍 UserRemoteDataSource: Getting former bubble members');

      final request = user_proto.GetFormerBubbleMembersRequest()
        ..bubbleId = bubbleId
        ..requestingUserId = requestingUserId
        ..page = page
        ..pageSize = pageSize;

      if (since != null) {
        request.since = timestamp_proto.Timestamp()
          ..seconds = Int64(since.millisecondsSinceEpoch ~/ 1000)
          ..nanos = (since.millisecondsSinceEpoch % 1000) * 1000000;
      }

      final response = await _grpcClientService.userService.getFormerBubbleMembers(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      LoggingService.success(
        '🔍 UserRemoteDataSource: Retrieved former bubble members - Bubble ID: $bubbleId, Total: ${response.totalCount}, Members: ${response.formerMembers.length}',
      );
      return response;
    },
  );

  // ================================================================
  // User Location Services Implementation
  // ================================================================

  @override
  Future<void> updateUserLocation({
    required String userId,
    required double latitude,
    required double longitude,
    double? accuracy,
    String? address,
  }) => executeGrpcCallWithRetry(
    operationName: 'updateUserLocation',
    grpcCall: () async {
      LoggingService.info('UserRemoteDataSource: Updating location for user $userId');

      final request = user_proto.UpdateUserLocationRequest()
        ..userId = userId
        ..latitude = latitude
        ..longitude = longitude;

      if (accuracy != null) {
        request.accuracy = accuracy;
      }

      // Implement address retrieval from coordinates if not provided
      if (address == null) {
        try {
          final retrievedAddress = await _getAddressFromCoordinates(latitude, longitude);
          if (retrievedAddress != null) {
            LoggingService.info('UserRemoteDataSource: Retrieved address: $retrievedAddress');
            // Note: Address is retrieved but not stored in the current request
            // This could be stored in user profile or a separate location table
          }
        } on Exception catch (e) {
          LoggingService.warning('UserRemoteDataSource: Failed to retrieve address: $e');
          // Continue without address - this is not a critical failure
        }
      }

      await _grpcClientService.userService.updateUserLocation(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      LoggingService.success('UserRemoteDataSource: Location updated successfully');
    },
  );

  /// Retrieves address from coordinates using reverse geocoding
  /// This is a placeholder implementation - in production, you would use a service like:
  /// - Google Maps Geocoding API
  /// - OpenStreetMap Nominatim
  /// - MapBox Geocoding API
  Future<String?> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      // Validate coordinates
      if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
        LoggingService.warning('UserRemoteDataSource: Invalid coordinates for geocoding');
        return null;
      }

      // FIXME: Implement actual geocoding service integration
      // For now, return a formatted coordinate string as a placeholder
      final formattedLat = latitude.toStringAsFixed(6);
      final formattedLng = longitude.toStringAsFixed(6);

      // This would be replaced with actual API call to geocoding service
      // Example implementation:
      // final response = await http.get(Uri.parse(
      //   'https://api.mapbox.com/geocoding/v5/mapbox.places/$longitude,$latitude.json?access_token=$token'
      // ));
      // final data = json.decode(response.body);
      // return data['features'][0]['place_name'];

      LoggingService.info('UserRemoteDataSource: Geocoding placeholder for coordinates: $formattedLat, $formattedLng');
      return 'Location at $formattedLat, $formattedLng'; // Placeholder address
    } on Exception catch (e) {
      LoggingService.error('UserRemoteDataSource: Error in geocoding: $e');
      return null;
    }
  }

  @override
  Future<user_proto.GetUserLocationResponse> getUserLocation(String userId) => executeGrpcCallWithRetry(
    operationName: 'getUserLocation',
    grpcCall: () async {
      LoggingService.info('UserRemoteDataSource: Getting location for user $userId');

      final request = user_proto.GetUserLocationRequest()..userId = userId;
      final response = await _grpcClientService.userService.getUserLocation(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      LoggingService.success('UserRemoteDataSource: Location retrieved successfully');
      return response;
    },
  );

  @override
  Future<void> updateLocationPrivacy({
    required String userId,
    required bool shareLocation,
    required String privacyLevel,
  }) => executeGrpcCallWithRetry(
    operationName: 'updateLocationPrivacy',
    grpcCall: () async {
      LoggingService.info('UserRemoteDataSource: Updating location privacy for user $userId');

      // Convert string privacy level to enum
      user_proto.LocationPrivacyLevel privacyLevelEnum;
      switch (privacyLevel.toLowerCase()) {
        case 'public':
          privacyLevelEnum = user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_PUBLIC;
          break;
        case 'friends':
          privacyLevelEnum = user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_FRIENDS;
          break;
        case 'bubbles':
          privacyLevelEnum = user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_BUBBLES;
          break;
        case 'custom':
          privacyLevelEnum = user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_CUSTOM;
          break;
        case 'private':
          privacyLevelEnum = user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_PRIVATE;
          break;
        default:
          privacyLevelEnum = user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_UNSPECIFIED;
      }

      final request = user_proto.UpdateLocationPrivacyRequest()
        ..userId = userId
        ..privacyLevel = privacyLevelEnum;

      // Handle shareLocation parameter and custom privacy rules
      if (!shareLocation) {
        // If user doesn't want to share location at all, force private mode
        request.privacyLevel = user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_PRIVATE;
        LoggingService.info('UserRemoteDataSource: Forcing private mode due to shareLocation=false');
      }

      // FIXME: Implement custom privacy rules support
      // Custom rules could include:
      // - Time-based sharing (only during certain hours)
      // - Distance-based sharing (only when within X km of friends)
      // - Activity-based sharing (only during specific activities)
      // - Group-based sharing (different rules for different friend groups)
      //
      // Example custom rule structure:
      // {
      //   "time_restrictions": {"start": "09:00", "end": "17:00"},
      //   "distance_limit": 5.0,
      //   "allowed_activities": ["bubble", "meeting"],
      //   "friend_groups": ["close_friends", "family"]
      // }
      //
      // For now, we use the basic privacy levels supported by the backend

      await _grpcClientService.userService.updateLocationPrivacy(
        request,
        options: _grpcClientService.createCallOptions(),
      );

      LoggingService.success('UserRemoteDataSource: Location privacy updated successfully');
    },
  );

  // ================================================================
  // Content Report Services Implementation
  // ================================================================

  @override
  Future<user_proto.ReportContentResponse> reportContent(
    user_proto.ReportContentRequest request,
  ) => executeGrpcCallWithRetry(
    operationName: 'reportContent',
    grpcCall: () => _grpcClientService.userService.reportContent(
      request,
      options: _grpcClientService.createCallOptions(),
    ),
  );

  @override
  Future<user_proto.GetContentReportResponse> getContentReport(
    user_proto.GetContentReportRequest request,
  ) => executeGrpcCallWithRetry(
    operationName: 'getContentReport',
    grpcCall: () => _grpcClientService.userService.getContentReport(
      request,
      options: _grpcClientService.createCallOptions(),
    ),
  );

  @override
  Future<user_proto.ListContentReportsResponse> listContentReports(
    user_proto.ListContentReportsRequest request,
  ) => executeGrpcCallWithRetry(
    operationName: 'listContentReports',
    grpcCall: () => _grpcClientService.userService.listContentReports(
      request,
      options: _grpcClientService.createCallOptions(),
    ),
  );

  @override
  Future<user_proto.UpdateContentReportStatusResponse> updateContentReportStatus(
    user_proto.UpdateContentReportStatusRequest request,
  ) => executeGrpcCallWithRetry(
    operationName: 'updateContentReportStatus',
    grpcCall: () => _grpcClientService.userService.updateContentReportStatus(
      request,
      options: _grpcClientService.createCallOptions(),
    ),
  );

  @override
  Future<user_proto.DeleteContentReportResponse> deleteContentReport(
    user_proto.DeleteContentReportRequest request,
  ) => executeGrpcCallWithRetry(
    operationName: 'deleteContentReport',
    grpcCall: () => _grpcClientService.userService.deleteContentReport(
      request,
      options: _grpcClientService.createCallOptions(),
    ),
  );


}