import 'package:drift/drift.dart';

import '../../generated/grpc/bubble.pb.dart' as bubble_proto;
import '../../generated/grpc/common.pb.dart' as common_proto;
import '../../repositories/bubble/bubble_join_request_repository.dart';
import '../../statefulbusinesslogic/core/db/app_database.dart';
import '../../statefulbusinesslogic/core/error/errors.dart';
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../statefulbusinesslogic/core/models/bubble_kickout_record.dart';
import '../../statefulbusinesslogic/core/models/bubble_request_model.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../models/unified_data_interface.dart';
import '../utils/timestamp_utils.dart';

/// Bubble mapper with optimized conversions and reduced boilerplate
///
/// This mapper consolidates all bubble-related conversions and leverages
/// code generation patterns to reduce manual mapping code.
///
/// Uses the unified data interface for consistent error handling and batch operations.
class BubbleMapper extends EnhancedDataModel<BubbleEntity, common_proto.Bubble, DriftBubbleHistory> {

  // Required implementations for abstract base class
  @override
  String get id => throw UnimplementedError('Use static methods for mapping');

  @override
  DateTime get lastUpdated => throw UnimplementedError('Use static methods for mapping');

  @override
  bool get isStale => throw UnimplementedError('Use static methods for mapping');

  @override
  DriftBubbleHistory toDrift() => throw UnimplementedError('Use static methods for mapping');

  @override
  common_proto.Bubble toProto() => throw UnimplementedError('Use static methods for mapping');
  
  // ============================================================================
  // PROTOBUF TO DOMAIN CONVERSIONS
  // ============================================================================
  
  /// Convert protobuf Bubble to domain BubbleEntity with comprehensive validation
  static Result<BubbleEntity> protoToDomain(common_proto.Bubble proto) {
    try {
      return BubbleEntity.create(
        id: proto.id,
        name: proto.name,
        capacity: proto.hasMaxMembers() ? proto.maxMembers : BubbleEntity.maxMembers,
        createdAt: proto.hasCreatedAt() 
            ? TimestampUtils.toDateTime(proto.createdAt)
            : DateTime.now(),
        updatedAt: proto.hasUpdatedAt() 
            ? TimestampUtils.toDateTime(proto.updatedAt)
            : null,
        endDate: proto.hasExpiresAt() 
            ? TimestampUtils.toDateTime(proto.expiresAt)
            : null,
        status: _mapProtoStatus(proto.status),
        createdBy: proto.hasCreatorId() ? proto.creatorId : '',
      );
    } on Exception catch (e) {
      return Result.failure(ValidationError(
        field: 'bubble',
        message: 'Failed to convert protobuf to domain: $e',
      ));
    }
  }
  
  /// Convert protobuf BubbleMember to domain BubbleMemberEntity
  static Result<BubbleMemberEntity> protoMemberToDomain(bubble_proto.BubbleMember proto) {
    try {
      // Build display name from user fields
      var displayName = 'Unknown User';
      if (proto.hasUser()) {
        final user = proto.user;
        if (user.hasFirstName() && user.hasLastName()) {
          displayName = '${user.firstName} ${user.lastName}'.trim();
        } else if (user.hasUsername()) {
          displayName = user.username;
        } else if (user.hasFirstName()) {
          displayName = user.firstName;
        }
      }

      return BubbleMemberEntity.create(
        id: proto.userId,
        name: displayName,
        avatarUrl: proto.hasUser() && proto.user.hasAvatarUrl() ? proto.user.avatarUrl : null,
        joinedAt: proto.hasJoinedAt()
            ? TimestampUtils.toDateTime(proto.joinedAt)
            : DateTime.now(),
        isOnline: proto.hasUser() && proto.user.hasIsOnline() && proto.user.isOnline,
        status: proto.isActive ? BubbleMemberStatus.active : BubbleMemberStatus.left,
        leftAt: proto.hasLeftAt() ? TimestampUtils.toDateTime(proto.leftAt) : null,
      );
    } on Exception catch (e) {
      return Result.failure(ValidationError(
        field: 'bubble_member',
        message: 'Failed to convert protobuf member to domain: $e',
      ));
    }
  }
  
  // ============================================================================
  // DRIFT TO DOMAIN CONVERSIONS
  // ============================================================================
  
  /// Convert Drift BubbleHistory to domain BubbleEntity
  static Result<BubbleEntity> driftToDomain(DriftBubbleHistory drift) {
    try {
      return BubbleEntity.create(
        id: drift.id,
        name: drift.name,
        capacity: drift.capacity,
        createdAt: drift.createdAt,
        updatedAt: drift.updatedAt,
        endDate: drift.endDate,
        status: _mapStringStatus(drift.status),
        createdBy: drift.creatorId ?? '',
      );
    } on Exception catch (e) {
      return Result.failure(ValidationError(
        field: 'bubble_history',
        message: 'Failed to convert Drift to domain: $e',
      ));
    }
  }
  
  /// Convert Drift BubbleMember to domain BubbleMemberEntity
  static Result<BubbleMemberEntity> driftMemberToDomain(DriftBubbleMember drift) {
    try {
      // Build display name from available fields
      var displayName = 'Unknown User';
      if (drift.firstName != null && drift.lastName != null) {
        displayName = '${drift.firstName} ${drift.lastName}'.trim();
      } else if (drift.username != null) {
        displayName = drift.username!;
      } else if (drift.firstName != null) {
        displayName = drift.firstName!;
      }

      return BubbleMemberEntity.create(
        id: drift.userId,
        name: displayName,
        avatarUrl: drift.profilePictureUrl,
        joinedAt: drift.joinedAt,
        status: drift.isActive ? BubbleMemberStatus.active : BubbleMemberStatus.left,
      );
    } on Exception catch (e) {
      return Result.failure(ValidationError(
        field: 'bubble_member_drift',
        message: 'Failed to convert Drift member to domain: $e',
      ));
    }
  }
  
  // ============================================================================
  // DOMAIN TO DRIFT CONVERSIONS
  // ============================================================================
  
  /// Convert domain BubbleEntity to Drift BubbleHistory for caching
  static BubbleHistoriesCompanion domainToDrift(
    BubbleEntity entity,
    String userId,
  ) {
    final now = DateTime.now();
    return BubbleHistoriesCompanion.insert(
      id: entity.id.value,
      name: entity.name.value,
      capacity: entity.capacity.value,
      creatorId: Value(entity.createdBy),
      createdAt: entity.createdAt,
      updatedAt: Value(entity.updatedAt),
      endDate: Value(entity.endDate),
      status: entity.status.name,
      userId: userId,
      cachedAt: now,
      lastRefreshed: now,
      lastAccessed: now,
      isStale: const Value(false),
    );
  }
  
  /// Convert domain BubbleMemberEntity to Drift BubbleMember for caching
  static BubbleMembersCompanion domainMemberToDrift(BubbleMemberEntity entity, String bubbleId) {
    final now = DateTime.now();

    // Parse name into first/last name components
    final nameParts = entity.name.split(' ');
    final firstName = nameParts.isNotEmpty ? nameParts.first : null;
    final lastName = nameParts.length > 1 ? nameParts.skip(1).join(' ') : null;

    return BubbleMembersCompanion.insert(
      bubbleId: bubbleId,
      userId: entity.id.value,
      username: const Value(null), // Username not available in BubbleMemberEntity
      firstName: Value(firstName),
      lastName: Value(lastName),
      profilePictureUrl: Value(entity.avatarUrl),
      role: const Value('member'), // Default role since BubbleMemberEntity doesn't have role
      joinedAt: entity.joinedAt,
      isActive: Value(entity.status == BubbleMemberStatus.active),
      cachedAt: now,
      lastRefreshed: now,
      isStale: const Value(false),
    );
  }
  
  // ============================================================================
  // BUBBLE REQUEST MAPPING (from original BubbleMapper)
  // ============================================================================

  /// Convert protobuf BubbleRequest to domain BubbleRequestModel
  static BubbleRequestModel fromProtoRequest(bubble_proto.BubbleRequest req) => BubbleRequestModel(
      id: req.id,
      bubbleId: req.bubbleId,
      requesterId: req.requesterId,
      type: fromProtoRequestType(req.type),
      status: fromProtoRequestStatus(req.status),
      createdAt: req.hasCreatedAt()
          ? TimestampUtils.toDateTime(req.createdAt)
          : DateTime.now(),
    );

  static BubbleRequestType fromProtoRequestType(common_proto.BubbleRequestType type) {
    switch (type) {
      case common_proto.BubbleRequestType.BUBBLE_REQUEST_TYPE_JOIN:
        return BubbleRequestType.join;
      case common_proto.BubbleRequestType.BUBBLE_REQUEST_TYPE_INVITE:
        return BubbleRequestType.invite;
      default:
        return BubbleRequestType.join;
    }
  }

  static BubbleRequestStatus fromProtoRequestStatus(common_proto.BubbleRequestStatus status) {
    switch (status) {
      case common_proto.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_PENDING:
        return BubbleRequestStatus.pending;
      case common_proto.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_ACCEPTED:
        return BubbleRequestStatus.accepted;
      case common_proto.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_REJECTED:
        return BubbleRequestStatus.declined;
      case common_proto.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_CANCELLED:
        return BubbleRequestStatus.cancelled;
      default:
        return BubbleRequestStatus.pending;
    }
  }

  // ============================================================================
  // BUBBLE KICKOUT MAPPING (from original BubbleMapper)
  // ============================================================================

  /// Map BubbleRequest protobuf to BubbleKickoutRecord domain model
  static BubbleKickoutRecord fromBubbleRequestToKickoutRecord(bubble_proto.BubbleRequest request) {
    final createdAt = request.hasCreatedAt()
        ? TimestampUtils.toDateTime(request.createdAt)
        : DateTime.now();

    return BubbleKickoutRecord(
      id: request.hasId() ? request.id : '',
      bubbleId: request.hasBubbleId() ? request.bubbleId : '',
      targetUserId: request.hasUserId() ? request.userId : '',
      targetUserName: '', // Will be populated from user data if available
      kickedBy: request.hasRequesterId() ? request.requesterId : '',
      kickedByName: '', // Will be populated from user data if available
      kickedAt: createdAt,
      reason: request.hasMessage() ? request.message : '',
    );
  }

  /// Map KickoutRecord protobuf to BubbleKickoutRecord domain model
  static BubbleKickoutRecord fromProtoKickoutRecord(bubble_proto.KickoutRecord record) {
    final kickedAt = record.hasKickedAt()
        ? TimestampUtils.toDateTime(record.kickedAt)
        : DateTime.now();

    final allowedToRejoinAt = record.hasAllowedToRejoinAt()
        ? TimestampUtils.toDateTime(record.allowedToRejoinAt)
        : null;

    return BubbleKickoutRecord(
      id: record.hasId() ? record.id : '',
      bubbleId: record.hasBubbleId() ? record.bubbleId : '',
      targetUserId: record.hasTargetUserId() ? record.targetUserId : '',
      targetUserName: record.hasTargetUserName() ? record.targetUserName : '',
      kickedBy: record.hasKickedByUserId() ? record.kickedByUserId : '',
      kickedByName: record.hasKickedByUserName() ? record.kickedByUserName : '',
      kickedAt: kickedAt,
      reason: record.hasReason() ? record.reason : '',
      allowedToRejoinAt: allowedToRejoinAt,
    );
  }

  // ============================================================================
  // BATCH CONVERSION UTILITIES
  // ============================================================================

  /// Convert list of protobuf bubbles to domain entities with error handling
  static List<BubbleEntity> protoBubbleListToDomain(List<common_proto.Bubble> protos) {
    final results = <BubbleEntity>[];
    for (final proto in protos) {
      final result = protoToDomain(proto);
      if (result.isSuccess) {
        results.add(result.data);
      }
      // Log errors but continue processing
    }
    return results;
  }

  /// Convert list of Drift bubble histories to domain entities
  static List<BubbleEntity> driftBubbleListToDomain(List<DriftBubbleHistory> drifts) {
    final results = <BubbleEntity>[];
    for (final drift in drifts) {
      final result = driftToDomain(drift);
      if (result.isSuccess) {
        results.add(result.data);
      }
    }
    return results;
  }
  
  // ============================================================================
  // USER MAPPING UTILITIES (from original BubbleMapper)
  // ============================================================================

  /// Map common User protobuf to UserModel domain model
  static UserModel fromCommonUser(common_proto.User user) => UserModel(
      id: user.hasId() ? user.id : '',
      username: user.hasUsername() ? user.username : '',
      firstName: user.hasFirstName() ? user.firstName : '',
      lastName: user.hasLastName() ? user.lastName : '',
      profilePictureUrl: user.hasAvatarUrl() ? user.avatarUrl : null,
      // Add other fields as needed based on the protobuf definition
    );

  /// Map list of common User protobuf objects to UserModel list
  static List<UserModel> fromCommonUserList(List<common_proto.User> users) =>
      users.map(fromCommonUser).toList();

  /// Create a BubbleKickoutRecord with enhanced user information
  static BubbleKickoutRecord createKickoutRecordWithUserInfo({
    required String id,
    required String bubbleId,
    required String targetUserId,
    required String kickedBy,
    required DateTime kickedAt,
    String? targetUserName,
    String? kickedByName,
    String? reason,
    DateTime? allowedToRejoinAt,
  }) => BubbleKickoutRecord(
      id: id,
      bubbleId: bubbleId,
      targetUserId: targetUserId,
      targetUserName: targetUserName ?? '',
      kickedBy: kickedBy,
      kickedByName: kickedByName ?? '',
      kickedAt: kickedAt,
      reason: reason,
      allowedToRejoinAt: allowedToRejoinAt,
    );

  /// Validate if a kickout record is complete and valid
  static bool isValidKickoutRecord(BubbleKickoutRecord record) =>
      record.id.isNotEmpty &&
      record.bubbleId.isNotEmpty &&
      record.targetUserId.isNotEmpty &&
      record.kickedBy.isNotEmpty;

  // ============================================================================
  // PROTOBUF RESPONSE TO DOMAIN CONVERSIONS (Repository Layer Mapping)
  // ============================================================================

  /// Convert GetKickoutHistoryResponse to domain BubbleKickoutRecord list
  static Result<List<BubbleKickoutRecord>> kickoutHistoryResponseToDomain(
    bubble_proto.GetKickoutHistoryResponse response,
  ) {
    try {
      final records = response.records.map((record) => BubbleKickoutRecord(
        id: record.id,
        bubbleId: record.bubbleId,
        targetUserId: record.targetUserId,
        targetUserName: record.targetUserName,
        kickedBy: record.kickedByUserId,
        kickedByName: record.kickedByUserName,
        kickedAt: TimestampUtils.toDateTime(record.kickedAt),
        reason: record.reason,
        allowedToRejoinAt: record.hasAllowedToRejoinAt()
            ? TimestampUtils.toDateTime(record.allowedToRejoinAt)
            : null,
      )).toList();

      return Result.success(records);
    } on Exception catch (e) {
      return Result.failure(ValidationError(
        field: 'kickout_history_response',
        message: 'Failed to convert kickout history response to domain: $e',
      ));
    }
  }

  /// Convert GetUserKickoutHistoryResponse to domain BubbleKickoutRecord list
  static Result<List<BubbleKickoutRecord>> userKickoutHistoryResponseToDomain(
    bubble_proto.GetUserKickoutHistoryResponse response,
  ) {
    try {
      final records = response.records.map((record) => BubbleKickoutRecord(
        id: record.id,
        bubbleId: record.bubbleId,
        targetUserId: record.targetUserId,
        targetUserName: record.targetUserName,
        kickedBy: record.kickedByUserId,
        kickedByName: record.kickedByUserName,
        kickedAt: TimestampUtils.toDateTime(record.kickedAt),
        reason: record.reason,
        allowedToRejoinAt: record.hasAllowedToRejoinAt()
            ? TimestampUtils.toDateTime(record.allowedToRejoinAt)
            : null,
      )).toList();

      return Result.success(records);
    } on Exception catch (e) {
      return Result.failure(ValidationError(
        field: 'user_kickout_history_response',
        message: 'Failed to convert user kickout history response to domain: $e',
      ));
    }
  }

  /// Convert GetUserJoinRequestsResponse to domain BubbleJoinRequest list
  static Result<List<BubbleJoinRequest>> userJoinRequestsResponseToDomain(
    bubble_proto.GetUserJoinRequestsResponse response,
    Map<String, String> userNames,
  ) {
    try {
      final requests = response.requests.map((request) => BubbleJoinRequest(
        id: request.id,
        bubbleId: request.bubbleId,
        userId: request.requesterId,
        userName: userNames[request.requesterId] ?? 'User ${request.requesterId}',
        requestedAt: TimestampUtils.toDateTime(request.createdAt),
        status: _parseJoinRequestStatus(request.status.name),
        message: request.hasMessage() ? request.message : null,
      )).toList();

      return Result.success(requests);
    } on Exception catch (e) {
      return Result.failure(ValidationError(
        field: 'user_join_requests_response',
        message: 'Failed to convert user join requests response to domain: $e',
      ));
    }
  }

  /// Parse join request status from string
  static JoinRequestStatus _parseJoinRequestStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return JoinRequestStatus.pending;
      case 'approved':
        return JoinRequestStatus.approved;
      case 'rejected':
        return JoinRequestStatus.rejected;
      case 'expired':
        return JoinRequestStatus.expired;
      default:
        return JoinRequestStatus.pending;
    }
  }

  // ============================================================================
  // LEGACY COMPATIBILITY (from original BubbleMapper)
  // ============================================================================

  /// Legacy method for backward compatibility - delegates to enhanced version
  static BubbleEntity fromProto(common_proto.Bubble b) {
    final result = protoToDomain(b);
    return result.isSuccess ? result.data : _createFallbackBubble(b);
  }



  /// Create fallback bubble for failed conversions
  static BubbleEntity _createFallbackBubble(common_proto.Bubble b) => BubbleEntity.create(
    id: b.id.isNotEmpty ? b.id : 'unknown',
    name: b.hasName() ? b.name : 'Bubble',
    createdAt: DateTime.now(),
    createdBy: b.hasCreatorId() ? b.creatorId : '',
    capacity: BubbleEntity.maxMembers,
  ).data;

  // ============================================================================
  // PRIVATE MAPPING HELPERS
  // ============================================================================

  static BubbleLifecycleStatus _mapProtoStatus(common_proto.BubbleStatus status) {
    switch (status) {
      case common_proto.BubbleStatus.BUBBLE_STATUS_ACTIVE:
        return BubbleLifecycleStatus.active;
      case common_proto.BubbleStatus.BUBBLE_STATUS_EXPIRED:
        return BubbleLifecycleStatus.expired;
      case common_proto.BubbleStatus.BUBBLE_STATUS_DISSOLVED:
        return BubbleLifecycleStatus.dissolved;
      case common_proto.BubbleStatus.BUBBLE_STATUS_ARCHIVED:
        return BubbleLifecycleStatus.archived;
      default:
        return BubbleLifecycleStatus.active;
    }
  }
  
  static BubbleLifecycleStatus _mapStringStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return BubbleLifecycleStatus.active;
      case 'expired':
        return BubbleLifecycleStatus.expired;
      case 'dissolved':
        return BubbleLifecycleStatus.dissolved;
      case 'archived':
        return BubbleLifecycleStatus.archived;
      default:
        return BubbleLifecycleStatus.active;
    }
  }
  
  // Note: Protobuf BubbleMember doesn't have role or status enums
  // Role information would come from a separate member roles API if needed
  // Status is determined by the is_active boolean field
}
