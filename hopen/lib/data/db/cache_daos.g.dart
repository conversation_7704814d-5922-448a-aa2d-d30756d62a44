// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cache_daos.dart';

// ignore_for_file: type=lint
mixin _$ChatInfoDaoMixin on DatabaseAccessor<AppDatabase> {
  $ChatInfosTable get chatInfos => attachedDatabase.chatInfos;
}
mixin _$BubbleHistoryDaoMixin on DatabaseAccessor<AppDatabase> {
  $BubbleHistoriesTable get bubbleHistories => attachedDatabase.bubbleHistories;
  $BubbleMembersTable get bubbleMembers => attachedDatabase.bubbleMembers;
  $BubbleStatisticsTable get bubbleStatistics =>
      attachedDatabase.bubbleStatistics;
}
mixin _$BubbleMemberDaoMixin on DatabaseAccessor<AppDatabase> {
  $BubbleMembersTable get bubbleMembers => attachedDatabase.bubbleMembers;
}
mixin _$BubbleStatisticsDaoMixin on DatabaseAccessor<AppDatabase> {
  $BubbleStatisticsTable get bubbleStatistics =>
      attachedDatabase.bubbleStatistics;
}
mixin _$UserPresenceDaoMixin on DatabaseAccessor<AppDatabase> {
  $UserPresencesTable get userPresences => attachedDatabase.userPresences;
}
mixin _$FriendshipDaoMixin on DatabaseAccessor<AppDatabase> {
  $FriendshipsTable get friendships => attachedDatabase.friendships;
}
mixin _$MemberHistoryEventDaoMixin on DatabaseAccessor<AppDatabase> {
  $MemberHistoryEventsTable get memberHistoryEvents =>
      attachedDatabase.memberHistoryEvents;
}
mixin _$ActivityHistoryEventDaoMixin on DatabaseAccessor<AppDatabase> {
  $ActivityHistoryEventsTable get activityHistoryEvents =>
      attachedDatabase.activityHistoryEvents;
}
mixin _$BubbleProposeRequestDaoMixin on DatabaseAccessor<AppDatabase> {
  $BubbleProposeRequestsTable get bubbleProposeRequests =>
      attachedDatabase.bubbleProposeRequests;
}
mixin _$BubbleStartRequestDaoMixin on DatabaseAccessor<AppDatabase> {
  $BubbleStartRequestsTable get bubbleStartRequests =>
      attachedDatabase.bubbleStartRequests;
}
