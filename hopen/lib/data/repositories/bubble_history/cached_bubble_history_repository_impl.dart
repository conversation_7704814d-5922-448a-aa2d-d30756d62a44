import 'dart:async';


import '../../../repositories/auth/auth_repository.dart';
import '../../../repositories/bubble_history/bubble_history_repository.dart';
import '../../../statefulbusinesslogic/core/db/app_database.dart';
import '../../../statefulbusinesslogic/core/error/errors.dart' as failures;
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/models/bubble_entity.dart';
import '../../../statefulbusinesslogic/core/models/bubble_history_events.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../datasources/bubble_remote_data_source.dart';
import '../../mappers/bubble_mapper.dart';
import '../../mappers/user_mapper.dart';
import '../cached_base_repository.dart';

/// Cached implementation of BubbleHistoryRepository using stale-while-revalidate pattern
class CachedBubbleHistoryRepositoryImpl extends BaseCachedRepository implements BubbleHistoryRepository {
  CachedBubbleHistoryRepositoryImpl({
    required BubbleHistoryDao bubbleHistoryDao,
    required BubbleMemberDao bubbleMemberDao,
    required BubbleStatisticsDao bubbleStatisticsDao,
    required MemberHistoryEventDao memberHistoryEventDao,
    required ActivityHistoryEventDao activityHistoryEventDao,
    required BubbleRemoteDataSource bubbleRemoteDataSource,
    required AuthRepository authRepository,
  }) : _bubbleHistoryDao = bubbleHistoryDao,
       _bubbleMemberDao = bubbleMemberDao,
       _bubbleStatisticsDao = bubbleStatisticsDao,
       _memberHistoryEventDao = memberHistoryEventDao,
       _activityHistoryEventDao = activityHistoryEventDao,
       _bubbleRemoteDataSource = bubbleRemoteDataSource,
       _authRepository = authRepository;

  final BubbleHistoryDao _bubbleHistoryDao;
  final BubbleMemberDao _bubbleMemberDao;
  final BubbleStatisticsDao _bubbleStatisticsDao;
  final MemberHistoryEventDao _memberHistoryEventDao;
  final ActivityHistoryEventDao _activityHistoryEventDao;
  final BubbleRemoteDataSource _bubbleRemoteDataSource;
  final AuthRepository _authRepository;

  @override
  String get repositoryTag => 'CachedBubbleHistoryRepository';

  @override
  Duration get cacheTtl => const Duration(minutes: 15);

  @override
  Duration get stalePeriod => const Duration(hours: 1);

  /// Get current user ID from auth repository
  Future<String> _getCurrentUserId() async {
    try {
      final currentUserResult = await _authRepository.getCurrentUser();
      if (currentUserResult.isSuccess) {
        return currentUserResult.data.id;
      }
      throw Exception('No authenticated user found');
    } on Exception catch (e) {
      LoggingService.error('$repositoryTag: Failed to get current user ID', error: e);
      rethrow;
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> getBubbleHistory(String userId) async {
    try {
      LoggingService.info('$repositoryTag: Getting bubble history for user $userId');

      final bubbles = await executeCacheFirst<List<BubbleEntity>>(
        cacheKey: 'bubble_history_$userId',
        cacheOperation: () async {
          final cachedBubbles = await _bubbleHistoryDao.getBubbleHistoryForUser(userId);
          final bubbleEntities = <BubbleEntity>[];

          for (final cached in cachedBubbles) {
            final bubbleResult = BubbleMapper.driftToDomain(cached);
            if (bubbleResult.isSuccess) {
              bubbleEntities.add(bubbleResult.data);
            }
          }

          return bubbleEntities.isNotEmpty ? bubbleEntities : null;
        },
        remoteOperation: () async {
          final userBubblesResponse = await _bubbleRemoteDataSource.getUserBubbles(userId);
          final bubbleEntities = <BubbleEntity>[];

          final bubbles = userBubblesResponse.bubbles;
          for (final protoBubble in bubbles) {
            final bubbleResult = BubbleMapper.protoToDomain(protoBubble);
            if (bubbleResult.isSuccess) {
              bubbleEntities.add(bubbleResult.data);
            }
          }

          return bubbleEntities;
        },
        cacheUpdate: (bubbles) async {
          for (final bubble in bubbles) {
            final driftBubble = _bubbleToDrift(bubble, userId);
            await _bubbleHistoryDao.insertOrUpdateBubble(driftBubble);
          }
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded ${bubbles.length} bubbles');
      return Result.success(bubbles);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting bubble history: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get bubble history: $e'));
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> getActiveBubbles(String userId) async {
    try {
      LoggingService.info('$repositoryTag: Getting active bubbles for user $userId');

      final bubbles = await executeCacheFirst<List<BubbleEntity>>(
        cacheKey: 'active_bubbles_$userId',
        cacheOperation: () async {
          final cachedBubbles = await _bubbleHistoryDao.getActiveBubbles(userId);

          final bubbleEntities = <BubbleEntity>[];
          for (final cached in cachedBubbles) {
            final bubbleResult = BubbleMapper.driftToDomain(cached);
            if (bubbleResult.isSuccess) {
              bubbleEntities.add(bubbleResult.data);
            }
          }

          return bubbleEntities.isNotEmpty ? bubbleEntities : null;
        },
        remoteOperation: () async {
          final activeBubblesResponse = await _bubbleRemoteDataSource.getActiveBubbles(userId);
          final bubbleEntities = <BubbleEntity>[];

          for (final protoBubble in activeBubblesResponse.bubbles) {
            final bubbleResult = BubbleMapper.protoToDomain(protoBubble);
            if (bubbleResult.isSuccess) {
              bubbleEntities.add(bubbleResult.data);
            }
          }

          return bubbleEntities;
        },
        cacheUpdate: (bubbles) async {
          for (final bubble in bubbles) {
            final driftBubble = _bubbleToDrift(bubble, userId);
            await _bubbleHistoryDao.insertOrUpdateBubble(driftBubble);
          }
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded ${bubbles.length} active bubbles');
      return Result.success(bubbles);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting active bubbles: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get active bubbles: $e'));
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> getCompletedBubbles(String userId) async {
    try {
      LoggingService.info('$repositoryTag: Getting completed bubbles for user $userId');

      final bubbles = await executeCacheFirst<List<BubbleEntity>>(
        cacheKey: 'completed_bubbles_$userId',
        cacheOperation: () async {
          final cachedBubbles = await _bubbleHistoryDao.getCompletedBubbles(userId);

          final bubbleEntities = <BubbleEntity>[];
          for (final cached in cachedBubbles) {
            final bubbleResult = BubbleMapper.driftToDomain(cached);
            if (bubbleResult.isSuccess) {
              bubbleEntities.add(bubbleResult.data);
            }
          }

          return bubbleEntities.isNotEmpty ? bubbleEntities : null;
        },
        remoteOperation: () async {
          final completedBubblesResponse = await _bubbleRemoteDataSource.getCompletedBubbles(userId);
          final bubbleEntities = <BubbleEntity>[];

          for (final protoBubble in completedBubblesResponse.bubbles) {
            final bubbleResult = BubbleMapper.protoToDomain(protoBubble);
            if (bubbleResult.isSuccess) {
              bubbleEntities.add(bubbleResult.data);
            }
          }

          return bubbleEntities;
        },
        cacheUpdate: (bubbles) async {
          for (final bubble in bubbles) {
            final driftBubble = _bubbleToDrift(bubble, userId);
            await _bubbleHistoryDao.insertOrUpdateBubble(driftBubble);
          }
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded ${bubbles.length} completed bubbles');
      return Result.success(bubbles);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting completed bubbles: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get completed bubbles: $e'));
    }
  }

  @override
  Future<Result<List<UserModel>>> getBubbleMembers(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Getting members for bubble $bubbleId');

      final members = await executeCacheFirst<List<UserModel>>(
        cacheKey: 'bubble_members_$bubbleId',
        cacheOperation: () async {
          final cachedMembers = await _bubbleMemberDao.getBubbleMembers(bubbleId);
          final userModels = <UserModel>[];

          for (final member in cachedMembers) {
            final userModel = UserModel(
              id: member.userId,
              username: member.username,
              firstName: member.firstName,
              lastName: member.lastName,
              profilePictureUrl: member.profilePictureUrl,
            );
            userModels.add(userModel);
          }

          return userModels.isNotEmpty ? userModels : null;
        },
        remoteOperation: () async {
          final protoUsers = await _bubbleRemoteDataSource.getBubbleMembers(bubbleId);
          final userModels = <UserModel>[];

          for (final protoUser in protoUsers) {
            final userModel = UserMapper.fromProto(protoUser);
            userModels.add(userModel);
          }

          return userModels;
        },
        cacheUpdate: (members) async {
          final now = DateTime.now();
          for (final member in members) {
            final driftMember = DriftBubbleMember(
              bubbleId: bubbleId,
              userId: member.id,
              username: member.username,
              firstName: member.firstName,
              lastName: member.lastName,
              profilePictureUrl: member.profilePictureUrl,
              role: 'member',
              joinedAt: now,
              isActive: true,
              cachedAt: now,
              lastRefreshed: now,
              isStale: false,
            );
            await _bubbleMemberDao.insertOrUpdateMember(driftMember);
          }
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded ${members.length} bubble members');
      return Result.success(members);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting bubble members: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get bubble members: $e'));
    }
  }

  @override
  Future<Result<BubbleEntity>> getBubbleById(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Getting bubble by ID $bubbleId');

      final userId = await _getCurrentUserId();

      final bubble = await executeCacheFirst<BubbleEntity>(
        cacheKey: 'bubble_$bubbleId',
        cacheOperation: () async {
          final cachedBubble = await _bubbleHistoryDao.getBubbleById(bubbleId);
          if (cachedBubble != null) {
            final bubbleResult = BubbleMapper.driftToDomain(cachedBubble);
            return bubbleResult.isSuccess ? bubbleResult.data : null;
          }
          return null;
        },
        remoteOperation: () async {
          final protoBubble = await _bubbleRemoteDataSource.getBubble(bubbleId);
          final bubbleResult = BubbleMapper.protoToDomain(protoBubble);
          if (bubbleResult.isSuccess) {
            return bubbleResult.data;
          }
          throw Exception('Failed to convert bubble from proto');
        },
        cacheUpdate: (bubble) async {
          final driftBubble = _bubbleToDrift(bubble, userId);
          await _bubbleHistoryDao.insertOrUpdateBubble(driftBubble);
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded bubble $bubbleId');
      return Result.success(bubble);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting bubble by ID: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get bubble: $e'));
    }
  }

  @override
  Future<Result<void>> archiveBubble(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Archiving bubble $bubbleId');

      final userId = await _getCurrentUserId();
      await _bubbleRemoteDataSource.archiveBubble(bubbleId, userId);

      // Invalidate cache
      await invalidateCache('bubble_history_$userId');
      await invalidateCache('bubble_$bubbleId');

      LoggingService.success('$repositoryTag: Successfully archived bubble $bubbleId');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error archiving bubble: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to archive bubble: $e'));
    }
  }

  @override
  Future<Result<void>> unarchiveBubble(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Unarchiving bubble $bubbleId');

      final userId = await _getCurrentUserId();
      await _bubbleRemoteDataSource.unarchiveBubble(bubbleId, userId);

      // Invalidate cache
      await invalidateCache('bubble_history_$userId');
      await invalidateCache('bubble_$bubbleId');

      LoggingService.success('$repositoryTag: Successfully unarchived bubble $bubbleId');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error unarchiving bubble: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to unarchive bubble: $e'));
    }
  }

  @override
  Future<Result<void>> favoriteBubble(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Adding bubble $bubbleId to favorites');

      final userId = await _getCurrentUserId();
      // Note: Favorite functionality not yet implemented in remote data source
      // For now, just update local cache
      // Note: updateBubbleFavoriteStatus method not available in DAO
      // For now, just invalidate cache to trigger refresh

      // Invalidate cache
      await invalidateCache('bubble_history_$userId');
      await invalidateCache('favorite_bubbles_$userId');

      LoggingService.success('$repositoryTag: Successfully favorited bubble $bubbleId');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error favoriting bubble: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to favorite bubble: $e'));
    }
  }

  @override
  Future<Result<void>> unfavoriteBubble(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Removing bubble $bubbleId from favorites');

      final userId = await _getCurrentUserId();
      // Note: Unfavorite functionality not yet implemented in remote data source
      // For now, just update local cache
      // Note: updateBubbleFavoriteStatus method not available in DAO
      // For now, just invalidate cache to trigger refresh

      // Invalidate cache
      await invalidateCache('bubble_history_$userId');
      await invalidateCache('favorite_bubbles_$userId');

      LoggingService.success('$repositoryTag: Successfully unfavorited bubble $bubbleId');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error unfavoriting bubble: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to unfavorite bubble: $e'));
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> getFavoriteBubbles(String userId) async {
    try {
      LoggingService.info('$repositoryTag: Getting favorite bubbles for user $userId');

      final bubbles = await executeCacheFirst<List<BubbleEntity>>(
        cacheKey: 'favorite_bubbles_$userId',
        cacheOperation: () async => <BubbleEntity>[],
        remoteOperation: () async => <BubbleEntity>[],
        cacheUpdate: (bubbles) async {
          for (final bubble in bubbles) {
            final driftBubble = _bubbleToDrift(bubble, userId);
            await _bubbleHistoryDao.insertOrUpdateBubble(driftBubble);
          }
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded ${bubbles.length} favorite bubbles');
      return Result.success(bubbles);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting favorite bubbles: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get favorite bubbles: $e'));
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> searchBubbleHistory(String userId, String query) async {
    try {
      LoggingService.info('$repositoryTag: Searching bubble history for user $userId with query: $query');

      final historyResult = await getBubbleHistory(userId);
      if (historyResult.isFailure) {
        return historyResult;
      }

      final filteredBubbles = historyResult.data
          .where((bubble) =>
              bubble.name.value.toLowerCase().contains(query.toLowerCase()))
          .toList();

      LoggingService.success('$repositoryTag: Found ${filteredBubbles.length} bubbles matching query');
      return Result.success(filteredBubbles);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error searching bubble history: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to search bubble history: $e'));
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> filterBubblesByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      LoggingService.info('$repositoryTag: Filtering bubbles by date range for user $userId');

      final historyResult = await getBubbleHistory(userId);
      if (historyResult.isFailure) {
        return historyResult;
      }

      final filteredBubbles = historyResult.data
          .where((bubble) =>
              bubble.createdAt.isAfter(startDate) &&
              bubble.createdAt.isBefore(endDate))
          .toList();

      LoggingService.success('$repositoryTag: Found ${filteredBubbles.length} bubbles in date range');
      return Result.success(filteredBubbles);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error filtering bubbles by date: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to filter bubbles by date: $e'));
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> sortBubbleHistory(
    String userId,
    BubbleSortBy sortBy, {
    bool ascending = true,
  }) async {
    try {
      LoggingService.info('$repositoryTag: Sorting bubble history for user $userId by $sortBy');

      final historyResult = await getBubbleHistory(userId);
      if (historyResult.isFailure) {
        return historyResult;
      }

      final sortedBubbles = List<BubbleEntity>.from(historyResult.data);

      switch (sortBy) {
        case BubbleSortBy.dateCreated:
          sortedBubbles.sort((a, b) => ascending
              ? a.createdAt.compareTo(b.createdAt)
              : b.createdAt.compareTo(a.createdAt));
          break;
        case BubbleSortBy.dateUpdated:
          sortedBubbles.sort((a, b) => ascending
              ? (a.updatedAt ?? a.createdAt).compareTo(b.updatedAt ?? b.createdAt)
              : (b.updatedAt ?? b.createdAt).compareTo(a.updatedAt ?? a.createdAt));
          break;
        case BubbleSortBy.name:
          sortedBubbles.sort((a, b) => ascending
              ? a.name.value.compareTo(b.name.value)
              : b.name.value.compareTo(a.name.value));
          break;
        case BubbleSortBy.memberCount:
          sortedBubbles.sort((a, b) => ascending
              ? a.capacity.value.compareTo(b.capacity.value)
              : b.capacity.value.compareTo(a.capacity.value));
          break;
        case BubbleSortBy.activity:
          // Sort by last activity (using updatedAt as proxy)
          sortedBubbles.sort((a, b) => ascending
              ? (a.updatedAt ?? a.createdAt).compareTo(b.updatedAt ?? b.createdAt)
              : (b.updatedAt ?? b.createdAt).compareTo(a.updatedAt ?? a.createdAt));
          break;
      }

      LoggingService.success('$repositoryTag: Successfully sorted ${sortedBubbles.length} bubbles');
      return Result.success(sortedBubbles);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error sorting bubble history: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to sort bubble history: $e'));
    }
  }

  @override
  Future<Result<Map<String, dynamic>>> getBubbleStatistics(String userId) async {
    try {
      LoggingService.info('$repositoryTag: Getting bubble statistics for user $userId');

      final statistics = await executeCacheFirst<Map<String, dynamic>>(
        cacheKey: 'bubble_statistics_$userId',
        cacheOperation: () async {
          final cachedStats = await _bubbleStatisticsDao.getStatistics(userId);
          if (cachedStats != null) {
            return {
              'totalBubbles': cachedStats.totalBubbles,
              'activeBubbles': cachedStats.activeBubbles,
              'completedBubbles': cachedStats.completedBubbles,
              'archivedBubbles': cachedStats.archivedBubbles,
            };
          }
          return null;
        },
        remoteOperation: () async {
          // For now, calculate statistics from bubble history
          final historyResult = await getBubbleHistory(userId);
          if (historyResult.isFailure) {
            throw Exception('Failed to get bubble history for statistics');
          }

          final bubbles = historyResult.data;
          final activeBubbles = bubbles.where((b) => b.status.name == 'active').length;
          final completedBubbles = bubbles.where((b) => b.status.name == 'completed' || b.status.name == 'expired').length;

          return {
            'totalBubbles': bubbles.length,
            'activeBubbles': activeBubbles,
            'completedBubbles': completedBubbles,
            'archivedBubbles': 0, // Would need to be calculated from archived bubbles
          };
        },
        cacheUpdate: (stats) async {
          final now = DateTime.now();
          final driftStats = DriftBubbleStatistics(
            userId: userId,
            totalBubbles: stats['totalBubbles'] as int,
            activeBubbles: stats['activeBubbles'] as int,
            completedBubbles: stats['completedBubbles'] as int,
            archivedBubbles: stats['archivedBubbles'] as int,
            averageDurationHours: 0,
            bubblesThisMonth: 0,
            bubblesThisWeek: 0,
            cachedAt: now,
            lastRefreshed: now,
            isStale: false,
          );
          await _bubbleStatisticsDao.insertOrUpdateStatistics(driftStats);
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded bubble statistics');
      return Result.success(statistics);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting bubble statistics: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get bubble statistics: $e'));
    }
  }

  @override
  Future<Result<void>> clearCache() async {
    try {
      LoggingService.info('$repositoryTag: Clearing bubble history cache');

      final userId = await _getCurrentUserId();

      // Clear database cache for this user
      await _bubbleHistoryDao.clearBubbleHistoryForUser(userId);
      await _bubbleStatisticsDao.deleteStatistics(userId);
      
      // Clear all cache keys for this user
      await invalidateCache('bubble_history_$userId');
      await invalidateCache('active_bubbles_$userId');
      await invalidateCache('completed_bubbles_$userId');
      await invalidateCache('favorite_bubbles_$userId');
      await invalidateCache('bubble_statistics_$userId');

      LoggingService.success('$repositoryTag: Successfully cleared cache');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error clearing cache: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to clear cache: $e'));
    }
  }

  @override
  Future<Result<void>> joinBubble(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Joining bubble $bubbleId');

      await _bubbleRemoteDataSource.joinBubble(bubbleId);

      final userId = await _getCurrentUserId();
      // Invalidate relevant caches
      await invalidateCache('bubble_history_$userId');
      await invalidateCache('active_bubbles_$userId');
      await invalidateCache('bubble_members_$bubbleId');

      LoggingService.success('$repositoryTag: Successfully joined bubble $bubbleId');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error joining bubble: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to join bubble: $e'));
    }
  }

  @override
  Future<Result<void>> leaveBubble(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Leaving bubble $bubbleId');

      await _bubbleRemoteDataSource.leaveBubble(bubbleId);

      final userId = await _getCurrentUserId();
      // Invalidate relevant caches
      await invalidateCache('bubble_history_$userId');
      await invalidateCache('active_bubbles_$userId');
      await invalidateCache('bubble_members_$bubbleId');

      LoggingService.success('$repositoryTag: Successfully left bubble $bubbleId');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error leaving bubble: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to leave bubble: $e'));
    }
  }

  @override
  Future<Result<void>> voteToRemoveMember(String bubbleId, String memberId) async {
    try {
      LoggingService.info('$repositoryTag: Voting to remove member $memberId from bubble $bubbleId');

      final currentUserId = await _getCurrentUserId();
      await _bubbleRemoteDataSource.voteToRemoveMember(bubbleId, currentUserId, memberId);

      // Invalidate relevant caches
      await invalidateCache('bubble_members_$bubbleId');

      LoggingService.success('$repositoryTag: Successfully voted to remove member');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error voting to remove member: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to vote to remove member: $e'));
    }
  }

  @override
  Future<Result<void>> deleteBubbleFromHistory(String bubbleId, String userId) async {
    try {
      LoggingService.info('$repositoryTag: Deleting bubble $bubbleId from history for user $userId');

      await _bubbleHistoryDao.deleteBubbleForUser(bubbleId, userId);

      // Invalidate relevant caches
      await invalidateCache('bubble_history_$userId');
      await invalidateCache('bubble_$bubbleId');

      LoggingService.success('$repositoryTag: Successfully deleted bubble from history');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error deleting bubble from history: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to delete bubble from history: $e'));
    }
  }

  @override
  Future<Result<void>> clearBubbleHistory(String userId) async {
    try {
      LoggingService.info('$repositoryTag: Clearing bubble history for user $userId');

      await _bubbleHistoryDao.clearBubbleHistoryForUser(userId);

      // Invalidate all caches for this user
      await clearCache();

      LoggingService.success('$repositoryTag: Successfully cleared bubble history');
      return Result.success(null);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error clearing bubble history: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to clear bubble history: $e'));
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> getRecentBubbles(String userId, {int limit = 10}) async {
    try {
      LoggingService.info('$repositoryTag: Getting recent bubbles for user $userId (limit: $limit)');

      final historyResult = await getBubbleHistory(userId);
      if (historyResult.isFailure) {
        return historyResult;
      }

      final recentBubbles = historyResult.data
        ..sort((a, b) => (b.updatedAt ?? b.createdAt).compareTo(a.updatedAt ?? a.createdAt));

      final limitedBubbles = recentBubbles.take(limit).toList();

      LoggingService.success('$repositoryTag: Successfully loaded ${limitedBubbles.length} recent bubbles');
      return Result.success(limitedBubbles);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting recent bubbles: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get recent bubbles: $e'));
    }
  }

  @override
  Future<Result<List<MemberHistoryEvent>>> getBubbleMemberHistory(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Getting member history for bubble $bubbleId');

      final events = await executeCacheFirst<List<MemberHistoryEvent>>(
        cacheKey: 'member_history_$bubbleId',
        cacheOperation: () async {
          final cachedEvents = await _memberHistoryEventDao.getMemberHistoryEvents(bubbleId);
          final memberEvents = <MemberHistoryEvent>[];

          for (final event in cachedEvents) {
            final memberEvent = MemberHistoryEvent(
              eventId: event.eventId,
              bubbleId: event.bubbleId,
              userId: event.userId,
              eventType: event.eventType,
              timestamp: event.timestamp,

            );
            memberEvents.add(memberEvent);
          }

          return memberEvents.isNotEmpty ? memberEvents : null;
        },
        remoteOperation: () async => <MemberHistoryEvent>[],
        cacheUpdate: (events) async {
          for (final event in events) {
            final driftEvent = DriftMemberHistoryEvent(
              eventId: event.eventId,
              bubbleId: event.bubbleId,
              userId: event.userId,
              eventType: event.eventType,
              timestamp: event.timestamp,
              metadata: event.metadata.toString(),
              cachedAt: DateTime.now(),
              lastRefreshed: DateTime.now(),
              isStale: false,
            );
            await _memberHistoryEventDao.insertOrUpdateMemberHistoryEvent(driftEvent);
          }
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded ${events.length} member history events');
      return Result.success(events);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting member history: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get member history: $e'));
    }
  }

  @override
  Future<Result<List<ActivityEvent>>> getBubbleActivityHistory(String bubbleId) async {
    try {
      LoggingService.info('$repositoryTag: Getting activity history for bubble $bubbleId');

      final events = await executeCacheFirst<List<ActivityEvent>>(
        cacheKey: 'activity_history_$bubbleId',
        cacheOperation: () async {
          final cachedEvents = await _activityHistoryEventDao.getActivityHistoryEvents(bubbleId);
          final activityEvents = <ActivityEvent>[];

          for (final event in cachedEvents) {
            final activityEvent = ActivityEvent(
              eventId: event.eventId,
              bubbleId: event.bubbleId,
              userId: event.userId ?? '', // Handle nullable userId from database
              eventType: event.eventType,
              timestamp: event.timestamp,

            );
            activityEvents.add(activityEvent);
          }

          return activityEvents.isNotEmpty ? activityEvents : null;
        },
        remoteOperation: () async => <ActivityEvent>[],
        cacheUpdate: (events) async {
          for (final event in events) {
            final driftEvent = DriftActivityHistoryEvent(
              eventId: event.eventId,
              bubbleId: event.bubbleId,
              userId: event.userId, // userId is non-nullable in protobuf
              eventType: event.eventType,
              timestamp: event.timestamp,
              metadata: event.metadata.toString(),
              cachedAt: DateTime.now(),
              lastRefreshed: DateTime.now(),
              isStale: false,
            );
            await _activityHistoryEventDao.insertOrUpdateActivityHistoryEvent(driftEvent);
          }
        },
      );

      LoggingService.success('$repositoryTag: Successfully loaded ${events.length} activity history events');
      return Result.success(events);
    } on Exception catch (e, stackTrace) {
      LoggingService.error('$repositoryTag: Error getting activity history: $e', stackTrace: stackTrace);
      return Result.failure(failures.NetworkFailure(message: 'Failed to get activity history: $e'));
    }
  }

  @override
  Future<Result<List<BubbleEntity>>> getUserBubbleHistory(String userId) => getBubbleHistory(userId);

  /// Convert BubbleEntity to DriftBubbleHistory
  DriftBubbleHistory _bubbleToDrift(BubbleEntity entity, String userId) {
    final companion = BubbleMapper.domainToDrift(entity, userId);
    return DriftBubbleHistory(
      id: companion.id.value,
      name: companion.name.value,
      capacity: companion.capacity.value,
      creatorId: companion.creatorId.value,
      createdAt: companion.createdAt.value,
      updatedAt: companion.updatedAt.value,
      endDate: companion.endDate.value,
      status: companion.status.value,
      description: companion.description.value,
      imageUrl: companion.imageUrl.value,
      userId: userId,
      cachedAt: companion.cachedAt.value,
      lastRefreshed: companion.lastRefreshed.value,
      lastAccessed: companion.lastAccessed.value,
      isStale: companion.isStale.value,
    );
  }
}
