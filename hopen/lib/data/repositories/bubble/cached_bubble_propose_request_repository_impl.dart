/// Cache-first implementation of BubbleProposeRequestRepository
///
/// This implementation uses Drift for persistent caching with
/// stale-while-revalidate pattern for optimal performance.
library;

import 'dart:async';

import '../../../generated/grpc/bubble.pbgrpc.dart' as bubble_proto;
import '../../../generated/grpc/common.pb.dart' as common;
import '../../../repositories/bubble/bubble_propose_request_repository.dart';
import '../../../statefulbusinesslogic/core/db/app_database.dart';
import '../../../statefulbusinesslogic/core/error/errors.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../datasources/bubble_remote_data_source.dart';

/// Cache-first implementation with Drift persistence
/// Uses stale-while-revalidate pattern for optimal performance
class CachedBubbleProposeRequestRepositoryImpl
    implements BubbleProposeRequestRepository {
  CachedBubbleProposeRequestRepositoryImpl({
    required BubbleRemoteDataSource bubbleRemoteDataSource,
    required BubbleProposeRequestDao bubbleProposeRequestDao,
  }) : _dao = bubbleProposeRequestDao,
       _bubbleRemoteDataSource = bubbleRemoteDataSource;

  final BubbleProposeRequestDao _dao;
  final BubbleRemoteDataSource _bubbleRemoteDataSource;

  // Cache configuration (currently unused but kept for future use)
  // ignore: unused_field
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Get pending propose requests for a user from cache first
  Future<List<DriftBubbleProposeRequest>> getPendingProposeRequests(String userId) async {
    try {
      // Check cache first
      final cached = await _dao.getPendingProposeRequests(userId);

      // For propose requests, we always want fresh data since they're time-sensitive
      // But we can return cached data immediately and refresh in background
      if (cached.isNotEmpty) {
        // Trigger background refresh for fresh data (cache expires in $_cacheExpiry)
        // ignore: unawaited_futures
        _refreshProposeRequestsInBackground(userId);
        return cached;
      }

      // No cached data, fetch from network
      return _fetchAndCacheProposeRequests(userId);
    } on Exception catch (e) {
      LoggingService.error(
        'CachedBubbleProposeRequestRepository: Error getting pending requests: $e',
      );
      return [];
    }
  }

  /// Refresh propose requests in background
  Future<void> _refreshProposeRequestsInBackground(String userId) async {
    try {
      await _fetchAndCacheProposeRequests(userId);
    } on Exception catch (e) {
      LoggingService.warning(
        'CachedBubbleProposeRequestRepository: Background refresh failed: $e',
      );
    }
  }

  /// Fetch propose requests from network and cache them
  Future<List<DriftBubbleProposeRequest>> _fetchAndCacheProposeRequests(String userId) async {
    try {
      LoggingService.info(
        'CachedBubbleProposeRequestRepository: Fetching propose requests for user $userId',
      );

      // Propose requests are voting requests for existing bubble members to approve/disapprove
      // new members joining via invite or join requests. All bubble members must vote.
      // These are INVITE or JOIN requests that need approval from existing bubble members.

      // Fetch pending bubble requests that require this user's vote
      final requestsResponse = await _bubbleRemoteDataSource.getBubbleRequests(
        bubble_proto.GetBubbleRequestsRequest()
          ..userId = userId
          ..status = common.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_PENDING,
      );

      final proposeRequests = <DriftBubbleProposeRequest>[];

      // Filter for requests that are in the "propose" phase (waiting for member votes)
      // These are INVITE or JOIN requests that need approval from existing bubble members
      for (final request in requestsResponse.requests) {
        if (request.type == common.BubbleRequestType.BUBBLE_REQUEST_TYPE_INVITE ||
            request.type == common.BubbleRequestType.BUBBLE_REQUEST_TYPE_JOIN) {
          // Create entity and cache the request
          final entity = DriftBubbleProposeRequest(
            id: request.id,
            bubbleId: request.bubbleId,
            proposerId: request.requesterId,
            proposedMemberId: request.userId,
            status: _mapRequestStatus(request.status),

            createdAt: request.hasCreatedAt()
                ? DateTime.fromMillisecondsSinceEpoch(request.createdAt.seconds.toInt() * 1000)
                : DateTime.now(),
            expiresAt: request.hasUpdatedAt()
                ? DateTime.fromMillisecondsSinceEpoch(request.updatedAt.seconds.toInt() * 1000)
                : null,

            cachedAt: DateTime.now(),
            lastRefreshed: DateTime.now(),
            isStale: false,
          );
          await _dao.insertOrUpdateProposeRequest(entity);

          // Convert to list format
          final cachedRequest = await _dao.getProposeRequest(request.id);
          if (cachedRequest != null) {
            proposeRequests.add(cachedRequest);
          }
        }
      }

      LoggingService.success(
        'CachedBubbleProposeRequestRepository: Fetched and cached ${proposeRequests.length} propose requests (voting requests)',
      );

      return proposeRequests;
    } on Exception catch (e) {
      LoggingService.error(
        'CachedBubbleProposeRequestRepository: Failed to fetch propose requests: $e',
      );
      return [];
    }
  }

  /// Map protobuf BubbleRequestStatus to string
  String _mapRequestStatus(common.BubbleRequestStatus status) {
    switch (status) {
      case common.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_PENDING:
        return 'pending';
      case common.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_ACCEPTED:
        return 'accepted';
      case common.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_REJECTED:
        return 'rejected';
      case common.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_CANCELLED:
        return 'cancelled';
      default:
        return 'pending';
    }
  }

  @override
  Future<Result<bool>> acceptProposeRequest({
    required String requestId,
    required String proposedMemberId,
  }) async {
    try {
      // Input validation
      if (requestId.trim().isEmpty) {
        return Result.failure(
          const ValidationFailure(message: 'Request ID cannot be empty'),
        );
      }
      if (proposedMemberId.trim().isEmpty) {
        return Result.failure(
          const ValidationFailure(message: 'Proposed member ID cannot be empty'),
        );
      }

      LoggingService.info(
        'CachedBubbleProposeRequestRepository: Accepting propose request $requestId for member $proposedMemberId',
      );

      // Accept the bubble request via gRPC
      await _bubbleRemoteDataSource.acceptBubbleRequest(
        requestId,
        '', // bubbleId - will be resolved by the remote data source
        proposedMemberId,
      );

      // Heuristic: after accepting, check if request still exists in pending list; if not, assume last acceptance
      final pendingResponse = await _bubbleRemoteDataSource.getBubbleRequests(
        bubble_proto.GetBubbleRequestsRequest()
          ..status = common.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_PENDING,
      );
      final stillExists = pendingResponse.requests.any(
        (r) => r.id == requestId,
      );
      final isFinalized = !stillExists;

      // Update cache - remove the request since it's been processed
      await _dao.deleteProposeRequest(requestId);

      LoggingService.success(
        'CachedBubbleProposeRequestRepository: Successfully accepted propose request $requestId (finalized=$isFinalized)',
      );
      return Result.success(isFinalized);
    } on Exception catch (e) {
      LoggingService.error(
        'CachedBubbleProposeRequestRepository: Error accepting propose request: $e',
      );
      return Result.failure(
        NetworkFailure(message: 'Failed to accept propose request: $e'),
      );
    }
  }

  @override
  Future<Result<void>> declineProposeRequest({
    required String requestId,
    required String proposedMemberId,
  }) async {
    try {
      // Input validation
      if (requestId.trim().isEmpty) {
        return Result.failure(
          const ValidationFailure(message: 'Request ID cannot be empty'),
        );
      }
      if (proposedMemberId.trim().isEmpty) {
        return Result.failure(
          const ValidationFailure(message: 'Proposed member ID cannot be empty'),
        );
      }

      LoggingService.info(
        'CachedBubbleProposeRequestRepository: Declining propose request $requestId for member $proposedMemberId',
      );

      // Reject the bubble request via gRPC
      await _bubbleRemoteDataSource.rejectBubbleRequest(
        requestId,
        '', // bubbleId - will be resolved by the remote data source
        proposedMemberId,
      );

      // Update cache - remove the request since it's been processed
      await _dao.deleteProposeRequest(requestId);

      LoggingService.success(
        'CachedBubbleProposeRequestRepository: Successfully declined propose request $requestId',
      );
      return Result.success(null);
    } on Exception catch (e) {
      LoggingService.error(
        'CachedBubbleProposeRequestRepository: Error declining propose request: $e',
      );
      return Result.failure(
        NetworkFailure(message: 'Failed to decline propose request: $e'),
      );
    }
  }

  /// Clear all cached propose requests
  Future<void> clearCache() async {
    try {
      await _dao.clearAllProposeRequests();
      LoggingService.info(
        'CachedBubbleProposeRequestRepository: Cache cleared successfully',
      );
    } on Exception catch (e) {
      LoggingService.error(
        'CachedBubbleProposeRequestRepository: Error clearing cache: $e',
      );
    }
  }

  /// Get propose requests for a specific bubble from cache
  Future<List<DriftBubbleProposeRequest>> getProposeRequestsForBubble(String bubbleId) async {
    try {
      return await _dao.getProposeRequestsForBubble(bubbleId);
    } on Exception catch (e) {
      LoggingService.error(
        'CachedBubbleProposeRequestRepository: Error getting requests for bubble: $e',
      );
      return [];
    }
  }

  /// Update propose request status in cache
  Future<void> updateProposeRequestStatus(String requestId, String status) async {
    try {
      await _dao.updateProposeRequestStatus(requestId, status);
      LoggingService.info(
        'CachedBubbleProposeRequestRepository: Updated request $requestId status to $status',
      );
    } on Exception catch (e) {
      LoggingService.error(
        'CachedBubbleProposeRequestRepository: Error updating request status: $e',
      );
    }
  }
}
