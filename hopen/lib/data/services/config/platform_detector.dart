import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';

/// Platform detection utility for determining runtime environment
class PlatformDetector {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  // Cache the result to avoid multiple async calls
  static bool? _isIosSimulator;
  static bool? _isAndroidEmulator;
  
  /// Check if running on iOS Simulator
  static Future<bool> isIosSimulator() async {
    if (!Platform.isIOS) {
      return false;
    }
    
    // Return cached result if available
    if (_isIosSimulator != null) {
      return _isIosSimulator!;
    }
    
    try {
      final iosInfo = await _deviceInfo.iosInfo;
      // Check if it's a simulator
      _isIosSimulator = !iosInfo.isPhysicalDevice;
      return _isIosSimulator!;
    } on Exception {
      // If we can't determine, assume it's a physical device
      _isIosSimulator = false;
      return false;
    }
  }
  
  /// Check if running on Android Emulator
  static Future<bool> isAndroidEmulator() async {
    if (!Platform.isAndroid) {
      return false;
    }
    
    // Return cached result if available
    if (_isAndroidEmulator != null) {
      return _isAndroidEmulator!;
    }
    
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      // Check various properties that indicate an emulator
      _isAndroidEmulator = !androidInfo.isPhysicalDevice ||
          androidInfo.brand == 'google' ||
          androidInfo.manufacturer == 'Google' ||
          androidInfo.model.contains('SDK') ||
          androidInfo.product.contains('sdk') ||
          androidInfo.hardware.contains('goldfish') ||
          androidInfo.hardware.contains('ranchu');
      return _isAndroidEmulator!;
    } on Exception {
      // Check using system properties as fallback
      try {
        final result = Process.runSync('getprop', ['ro.kernel.qemu']);
        _isAndroidEmulator = result.stdout.toString().trim() == '1';
        return _isAndroidEmulator!;
      } on Exception catch (_) {
        // If we can't determine, assume it's a physical device
        _isAndroidEmulator = false;
        return false;
      }
    }
  }
  
  /// Get the appropriate host IP based on platform and environment
  static Future<String> getHostIPForDevelopment({
    required String defaultHost,
    required String emulatorHost,
    required String dockerHost,
    required String simulatorHost,
    String? webHost,
  }) async {
    // For web platforms
    if (kIsWeb) {
      return webHost ?? defaultHost;
    }
    
    // For iOS
    if (Platform.isIOS) {
      final isSimulator = await isIosSimulator();
      if (isSimulator) {
        // iOS Simulator needs the Mac's actual IP address
        return simulatorHost;
      } else {
        // Physical iOS device needs the Docker host IP
        return dockerHost;
      }
    }
    
    // For Android
    if (Platform.isAndroid) {
      final isEmulator = await isAndroidEmulator();
      if (isEmulator) {
        // Android emulator uses special IP
        return emulatorHost;
      } else {
        // Physical Android device needs the Docker host IP
        return dockerHost;
      }
    }
    
    // For other platforms (macOS, Windows, Linux desktop apps)
    return dockerHost;
  }
  
  /// Clear cached detection results (useful for testing)
  static void clearCache() {
    _isIosSimulator = null;
    _isAndroidEmulator = null;
  }
}