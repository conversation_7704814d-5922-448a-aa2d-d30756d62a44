import 'dart:async';

import 'package:flutter/services.dart';

import '../../../statefulbusinesslogic/core/services/logging_service.dart';

/// Platform-specific call handler for native integrations
class PlatformCallHandler {
  PlatformCallHandler();

  static const String _tag = 'PlatformCallHandler';
  static const MethodChannel _channel = MethodChannel('com.hopen.app/platform_calls');

  final StreamController<Map<String, dynamic>> _callResultController = 
      StreamController<Map<String, dynamic>>.broadcast();

  bool _isInitialized = false;

  /// Stream of platform call results
  Stream<Map<String, dynamic>> get callResultStream => _callResultController.stream;

  /// Initialize the platform call handler
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggingService.warning('$_tag: Already initialized');
      return;
    }

    try {
      // Set up method call handler for incoming calls from native side
      _channel.setMethodCallHandler(_handleMethodCall);

      _isInitialized = true;
      LoggingService.success('$_tag: Initialized successfully');
    } on Exception catch (e) {
      LoggingService.error('$_tag: Failed to initialize: $e');
      rethrow;
    }
  }

  /// Handle method calls from native platforms
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    LoggingService.info('$_tag: Received method call: ${call.method}');

    try {
      switch (call.method) {
        case 'onCallStateChanged':
          return _handleCallStateChanged(call.arguments as Map<String, dynamic>);
        case 'onAudioRouteChanged':
          return _handleAudioRouteChanged(call.arguments as Map<String, dynamic>);
        case 'onPermissionResult':
          return _handlePermissionResult(call.arguments as Map<String, dynamic>);
        case 'onBatteryOptimizationResult':
          return _handleBatteryOptimizationResult(call.arguments as Map<String, dynamic>);
        default:
          LoggingService.warning('$_tag: Unknown method call: ${call.method}');
          throw PlatformException(
            code: 'UNKNOWN_METHOD',
            message: 'Method ${call.method} not implemented',
          );
      }
    } on Exception catch (e) {
      LoggingService.error('$_tag: Failed to handle method call ${call.method}: $e');
      rethrow;
    }
  }

  /// Handle call state changes from native side
  Future<void> _handleCallStateChanged(Map<String, dynamic> arguments) async {
    final callId = arguments['callId'] as String?;
    final state = arguments['state'] as String?;
    final reason = arguments['reason'] as String?;

    LoggingService.info('$_tag: Call state changed - ID: $callId, State: $state, Reason: $reason');

    _callResultController.add({
      'type': 'callStateChanged',
      'callId': callId,
      'state': state,
      'reason': reason,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Handle audio route changes
  Future<void> _handleAudioRouteChanged(Map<String, dynamic> arguments) async {
    final route = arguments['route'] as String?;
    final available = arguments['availableRoutes'] as List<dynamic>?;

    LoggingService.info('$_tag: Audio route changed - Route: $route, Available: $available');

    _callResultController.add({
      'type': 'audioRouteChanged',
      'route': route,
      'availableRoutes': available,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Handle permission results
  Future<void> _handlePermissionResult(Map<String, dynamic> arguments) async {
    final permission = arguments['permission'] as String?;
    final granted = arguments['granted'] as bool?;

    LoggingService.info('$_tag: Permission result - Permission: $permission, Granted: $granted');

    _callResultController.add({
      'type': 'permissionResult',
      'permission': permission,
      'granted': granted,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Handle battery optimization results
  Future<void> _handleBatteryOptimizationResult(Map<String, dynamic> arguments) async {
    final optimized = arguments['optimized'] as bool?;
    final canRequest = arguments['canRequest'] as bool?;

    LoggingService.info('$_tag: Battery optimization result - Optimized: $optimized, Can request: $canRequest');

    _callResultController.add({
      'type': 'batteryOptimizationResult',
      'optimized': optimized,
      'canRequest': canRequest,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Request microphone permission
  Future<bool> requestMicrophonePermission() async {
    try {
      final result = await _channel.invokeMethod<bool>('requestMicrophonePermission');
      LoggingService.info('$_tag: Microphone permission result: $result');
      return result ?? false;
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to request microphone permission: $e');
      return false;
    }
  }

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    try {
      final result = await _channel.invokeMethod<bool>('requestCameraPermission');
      LoggingService.info('$_tag: Camera permission result: $result');
      return result ?? false;
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to request camera permission: $e');
      return false;
    }
  }

  /// Check if battery optimization is disabled
  Future<bool> isBatteryOptimizationDisabled() async {
    try {
      final result = await _channel.invokeMethod<bool>('isBatteryOptimizationDisabled');
      LoggingService.info('$_tag: Battery optimization disabled: $result');
      return result ?? false;
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to check battery optimization: $e');
      return false;
    }
  }

  /// Request to disable battery optimization
  Future<void> requestDisableBatteryOptimization() async {
    try {
      await _channel.invokeMethod<void>('requestDisableBatteryOptimization');
      LoggingService.info('$_tag: Requested to disable battery optimization');
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to request disable battery optimization: $e');
    }
  }

  /// Set audio route (speaker, earpiece, bluetooth, etc.)
  Future<bool> setAudioRoute(String route) async {
    try {
      final result = await _channel.invokeMethod<bool>('setAudioRoute', {'route': route});
      LoggingService.info('$_tag: Set audio route to $route: $result');
      return result ?? false;
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to set audio route to $route: $e');
      return false;
    }
  }

  /// Get available audio routes
  Future<List<String>> getAvailableAudioRoutes() async {
    try {
      final result = await _channel.invokeMethod<List<dynamic>>('getAvailableAudioRoutes');
      final routes = result?.cast<String>() ?? <String>[];
      LoggingService.info('$_tag: Available audio routes: $routes');
      return routes;
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to get available audio routes: $e');
      return <String>[];
    }
  }

  /// Start proximity sensor monitoring
  Future<void> startProximitySensor() async {
    try {
      await _channel.invokeMethod<void>('startProximitySensor');
      LoggingService.info('$_tag: Started proximity sensor monitoring');
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to start proximity sensor: $e');
    }
  }

  /// Stop proximity sensor monitoring
  Future<void> stopProximitySensor() async {
    try {
      await _channel.invokeMethod<void>('stopProximitySensor');
      LoggingService.info('$_tag: Stopped proximity sensor monitoring');
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to stop proximity sensor: $e');
    }
  }

  /// Configure audio session for call
  Future<void> configureAudioSession({
    required bool isActive,
    String? category,
    String? mode,
  }) async {
    try {
      await _channel.invokeMethod<void>('configureAudioSession', {
        'isActive': isActive,
        'category': category ?? 'playAndRecord',
        'mode': mode ?? 'voiceChat',
      });
      LoggingService.info('$_tag: Configured audio session - active: $isActive');
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to configure audio session: $e');
    }
  }

  /// Update call service with current call state
  Future<void> updateCallService({
    required String callId,
    required String callState,
    String? callerName,
    String? callType,
  }) async {
    try {
      await _channel.invokeMethod<void>('updateCallService', {
        'callId': callId,
        'callState': callState,
        'callerName': callerName,
        'callType': callType ?? 'audio',
      });
      LoggingService.info('$_tag: Updated call service - state: $callState');
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to update call service: $e');
    }
  }

  /// Stop call service
  Future<void> stopCallService() async {
    try {
      await _channel.invokeMethod<void>('stopCallService');
      LoggingService.info('$_tag: Stopped call service');
    } on PlatformException catch (e) {
      LoggingService.error('$_tag: Failed to stop call service: $e');
    }
  }

  /// Dispose of the platform call handler
  void dispose() {
    _callResultController.close();
    LoggingService.info('$_tag: Disposed');
  }
}
