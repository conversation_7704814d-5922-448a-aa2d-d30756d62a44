import 'package:flutter/material.dart';

import '../../../presentation/widgets/custom_toast.dart';
import '../../../repositories/notification/toast_service.dart';

/// Implementation of ToastService using CustomToast widget
/// This follows clean architecture by implementing the repository interface
class ToastServiceImpl implements ToastService {
  ToastServiceImpl({required this.navigatorKey});

  final GlobalKey<NavigatorState> navigatorKey;

  BuildContext? get _context => navigatorKey.currentContext;

  @override
  void showErrorToast(String message) {
    final context = _context;
    if (context != null) {
      CustomToast.showError(context, message);
    }
  }

  @override
  void showWarningToast(String message) {
    final context = _context;
    if (context != null) {
      CustomToast.showWarning(context, message);
    }
  }

  @override
  void showInfoToast(String message) {
    final context = _context;
    if (context != null) {
      CustomToast.showInfo(context, message);
    }
  }

  @override
  void showSuccessToast(String message) {
    final context = _context;
    if (context != null) {
      CustomToast.showSuccess(context, message);
    }
  }

  @override
  void showInfo(String title, {String? description}) {
    final context = _context;
    if (context != null) {
      final message = description != null ? '$title: $description' : title;
      CustomToast.showInfo(context, message);
    }
  }
}
