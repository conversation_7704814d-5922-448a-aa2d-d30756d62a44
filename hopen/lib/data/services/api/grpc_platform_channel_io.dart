import 'package:grpc/grpc.dart';

ClientChannel createPlatformChannel({
  required String host,
  required int port,
  required bool useTls,
  required Duration timeout,
}) {
  final channelOptions = ChannelOptions(
    credentials:
        useTls ? const ChannelCredentials.secure() : const ChannelCredentials.insecure(),
    codecRegistry: CodecRegistry(codecs: const [GzipCodec()]),
    connectionTimeout: timeout,
    userAgent: 'Hopen-Mobile-gRPC/1.0',
    keepAlive: const ClientKeepAliveOptions(
      pingInterval: Duration(seconds: 30),
      timeout: Duration(seconds: 5),
      permitWithoutCalls: true,
    ),
  );

  return ClientChannel(
    host,
    port: port,
    options: channelOptions,
  );
}
