/// Toast service interface for showing user notifications
/// This follows clean architecture by providing an abstraction for toast functionality
abstract class ToastService {
  /// Show an error toast message
  void showErrorToast(String message);

  /// Show a warning toast message  
  void showWarningToast(String message);

  /// Show an info toast message
  void showInfoToast(String message);

  /// Show a success toast message
  void showSuccessToast(String message);

  /// Show an info toast with title and description
  void showInfo(String title, {String? description});
}
