import 'dart:async'; // Required for Timer/Future.delayed

import 'package:flutter/material.dart';

/// Custom Custom toast widget for Hopen app
/// Provides beautiful, modern toast notifications with blur effects
class CustomToast {
  /// Shows a custom Custom toast notification
  ///
  /// [context] - BuildContext for showing the toast
  /// [message] - Text message to display
  /// [icon] - Optional icon to show (defaults to check circle)
  /// [type] - Toast type for different styling (success, warning, error, info)
  /// [duration] - How long to show the toast (defaults to 4 seconds)
  static void show(
    BuildContext context,
    String message, {
    IconData? icon,
    ToastType type = ToastType.success,
    Duration duration = const Duration(seconds: 4),
  }) {
    // Create overlay entry
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder:
          (context) => _ToastWidget(
            message: message,
            icon: icon ?? _getDefaultIcon(type),
            type: type,
            onRemove: () {
              overlayEntry.remove();
            },
            duration: duration,
          ),
    );

    // Insert overlay entry
    Overlay.of(context).insert(overlayEntry);
  }

  /// Convenience method for success toast
  static void showSuccess(BuildContext context, String message) {
    show(context, message);
  }

  /// Convenience method for error toast
  static void showError(BuildContext context, String message) {
    show(context, message, type: ToastType.error);
  }

  /// Convenience method for warning toast
  static void showWarning(BuildContext context, String message) {
    show(context, message, type: ToastType.warning);
  }

  /// Convenience method for info toast
  static void showInfo(BuildContext context, String message) {
    show(context, message, type: ToastType.info);
  }

  /// Authentication error toast with specific styling
  static void showAuthError(BuildContext context, String message) {
    show(
      context,
      message,
      type: ToastType.error,
      duration: const Duration(seconds: 4),
    );
  }

  /// Get default icon based on toast type
  static IconData _getDefaultIcon(ToastType type) {
    switch (type) {
      case ToastType.success:
        return Icons.check_circle_outline_rounded;
      case ToastType.error:
        return Icons.error_outline_rounded;
      case ToastType.warning:
        return Icons.warning_amber_rounded;
      case ToastType.info:
        return Icons.info_outline_rounded;
    }
  }
}

/// Toast types for different styling
enum ToastType { success, error, warning, info }

/// Internal toast widget with auto-dismiss functionality
class _ToastWidget extends StatefulWidget {
  const _ToastWidget({
    required this.message,
    required this.icon,
    required this.type,
    required this.onRemove,
    required this.duration,
  });
  final String message;
  final IconData icon;
  final ToastType type;
  final VoidCallback onRemove;
  final Duration duration;

  @override
  State<_ToastWidget> createState() => _ToastWidgetState();
}

class _ToastWidgetState extends State<_ToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  Timer? _dismissTimer;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(parent: _controller, curve: Curves.easeIn);

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));

    // Start animation
    _controller.forward();

    // Set up auto-dismiss timer
    _dismissTimer = Timer(widget.duration, _dismiss);
  }

  @override
  void dispose() {
    _dismissTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  Future<void> _dismiss() async {
    if (_controller.isCompleted) {
      await _controller.reverse();
    }
    widget.onRemove();
  }

  @override
  Widget build(BuildContext context) => Positioned(
    left: 20,
    right: 20,
    bottom: 40,
    child: SafeArea(
      child: IgnorePointer(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Center(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.85,
                ),
                child: CustomToastWidget(
                  message: widget.message,
                  icon: widget.icon,
                  type: widget.type,
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  );
}

/// The actual Custom toast widget
class CustomToastWidget extends StatelessWidget {
  const CustomToastWidget({
    required this.message,
    super.key,
    this.icon,
    this.type = ToastType.success,
  });
  final String message;
  final IconData? icon;
  final ToastType type;

  @override
  Widget build(BuildContext context) => Material(
      type: MaterialType.transparency,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
        decoration: ShapeDecoration(
          color: _getBackgroundColor(),
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(18),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(icon, color: _getIconColor(), size: 18),
              const SizedBox(width: 6),
            ],
            Flexible(
              child: Text(
                message,
                style: TextStyle(
                  color: _getTextColor(),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                softWrap: true,
              ),
            ),
          ],
        ),
      ),
    );

  /// Get background color based on toast type
  Color _getBackgroundColor() => switch (type) {
    ToastType.success => const Color(0xFFDFF8E9), // Light pastel green
    ToastType.error => const Color(0xFFFDECEC), // Light pastel red
    ToastType.warning => const Color(0xFFFFF7E6), // Light pastel orange
    ToastType.info => const Color(0xFFEAF5FF), // Light pastel blue
  };

  /// Get icon color based on toast type
  Color _getIconColor() => switch (type) {
    ToastType.success => const Color(0xFF34C759),
    ToastType.error => const Color(0xFFFF3B30),
    ToastType.warning => const Color(0xFFFF9500),
    ToastType.info => const Color(0xFF0A84FF),
  };

  /// Get text color based on toast type
  Color _getTextColor() => switch (type) {
    ToastType.success => const Color(0xFF34C759),
    ToastType.error => const Color(0xFFFF3B30),
    ToastType.warning => const Color(0xFFFF9500),
    ToastType.info => const Color(0xFF0A84FF),
  };
}
