import 'package:flutter/material.dart';
import 'custom_toast.dart';

class ToastDemo extends StatelessWidget {
  const ToastDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Toast Demo'),
      ),
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ElevatedButton(
              onPressed: () {
                CustomToast.showSuccess(context, 'Success Toast');
              },
              child: const Text('Show Success Toast'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () {
                CustomToast.showError(context, 'Error Toast');
              },
              child: const Text('Show Error Toast'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () {
                CustomToast.showWarning(context, 'Warning Toast');
              },
              child: const Text('Show Warning Toast'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () {
                CustomToast.showInfo(context, 'Info Toast');
              },
              child: const Text('Show Info Toast'),
            ),
          ],
        ),
      ),
    );
  }
}
