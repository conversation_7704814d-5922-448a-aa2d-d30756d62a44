// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'core_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(grpcClientServiceModule)
const grpcClientServiceModuleProvider = GrpcClientServiceModuleProvider._();

final class GrpcClientServiceModuleProvider
    extends
        $FunctionalProvider<
          GrpcClientService,
          GrpcClientService,
          GrpcClientService
        >
    with $Provider<GrpcClientService> {
  const GrpcClientServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'grpcClientServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$grpcClientServiceModuleHash();

  @$internal
  @override
  $ProviderElement<GrpcClientService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  GrpcClientService create(Ref ref) {
    return grpcClientServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(GrpcClientService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<GrpcClientService>(value),
    );
  }
}

String _$grpcClientServiceModuleHash() =>
    r'30a6d7f452ad6c4bd21348530a685f36a8773206';

@ProviderFor(connectivityServiceModule)
const connectivityServiceModuleProvider = ConnectivityServiceModuleProvider._();

final class ConnectivityServiceModuleProvider
    extends
        $FunctionalProvider<
          ConnectivityService,
          ConnectivityService,
          ConnectivityService
        >
    with $Provider<ConnectivityService> {
  const ConnectivityServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'connectivityServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$connectivityServiceModuleHash();

  @$internal
  @override
  $ProviderElement<ConnectivityService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ConnectivityService create(Ref ref) {
    return connectivityServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ConnectivityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ConnectivityService>(value),
    );
  }
}

String _$connectivityServiceModuleHash() =>
    r'6bcf1229a102efb940c0fc8b699b3645b6b30d01';

@ProviderFor(securityServiceModule)
const securityServiceModuleProvider = SecurityServiceModuleProvider._();

final class SecurityServiceModuleProvider
    extends
        $FunctionalProvider<SecurityService, SecurityService, SecurityService>
    with $Provider<SecurityService> {
  const SecurityServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'securityServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$securityServiceModuleHash();

  @$internal
  @override
  $ProviderElement<SecurityService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  SecurityService create(Ref ref) {
    return securityServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SecurityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SecurityService>(value),
    );
  }
}

String _$securityServiceModuleHash() =>
    r'f64a5b6f2182d8cb6c589e123c265ac0c9086690';

@ProviderFor(startupPerformanceServiceModule)
const startupPerformanceServiceModuleProvider =
    StartupPerformanceServiceModuleProvider._();

final class StartupPerformanceServiceModuleProvider
    extends
        $FunctionalProvider<
          StartupPerformanceService,
          StartupPerformanceService,
          StartupPerformanceService
        >
    with $Provider<StartupPerformanceService> {
  const StartupPerformanceServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'startupPerformanceServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$startupPerformanceServiceModuleHash();

  @$internal
  @override
  $ProviderElement<StartupPerformanceService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  StartupPerformanceService create(Ref ref) {
    return startupPerformanceServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(StartupPerformanceService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<StartupPerformanceService>(value),
    );
  }
}

String _$startupPerformanceServiceModuleHash() =>
    r'4a9a5454228a6f356a3a4c51d220922b2fb3426f';

@ProviderFor(resilienceRepositoryModule)
const resilienceRepositoryModuleProvider =
    ResilienceRepositoryModuleProvider._();

final class ResilienceRepositoryModuleProvider
    extends
        $FunctionalProvider<
          ResilienceRepository,
          ResilienceRepository,
          ResilienceRepository
        >
    with $Provider<ResilienceRepository> {
  const ResilienceRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'resilienceRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$resilienceRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<ResilienceRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ResilienceRepository create(Ref ref) {
    return resilienceRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ResilienceRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ResilienceRepository>(value),
    );
  }
}

String _$resilienceRepositoryModuleHash() =>
    r'f3800bd8eb36bb3af69a05448abf2e5f792c9228';

@ProviderFor(appDatabaseModule)
const appDatabaseModuleProvider = AppDatabaseModuleProvider._();

final class AppDatabaseModuleProvider
    extends $FunctionalProvider<AppDatabase, AppDatabase, AppDatabase>
    with $Provider<AppDatabase> {
  const AppDatabaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'appDatabaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$appDatabaseModuleHash();

  @$internal
  @override
  $ProviderElement<AppDatabase> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppDatabase create(Ref ref) {
    return appDatabaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppDatabase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppDatabase>(value),
    );
  }
}

String _$appDatabaseModuleHash() => r'f72f770b75f13356ece93227bcbc538972541f03';

@ProviderFor(_dataSourceDependencies)
const _dataSourceDependenciesProvider = _DataSourceDependenciesProvider._();

final class _DataSourceDependenciesProvider
    extends
        $FunctionalProvider<
          (GrpcClientService, ResilienceRepository),
          (GrpcClientService, ResilienceRepository),
          (GrpcClientService, ResilienceRepository)
        >
    with $Provider<(GrpcClientService, ResilienceRepository)> {
  const _DataSourceDependenciesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'_dataSourceDependenciesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$_dataSourceDependenciesHash();

  @$internal
  @override
  $ProviderElement<(GrpcClientService, ResilienceRepository)> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  (GrpcClientService, ResilienceRepository) create(Ref ref) {
    return _dataSourceDependencies(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue((GrpcClientService, ResilienceRepository) value) {
    return $ProviderOverride(
      origin: this,
      providerOverride:
          $SyncValueProvider<(GrpcClientService, ResilienceRepository)>(value),
    );
  }
}

String _$_dataSourceDependenciesHash() =>
    r'04290fecd4abc84a24160d96b398054adcf79ae7';

@ProviderFor(authRemoteDataSourceModule)
const authRemoteDataSourceModuleProvider =
    AuthRemoteDataSourceModuleProvider._();

final class AuthRemoteDataSourceModuleProvider
    extends
        $FunctionalProvider<
          AuthRemoteDataSource,
          AuthRemoteDataSource,
          AuthRemoteDataSource
        >
    with $Provider<AuthRemoteDataSource> {
  const AuthRemoteDataSourceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'authRemoteDataSourceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$authRemoteDataSourceModuleHash();

  @$internal
  @override
  $ProviderElement<AuthRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AuthRemoteDataSource create(Ref ref) {
    return authRemoteDataSourceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AuthRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AuthRemoteDataSource>(value),
    );
  }
}

String _$authRemoteDataSourceModuleHash() =>
    r'542b314914709af06b778ed110bd8fe60d97a191';

@ProviderFor(bubbleRemoteDataSourceModule)
const bubbleRemoteDataSourceModuleProvider =
    BubbleRemoteDataSourceModuleProvider._();

final class BubbleRemoteDataSourceModuleProvider
    extends
        $FunctionalProvider<
          BubbleRemoteDataSource,
          BubbleRemoteDataSource,
          BubbleRemoteDataSource
        >
    with $Provider<BubbleRemoteDataSource> {
  const BubbleRemoteDataSourceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleRemoteDataSourceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleRemoteDataSourceModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleRemoteDataSource create(Ref ref) {
    return bubbleRemoteDataSourceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleRemoteDataSource>(value),
    );
  }
}

String _$bubbleRemoteDataSourceModuleHash() =>
    r'c24cfca4a194d7d40a917d4f947368617cbedec5';

@ProviderFor(callRemoteDataSourceModule)
const callRemoteDataSourceModuleProvider =
    CallRemoteDataSourceModuleProvider._();

final class CallRemoteDataSourceModuleProvider
    extends
        $FunctionalProvider<
          CallRemoteDataSource,
          CallRemoteDataSource,
          CallRemoteDataSource
        >
    with $Provider<CallRemoteDataSource> {
  const CallRemoteDataSourceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callRemoteDataSourceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callRemoteDataSourceModuleHash();

  @$internal
  @override
  $ProviderElement<CallRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  CallRemoteDataSource create(Ref ref) {
    return callRemoteDataSourceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(CallRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<CallRemoteDataSource>(value),
    );
  }
}

String _$callRemoteDataSourceModuleHash() =>
    r'ce5810fcc47f7791a80af05b8152da5baadda3df';

@ProviderFor(presenceRemoteDataSourceModule)
const presenceRemoteDataSourceModuleProvider =
    PresenceRemoteDataSourceModuleProvider._();

final class PresenceRemoteDataSourceModuleProvider
    extends
        $FunctionalProvider<
          PresenceRemoteDataSource,
          PresenceRemoteDataSource,
          PresenceRemoteDataSource
        >
    with $Provider<PresenceRemoteDataSource> {
  const PresenceRemoteDataSourceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'presenceRemoteDataSourceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$presenceRemoteDataSourceModuleHash();

  @$internal
  @override
  $ProviderElement<PresenceRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  PresenceRemoteDataSource create(Ref ref) {
    return presenceRemoteDataSourceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PresenceRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<PresenceRemoteDataSource>(value),
    );
  }
}

String _$presenceRemoteDataSourceModuleHash() =>
    r'68f1b83826fc7db4da425f7cb35895e61f157476';

@ProviderFor(realtimeRemoteDataSourceModule)
const realtimeRemoteDataSourceModuleProvider =
    RealtimeRemoteDataSourceModuleProvider._();

final class RealtimeRemoteDataSourceModuleProvider
    extends
        $FunctionalProvider<
          RealtimeRemoteDataSource,
          RealtimeRemoteDataSource,
          RealtimeRemoteDataSource
        >
    with $Provider<RealtimeRemoteDataSource> {
  const RealtimeRemoteDataSourceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'realtimeRemoteDataSourceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$realtimeRemoteDataSourceModuleHash();

  @$internal
  @override
  $ProviderElement<RealtimeRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  RealtimeRemoteDataSource create(Ref ref) {
    return realtimeRemoteDataSourceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(RealtimeRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<RealtimeRemoteDataSource>(value),
    );
  }
}

String _$realtimeRemoteDataSourceModuleHash() =>
    r'05bcf5fb625d89a6891e245642bade39ca2ac9c8';

@ProviderFor(searchRemoteDataSourceModule)
const searchRemoteDataSourceModuleProvider =
    SearchRemoteDataSourceModuleProvider._();

final class SearchRemoteDataSourceModuleProvider
    extends
        $FunctionalProvider<
          SearchRemoteDataSource,
          SearchRemoteDataSource,
          SearchRemoteDataSource
        >
    with $Provider<SearchRemoteDataSource> {
  const SearchRemoteDataSourceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'searchRemoteDataSourceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$searchRemoteDataSourceModuleHash();

  @$internal
  @override
  $ProviderElement<SearchRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SearchRemoteDataSource create(Ref ref) {
    return searchRemoteDataSourceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SearchRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SearchRemoteDataSource>(value),
    );
  }
}

String _$searchRemoteDataSourceModuleHash() =>
    r'69c3dd01eb75e34ae0256bc0c6f400392461b0bc';

@ProviderFor(syncRemoteDataSourceModule)
const syncRemoteDataSourceModuleProvider =
    SyncRemoteDataSourceModuleProvider._();

final class SyncRemoteDataSourceModuleProvider
    extends
        $FunctionalProvider<
          SyncRemoteDataSource,
          SyncRemoteDataSource,
          SyncRemoteDataSource
        >
    with $Provider<SyncRemoteDataSource> {
  const SyncRemoteDataSourceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncRemoteDataSourceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncRemoteDataSourceModuleHash();

  @$internal
  @override
  $ProviderElement<SyncRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SyncRemoteDataSource create(Ref ref) {
    return syncRemoteDataSourceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SyncRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SyncRemoteDataSource>(value),
    );
  }
}

String _$syncRemoteDataSourceModuleHash() =>
    r'60bd8293422271e2c9fd305f7c9c1b510101be3a';

@ProviderFor(userRemoteDataSourceModule)
const userRemoteDataSourceModuleProvider =
    UserRemoteDataSourceModuleProvider._();

final class UserRemoteDataSourceModuleProvider
    extends
        $FunctionalProvider<
          UserRemoteDataSource,
          UserRemoteDataSource,
          UserRemoteDataSource
        >
    with $Provider<UserRemoteDataSource> {
  const UserRemoteDataSourceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userRemoteDataSourceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userRemoteDataSourceModuleHash();

  @$internal
  @override
  $ProviderElement<UserRemoteDataSource> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  UserRemoteDataSource create(Ref ref) {
    return userRemoteDataSourceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserRemoteDataSource value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserRemoteDataSource>(value),
    );
  }
}

String _$userRemoteDataSourceModuleHash() =>
    r'75dfb750b35d5b495688d6199f7194a4702a9a41';
