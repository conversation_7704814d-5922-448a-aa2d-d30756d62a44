// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(userRepositoryModule)
const userRepositoryModuleProvider = UserRepositoryModuleProvider._();

final class UserRepositoryModuleProvider
    extends $FunctionalProvider<UserRepository, UserRepository, UserRepository>
    with $Provider<UserRepository> {
  const UserRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<UserRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  UserRepository create(Ref ref) {
    return userRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserRepository>(value),
    );
  }
}

String _$userRepositoryModuleHash() =>
    r'a14273f97a6d293c42270b805853460df2677e85';

@ProviderFor(profilePictureRepositoryModule)
const profilePictureRepositoryModuleProvider =
    ProfilePictureRepositoryModuleProvider._();

final class ProfilePictureRepositoryModuleProvider
    extends
        $FunctionalProvider<
          ProfilePictureRepository,
          ProfilePictureRepository,
          ProfilePictureRepository
        >
    with $Provider<ProfilePictureRepository> {
  const ProfilePictureRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePictureRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePictureRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<ProfilePictureRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ProfilePictureRepository create(Ref ref) {
    return profilePictureRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ProfilePictureRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ProfilePictureRepository>(value),
    );
  }
}

String _$profilePictureRepositoryModuleHash() =>
    r'63d0aa6b943068ae5d9540c23701e882dbb4e814';

@ProviderFor(activityStatusRepositoryModule)
const activityStatusRepositoryModuleProvider =
    ActivityStatusRepositoryModuleProvider._();

final class ActivityStatusRepositoryModuleProvider
    extends
        $FunctionalProvider<
          ActivityStatusRepository,
          ActivityStatusRepository,
          ActivityStatusRepository
        >
    with $Provider<ActivityStatusRepository> {
  const ActivityStatusRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'activityStatusRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$activityStatusRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<ActivityStatusRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ActivityStatusRepository create(Ref ref) {
    return activityStatusRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ActivityStatusRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ActivityStatusRepository>(value),
    );
  }
}

String _$activityStatusRepositoryModuleHash() =>
    r'cb422c485dad948fd4e278343dbe8c880d7451f9';

@ProviderFor(syncValidationServiceModule)
const syncValidationServiceModuleProvider =
    SyncValidationServiceModuleProvider._();

final class SyncValidationServiceModuleProvider
    extends
        $FunctionalProvider<
          SyncValidationService,
          SyncValidationService,
          SyncValidationService
        >
    with $Provider<SyncValidationService> {
  const SyncValidationServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncValidationServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncValidationServiceModuleHash();

  @$internal
  @override
  $ProviderElement<SyncValidationService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SyncValidationService create(Ref ref) {
    return syncValidationServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SyncValidationService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SyncValidationService>(value),
    );
  }
}

String _$syncValidationServiceModuleHash() =>
    r'5cf010a6bc1657971e3a561b4fdefd8113a63463';

@ProviderFor(userProfilePreloaderServiceModule)
const userProfilePreloaderServiceModuleProvider =
    UserProfilePreloaderServiceModuleProvider._();

final class UserProfilePreloaderServiceModuleProvider
    extends
        $FunctionalProvider<
          UserProfilePreloaderService,
          UserProfilePreloaderService,
          UserProfilePreloaderService
        >
    with $Provider<UserProfilePreloaderService> {
  const UserProfilePreloaderServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userProfilePreloaderServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$userProfilePreloaderServiceModuleHash();

  @$internal
  @override
  $ProviderElement<UserProfilePreloaderService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  UserProfilePreloaderService create(Ref ref) {
    return userProfilePreloaderServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserProfilePreloaderService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserProfilePreloaderService>(value),
    );
  }
}

String _$userProfilePreloaderServiceModuleHash() =>
    r'8f2e526872febe17a58d2a416b525b12cbc36137';
