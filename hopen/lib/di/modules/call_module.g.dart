// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(callRepositoryModule)
const callRepositoryModuleProvider = CallRepositoryModuleProvider._();

final class CallRepositoryModuleProvider
    extends
        $FunctionalProvider<
          CallRepositoryExtended,
          CallRepositoryExtended,
          CallRepositoryExtended
        >
    with $Provider<CallRepositoryExtended> {
  const CallRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<CallRepositoryExtended> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  CallRepositoryExtended create(Ref ref) {
    return callRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(CallRepositoryExtended value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<CallRepositoryExtended>(value),
    );
  }
}

String _$callRepositoryModuleHash() =>
    r'4ad2150cff6de87f42768dcf23b3ee51fc91bf4d';
