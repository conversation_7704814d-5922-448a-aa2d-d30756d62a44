// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contacts_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(contactsRepositoryModule)
const contactsRepositoryModuleProvider = ContactsRepositoryModuleProvider._();

final class ContactsRepositoryModuleProvider
    extends
        $FunctionalProvider<
          ContactsRepository,
          ContactsRepository,
          ContactsRepository
        >
    with $Provider<ContactsRepository> {
  const ContactsRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'contactsRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$contactsRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<ContactsRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ContactsRepository create(Ref ref) {
    return contactsRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ContactsRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ContactsRepository>(value),
    );
  }
}

String _$contactsRepositoryModuleHash() =>
    r'd393f6cc35498952f97dbe48b64a24e770d7f9b8';

@ProviderFor(contactRequestsRepositoryModule)
const contactRequestsRepositoryModuleProvider =
    ContactRequestsRepositoryModuleProvider._();

final class ContactRequestsRepositoryModuleProvider
    extends
        $FunctionalProvider<
          ContactsRepository,
          ContactsRepository,
          ContactsRepository
        >
    with $Provider<ContactsRepository> {
  const ContactRequestsRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'contactRequestsRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$contactRequestsRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<ContactsRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ContactsRepository create(Ref ref) {
    return contactRequestsRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ContactsRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ContactsRepository>(value),
    );
  }
}

String _$contactRequestsRepositoryModuleHash() =>
    r'ba8f2762c4a64676c590683808feb54758bdcf6d';
