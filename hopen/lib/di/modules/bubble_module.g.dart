// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bubble_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(bubbleRepositoryModule)
const bubbleRepositoryModuleProvider = BubbleRepositoryModuleProvider._();

final class BubbleRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleRepository,
          BubbleRepository,
          BubbleRepository
        >
    with $Provider<BubbleRepository> {
  const BubbleRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  BubbleRepository create(Ref ref) {
    return bubbleRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleRepository>(value),
    );
  }
}

String _$bubbleRepositoryModuleHash() =>
    r'2e81268b8d534167699b3993503288ab2c6dd882';

@ProviderFor(bubbleJoinRequestRepositoryModule)
const bubbleJoinRequestRepositoryModuleProvider =
    BubbleJoinRequestRepositoryModuleProvider._();

final class BubbleJoinRequestRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleJoinRequestRepository,
          BubbleJoinRequestRepository,
          BubbleJoinRequestRepository
        >
    with $Provider<BubbleJoinRequestRepository> {
  const BubbleJoinRequestRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleJoinRequestRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$bubbleJoinRequestRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleJoinRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleJoinRequestRepository create(Ref ref) {
    return bubbleJoinRequestRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleJoinRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleJoinRequestRepository>(value),
    );
  }
}

String _$bubbleJoinRequestRepositoryModuleHash() =>
    r'40857253fa6bef447fc0b580196a5df087a5db34';

@ProviderFor(bubbleInviteRequestRepositoryModule)
const bubbleInviteRequestRepositoryModuleProvider =
    BubbleInviteRequestRepositoryModuleProvider._();

final class BubbleInviteRequestRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleInviteRequestRepository,
          BubbleInviteRequestRepository,
          BubbleInviteRequestRepository
        >
    with $Provider<BubbleInviteRequestRepository> {
  const BubbleInviteRequestRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleInviteRequestRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$bubbleInviteRequestRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleInviteRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleInviteRequestRepository create(Ref ref) {
    return bubbleInviteRequestRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleInviteRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleInviteRequestRepository>(
        value,
      ),
    );
  }
}

String _$bubbleInviteRequestRepositoryModuleHash() =>
    r'df4c53b345f548560a333cac0417f6cff8610501';

@ProviderFor(bubbleKickoutRepositoryModule)
const bubbleKickoutRepositoryModuleProvider =
    BubbleKickoutRepositoryModuleProvider._();

final class BubbleKickoutRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleKickoutRepository,
          BubbleKickoutRepository,
          BubbleKickoutRepository
        >
    with $Provider<BubbleKickoutRepository> {
  const BubbleKickoutRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleKickoutRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleKickoutRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleKickoutRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleKickoutRepository create(Ref ref) {
    return bubbleKickoutRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleKickoutRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleKickoutRepository>(value),
    );
  }
}

String _$bubbleKickoutRepositoryModuleHash() =>
    r'319e8df011d0a9913aa91341cb8b9b8486312576';

@ProviderFor(bubbleProposeRequestRepositoryModule)
const bubbleProposeRequestRepositoryModuleProvider =
    BubbleProposeRequestRepositoryModuleProvider._();

final class BubbleProposeRequestRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleProposeRequestRepository,
          BubbleProposeRequestRepository,
          BubbleProposeRequestRepository
        >
    with $Provider<BubbleProposeRequestRepository> {
  const BubbleProposeRequestRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleProposeRequestRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$bubbleProposeRequestRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleProposeRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleProposeRequestRepository create(Ref ref) {
    return bubbleProposeRequestRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleProposeRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleProposeRequestRepository>(
        value,
      ),
    );
  }
}

String _$bubbleProposeRequestRepositoryModuleHash() =>
    r'177f4f04992c413d7a3890bb7c1365e9464890d9';

@ProviderFor(bubbleHistoryRepositoryModule)
const bubbleHistoryRepositoryModuleProvider =
    BubbleHistoryRepositoryModuleProvider._();

final class BubbleHistoryRepositoryModuleProvider
    extends
        $FunctionalProvider<
          BubbleHistoryRepository,
          BubbleHistoryRepository,
          BubbleHistoryRepository
        >
    with $Provider<BubbleHistoryRepository> {
  const BubbleHistoryRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleHistoryRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleHistoryRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<BubbleHistoryRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BubbleHistoryRepository create(Ref ref) {
    return bubbleHistoryRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleHistoryRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleHistoryRepository>(value),
    );
  }
}

String _$bubbleHistoryRepositoryModuleHash() =>
    r'1036ba8c347f0971c39084975131760e93cc0473';
