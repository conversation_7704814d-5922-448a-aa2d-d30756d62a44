// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friendship_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(friendshipRepositoryModule)
const friendshipRepositoryModuleProvider =
    FriendshipRepositoryModuleProvider._();

final class FriendshipRepositoryModuleProvider
    extends
        $FunctionalProvider<
          FriendshipRepository,
          FriendshipRepository,
          FriendshipRepository
        >
    with $Provider<FriendshipRepository> {
  const FriendshipRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'friendshipRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$friendshipRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<FriendshipRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  FriendshipRepository create(Ref ref) {
    return friendshipRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(FriendshipRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<FriendshipRepository>(value),
    );
  }
}

String _$friendshipRepositoryModuleHash() =>
    r'5b67ca8fe12c04a0b94aa74901fec801ba75a132';

@ProviderFor(friendRequestRepositoryModule)
const friendRequestRepositoryModuleProvider =
    FriendRequestRepositoryModuleProvider._();

final class FriendRequestRepositoryModuleProvider
    extends
        $FunctionalProvider<
          FriendRequestRepository,
          FriendRequestRepository,
          FriendRequestRepository
        >
    with $Provider<FriendRequestRepository> {
  const FriendRequestRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'friendRequestRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$friendRequestRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<FriendRequestRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  FriendRequestRepository create(Ref ref) {
    return friendRequestRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(FriendRequestRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<FriendRequestRepository>(value),
    );
  }
}

String _$friendRequestRepositoryModuleHash() =>
    r'44f68a9f857e28736c5c8ed58424508dd7c4e1db';
