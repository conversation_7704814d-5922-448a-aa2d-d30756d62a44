// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'services_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(reportRepositoryModule)
const reportRepositoryModuleProvider = ReportRepositoryModuleProvider._();

final class ReportRepositoryModuleProvider
    extends
        $FunctionalProvider<
          ReportRepository,
          ReportRepository,
          ReportRepository
        >
    with $Provider<ReportRepository> {
  const ReportRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'reportRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$reportRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<ReportRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  ReportRepository create(Ref ref) {
    return reportRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ReportRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ReportRepository>(value),
    );
  }
}

String _$reportRepositoryModuleHash() =>
    r'df1f09d1c75f3040cef9fc40c1569eb9526d1d0a';

@ProviderFor(audioRecordingRepositoryModule)
const audioRecordingRepositoryModuleProvider =
    AudioRecordingRepositoryModuleProvider._();

final class AudioRecordingRepositoryModuleProvider
    extends
        $FunctionalProvider<
          AudioRecordingRepository,
          AudioRecordingRepository,
          AudioRecordingRepository
        >
    with $Provider<AudioRecordingRepository> {
  const AudioRecordingRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'audioRecordingRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$audioRecordingRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<AudioRecordingRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AudioRecordingRepository create(Ref ref) {
    return audioRecordingRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AudioRecordingRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AudioRecordingRepository>(value),
    );
  }
}

String _$audioRecordingRepositoryModuleHash() =>
    r'7552097350f8be249d6d0083e1adfb5998adda35';

@ProviderFor(imageProcessingRepositoryModule)
const imageProcessingRepositoryModuleProvider =
    ImageProcessingRepositoryModuleProvider._();

final class ImageProcessingRepositoryModuleProvider
    extends
        $FunctionalProvider<
          ImageProcessingRepository,
          ImageProcessingRepository,
          ImageProcessingRepository
        >
    with $Provider<ImageProcessingRepository> {
  const ImageProcessingRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'imageProcessingRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$imageProcessingRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<ImageProcessingRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ImageProcessingRepository create(Ref ref) {
    return imageProcessingRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ImageProcessingRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ImageProcessingRepository>(value),
    );
  }
}

String _$imageProcessingRepositoryModuleHash() =>
    r'66f6dc259a8f28cf824a93a9b06249a2a76bd4a2';

@ProviderFor(userSettingsRepositoryModule)
const userSettingsRepositoryModuleProvider =
    UserSettingsRepositoryModuleProvider._();

final class UserSettingsRepositoryModuleProvider
    extends
        $FunctionalProvider<
          UserSettingsRepository,
          UserSettingsRepository,
          UserSettingsRepository
        >
    with $Provider<UserSettingsRepository> {
  const UserSettingsRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userSettingsRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userSettingsRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<UserSettingsRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  UserSettingsRepository create(Ref ref) {
    return userSettingsRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserSettingsRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserSettingsRepository>(value),
    );
  }
}

String _$userSettingsRepositoryModuleHash() =>
    r'4f1b262204f55be0bb2c5260ef8504667c0cef8a';

@ProviderFor(dialogStateRepositoryModule)
const dialogStateRepositoryModuleProvider =
    DialogStateRepositoryModuleProvider._();

final class DialogStateRepositoryModuleProvider
    extends
        $FunctionalProvider<
          DialogStateRepository,
          DialogStateRepository,
          DialogStateRepository
        >
    with $Provider<DialogStateRepository> {
  const DialogStateRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'dialogStateRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$dialogStateRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<DialogStateRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  DialogStateRepository create(Ref ref) {
    return dialogStateRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DialogStateRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DialogStateRepository>(value),
    );
  }
}

String _$dialogStateRepositoryModuleHash() =>
    r'78c4f5b6c89baac2c9bf2e37626e059d01f43450';

@ProviderFor(audioRecordingServiceModule)
const audioRecordingServiceModuleProvider =
    AudioRecordingServiceModuleProvider._();

final class AudioRecordingServiceModuleProvider
    extends
        $FunctionalProvider<
          AudioRecordingService,
          AudioRecordingService,
          AudioRecordingService
        >
    with $Provider<AudioRecordingService> {
  const AudioRecordingServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'audioRecordingServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$audioRecordingServiceModuleHash();

  @$internal
  @override
  $ProviderElement<AudioRecordingService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AudioRecordingService create(Ref ref) {
    return audioRecordingServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AudioRecordingService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AudioRecordingService>(value),
    );
  }
}

String _$audioRecordingServiceModuleHash() =>
    r'29eb51558f8d61e8eadca1ee81b7ede36fca554d';

@ProviderFor(storageServiceModule)
const storageServiceModuleProvider = StorageServiceModuleProvider._();

final class StorageServiceModuleProvider
    extends $FunctionalProvider<StorageService, StorageService, StorageService>
    with $Provider<StorageService> {
  const StorageServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'storageServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$storageServiceModuleHash();

  @$internal
  @override
  $ProviderElement<StorageService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  StorageService create(Ref ref) {
    return storageServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(StorageService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<StorageService>(value),
    );
  }
}

String _$storageServiceModuleHash() =>
    r'02e4d7b1b98c01dc5dacfa6dd6a415948bd1027f';

@ProviderFor(syncEventDispatcherModule)
const syncEventDispatcherModuleProvider = SyncEventDispatcherModuleProvider._();

final class SyncEventDispatcherModuleProvider
    extends
        $FunctionalProvider<
          SyncEventDispatcher,
          SyncEventDispatcher,
          SyncEventDispatcher
        >
    with $Provider<SyncEventDispatcher> {
  const SyncEventDispatcherModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncEventDispatcherModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncEventDispatcherModuleHash();

  @$internal
  @override
  $ProviderElement<SyncEventDispatcher> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SyncEventDispatcher create(Ref ref) {
    return syncEventDispatcherModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SyncEventDispatcher value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SyncEventDispatcher>(value),
    );
  }
}

String _$syncEventDispatcherModuleHash() =>
    r'34b7ee4a7c0c3e843c6f70d2d91a010dc5e04d5c';

@ProviderFor(languageServiceModule)
const languageServiceModuleProvider = LanguageServiceModuleProvider._();

final class LanguageServiceModuleProvider
    extends
        $FunctionalProvider<LanguageService, LanguageService, LanguageService>
    with $Provider<LanguageService> {
  const LanguageServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'languageServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$languageServiceModuleHash();

  @$internal
  @override
  $ProviderElement<LanguageService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  LanguageService create(Ref ref) {
    return languageServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(LanguageService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<LanguageService>(value),
    );
  }
}

String _$languageServiceModuleHash() =>
    r'5a0b449f0c85acc7dcf213f13d8c61e5f6e17fde';

@ProviderFor(mqttOnlyRealTimeServiceModule)
const mqttOnlyRealTimeServiceModuleProvider =
    MqttOnlyRealTimeServiceModuleProvider._();

final class MqttOnlyRealTimeServiceModuleProvider
    extends
        $FunctionalProvider<
          MqttOnlyRealTimeService,
          MqttOnlyRealTimeService,
          MqttOnlyRealTimeService
        >
    with $Provider<MqttOnlyRealTimeService> {
  const MqttOnlyRealTimeServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mqttOnlyRealTimeServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mqttOnlyRealTimeServiceModuleHash();

  @$internal
  @override
  $ProviderElement<MqttOnlyRealTimeService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  MqttOnlyRealTimeService create(Ref ref) {
    return mqttOnlyRealTimeServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MqttOnlyRealTimeService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MqttOnlyRealTimeService>(value),
    );
  }
}

String _$mqttOnlyRealTimeServiceModuleHash() =>
    r'd201924e13ff550ee79b94c5ce830bace9dd4a6c';

@ProviderFor(webRtcServiceModule)
const webRtcServiceModuleProvider = WebRtcServiceModuleProvider._();

final class WebRtcServiceModuleProvider
    extends $FunctionalProvider<WebRTCService, WebRTCService, WebRTCService>
    with $Provider<WebRTCService> {
  const WebRtcServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'webRtcServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$webRtcServiceModuleHash();

  @$internal
  @override
  $ProviderElement<WebRTCService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  WebRTCService create(Ref ref) {
    return webRtcServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(WebRTCService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<WebRTCService>(value),
    );
  }
}

String _$webRtcServiceModuleHash() =>
    r'169fe3e64cbe6bf49c82a2497547c99af9398b24';

@ProviderFor(backendAuthServiceModule)
const backendAuthServiceModuleProvider = BackendAuthServiceModuleProvider._();

final class BackendAuthServiceModuleProvider
    extends
        $FunctionalProvider<
          BackendAuthService,
          BackendAuthService,
          BackendAuthService
        >
    with $Provider<BackendAuthService> {
  const BackendAuthServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'backendAuthServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$backendAuthServiceModuleHash();

  @$internal
  @override
  $ProviderElement<BackendAuthService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  BackendAuthService create(Ref ref) {
    return backendAuthServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BackendAuthService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BackendAuthService>(value),
    );
  }
}

String _$backendAuthServiceModuleHash() =>
    r'c9b847246038925f1fa39abc7ce5ed481dc2874b';

@ProviderFor(kratosAuthServiceModule)
const kratosAuthServiceModuleProvider = KratosAuthServiceModuleProvider._();

final class KratosAuthServiceModuleProvider
    extends
        $FunctionalProvider<
          KratosAuthService,
          KratosAuthService,
          KratosAuthService
        >
    with $Provider<KratosAuthService> {
  const KratosAuthServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'kratosAuthServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$kratosAuthServiceModuleHash();

  @$internal
  @override
  $ProviderElement<KratosAuthService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  KratosAuthService create(Ref ref) {
    return kratosAuthServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(KratosAuthService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<KratosAuthService>(value),
    );
  }
}

String _$kratosAuthServiceModuleHash() =>
    r'6ab647d3aae7bb3bb565f69c665912bca8cfdddc';

@ProviderFor(platformCallHandlerModule)
const platformCallHandlerModuleProvider = PlatformCallHandlerModuleProvider._();

final class PlatformCallHandlerModuleProvider
    extends
        $FunctionalProvider<
          PlatformCallHandler,
          PlatformCallHandler,
          PlatformCallHandler
        >
    with $Provider<PlatformCallHandler> {
  const PlatformCallHandlerModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'platformCallHandlerModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$platformCallHandlerModuleHash();

  @$internal
  @override
  $ProviderElement<PlatformCallHandler> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  PlatformCallHandler create(Ref ref) {
    return platformCallHandlerModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PlatformCallHandler value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<PlatformCallHandler>(value),
    );
  }
}

String _$platformCallHandlerModuleHash() =>
    r'e3c3ab6b35ebbdcef90eb4fddfea6423a1d35415';

@ProviderFor(mediaUploadServiceModule)
const mediaUploadServiceModuleProvider = MediaUploadServiceModuleProvider._();

final class MediaUploadServiceModuleProvider
    extends
        $FunctionalProvider<
          MediaUploadService,
          MediaUploadService,
          MediaUploadService
        >
    with $Provider<MediaUploadService> {
  const MediaUploadServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mediaUploadServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mediaUploadServiceModuleHash();

  @$internal
  @override
  $ProviderElement<MediaUploadService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  MediaUploadService create(Ref ref) {
    return mediaUploadServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MediaUploadService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MediaUploadService>(value),
    );
  }
}

String _$mediaUploadServiceModuleHash() =>
    r'ce97943efd204e396c955bd52ee9efea6c2f2afe';

@ProviderFor(conflictResolutionServiceModule)
const conflictResolutionServiceModuleProvider =
    ConflictResolutionServiceModuleProvider._();

final class ConflictResolutionServiceModuleProvider
    extends
        $FunctionalProvider<
          ConflictResolutionService,
          ConflictResolutionService,
          ConflictResolutionService
        >
    with $Provider<ConflictResolutionService> {
  const ConflictResolutionServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'conflictResolutionServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$conflictResolutionServiceModuleHash();

  @$internal
  @override
  $ProviderElement<ConflictResolutionService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ConflictResolutionService create(Ref ref) {
    return conflictResolutionServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ConflictResolutionService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ConflictResolutionService>(value),
    );
  }
}

String _$conflictResolutionServiceModuleHash() =>
    r'46e286bb77fc39a6716187012b6c4156743b5698';

@ProviderFor(localStorageServiceModule)
const localStorageServiceModuleProvider = LocalStorageServiceModuleProvider._();

final class LocalStorageServiceModuleProvider
    extends
        $FunctionalProvider<
          LocalStorageService,
          LocalStorageService,
          LocalStorageService
        >
    with $Provider<LocalStorageService> {
  const LocalStorageServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'localStorageServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$localStorageServiceModuleHash();

  @$internal
  @override
  $ProviderElement<LocalStorageService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  LocalStorageService create(Ref ref) {
    return localStorageServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(LocalStorageService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<LocalStorageService>(value),
    );
  }
}

String _$localStorageServiceModuleHash() =>
    r'e941af017c45734149b05869cbec08e74b44ffb4';

@ProviderFor(toastServiceModule)
const toastServiceModuleProvider = ToastServiceModuleProvider._();

final class ToastServiceModuleProvider
    extends $FunctionalProvider<ToastService, ToastService, ToastService>
    with $Provider<ToastService> {
  const ToastServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'toastServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$toastServiceModuleHash();

  @$internal
  @override
  $ProviderElement<ToastService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  ToastService create(Ref ref) {
    return toastServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ToastService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ToastService>(value),
    );
  }
}

String _$toastServiceModuleHash() =>
    r'e0fabc7c03d52ffe223ad9427c7f6c2f68dd0302';
