// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'usecase_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(mapUserModelsUseCaseModule)
const mapUserModelsUseCaseModuleProvider =
    MapUserModelsUseCaseModuleProvider._();

final class MapUserModelsUseCaseModuleProvider
    extends
        $FunctionalProvider<
          MapUserModelsUseCase,
          MapUserModelsUseCase,
          MapUserModelsUseCase
        >
    with $Provider<MapUserModelsUseCase> {
  const MapUserModelsUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mapUserModelsUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mapUserModelsUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<MapUserModelsUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  MapUserModelsUseCase create(Ref ref) {
    return mapUserModelsUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MapUserModelsUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MapUserModelsUseCase>(value),
    );
  }
}

String _$mapUserModelsUseCaseModuleHash() =>
    r'f3f37871053b50245de92b1cbeddc84890b18e70';

@ProviderFor(getUnifiedProfileDataUseCaseModule)
const getUnifiedProfileDataUseCaseModuleProvider =
    GetUnifiedProfileDataUseCaseModuleProvider._();

final class GetUnifiedProfileDataUseCaseModuleProvider
    extends
        $FunctionalProvider<
          GetUnifiedProfileDataUseCase,
          GetUnifiedProfileDataUseCase,
          GetUnifiedProfileDataUseCase
        >
    with $Provider<GetUnifiedProfileDataUseCase> {
  const GetUnifiedProfileDataUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'getUnifiedProfileDataUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$getUnifiedProfileDataUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<GetUnifiedProfileDataUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  GetUnifiedProfileDataUseCase create(Ref ref) {
    return getUnifiedProfileDataUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(GetUnifiedProfileDataUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<GetUnifiedProfileDataUseCase>(value),
    );
  }
}

String _$getUnifiedProfileDataUseCaseModuleHash() =>
    r'1df92b09020ed812ada43857e1ecc1a606d9cc9b';

@ProviderFor(syncInitialStateUseCaseModule)
const syncInitialStateUseCaseModuleProvider =
    SyncInitialStateUseCaseModuleProvider._();

final class SyncInitialStateUseCaseModuleProvider
    extends
        $FunctionalProvider<
          SyncInitialStateUseCase,
          SyncInitialStateUseCase,
          SyncInitialStateUseCase
        >
    with $Provider<SyncInitialStateUseCase> {
  const SyncInitialStateUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncInitialStateUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncInitialStateUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<SyncInitialStateUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SyncInitialStateUseCase create(Ref ref) {
    return syncInitialStateUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SyncInitialStateUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SyncInitialStateUseCase>(value),
    );
  }
}

String _$syncInitialStateUseCaseModuleHash() =>
    r'be1191f5be1e3fdc0c75bd17e405e26d0861dfd9';

@ProviderFor(sendContactRequestUseCaseModule)
const sendContactRequestUseCaseModuleProvider =
    SendContactRequestUseCaseModuleProvider._();

final class SendContactRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          SendContactRequestUseCase,
          SendContactRequestUseCase,
          SendContactRequestUseCase
        >
    with $Provider<SendContactRequestUseCase> {
  const SendContactRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'sendContactRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$sendContactRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<SendContactRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SendContactRequestUseCase create(Ref ref) {
    return sendContactRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SendContactRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SendContactRequestUseCase>(value),
    );
  }
}

String _$sendContactRequestUseCaseModuleHash() =>
    r'12cfffe6337467f537308f5fbca6b344d27f410b';

@ProviderFor(acceptContactRequestUseCaseModule)
const acceptContactRequestUseCaseModuleProvider =
    AcceptContactRequestUseCaseModuleProvider._();

final class AcceptContactRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          AcceptContactRequestUseCase,
          AcceptContactRequestUseCase,
          AcceptContactRequestUseCase
        >
    with $Provider<AcceptContactRequestUseCase> {
  const AcceptContactRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'acceptContactRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$acceptContactRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<AcceptContactRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AcceptContactRequestUseCase create(Ref ref) {
    return acceptContactRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AcceptContactRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AcceptContactRequestUseCase>(value),
    );
  }
}

String _$acceptContactRequestUseCaseModuleHash() =>
    r'fa0b937c32275e54c66539640d652cce97297371';

@ProviderFor(declineContactRequestUseCaseModule)
const declineContactRequestUseCaseModuleProvider =
    DeclineContactRequestUseCaseModuleProvider._();

final class DeclineContactRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          DeclineContactRequestUseCase,
          DeclineContactRequestUseCase,
          DeclineContactRequestUseCase
        >
    with $Provider<DeclineContactRequestUseCase> {
  const DeclineContactRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'declineContactRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$declineContactRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<DeclineContactRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  DeclineContactRequestUseCase create(Ref ref) {
    return declineContactRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DeclineContactRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DeclineContactRequestUseCase>(value),
    );
  }
}

String _$declineContactRequestUseCaseModuleHash() =>
    r'5fdabe061b5dce6a2af33b9e1f875ba000e3966e';

@ProviderFor(acceptFriendRequestUseCaseModule)
const acceptFriendRequestUseCaseModuleProvider =
    AcceptFriendRequestUseCaseModuleProvider._();

final class AcceptFriendRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          AcceptFriendRequestUseCase,
          AcceptFriendRequestUseCase,
          AcceptFriendRequestUseCase
        >
    with $Provider<AcceptFriendRequestUseCase> {
  const AcceptFriendRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'acceptFriendRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$acceptFriendRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<AcceptFriendRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AcceptFriendRequestUseCase create(Ref ref) {
    return acceptFriendRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AcceptFriendRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AcceptFriendRequestUseCase>(value),
    );
  }
}

String _$acceptFriendRequestUseCaseModuleHash() =>
    r'ea48a6da3918872efdd8cea1feae0a97357a12a4';

@ProviderFor(declineFriendRequestUseCaseModule)
const declineFriendRequestUseCaseModuleProvider =
    DeclineFriendRequestUseCaseModuleProvider._();

final class DeclineFriendRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          DeclineFriendRequestUseCase,
          DeclineFriendRequestUseCase,
          DeclineFriendRequestUseCase
        >
    with $Provider<DeclineFriendRequestUseCase> {
  const DeclineFriendRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'declineFriendRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$declineFriendRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<DeclineFriendRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  DeclineFriendRequestUseCase create(Ref ref) {
    return declineFriendRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DeclineFriendRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DeclineFriendRequestUseCase>(value),
    );
  }
}

String _$declineFriendRequestUseCaseModuleHash() =>
    r'af79668e3d3030825d83e472762bb40aebf43c22';

@ProviderFor(sendBubbleStartRequestUseCaseModule)
const sendBubbleStartRequestUseCaseModuleProvider =
    SendBubbleStartRequestUseCaseModuleProvider._();

final class SendBubbleStartRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          SendBubbleStartRequestUseCase,
          SendBubbleStartRequestUseCase,
          SendBubbleStartRequestUseCase
        >
    with $Provider<SendBubbleStartRequestUseCase> {
  const SendBubbleStartRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'sendBubbleStartRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$sendBubbleStartRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<SendBubbleStartRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SendBubbleStartRequestUseCase create(Ref ref) {
    return sendBubbleStartRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SendBubbleStartRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SendBubbleStartRequestUseCase>(
        value,
      ),
    );
  }
}

String _$sendBubbleStartRequestUseCaseModuleHash() =>
    r'46bcdbb1e276559f1c2881381ad115b9f1c1ec4b';

@ProviderFor(sendBubbleInviteRequestUseCaseModule)
const sendBubbleInviteRequestUseCaseModuleProvider =
    SendBubbleInviteRequestUseCaseModuleProvider._();

final class SendBubbleInviteRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          SendBubbleInviteRequestUseCase,
          SendBubbleInviteRequestUseCase,
          SendBubbleInviteRequestUseCase
        >
    with $Provider<SendBubbleInviteRequestUseCase> {
  const SendBubbleInviteRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'sendBubbleInviteRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$sendBubbleInviteRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<SendBubbleInviteRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  SendBubbleInviteRequestUseCase create(Ref ref) {
    return sendBubbleInviteRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SendBubbleInviteRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SendBubbleInviteRequestUseCase>(
        value,
      ),
    );
  }
}

String _$sendBubbleInviteRequestUseCaseModuleHash() =>
    r'511e4bbd38e4a68dcbc02c5d5003963007b7792a';

@ProviderFor(acceptBubbleRequestUseCaseModule)
const acceptBubbleRequestUseCaseModuleProvider =
    AcceptBubbleRequestUseCaseModuleProvider._();

final class AcceptBubbleRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          AcceptBubbleRequestUseCase,
          AcceptBubbleRequestUseCase,
          AcceptBubbleRequestUseCase
        >
    with $Provider<AcceptBubbleRequestUseCase> {
  const AcceptBubbleRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'acceptBubbleRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$acceptBubbleRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<AcceptBubbleRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AcceptBubbleRequestUseCase create(Ref ref) {
    return acceptBubbleRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AcceptBubbleRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AcceptBubbleRequestUseCase>(value),
    );
  }
}

String _$acceptBubbleRequestUseCaseModuleHash() =>
    r'865fd955bb98a19267d7c9f65995e96782eadcbe';

@ProviderFor(declineBubbleRequestUseCaseModule)
const declineBubbleRequestUseCaseModuleProvider =
    DeclineBubbleRequestUseCaseModuleProvider._();

final class DeclineBubbleRequestUseCaseModuleProvider
    extends
        $FunctionalProvider<
          DeclineBubbleRequestUseCase,
          DeclineBubbleRequestUseCase,
          DeclineBubbleRequestUseCase
        >
    with $Provider<DeclineBubbleRequestUseCase> {
  const DeclineBubbleRequestUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'declineBubbleRequestUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$declineBubbleRequestUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<DeclineBubbleRequestUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  DeclineBubbleRequestUseCase create(Ref ref) {
    return declineBubbleRequestUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DeclineBubbleRequestUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DeclineBubbleRequestUseCase>(value),
    );
  }
}

String _$declineBubbleRequestUseCaseModuleHash() =>
    r'ca70793c9ad0a2cf64a737ce6f1930e9cd9afb8f';

@ProviderFor(joinBubbleUseCaseModule)
const joinBubbleUseCaseModuleProvider = JoinBubbleUseCaseModuleProvider._();

final class JoinBubbleUseCaseModuleProvider
    extends
        $FunctionalProvider<
          JoinBubbleUseCase,
          JoinBubbleUseCase,
          JoinBubbleUseCase
        >
    with $Provider<JoinBubbleUseCase> {
  const JoinBubbleUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'joinBubbleUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$joinBubbleUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<JoinBubbleUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  JoinBubbleUseCase create(Ref ref) {
    return joinBubbleUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(JoinBubbleUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<JoinBubbleUseCase>(value),
    );
  }
}

String _$joinBubbleUseCaseModuleHash() =>
    r'9916d988dbadbb254113db55ae3f24060aa03ec3';

@ProviderFor(loadUserBubbleUseCaseModule)
const loadUserBubbleUseCaseModuleProvider =
    LoadUserBubbleUseCaseModuleProvider._();

final class LoadUserBubbleUseCaseModuleProvider
    extends
        $FunctionalProvider<
          LoadUserBubbleUseCase,
          LoadUserBubbleUseCase,
          LoadUserBubbleUseCase
        >
    with $Provider<LoadUserBubbleUseCase> {
  const LoadUserBubbleUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'loadUserBubbleUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$loadUserBubbleUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<LoadUserBubbleUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  LoadUserBubbleUseCase create(Ref ref) {
    return loadUserBubbleUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(LoadUserBubbleUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<LoadUserBubbleUseCase>(value),
    );
  }
}

String _$loadUserBubbleUseCaseModuleHash() =>
    r'7098efb638c4b5cb997d80d084ba4cb8a8230eae';

@ProviderFor(leaveBubbleUseCaseModule)
const leaveBubbleUseCaseModuleProvider = LeaveBubbleUseCaseModuleProvider._();

final class LeaveBubbleUseCaseModuleProvider
    extends
        $FunctionalProvider<
          LeaveBubbleUseCase,
          LeaveBubbleUseCase,
          LeaveBubbleUseCase
        >
    with $Provider<LeaveBubbleUseCase> {
  const LeaveBubbleUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'leaveBubbleUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$leaveBubbleUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<LeaveBubbleUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  LeaveBubbleUseCase create(Ref ref) {
    return leaveBubbleUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(LeaveBubbleUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<LeaveBubbleUseCase>(value),
    );
  }
}

String _$leaveBubbleUseCaseModuleHash() =>
    r'a6613c5244bef639fefa6cf80494e06b63c99244';

@ProviderFor(inviteUserToBubbleUseCaseModule)
const inviteUserToBubbleUseCaseModuleProvider =
    InviteUserToBubbleUseCaseModuleProvider._();

final class InviteUserToBubbleUseCaseModuleProvider
    extends
        $FunctionalProvider<
          InviteUserToBubbleUseCase,
          InviteUserToBubbleUseCase,
          InviteUserToBubbleUseCase
        >
    with $Provider<InviteUserToBubbleUseCase> {
  const InviteUserToBubbleUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'inviteUserToBubbleUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$inviteUserToBubbleUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<InviteUserToBubbleUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  InviteUserToBubbleUseCase create(Ref ref) {
    return inviteUserToBubbleUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(InviteUserToBubbleUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<InviteUserToBubbleUseCase>(value),
    );
  }
}

String _$inviteUserToBubbleUseCaseModuleHash() =>
    r'cb8c581d1a3d051f261be76a5a42d19e6931f968';

@ProviderFor(handleBubbleDissolutionUseCaseModule)
const handleBubbleDissolutionUseCaseModuleProvider =
    HandleBubbleDissolutionUseCaseModuleProvider._();

final class HandleBubbleDissolutionUseCaseModuleProvider
    extends
        $FunctionalProvider<
          HandleBubbleDissolutionUseCase,
          HandleBubbleDissolutionUseCase,
          HandleBubbleDissolutionUseCase
        >
    with $Provider<HandleBubbleDissolutionUseCase> {
  const HandleBubbleDissolutionUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'handleBubbleDissolutionUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$handleBubbleDissolutionUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<HandleBubbleDissolutionUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  HandleBubbleDissolutionUseCase create(Ref ref) {
    return handleBubbleDissolutionUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(HandleBubbleDissolutionUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<HandleBubbleDissolutionUseCase>(
        value,
      ),
    );
  }
}

String _$handleBubbleDissolutionUseCaseModuleHash() =>
    r'031247148ae132ad02e516b32e699740aceb1ad5';

@ProviderFor(handleBubbleExpiryUseCaseModule)
const handleBubbleExpiryUseCaseModuleProvider =
    HandleBubbleExpiryUseCaseModuleProvider._();

final class HandleBubbleExpiryUseCaseModuleProvider
    extends
        $FunctionalProvider<
          HandleBubbleExpiryUseCase,
          HandleBubbleExpiryUseCase,
          HandleBubbleExpiryUseCase
        >
    with $Provider<HandleBubbleExpiryUseCase> {
  const HandleBubbleExpiryUseCaseModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'handleBubbleExpiryUseCaseModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$handleBubbleExpiryUseCaseModuleHash();

  @$internal
  @override
  $ProviderElement<HandleBubbleExpiryUseCase> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  HandleBubbleExpiryUseCase create(Ref ref) {
    return handleBubbleExpiryUseCaseModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(HandleBubbleExpiryUseCase value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<HandleBubbleExpiryUseCase>(value),
    );
  }
}

String _$handleBubbleExpiryUseCaseModuleHash() =>
    r'6ab353bc4045624f98db50a63be3b86ab1b15e58';
