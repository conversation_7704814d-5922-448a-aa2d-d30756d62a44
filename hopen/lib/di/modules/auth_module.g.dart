// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(authRepositoryModule)
const authRepositoryModuleProvider = AuthRepositoryModuleProvider._();

final class AuthRepositoryModuleProvider
    extends $FunctionalProvider<AuthRepository, AuthRepository, AuthRepository>
    with $Provider<AuthRepository> {
  const AuthRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'authRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$authRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<AuthRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AuthRepository create(Ref ref) {
    return authRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AuthRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AuthRepository>(value),
    );
  }
}

String _$authRepositoryModuleHash() =>
    r'52cd0a2f967dd35909ce798299e414999602f034';

@ProviderFor(availabilityRepositoryModule)
const availabilityRepositoryModuleProvider =
    AvailabilityRepositoryModuleProvider._();

final class AvailabilityRepositoryModuleProvider
    extends
        $FunctionalProvider<
          AvailabilityRepository,
          AvailabilityRepository,
          AvailabilityRepository
        >
    with $Provider<AvailabilityRepository> {
  const AvailabilityRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availabilityRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availabilityRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<AvailabilityRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AvailabilityRepository create(Ref ref) {
    return availabilityRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AvailabilityRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AvailabilityRepository>(value),
    );
  }
}

String _$availabilityRepositoryModuleHash() =>
    r'24151b06c49be3c7bfd8c3b0190c710363043620';

@ProviderFor(availabilityServiceModule)
const availabilityServiceModuleProvider = AvailabilityServiceModuleProvider._();

final class AvailabilityServiceModuleProvider
    extends
        $FunctionalProvider<
          AvailabilityService,
          AvailabilityService,
          AvailabilityService
        >
    with $Provider<AvailabilityService> {
  const AvailabilityServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availabilityServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availabilityServiceModuleHash();

  @$internal
  @override
  $ProviderElement<AvailabilityService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  AvailabilityService create(Ref ref) {
    return availabilityServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AvailabilityService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AvailabilityService>(value),
    );
  }
}

String _$availabilityServiceModuleHash() =>
    r'9959bcd5a3188fe989d28bd99b5c346b9f6f1a5e';

@ProviderFor(syncRepositoryModule)
const syncRepositoryModuleProvider = SyncRepositoryModuleProvider._();

final class SyncRepositoryModuleProvider
    extends $FunctionalProvider<SyncRepository, SyncRepository, SyncRepository>
    with $Provider<SyncRepository> {
  const SyncRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<SyncRepository> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  SyncRepository create(Ref ref) {
    return syncRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(SyncRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<SyncRepository>(value),
    );
  }
}

String _$syncRepositoryModuleHash() =>
    r'5187ec07f15ca62fd747aaac2aa8338a564a778b';
