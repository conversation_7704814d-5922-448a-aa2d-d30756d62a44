// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(notificationRepositoryModule)
const notificationRepositoryModuleProvider =
    NotificationRepositoryModuleProvider._();

final class NotificationRepositoryModuleProvider
    extends
        $FunctionalProvider<
          NotificationRepository,
          NotificationRepository,
          NotificationRepository
        >
    with $Provider<NotificationRepository> {
  const NotificationRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<NotificationRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationRepository create(Ref ref) {
    return notificationRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationRepository>(value),
    );
  }
}

String _$notificationRepositoryModuleHash() =>
    r'6effdff8d4c13f0ffc91ee38f09c9ef356c9b539';

@ProviderFor(notificationPermissionRepositoryModule)
const notificationPermissionRepositoryModuleProvider =
    NotificationPermissionRepositoryModuleProvider._();

final class NotificationPermissionRepositoryModuleProvider
    extends
        $FunctionalProvider<
          NotificationPermissionRepository,
          NotificationPermissionRepository,
          NotificationPermissionRepository
        >
    with $Provider<NotificationPermissionRepository> {
  const NotificationPermissionRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationPermissionRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() =>
      _$notificationPermissionRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<NotificationPermissionRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationPermissionRepository create(Ref ref) {
    return notificationPermissionRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationPermissionRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationPermissionRepository>(
        value,
      ),
    );
  }
}

String _$notificationPermissionRepositoryModuleHash() =>
    r'1e552f2d47b7ed94391540f576f29ec4a90e208d';

@ProviderFor(firebaseMessagingModule)
const firebaseMessagingModuleProvider = FirebaseMessagingModuleProvider._();

final class FirebaseMessagingModuleProvider
    extends
        $FunctionalProvider<
          FirebaseMessaging,
          FirebaseMessaging,
          FirebaseMessaging
        >
    with $Provider<FirebaseMessaging> {
  const FirebaseMessagingModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'firebaseMessagingModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$firebaseMessagingModuleHash();

  @$internal
  @override
  $ProviderElement<FirebaseMessaging> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  FirebaseMessaging create(Ref ref) {
    return firebaseMessagingModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(FirebaseMessaging value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<FirebaseMessaging>(value),
    );
  }
}

String _$firebaseMessagingModuleHash() =>
    r'e0eab17ed01aefb40f90a49d7177ec2c12876eda';

@ProviderFor(flutterLocalNotificationsModule)
const flutterLocalNotificationsModuleProvider =
    FlutterLocalNotificationsModuleProvider._();

final class FlutterLocalNotificationsModuleProvider
    extends
        $FunctionalProvider<
          FlutterLocalNotificationsPlugin,
          FlutterLocalNotificationsPlugin,
          FlutterLocalNotificationsPlugin
        >
    with $Provider<FlutterLocalNotificationsPlugin> {
  const FlutterLocalNotificationsModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'flutterLocalNotificationsModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$flutterLocalNotificationsModuleHash();

  @$internal
  @override
  $ProviderElement<FlutterLocalNotificationsPlugin> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  FlutterLocalNotificationsPlugin create(Ref ref) {
    return flutterLocalNotificationsModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(FlutterLocalNotificationsPlugin value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<FlutterLocalNotificationsPlugin>(
        value,
      ),
    );
  }
}

String _$flutterLocalNotificationsModuleHash() =>
    r'7973e7f2a51bf6b213083ba34e170e87335f715a';

@ProviderFor(notificationServiceFCMModule)
const notificationServiceFCMModuleProvider =
    NotificationServiceFCMModuleProvider._();

final class NotificationServiceFCMModuleProvider
    extends
        $FunctionalProvider<
          NotificationServiceFCM,
          NotificationServiceFCM,
          NotificationServiceFCM
        >
    with $Provider<NotificationServiceFCM> {
  const NotificationServiceFCMModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationServiceFCMModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationServiceFCMModuleHash();

  @$internal
  @override
  $ProviderElement<NotificationServiceFCM> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationServiceFCM create(Ref ref) {
    return notificationServiceFCMModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationServiceFCM value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationServiceFCM>(value),
    );
  }
}

String _$notificationServiceFCMModuleHash() =>
    r'640cf94246bc3f940c73709153cf92f90a7887b2';

@ProviderFor(notificationServiceModule)
const notificationServiceModuleProvider = NotificationServiceModuleProvider._();

final class NotificationServiceModuleProvider
    extends
        $FunctionalProvider<
          NotificationService,
          NotificationService,
          NotificationService
        >
    with $Provider<NotificationService> {
  const NotificationServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationServiceModuleHash();

  @$internal
  @override
  $ProviderElement<NotificationService> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  NotificationService create(Ref ref) {
    return notificationServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NotificationService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NotificationService>(value),
    );
  }
}

String _$notificationServiceModuleHash() =>
    r'72ab91488cfaf2b8bb5568ed12724030291f2a8a';
