// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'presence_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(presenceRepositoryModule)
const presenceRepositoryModuleProvider = PresenceRepositoryModuleProvider._();

final class PresenceRepositoryModuleProvider
    extends
        $FunctionalProvider<
          PresenceRepository,
          PresenceRepository,
          PresenceRepository
        >
    with $Provider<PresenceRepository> {
  const PresenceRepositoryModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'presenceRepositoryModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$presenceRepositoryModuleHash();

  @$internal
  @override
  $ProviderElement<PresenceRepository> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  PresenceRepository create(Ref ref) {
    return presenceRepositoryModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PresenceRepository value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<PresenceRepository>(value),
    );
  }
}

String _$presenceRepositoryModuleHash() =>
    r'4b13f29133eb7a5db8aee3cfd698542c30ea8f86';

@ProviderFor(presenceServiceModule)
const presenceServiceModuleProvider = PresenceServiceModuleProvider._();

final class PresenceServiceModuleProvider
    extends
        $FunctionalProvider<PresenceService, PresenceService, PresenceService>
    with $Provider<PresenceService> {
  const PresenceServiceModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'presenceServiceModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$presenceServiceModuleHash();

  @$internal
  @override
  $ProviderElement<PresenceService> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  PresenceService create(Ref ref) {
    return presenceServiceModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(PresenceService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<PresenceService>(value),
    );
  }
}

String _$presenceServiceModuleHash() =>
    r'9b47939c97b78b8eae995d78b116d690b8f07d61';
