// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'integration_module.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning

@ProviderFor(mqttEventHandlerModule)
const mqttEventHandlerModuleProvider = MqttEventHandlerModuleProvider._();

final class MqttEventHandlerModuleProvider
    extends
        $FunctionalProvider<
          MqttEventHandlerImpl,
          MqttEventHandlerImpl,
          MqttEventHandlerImpl
        >
    with $Provider<MqttEventHandlerImpl> {
  const MqttEventHandlerModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mqttEventHandlerModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mqttEventHandlerModuleHash();

  @$internal
  @override
  $ProviderElement<MqttEventHandlerImpl> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  MqttEventHandlerImpl create(Ref ref) {
    return mqttEventHandlerModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MqttEventHandlerImpl value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MqttEventHandlerImpl>(value),
    );
  }
}

String _$mqttEventHandlerModuleHash() =>
    r'2b45f4ce05e6103b419beb6b2ef9779b70637304';

@ProviderFor(realTimeServiceManagerModule)
const realTimeServiceManagerModuleProvider =
    RealTimeServiceManagerModuleProvider._();

final class RealTimeServiceManagerModuleProvider
    extends
        $FunctionalProvider<
          RealTimeServiceManager,
          RealTimeServiceManager,
          RealTimeServiceManager
        >
    with $Provider<RealTimeServiceManager> {
  const RealTimeServiceManagerModuleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'realTimeServiceManagerModuleProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$realTimeServiceManagerModuleHash();

  @$internal
  @override
  $ProviderElement<RealTimeServiceManager> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  RealTimeServiceManager create(Ref ref) {
    return realTimeServiceManagerModule(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(RealTimeServiceManager value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<RealTimeServiceManager>(value),
    );
  }
}

String _$realTimeServiceManagerModuleHash() =>
    r'c6015185c4c3dd9bc7e0ebfebe61a9cfb78c97f4';
