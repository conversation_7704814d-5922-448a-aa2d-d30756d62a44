// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_event_listeners.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Event listener that refreshes controllers when sync completes
/// This decouples the sync controller from other controllers

@ProviderFor(SyncEventListenerController)
const syncEventListenerControllerProvider =
    SyncEventListenerControllerProvider._();

/// Event listener that refreshes controllers when sync completes
/// This decouples the sync controller from other controllers
final class SyncEventListenerControllerProvider
    extends $NotifierProvider<SyncEventListenerController, void> {
  /// Event listener that refreshes controllers when sync completes
  /// This decouples the sync controller from other controllers
  const SyncEventListenerControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncEventListenerControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncEventListenerControllerHash();

  @$internal
  @override
  SyncEventListenerController create() => SyncEventListenerController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(void value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<void>(value),
    );
  }
}

String _$syncEventListenerControllerHash() =>
    r'e2fe46f3bc58b4ec900bb97885e8ed08c8a89a59';

/// Event listener that refreshes controllers when sync completes
/// This decouples the sync controller from other controllers

abstract class _$SyncEventListenerController extends $Notifier<void> {
  void build();
  @$mustCallSuper
  @override
  void runBuild() {
    build();
    final ref = this.ref as $Ref<void, void>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<void, void>,
              void,
              Object?,
              Object?
            >;
    element.handleValue(ref, null);
  }
}

/// Provider to initialize the sync event listener
/// This should be watched by the app to ensure the listener is active

@ProviderFor(initializeSyncEventListener)
const initializeSyncEventListenerProvider =
    InitializeSyncEventListenerProvider._();

/// Provider to initialize the sync event listener
/// This should be watched by the app to ensure the listener is active

final class InitializeSyncEventListenerProvider
    extends $FunctionalProvider<void, void, void>
    with $Provider<void> {
  /// Provider to initialize the sync event listener
  /// This should be watched by the app to ensure the listener is active
  const InitializeSyncEventListenerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'initializeSyncEventListenerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$initializeSyncEventListenerHash();

  @$internal
  @override
  $ProviderElement<void> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  void create(Ref ref) {
    return initializeSyncEventListener(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(void value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<void>(value),
    );
  }
}

String _$initializeSyncEventListenerHash() =>
    r'c31de23eefdd22a498eb1e30e4cdd3e2a60fb2d5';
