// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nav_bar_visibility_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Riverpod 3.0 provider for navigation bar visibility state

@ProviderFor(NavBarVisibility)
const navBarVisibilityProvider = NavBarVisibilityProvider._();

/// Riverpod 3.0 provider for navigation bar visibility state
final class NavBarVisibilityProvider
    extends $NotifierProvider<NavBarVisibility, bool> {
  /// Riverpod 3.0 provider for navigation bar visibility state
  const NavBarVisibilityProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'navBarVisibilityProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$navBarVisibilityHash();

  @$internal
  @override
  NavBarVisibility create() => NavBarVisibility();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$navBarVisibilityHash() => r'fc79f36c47f8a6d2b28f87d6ffce5b7910328a62';

/// Riverpod 3.0 provider for navigation bar visibility state

abstract class _$NavBarVisibility extends $Notifier<bool> {
  bool build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<bool, bool>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<bool, bool>,
              bool,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}
