import '../models/bubble_entity.dart';
import '../usecases/map_bubble_status_usecase.dart';

/// Service for applying business logic to bubble status mapping
/// 
/// This service acts as a bridge between the data layer (which does simple mapping)
/// and the business layer (which applies business rules). It ensures that business
/// logic for status interpretation is consistently applied across the application.
class BubbleStatusService {
  BubbleStatusService({MapBubbleStatusUseCase? statusUseCase})
      : _statusUseCase = statusUseCase ?? MapBubbleStatusUseCase();

  final MapBubbleStatusUseCase _statusUseCase;

  /// Apply business logic to a bubble entity's status
  /// 
  /// This method takes a bubble entity that has been mapped from protobuf/drift
  /// and applies business rules to ensure the status reflects the intended UX.
  /// 
  /// For example:
  /// - CREATING bubbles appear as ACTIVE to users
  /// - DELETED bubbles appear as ARCHIVED for better UX
  /// - INACTIVE bubbles appear as ACTIVE during temporary issues
  BubbleEntity applyBusinessLogicToStatus(BubbleEntity entity, String? originalBackendStatus) {
    // If we don't have the original backend status, return as-is
    if (originalBackendStatus == null) {
      return entity;
    }

    // Apply business logic to determine the correct status
    final businessLogicStatus = _statusUseCase.mapFromBackendStatus(originalBackendStatus);
    
    // If the business logic status is different from the mapped status, update it
    if (businessLogicStatus != entity.status) {
      return entity.copyWith(status: businessLogicStatus);
    }

    return entity;
  }

  /// Apply business logic to a list of bubble entities
  List<BubbleEntity> applyBusinessLogicToStatusList(
    List<BubbleEntity> entities, 
    Map<String, String>? originalBackendStatuses,
  ) {
    if (originalBackendStatuses == null) {
      return entities;
    }

    return entities.map((entity) {
      final originalStatus = originalBackendStatuses[entity.id.value];
      return applyBusinessLogicToStatus(entity, originalStatus);
    }).toList();
  }

  /// Check if a bubble allows new interactions based on business rules
  bool allowsNewInteractions(BubbleEntity entity) => _statusUseCase.allowsNewInteractions(entity.status);

  /// Check if a bubble allows viewing history based on business rules
  bool allowsViewingHistory(BubbleEntity entity) => _statusUseCase.allowsViewingHistory(entity.status);

  /// Check if a bubble can transition to a new status
  bool canTransitionTo(BubbleEntity entity, BubbleLifecycleStatus newStatus) => _statusUseCase.canTransitionTo(entity.status, newStatus);

  /// Get user-friendly display text for a bubble status
  String getDisplayText(BubbleEntity entity) => _statusUseCase.getDisplayText(entity.status);

  /// Get user-friendly description for a bubble status
  String getDescription(BubbleEntity entity) => _statusUseCase.getDescription(entity.status);

  /// Get display priority for sorting bubbles
  int getDisplayPriority(BubbleEntity entity) => _statusUseCase.getDisplayPriority(entity.status);

  /// Check if a bubble should trigger notifications
  bool shouldTriggerNotifications(BubbleEntity entity) => _statusUseCase.shouldTriggerNotifications(entity.status);
}
