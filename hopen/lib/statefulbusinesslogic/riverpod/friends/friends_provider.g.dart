// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friends_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Friends controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(FriendsController)
const friendsControllerProvider = FriendsControllerProvider._();

/// Friends controller using Riverpod 3.0 AsyncNotifier pattern
final class FriendsControllerProvider
    extends $AsyncNotifierProvider<FriendsController, FriendsState> {
  /// Friends controller using Riverpod 3.0 AsyncNotifier pattern
  const FriendsControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'friendsControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$friendsControllerHash();

  @$internal
  @override
  FriendsController create() => FriendsController();
}

String _$friendsControllerHash() => r'e5d538a08eeeeaec5a8a2fb9d2ef6e1cca3a3bf5';

/// Friends controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$FriendsController extends $AsyncNotifier<FriendsState> {
  FutureOr<FriendsState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<FriendsState>, FriendsState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<FriendsState>, FriendsState>,
              AsyncValue<FriendsState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing friends state properties

@ProviderFor(hasFriends)
const hasFriendsProvider = HasFriendsProvider._();

/// Convenience providers for accessing friends state properties

final class HasFriendsProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing friends state properties
  const HasFriendsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasFriendsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasFriendsHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasFriends(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasFriendsHash() => r'88207902caeeb44c278296eaa00ee030a8a8ffa1';

@ProviderFor(allFriends)
const allFriendsProvider = AllFriendsProvider._();

final class AllFriendsProvider
    extends
        $FunctionalProvider<
          List<Map<String, dynamic>>,
          List<Map<String, dynamic>>,
          List<Map<String, dynamic>>
        >
    with $Provider<List<Map<String, dynamic>>> {
  const AllFriendsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'allFriendsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$allFriendsHash();

  @$internal
  @override
  $ProviderElement<List<Map<String, dynamic>>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<Map<String, dynamic>> create(Ref ref) {
    return allFriends(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<Map<String, dynamic>> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<Map<String, dynamic>>>(value),
    );
  }
}

String _$allFriendsHash() => r'4c7e1c0eef7b40d8f1379e882027e90e040d8bdb';

@ProviderFor(filteredFriends)
const filteredFriendsProvider = FilteredFriendsProvider._();

final class FilteredFriendsProvider
    extends
        $FunctionalProvider<
          List<Map<String, dynamic>>,
          List<Map<String, dynamic>>,
          List<Map<String, dynamic>>
        >
    with $Provider<List<Map<String, dynamic>>> {
  const FilteredFriendsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'filteredFriendsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$filteredFriendsHash();

  @$internal
  @override
  $ProviderElement<List<Map<String, dynamic>>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<Map<String, dynamic>> create(Ref ref) {
    return filteredFriends(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<Map<String, dynamic>> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<Map<String, dynamic>>>(value),
    );
  }
}

String _$filteredFriendsHash() => r'58ab56ffb061eb29f4416fe2ef9be437fd9c83d7';

@ProviderFor(onlineFriendsCount)
const onlineFriendsCountProvider = OnlineFriendsCountProvider._();

final class OnlineFriendsCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const OnlineFriendsCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'onlineFriendsCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$onlineFriendsCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return onlineFriendsCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$onlineFriendsCountHash() =>
    r'96c6676604d8b097957b0f45deadbc17248b566b';

@ProviderFor(totalFriendsCount)
const totalFriendsCountProvider = TotalFriendsCountProvider._();

final class TotalFriendsCountProvider extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const TotalFriendsCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'totalFriendsCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$totalFriendsCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return totalFriendsCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$totalFriendsCountHash() => r'f0106a44258f6547b161e0a3b4b8710feb953588';

@ProviderFor(isFriendsLoading)
const isFriendsLoadingProvider = IsFriendsLoadingProvider._();

final class IsFriendsLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsFriendsLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isFriendsLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isFriendsLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isFriendsLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isFriendsLoadingHash() => r'e372ae489d3001f7fea73f86ed385d2bccb0032a';

@ProviderFor(hasFriendsError)
const hasFriendsErrorProvider = HasFriendsErrorProvider._();

final class HasFriendsErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasFriendsErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasFriendsErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasFriendsErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasFriendsError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasFriendsErrorHash() => r'f9752ee4a7de7a0c582403b91086833afb4e7a93';

@ProviderFor(friendsError)
const friendsErrorProvider = FriendsErrorProvider._();

final class FriendsErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const FriendsErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'friendsErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$friendsErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return friendsError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$friendsErrorHash() => r'a53f1e64a064c7cc0fe209b65fe4a990c8b4bf6d';

@ProviderFor(currentFriendsSearchQuery)
const currentFriendsSearchQueryProvider = CurrentFriendsSearchQueryProvider._();

final class CurrentFriendsSearchQueryProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  const CurrentFriendsSearchQueryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentFriendsSearchQueryProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentFriendsSearchQueryHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return currentFriendsSearchQuery(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$currentFriendsSearchQueryHash() =>
    r'889d335e4bb5b3b5506bfbb1fa1c73ecdc237822';

@ProviderFor(currentFriendsSortOption)
const currentFriendsSortOptionProvider = CurrentFriendsSortOptionProvider._();

final class CurrentFriendsSortOptionProvider
    extends
        $FunctionalProvider<
          FriendSortOption,
          FriendSortOption,
          FriendSortOption
        >
    with $Provider<FriendSortOption> {
  const CurrentFriendsSortOptionProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentFriendsSortOptionProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentFriendsSortOptionHash();

  @$internal
  @override
  $ProviderElement<FriendSortOption> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  FriendSortOption create(Ref ref) {
    return currentFriendsSortOption(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(FriendSortOption value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<FriendSortOption>(value),
    );
  }
}

String _$currentFriendsSortOptionHash() =>
    r'86ec4725f15decbfb20c9b43014624cd6173e388';
