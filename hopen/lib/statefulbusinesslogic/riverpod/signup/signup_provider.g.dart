// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'signup_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// SignUp controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since signup is a temporary operation that should be cleaned up after completion

@ProviderFor(SignUpController)
const signUpControllerProvider = SignUpControllerProvider._();

/// SignUp controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since signup is a temporary operation that should be cleaned up after completion
final class SignUpControllerProvider
    extends $AsyncNotifierProvider<SignUpController, SignUpState> {
  /// SignUp controller using Riverpod 3.0 AsyncNotifier pattern
  /// Uses autoDispose since signup is a temporary operation that should be cleaned up after completion
  const SignUpControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'signUpControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$signUpControllerHash();

  @$internal
  @override
  SignUpController create() => SignUpController();
}

String _$signUpControllerHash() => r'6a4316444ed34d25f72361eba6d7ab2db6dae3dd';

/// SignUp controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since signup is a temporary operation that should be cleaned up after completion

abstract class _$SignUpController extends $AsyncNotifier<SignUpState> {
  FutureOr<SignUpState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<SignUpState>, SignUpState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<SignUpState>, SignUpState>,
              AsyncValue<SignUpState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing signup state properties

@ProviderFor(isSignUpLoading)
const isSignUpLoadingProvider = IsSignUpLoadingProvider._();

/// Convenience providers for accessing signup state properties

final class IsSignUpLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing signup state properties
  const IsSignUpLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSignUpLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSignUpLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSignUpLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSignUpLoadingHash() => r'dbabf18b7c15188539d01129dae42f07e8f9bab7';

@ProviderFor(isSignUpSuccess)
const isSignUpSuccessProvider = IsSignUpSuccessProvider._();

final class IsSignUpSuccessProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsSignUpSuccessProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSignUpSuccessProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSignUpSuccessHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSignUpSuccess(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSignUpSuccessHash() => r'9e270ac7a41903ab66662a578e7ac83dd2ca6083';

@ProviderFor(isSignUpPartialSuccess)
const isSignUpPartialSuccessProvider = IsSignUpPartialSuccessProvider._();

final class IsSignUpPartialSuccessProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsSignUpPartialSuccessProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSignUpPartialSuccessProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSignUpPartialSuccessHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSignUpPartialSuccess(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSignUpPartialSuccessHash() =>
    r'3a0bcde3e530ca86b96e71e89f163b30a27cd302';

@ProviderFor(hasSignUpError)
const hasSignUpErrorProvider = HasSignUpErrorProvider._();

final class HasSignUpErrorProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasSignUpErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasSignUpErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasSignUpErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasSignUpError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasSignUpErrorHash() => r'1fd10a93e00c4173f66462f01d2ef7d79d35887b';

@ProviderFor(signUpErrorMessage)
const signUpErrorMessageProvider = SignUpErrorMessageProvider._();

final class SignUpErrorMessageProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const SignUpErrorMessageProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'signUpErrorMessageProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$signUpErrorMessageHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return signUpErrorMessage(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$signUpErrorMessageHash() =>
    r'dfbcb3e7009ce9308d0f71b141239cf62f0a7c4c';

@ProviderFor(signUpUser)
const signUpUserProvider = SignUpUserProvider._();

final class SignUpUserProvider
    extends $FunctionalProvider<UserModel?, UserModel?, UserModel?>
    with $Provider<UserModel?> {
  const SignUpUserProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'signUpUserProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$signUpUserHash();

  @$internal
  @override
  $ProviderElement<UserModel?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  UserModel? create(Ref ref) {
    return signUpUser(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserModel? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserModel?>(value),
    );
  }
}

String _$signUpUserHash() => r'df3fd395bc3935bd2e41ce7ac7f8a5920d2357b1';

@ProviderFor(partialSuccessMessage)
const partialSuccessMessageProvider = PartialSuccessMessageProvider._();

final class PartialSuccessMessageProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const PartialSuccessMessageProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'partialSuccessMessageProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$partialSuccessMessageHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return partialSuccessMessage(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$partialSuccessMessageHash() =>
    r'e81f04ced0641a047740eea5681ba1fe2f9412df';
