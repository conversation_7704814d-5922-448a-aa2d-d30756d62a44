// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contacts_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Stream provider for presence updates

@ProviderFor(presenceUpdatesStream)
const presenceUpdatesStreamProvider = PresenceUpdatesStreamProvider._();

/// Stream provider for presence updates

final class PresenceUpdatesStreamProvider
    extends
        $FunctionalProvider<
          AsyncValue<Map<String, UserPresence>>,
          Map<String, UserPresence>,
          Stream<Map<String, UserPresence>>
        >
    with
        $FutureModifier<Map<String, UserPresence>>,
        $StreamProvider<Map<String, UserPresence>> {
  /// Stream provider for presence updates
  const PresenceUpdatesStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'presenceUpdatesStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$presenceUpdatesStreamHash();

  @$internal
  @override
  $StreamProviderElement<Map<String, UserPresence>> $createElement(
    $ProviderPointer pointer,
  ) => $StreamProviderElement(pointer);

  @override
  Stream<Map<String, UserPresence>> create(Ref ref) {
    return presenceUpdatesStream(ref);
  }
}

String _$presenceUpdatesStreamHash() =>
    r'1003e05d44e20d8a21f5e46c2080ceb848b53c4f';

/// Contacts controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(ContactsController)
const contactsControllerProvider = ContactsControllerProvider._();

/// Contacts controller using Riverpod 3.0 AsyncNotifier pattern
final class ContactsControllerProvider
    extends $AsyncNotifierProvider<ContactsController, ContactsState> {
  /// Contacts controller using Riverpod 3.0 AsyncNotifier pattern
  const ContactsControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'contactsControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$contactsControllerHash();

  @$internal
  @override
  ContactsController create() => ContactsController();
}

String _$contactsControllerHash() =>
    r'9f25eebd062fcaa0880a5e69e592fbde52e37647';

/// Contacts controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$ContactsController extends $AsyncNotifier<ContactsState> {
  FutureOr<ContactsState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<ContactsState>, ContactsState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<ContactsState>, ContactsState>,
              AsyncValue<ContactsState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing contacts state properties

@ProviderFor(hasContacts)
const hasContactsProvider = HasContactsProvider._();

/// Convenience providers for accessing contacts state properties

final class HasContactsProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing contacts state properties
  const HasContactsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasContactsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasContactsHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasContacts(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasContactsHash() => r'a42df4fbec2d7b16e34964c51dd44359357f0d69';

@ProviderFor(allContacts)
const allContactsProvider = AllContactsProvider._();

final class AllContactsProvider
    extends
        $FunctionalProvider<
          List<UserContact>,
          List<UserContact>,
          List<UserContact>
        >
    with $Provider<List<UserContact>> {
  const AllContactsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'allContactsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$allContactsHash();

  @$internal
  @override
  $ProviderElement<List<UserContact>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<UserContact> create(Ref ref) {
    return allContacts(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<UserContact> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<UserContact>>(value),
    );
  }
}

String _$allContactsHash() => r'98bdc63b6d120868816e7dadd7b6aeae7463e633';

@ProviderFor(filteredContacts)
const filteredContactsProvider = FilteredContactsProvider._();

final class FilteredContactsProvider
    extends
        $FunctionalProvider<
          List<UserContact>,
          List<UserContact>,
          List<UserContact>
        >
    with $Provider<List<UserContact>> {
  const FilteredContactsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'filteredContactsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$filteredContactsHash();

  @$internal
  @override
  $ProviderElement<List<UserContact>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<UserContact> create(Ref ref) {
    return filteredContacts(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<UserContact> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<UserContact>>(value),
    );
  }
}

String _$filteredContactsHash() => r'68fd9af072e7f41b1a4e322599edcfc4a80c6e30';

@ProviderFor(onlineContactsCount)
const onlineContactsCountProvider = OnlineContactsCountProvider._();

final class OnlineContactsCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const OnlineContactsCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'onlineContactsCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$onlineContactsCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return onlineContactsCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$onlineContactsCountHash() =>
    r'd97dc6a4e980eb41e1ace28d1ac5cc7fb99edf78';

@ProviderFor(totalContactsCount)
const totalContactsCountProvider = TotalContactsCountProvider._();

final class TotalContactsCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const TotalContactsCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'totalContactsCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$totalContactsCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return totalContactsCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$totalContactsCountHash() =>
    r'e514374e0674834571299aef00d6281c7e8833b3';

@ProviderFor(isContactsLoading)
const isContactsLoadingProvider = IsContactsLoadingProvider._();

final class IsContactsLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsContactsLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isContactsLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isContactsLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isContactsLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isContactsLoadingHash() => r'b7f434c73c87a3b8da061271607a63757ac16db1';

@ProviderFor(hasContactsError)
const hasContactsErrorProvider = HasContactsErrorProvider._();

final class HasContactsErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasContactsErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasContactsErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasContactsErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasContactsError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasContactsErrorHash() => r'8367950a1b09e2225c40286e594a62266b9ae8c6';

@ProviderFor(contactsError)
const contactsErrorProvider = ContactsErrorProvider._();

final class ContactsErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const ContactsErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'contactsErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$contactsErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return contactsError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$contactsErrorHash() => r'651a7d6779d718a9928344c5c6413b491f81e180';

@ProviderFor(currentSearchQuery)
const currentSearchQueryProvider = CurrentSearchQueryProvider._();

final class CurrentSearchQueryProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  const CurrentSearchQueryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentSearchQueryProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentSearchQueryHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return currentSearchQuery(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$currentSearchQueryHash() =>
    r'069711c6acaca842aee30cd79433555170782391';

@ProviderFor(currentFilterType)
const currentFilterTypeProvider = CurrentFilterTypeProvider._();

final class CurrentFilterTypeProvider
    extends
        $FunctionalProvider<
          ContactFilterType,
          ContactFilterType,
          ContactFilterType
        >
    with $Provider<ContactFilterType> {
  const CurrentFilterTypeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentFilterTypeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentFilterTypeHash();

  @$internal
  @override
  $ProviderElement<ContactFilterType> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ContactFilterType create(Ref ref) {
    return currentFilterType(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ContactFilterType value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ContactFilterType>(value),
    );
  }
}

String _$currentFilterTypeHash() => r'b70861c830633a46c59b09996fdc81899f380e66';

@ProviderFor(currentSortOption)
const currentSortOptionProvider = CurrentSortOptionProvider._();

final class CurrentSortOptionProvider
    extends
        $FunctionalProvider<
          ContactSortOption,
          ContactSortOption,
          ContactSortOption
        >
    with $Provider<ContactSortOption> {
  const CurrentSortOptionProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentSortOptionProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentSortOptionHash();

  @$internal
  @override
  $ProviderElement<ContactSortOption> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ContactSortOption create(Ref ref) {
    return currentSortOption(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ContactSortOption value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ContactSortOption>(value),
    );
  }
}

String _$currentSortOptionHash() => r'67e2a02c2009eb304b63425c7781643377478ab6';
