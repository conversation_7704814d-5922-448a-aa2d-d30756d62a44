// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bubble_countdown_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Bubble countdown controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since countdown is feature-specific and should be cleaned up when not in use

@ProviderFor(BubbleCountdownController)
const bubbleCountdownControllerProvider = BubbleCountdownControllerProvider._();

/// Bubble countdown controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since countdown is feature-specific and should be cleaned up when not in use
final class BubbleCountdownControllerProvider
    extends
        $AsyncNotifierProvider<
          BubbleCountdownController,
          BubbleCountdownState
        > {
  /// Bubble countdown controller using Riverpod 3.0 AsyncNotifier pattern
  /// Uses autoDispose since countdown is feature-specific and should be cleaned up when not in use
  const BubbleCountdownControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleCountdownControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleCountdownControllerHash();

  @$internal
  @override
  BubbleCountdownController create() => BubbleCountdownController();
}

String _$bubbleCountdownControllerHash() =>
    r'546e73b1d2e45f13d50ec495844133f992b87032';

/// Bubble countdown controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since countdown is feature-specific and should be cleaned up when not in use

abstract class _$BubbleCountdownController
    extends $AsyncNotifier<BubbleCountdownState> {
  FutureOr<BubbleCountdownState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<AsyncValue<BubbleCountdownState>, BubbleCountdownState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<BubbleCountdownState>,
                BubbleCountdownState
              >,
              AsyncValue<BubbleCountdownState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing bubble countdown state properties

@ProviderFor(isCountdownRunning)
const isCountdownRunningProvider = IsCountdownRunningProvider._();

/// Convenience providers for accessing bubble countdown state properties

final class IsCountdownRunningProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing bubble countdown state properties
  const IsCountdownRunningProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCountdownRunningProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCountdownRunningHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCountdownRunning(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCountdownRunningHash() =>
    r'6b2b91a751b3ff701b8f887344068eb688db9d80';

@ProviderFor(isCountdownPaused)
const isCountdownPausedProvider = IsCountdownPausedProvider._();

final class IsCountdownPausedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCountdownPausedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCountdownPausedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCountdownPausedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCountdownPaused(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCountdownPausedHash() => r'25d03b679a3a162d221363e66f314fb3bec34c8a';

@ProviderFor(isCountdownActivelyRunning)
const isCountdownActivelyRunningProvider =
    IsCountdownActivelyRunningProvider._();

final class IsCountdownActivelyRunningProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCountdownActivelyRunningProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCountdownActivelyRunningProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCountdownActivelyRunningHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCountdownActivelyRunning(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCountdownActivelyRunningHash() =>
    r'4141357ae9141e569ef36d300434be11cce4b089';

@ProviderFor(isCountdownFinished)
const isCountdownFinishedProvider = IsCountdownFinishedProvider._();

final class IsCountdownFinishedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCountdownFinishedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCountdownFinishedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCountdownFinishedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCountdownFinished(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCountdownFinishedHash() =>
    r'86a4b1bf9a84245f2bce0a589e8ca875bb98ce77';

@ProviderFor(remainingTime)
const remainingTimeProvider = RemainingTimeProvider._();

final class RemainingTimeProvider
    extends $FunctionalProvider<Duration?, Duration?, Duration?>
    with $Provider<Duration?> {
  const RemainingTimeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'remainingTimeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$remainingTimeHash();

  @$internal
  @override
  $ProviderElement<Duration?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Duration? create(Ref ref) {
    return remainingTime(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Duration? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Duration?>(value),
    );
  }
}

String _$remainingTimeHash() => r'ccfae7b9763de7d0a314a68d7ff0bc5232fe263f';

@ProviderFor(totalTime)
const totalTimeProvider = TotalTimeProvider._();

final class TotalTimeProvider
    extends $FunctionalProvider<Duration?, Duration?, Duration?>
    with $Provider<Duration?> {
  const TotalTimeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'totalTimeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$totalTimeHash();

  @$internal
  @override
  $ProviderElement<Duration?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Duration? create(Ref ref) {
    return totalTime(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Duration? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Duration?>(value),
    );
  }
}

String _$totalTimeHash() => r'c92c81485eb4db629b49554e5eb40fd4856c1ebc';

@ProviderFor(countdownProgress)
const countdownProgressProvider = CountdownProgressProvider._();

final class CountdownProgressProvider
    extends $FunctionalProvider<double, double, double>
    with $Provider<double> {
  const CountdownProgressProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'countdownProgressProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$countdownProgressHash();

  @$internal
  @override
  $ProviderElement<double> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  double create(Ref ref) {
    return countdownProgress(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(double value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<double>(value),
    );
  }
}

String _$countdownProgressHash() => r'c78eccf632b9b604ac678da3d999ae394547abe3';

@ProviderFor(formattedCountdownTime)
const formattedCountdownTimeProvider = FormattedCountdownTimeProvider._();

final class FormattedCountdownTimeProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  const FormattedCountdownTimeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'formattedCountdownTimeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$formattedCountdownTimeHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return formattedCountdownTime(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$formattedCountdownTimeHash() =>
    r'a482b70d5e78d687062979bc4f6e3c8697fe847c';

@ProviderFor(isCountdownAlmostFinished)
const isCountdownAlmostFinishedProvider = IsCountdownAlmostFinishedProvider._();

final class IsCountdownAlmostFinishedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCountdownAlmostFinishedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCountdownAlmostFinishedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCountdownAlmostFinishedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCountdownAlmostFinished(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCountdownAlmostFinishedHash() =>
    r'd4f869ee31dc783de5809392d4da6027d23768c0';

@ProviderFor(isCountdownInFinalSeconds)
const isCountdownInFinalSecondsProvider = IsCountdownInFinalSecondsProvider._();

final class IsCountdownInFinalSecondsProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCountdownInFinalSecondsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCountdownInFinalSecondsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCountdownInFinalSecondsHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCountdownInFinalSeconds(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCountdownInFinalSecondsHash() =>
    r'99a13ffa0d2171e37987949ab0d71ebd9061f102';

@ProviderFor(countdownFinishedAt)
const countdownFinishedAtProvider = CountdownFinishedAtProvider._();

final class CountdownFinishedAtProvider
    extends $FunctionalProvider<DateTime?, DateTime?, DateTime?>
    with $Provider<DateTime?> {
  const CountdownFinishedAtProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'countdownFinishedAtProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$countdownFinishedAtHash();

  @$internal
  @override
  $ProviderElement<DateTime?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  DateTime? create(Ref ref) {
    return countdownFinishedAt(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DateTime? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DateTime?>(value),
    );
  }
}

String _$countdownFinishedAtHash() =>
    r'de0289959841f53da7a88a17a1aa2e958ab1eb87';
