// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_recording_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Voice recording controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since voice recording is a temporary, session-specific operation

@ProviderFor(VoiceRecordingController)
const voiceRecordingControllerProvider = VoiceRecordingControllerProvider._();

/// Voice recording controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since voice recording is a temporary, session-specific operation
final class VoiceRecordingControllerProvider
    extends
        $AsyncNotifierProvider<VoiceRecordingController, VoiceRecordingState> {
  /// Voice recording controller using Riverpod 3.0 AsyncNotifier pattern
  /// Uses autoDispose since voice recording is a temporary, session-specific operation
  const VoiceRecordingControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'voiceRecordingControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$voiceRecordingControllerHash();

  @$internal
  @override
  VoiceRecordingController create() => VoiceRecordingController();
}

String _$voiceRecordingControllerHash() =>
    r'088aa2942c853369ef62f611dfc1d0a7f4c25d09';

/// Voice recording controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since voice recording is a temporary, session-specific operation

abstract class _$VoiceRecordingController
    extends $AsyncNotifier<VoiceRecordingState> {
  FutureOr<VoiceRecordingState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<VoiceRecordingState>, VoiceRecordingState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<VoiceRecordingState>, VoiceRecordingState>,
              AsyncValue<VoiceRecordingState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing voice recording state properties

@ProviderFor(isRecording)
const isRecordingProvider = IsRecordingProvider._();

/// Convenience providers for accessing voice recording state properties

final class IsRecordingProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing voice recording state properties
  const IsRecordingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isRecordingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isRecordingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isRecording(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isRecordingHash() => r'b104e63f8d1f648964307b8002f5d85616c9d4a7';

@ProviderFor(isRecordingPaused)
const isRecordingPausedProvider = IsRecordingPausedProvider._();

final class IsRecordingPausedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsRecordingPausedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isRecordingPausedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isRecordingPausedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isRecordingPaused(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isRecordingPausedHash() => r'f1c32cfd4a36459f7b7aee5722ef3dabd46f6f51';

@ProviderFor(isActivelyRecording)
const isActivelyRecordingProvider = IsActivelyRecordingProvider._();

final class IsActivelyRecordingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsActivelyRecordingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isActivelyRecordingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isActivelyRecordingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isActivelyRecording(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isActivelyRecordingHash() =>
    r'762629545e375a73ed5f0d695c323ee15d7d3ad2';

@ProviderFor(recordingDuration)
const recordingDurationProvider = RecordingDurationProvider._();

final class RecordingDurationProvider
    extends $FunctionalProvider<Duration, Duration, Duration>
    with $Provider<Duration> {
  const RecordingDurationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'recordingDurationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$recordingDurationHash();

  @$internal
  @override
  $ProviderElement<Duration> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Duration create(Ref ref) {
    return recordingDuration(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Duration value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Duration>(value),
    );
  }
}

String _$recordingDurationHash() => r'e5188d0eab29ef84eb2808424dd1639ccbe71aed';

@ProviderFor(formattedRecordingDuration)
const formattedRecordingDurationProvider =
    FormattedRecordingDurationProvider._();

final class FormattedRecordingDurationProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  const FormattedRecordingDurationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'formattedRecordingDurationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$formattedRecordingDurationHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return formattedRecordingDuration(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$formattedRecordingDurationHash() =>
    r'553ebab63febd458204c8bd0fcbcc36fc29a7540';

@ProviderFor(isRecordingCompleted)
const isRecordingCompletedProvider = IsRecordingCompletedProvider._();

final class IsRecordingCompletedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsRecordingCompletedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isRecordingCompletedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isRecordingCompletedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isRecordingCompleted(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isRecordingCompletedHash() =>
    r'7ed3c158f1e14ecada95e1e8ddaf527bbdde6ea1';

@ProviderFor(completedRecordingPath)
const completedRecordingPathProvider = CompletedRecordingPathProvider._();

final class CompletedRecordingPathProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CompletedRecordingPathProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'completedRecordingPathProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$completedRecordingPathHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return completedRecordingPath(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$completedRecordingPathHash() =>
    r'fb7e9c839df22b207cc184cca02647c6cd5dabdd';

@ProviderFor(completedRecordingDuration)
const completedRecordingDurationProvider =
    CompletedRecordingDurationProvider._();

final class CompletedRecordingDurationProvider
    extends $FunctionalProvider<Duration?, Duration?, Duration?>
    with $Provider<Duration?> {
  const CompletedRecordingDurationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'completedRecordingDurationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$completedRecordingDurationHash();

  @$internal
  @override
  $ProviderElement<Duration?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Duration? create(Ref ref) {
    return completedRecordingDuration(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Duration? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Duration?>(value),
    );
  }
}

String _$completedRecordingDurationHash() =>
    r'df3b4669c94f9b689548511d4506780c8cdae5d0';

@ProviderFor(hasRecordingError)
const hasRecordingErrorProvider = HasRecordingErrorProvider._();

final class HasRecordingErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasRecordingErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasRecordingErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasRecordingErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasRecordingError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasRecordingErrorHash() => r'2211e91180a3a20cbf4fac8d1a323392daf39cd8';

@ProviderFor(recordingError)
const recordingErrorProvider = RecordingErrorProvider._();

final class RecordingErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const RecordingErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'recordingErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$recordingErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return recordingError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$recordingErrorHash() => r'a6ce6aa98d461f582a6c8c29285cc64387deeb18';
