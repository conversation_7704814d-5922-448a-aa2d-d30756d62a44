// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_lifecycle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// App lifecycle controller using Riverpod 3.0 AsyncNotifier pattern
/// Manages service initialization and app lifecycle events

@ProviderFor(AppLifecycleController)
const appLifecycleControllerProvider = AppLifecycleControllerProvider._();

/// App lifecycle controller using Riverpod 3.0 AsyncNotifier pattern
/// Manages service initialization and app lifecycle events
final class AppLifecycleControllerProvider
    extends
        $AsyncNotifierProvider<
          AppLifecycleController,
          AppLifecycleProviderState
        > {
  /// App lifecycle controller using Riverpod 3.0 AsyncNotifier pattern
  /// Manages service initialization and app lifecycle events
  const AppLifecycleControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'appLifecycleControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$appLifecycleControllerHash();

  @$internal
  @override
  AppLifecycleController create() => AppLifecycleController();
}

String _$appLifecycleControllerHash() =>
    r'54ea1041bd51821b953f75816fcd996dca3238b3';

/// App lifecycle controller using Riverpod 3.0 AsyncNotifier pattern
/// Manages service initialization and app lifecycle events

abstract class _$AppLifecycleController
    extends $AsyncNotifier<AppLifecycleProviderState> {
  FutureOr<AppLifecycleProviderState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<
              AsyncValue<AppLifecycleProviderState>,
              AppLifecycleProviderState
            >;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<AppLifecycleProviderState>,
                AppLifecycleProviderState
              >,
              AsyncValue<AppLifecycleProviderState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing app lifecycle state properties

@ProviderFor(isAppInitializing)
const isAppInitializingProvider = IsAppInitializingProvider._();

/// Convenience providers for accessing app lifecycle state properties

final class IsAppInitializingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing app lifecycle state properties
  const IsAppInitializingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isAppInitializingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isAppInitializingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isAppInitializing(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isAppInitializingHash() => r'c794c4b96e2bd433d9fe9b00cdb57967a03c6015';

@ProviderFor(isAppInitialized)
const isAppInitializedProvider = IsAppInitializedProvider._();

final class IsAppInitializedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsAppInitializedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isAppInitializedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isAppInitializedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isAppInitialized(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isAppInitializedHash() => r'df628c3cf7ed7c37dc8d995d3b8982cfbd18bf68';

@ProviderFor(areAllServicesReady)
const areAllServicesReadyProvider = AreAllServicesReadyProvider._();

final class AreAllServicesReadyProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const AreAllServicesReadyProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'areAllServicesReadyProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$areAllServicesReadyHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return areAllServicesReady(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$areAllServicesReadyHash() =>
    r'1916d7d70a1483c047b69bb29ceabb5243e18652';

@ProviderFor(hasAppLifecycleError)
const hasAppLifecycleErrorProvider = HasAppLifecycleErrorProvider._();

final class HasAppLifecycleErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasAppLifecycleErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasAppLifecycleErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasAppLifecycleErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasAppLifecycleError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasAppLifecycleErrorHash() =>
    r'84ab0a0b06936d077f6fbdc45ad7e2179217e530';

@ProviderFor(appLifecycleError)
const appLifecycleErrorProvider = AppLifecycleErrorProvider._();

final class AppLifecycleErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const AppLifecycleErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'appLifecycleErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$appLifecycleErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return appLifecycleError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$appLifecycleErrorHash() => r'64cad004f3135d34f928f750859dc87c1db4035f';

/// App initializer provider - alias for app lifecycle controller
/// This provides compatibility with existing code that expects appInitializerProvider

@ProviderFor(appInitializer)
const appInitializerProvider = AppInitializerProvider._();

/// App initializer provider - alias for app lifecycle controller
/// This provides compatibility with existing code that expects appInitializerProvider

final class AppInitializerProvider
    extends
        $FunctionalProvider<
          AsyncValue<AppLifecycleProviderState>,
          AppLifecycleProviderState,
          FutureOr<AppLifecycleProviderState>
        >
    with
        $FutureModifier<AppLifecycleProviderState>,
        $FutureProvider<AppLifecycleProviderState> {
  /// App initializer provider - alias for app lifecycle controller
  /// This provides compatibility with existing code that expects appInitializerProvider
  const AppInitializerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'appInitializerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$appInitializerHash();

  @$internal
  @override
  $FutureProviderElement<AppLifecycleProviderState> $createElement(
    $ProviderPointer pointer,
  ) => $FutureProviderElement(pointer);

  @override
  FutureOr<AppLifecycleProviderState> create(Ref ref) {
    return appInitializer(ref);
  }
}

String _$appInitializerHash() => r'8c27d903e37d2cecda04119d596b1ae84a78ef4f';
