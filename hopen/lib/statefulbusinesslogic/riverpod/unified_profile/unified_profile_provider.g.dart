// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_profile_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Unified profile controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(UnifiedProfileController)
const unifiedProfileControllerProvider = UnifiedProfileControllerProvider._();

/// Unified profile controller using Riverpod 3.0 AsyncNotifier pattern
final class UnifiedProfileControllerProvider
    extends
        $AsyncNotifierProvider<UnifiedProfileController, UnifiedProfileState> {
  /// Unified profile controller using Riverpod 3.0 AsyncNotifier pattern
  const UnifiedProfileControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'unifiedProfileControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$unifiedProfileControllerHash();

  @$internal
  @override
  UnifiedProfileController create() => UnifiedProfileController();
}

String _$unifiedProfileControllerHash() =>
    r'1effb9562f187d1c0f451b43d7a1a43106e8425c';

/// Unified profile controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$UnifiedProfileController
    extends $AsyncNotifier<UnifiedProfileState> {
  FutureOr<UnifiedProfileState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<UnifiedProfileState>, UnifiedProfileState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<UnifiedProfileState>, UnifiedProfileState>,
              AsyncValue<UnifiedProfileState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing unified profile state properties

@ProviderFor(isUnifiedProfileLoading)
const isUnifiedProfileLoadingProvider = IsUnifiedProfileLoadingProvider._();

/// Convenience providers for accessing unified profile state properties

final class IsUnifiedProfileLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing unified profile state properties
  const IsUnifiedProfileLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isUnifiedProfileLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isUnifiedProfileLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isUnifiedProfileLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isUnifiedProfileLoadingHash() =>
    r'7b060755e11356edba826b2923d77b4f0200d8be';

@ProviderFor(isUnifiedProfileLoaded)
const isUnifiedProfileLoadedProvider = IsUnifiedProfileLoadedProvider._();

final class IsUnifiedProfileLoadedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsUnifiedProfileLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isUnifiedProfileLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isUnifiedProfileLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isUnifiedProfileLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isUnifiedProfileLoadedHash() =>
    r'50a06fa63fe780a1d043e922fcfddcf265daae93';

@ProviderFor(currentProfileUser)
const currentProfileUserProvider = CurrentProfileUserProvider._();

final class CurrentProfileUserProvider
    extends $FunctionalProvider<UserModel?, UserModel?, UserModel?>
    with $Provider<UserModel?> {
  const CurrentProfileUserProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentProfileUserProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentProfileUserHash();

  @$internal
  @override
  $ProviderElement<UserModel?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  UserModel? create(Ref ref) {
    return currentProfileUser(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserModel? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserModel?>(value),
    );
  }
}

String _$currentProfileUserHash() =>
    r'406a47fb75612aa95628e2bbd017034ecd290f52';

@ProviderFor(currentRelationshipType)
const currentRelationshipTypeProvider = CurrentRelationshipTypeProvider._();

final class CurrentRelationshipTypeProvider
    extends
        $FunctionalProvider<
          RelationshipType?,
          RelationshipType?,
          RelationshipType?
        >
    with $Provider<RelationshipType?> {
  const CurrentRelationshipTypeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentRelationshipTypeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentRelationshipTypeHash();

  @$internal
  @override
  $ProviderElement<RelationshipType?> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  RelationshipType? create(Ref ref) {
    return currentRelationshipType(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(RelationshipType? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<RelationshipType?>(value),
    );
  }
}

String _$currentRelationshipTypeHash() =>
    r'87f665a59984ac2b3d7470117cac0a0953ef06ab';

@ProviderFor(mutualFriends)
const mutualFriendsProvider = MutualFriendsProvider._();

final class MutualFriendsProvider
    extends
        $FunctionalProvider<List<UserModel>, List<UserModel>, List<UserModel>>
    with $Provider<List<UserModel>> {
  const MutualFriendsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mutualFriendsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mutualFriendsHash();

  @$internal
  @override
  $ProviderElement<List<UserModel>> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<UserModel> create(Ref ref) {
    return mutualFriends(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<UserModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<UserModel>>(value),
    );
  }
}

String _$mutualFriendsHash() => r'ca580288a0bcdd794fc2464bde7e0ea66a2f384a';

@ProviderFor(mutualContacts)
const mutualContactsProvider = MutualContactsProvider._();

final class MutualContactsProvider
    extends
        $FunctionalProvider<List<UserModel>, List<UserModel>, List<UserModel>>
    with $Provider<List<UserModel>> {
  const MutualContactsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mutualContactsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mutualContactsHash();

  @$internal
  @override
  $ProviderElement<List<UserModel>> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<UserModel> create(Ref ref) {
    return mutualContacts(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<UserModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<UserModel>>(value),
    );
  }
}

String _$mutualContactsHash() => r'a19a96c0646f6a733368ddee9214c66d3e9c833b';

@ProviderFor(bubbleMembers)
const bubbleMembersProvider = BubbleMembersProvider._();

final class BubbleMembersProvider
    extends
        $FunctionalProvider<
          List<BubbleMember>,
          List<BubbleMember>,
          List<BubbleMember>
        >
    with $Provider<List<BubbleMember>> {
  const BubbleMembersProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleMembersProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleMembersHash();

  @$internal
  @override
  $ProviderElement<List<BubbleMember>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<BubbleMember> create(Ref ref) {
    return bubbleMembers(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<BubbleMember> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<BubbleMember>>(value),
    );
  }
}

String _$bubbleMembersHash() => r'1c83311847280be3c0207a6c91bfeba6cc66b76f';

@ProviderFor(mutualFriendsCount)
const mutualFriendsCountProvider = MutualFriendsCountProvider._();

final class MutualFriendsCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const MutualFriendsCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mutualFriendsCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mutualFriendsCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return mutualFriendsCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$mutualFriendsCountHash() =>
    r'3620bb25f5c591a55db766fa8840c5d49a4860e1';

@ProviderFor(mutualContactsCount)
const mutualContactsCountProvider = MutualContactsCountProvider._();

final class MutualContactsCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const MutualContactsCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'mutualContactsCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$mutualContactsCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return mutualContactsCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$mutualContactsCountHash() =>
    r'aaedda685bd51582c69a645fe57641a29c91b417';

@ProviderFor(bubbleMembersCount)
const bubbleMembersCountProvider = BubbleMembersCountProvider._();

final class BubbleMembersCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const BubbleMembersCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleMembersCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleMembersCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return bubbleMembersCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$bubbleMembersCountHash() =>
    r'8145219d80abb99fb86f8ca479b5da1c09a0482e';

@ProviderFor(isProfileUserOnline)
const isProfileUserOnlineProvider = IsProfileUserOnlineProvider._();

final class IsProfileUserOnlineProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsProfileUserOnlineProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isProfileUserOnlineProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isProfileUserOnlineHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isProfileUserOnline(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isProfileUserOnlineHash() =>
    r'ca57408bce4f6a86e25b2c883f2dc4727b809359';

@ProviderFor(profilePageActionType)
const profilePageActionTypeProvider = ProfilePageActionTypeProvider._();

final class ProfilePageActionTypeProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const ProfilePageActionTypeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePageActionTypeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePageActionTypeHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return profilePageActionType(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$profilePageActionTypeHash() =>
    r'f3b6b0008812bea4f778ab40a423ef0b55af030c';

@ProviderFor(profilePageButtonText)
const profilePageButtonTextProvider = ProfilePageButtonTextProvider._();

final class ProfilePageButtonTextProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const ProfilePageButtonTextProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePageButtonTextProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePageButtonTextHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return profilePageButtonText(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$profilePageButtonTextHash() =>
    r'04db7cd20245310de6098e14965f53832fa4f65e';

@ProviderFor(isProfilePageButtonEnabled)
const isProfilePageButtonEnabledProvider =
    IsProfilePageButtonEnabledProvider._();

final class IsProfilePageButtonEnabledProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsProfilePageButtonEnabledProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isProfilePageButtonEnabledProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isProfilePageButtonEnabledHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isProfilePageButtonEnabled(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isProfilePageButtonEnabledHash() =>
    r'22ee2a247eaf406a8c657264bd38eabcdc3408a4';

@ProviderFor(hasUnifiedProfileError)
const hasUnifiedProfileErrorProvider = HasUnifiedProfileErrorProvider._();

final class HasUnifiedProfileErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasUnifiedProfileErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasUnifiedProfileErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasUnifiedProfileErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasUnifiedProfileError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasUnifiedProfileErrorHash() =>
    r'1a73a835cc3f968dc498552be0a0a1515e77aab8';

@ProviderFor(unifiedProfileError)
const unifiedProfileErrorProvider = UnifiedProfileErrorProvider._();

final class UnifiedProfileErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const UnifiedProfileErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'unifiedProfileErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$unifiedProfileErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return unifiedProfileError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$unifiedProfileErrorHash() =>
    r'06815f7f318202e519e0739bcdfb820fd4be7039';

@ProviderFor(unifiedProfileSuccessMessage)
const unifiedProfileSuccessMessageProvider =
    UnifiedProfileSuccessMessageProvider._();

final class UnifiedProfileSuccessMessageProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const UnifiedProfileSuccessMessageProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'unifiedProfileSuccessMessageProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$unifiedProfileSuccessMessageHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return unifiedProfileSuccessMessage(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$unifiedProfileSuccessMessageHash() =>
    r'628a856b8741a78f19d7248b198e93845be65e51';
