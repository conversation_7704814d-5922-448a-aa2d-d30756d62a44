// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_permission_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Notification permission controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since permission checks are temporary operations

@ProviderFor(NotificationPermissionController)
const notificationPermissionControllerProvider =
    NotificationPermissionControllerProvider._();

/// Notification permission controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since permission checks are temporary operations
final class NotificationPermissionControllerProvider
    extends
        $AsyncNotifierProvider<
          NotificationPermissionController,
          NotificationPermissionState
        > {
  /// Notification permission controller using Riverpod 3.0 AsyncNotifier pattern
  /// Uses autoDispose since permission checks are temporary operations
  const NotificationPermissionControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationPermissionControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationPermissionControllerHash();

  @$internal
  @override
  NotificationPermissionController create() =>
      NotificationPermissionController();
}

String _$notificationPermissionControllerHash() =>
    r'f2a908fe7771148913c2b59ab8a9fc28b88c5c73';

/// Notification permission controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since permission checks are temporary operations

abstract class _$NotificationPermissionController
    extends $AsyncNotifier<NotificationPermissionState> {
  FutureOr<NotificationPermissionState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<
              AsyncValue<NotificationPermissionState>,
              NotificationPermissionState
            >;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<NotificationPermissionState>,
                NotificationPermissionState
              >,
              AsyncValue<NotificationPermissionState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing notification permission state properties

@ProviderFor(isPermissionRequesting)
const isPermissionRequestingProvider = IsPermissionRequestingProvider._();

/// Convenience providers for accessing notification permission state properties

final class IsPermissionRequestingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing notification permission state properties
  const IsPermissionRequestingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isPermissionRequestingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isPermissionRequestingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isPermissionRequesting(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isPermissionRequestingHash() =>
    r'befddd2fdafa98605eb64971732fdeebdc0f6947';

@ProviderFor(isPermissionGranted)
const isPermissionGrantedProvider = IsPermissionGrantedProvider._();

final class IsPermissionGrantedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsPermissionGrantedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isPermissionGrantedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isPermissionGrantedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isPermissionGranted(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isPermissionGrantedHash() =>
    r'd88fc742294d2696f1ef6af942f57caf0341282e';

@ProviderFor(isPermissionDenied)
const isPermissionDeniedProvider = IsPermissionDeniedProvider._();

final class IsPermissionDeniedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsPermissionDeniedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isPermissionDeniedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isPermissionDeniedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isPermissionDenied(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isPermissionDeniedHash() =>
    r'ea65d57c873481e2f87066e0a8b7efdd09a8bb0e';

@ProviderFor(hasPermissionError)
const hasPermissionErrorProvider = HasPermissionErrorProvider._();

final class HasPermissionErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasPermissionErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasPermissionErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasPermissionErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasPermissionError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasPermissionErrorHash() =>
    r'ff61b9c441f16c7935682ae942945393a1f95a0d';

@ProviderFor(permissionError)
const permissionErrorProvider = PermissionErrorProvider._();

final class PermissionErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const PermissionErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'permissionErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$permissionErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return permissionError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$permissionErrorHash() => r'c25d15e1158e538b6c72821b63c1e22936291415';

@ProviderFor(isPermissionInitial)
const isPermissionInitialProvider = IsPermissionInitialProvider._();

final class IsPermissionInitialProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsPermissionInitialProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isPermissionInitialProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isPermissionInitialHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isPermissionInitial(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isPermissionInitialHash() =>
    r'18c5203e1dcffb1ad4ad5094fc67d608676220d1';
