// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_management_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Request management controller using Riverpod 3.0 AsyncNotifier pattern
///
/// This controller orchestrates contact requests, friend requests, and bubble requests
/// according to the app principles:
/// - Contact requests are manual
/// - Friend requests are auto-generated from bubble expiry
/// - Bubble requests include start, invite, join, and kickout

@ProviderFor(RequestManagementController)
const requestManagementControllerProvider =
    RequestManagementControllerProvider._();

/// Request management controller using Riverpod 3.0 AsyncNotifier pattern
///
/// This controller orchestrates contact requests, friend requests, and bubble requests
/// according to the app principles:
/// - Contact requests are manual
/// - Friend requests are auto-generated from bubble expiry
/// - Bubble requests include start, invite, join, and kickout
final class RequestManagementControllerProvider
    extends
        $AsyncNotifierProvider<
          RequestManagementController,
          RequestManagementState
        > {
  /// Request management controller using Riverpod 3.0 AsyncNotifier pattern
  ///
  /// This controller orchestrates contact requests, friend requests, and bubble requests
  /// according to the app principles:
  /// - Contact requests are manual
  /// - Friend requests are auto-generated from bubble expiry
  /// - Bubble requests include start, invite, join, and kickout
  const RequestManagementControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'requestManagementControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$requestManagementControllerHash();

  @$internal
  @override
  RequestManagementController create() => RequestManagementController();
}

String _$requestManagementControllerHash() =>
    r'25dbf2d244b461f92ffcccfc9e51614bf08bdc11';

/// Request management controller using Riverpod 3.0 AsyncNotifier pattern
///
/// This controller orchestrates contact requests, friend requests, and bubble requests
/// according to the app principles:
/// - Contact requests are manual
/// - Friend requests are auto-generated from bubble expiry
/// - Bubble requests include start, invite, join, and kickout

abstract class _$RequestManagementController
    extends $AsyncNotifier<RequestManagementState> {
  FutureOr<RequestManagementState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<AsyncValue<RequestManagementState>, RequestManagementState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<RequestManagementState>,
                RequestManagementState
              >,
              AsyncValue<RequestManagementState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing request management state properties

@ProviderFor(hasAnyPendingRequests)
const hasAnyPendingRequestsProvider = HasAnyPendingRequestsProvider._();

/// Convenience providers for accessing request management state properties

final class HasAnyPendingRequestsProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing request management state properties
  const HasAnyPendingRequestsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasAnyPendingRequestsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasAnyPendingRequestsHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasAnyPendingRequests(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasAnyPendingRequestsHash() =>
    r'221ad4564dae94b8f38933bbf8f01fdd9b2ca805';

@ProviderFor(totalPendingRequestsCount)
const totalPendingRequestsCountProvider = TotalPendingRequestsCountProvider._();

final class TotalPendingRequestsCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const TotalPendingRequestsCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'totalPendingRequestsCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$totalPendingRequestsCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return totalPendingRequestsCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$totalPendingRequestsCountHash() =>
    r'a983268652d326371cfbb02051ae575f733dbfac';

@ProviderFor(receivedContactRequests)
const receivedContactRequestsProvider = ReceivedContactRequestsProvider._();

final class ReceivedContactRequestsProvider
    extends
        $FunctionalProvider<
          List<ContactRequestModel>,
          List<ContactRequestModel>,
          List<ContactRequestModel>
        >
    with $Provider<List<ContactRequestModel>> {
  const ReceivedContactRequestsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'receivedContactRequestsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$receivedContactRequestsHash();

  @$internal
  @override
  $ProviderElement<List<ContactRequestModel>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<ContactRequestModel> create(Ref ref) {
    return receivedContactRequests(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<ContactRequestModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<ContactRequestModel>>(value),
    );
  }
}

String _$receivedContactRequestsHash() =>
    r'22464ebd67b6a5c3e3f0be3d02c50554cd887a78';

@ProviderFor(sentContactRequests)
const sentContactRequestsProvider = SentContactRequestsProvider._();

final class SentContactRequestsProvider
    extends
        $FunctionalProvider<
          List<ContactRequestModel>,
          List<ContactRequestModel>,
          List<ContactRequestModel>
        >
    with $Provider<List<ContactRequestModel>> {
  const SentContactRequestsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'sentContactRequestsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$sentContactRequestsHash();

  @$internal
  @override
  $ProviderElement<List<ContactRequestModel>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<ContactRequestModel> create(Ref ref) {
    return sentContactRequests(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<ContactRequestModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<ContactRequestModel>>(value),
    );
  }
}

String _$sentContactRequestsHash() =>
    r'299a73064e79c22d63098682fa61af28bb7e2c65';

@ProviderFor(receivedFriendRequests)
const receivedFriendRequestsProvider = ReceivedFriendRequestsProvider._();

final class ReceivedFriendRequestsProvider
    extends
        $FunctionalProvider<
          List<FriendRequestModel>,
          List<FriendRequestModel>,
          List<FriendRequestModel>
        >
    with $Provider<List<FriendRequestModel>> {
  const ReceivedFriendRequestsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'receivedFriendRequestsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$receivedFriendRequestsHash();

  @$internal
  @override
  $ProviderElement<List<FriendRequestModel>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<FriendRequestModel> create(Ref ref) {
    return receivedFriendRequests(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<FriendRequestModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<FriendRequestModel>>(value),
    );
  }
}

String _$receivedFriendRequestsHash() =>
    r'c2249d7cf180b4d5f7c9a629e1d9e3eed00a3ae3';

@ProviderFor(receivedBubbleRequests)
const receivedBubbleRequestsProvider = ReceivedBubbleRequestsProvider._();

final class ReceivedBubbleRequestsProvider
    extends
        $FunctionalProvider<
          List<BubbleRequestModel>,
          List<BubbleRequestModel>,
          List<BubbleRequestModel>
        >
    with $Provider<List<BubbleRequestModel>> {
  const ReceivedBubbleRequestsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'receivedBubbleRequestsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$receivedBubbleRequestsHash();

  @$internal
  @override
  $ProviderElement<List<BubbleRequestModel>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<BubbleRequestModel> create(Ref ref) {
    return receivedBubbleRequests(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<BubbleRequestModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<BubbleRequestModel>>(value),
    );
  }
}

String _$receivedBubbleRequestsHash() =>
    r'8d28e8edc0ddb27214e67feabf9fdb16b444489c';

@ProviderFor(sentBubbleRequests)
const sentBubbleRequestsProvider = SentBubbleRequestsProvider._();

final class SentBubbleRequestsProvider
    extends
        $FunctionalProvider<
          List<BubbleRequestModel>,
          List<BubbleRequestModel>,
          List<BubbleRequestModel>
        >
    with $Provider<List<BubbleRequestModel>> {
  const SentBubbleRequestsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'sentBubbleRequestsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$sentBubbleRequestsHash();

  @$internal
  @override
  $ProviderElement<List<BubbleRequestModel>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<BubbleRequestModel> create(Ref ref) {
    return sentBubbleRequests(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<BubbleRequestModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<BubbleRequestModel>>(value),
    );
  }
}

String _$sentBubbleRequestsHash() =>
    r'398b9eea635f1bee5e8aebcf4f01c0ab80024b4f';

@ProviderFor(sentFriendRequests)
const sentFriendRequestsProvider = SentFriendRequestsProvider._();

final class SentFriendRequestsProvider
    extends
        $FunctionalProvider<
          List<FriendRequestModel>,
          List<FriendRequestModel>,
          List<FriendRequestModel>
        >
    with $Provider<List<FriendRequestModel>> {
  const SentFriendRequestsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'sentFriendRequestsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$sentFriendRequestsHash();

  @$internal
  @override
  $ProviderElement<List<FriendRequestModel>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<FriendRequestModel> create(Ref ref) {
    return sentFriendRequests(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<FriendRequestModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<FriendRequestModel>>(value),
    );
  }
}

String _$sentFriendRequestsHash() =>
    r'764ef6d4d618d14f8af66a4025dc695a5f290a4a';

@ProviderFor(isRequestManagementLoading)
const isRequestManagementLoadingProvider =
    IsRequestManagementLoadingProvider._();

final class IsRequestManagementLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsRequestManagementLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isRequestManagementLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isRequestManagementLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isRequestManagementLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isRequestManagementLoadingHash() =>
    r'ef3329043f9d5713d7611130bb7229f18575a518';

@ProviderFor(hasRequestManagementError)
const hasRequestManagementErrorProvider = HasRequestManagementErrorProvider._();

final class HasRequestManagementErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasRequestManagementErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasRequestManagementErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasRequestManagementErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasRequestManagementError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasRequestManagementErrorHash() =>
    r'8f0288db53e2a478b1452494ab95c8060da177e4';

@ProviderFor(requestManagementError)
const requestManagementErrorProvider = RequestManagementErrorProvider._();

final class RequestManagementErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const RequestManagementErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'requestManagementErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$requestManagementErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return requestManagementError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$requestManagementErrorHash() =>
    r'd65eaea0be924eebce0f5133043e39efd20cd059';

@ProviderFor(currentRequestOperation)
const currentRequestOperationProvider = CurrentRequestOperationProvider._();

final class CurrentRequestOperationProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentRequestOperationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentRequestOperationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentRequestOperationHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentRequestOperation(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentRequestOperationHash() =>
    r'49a958c143ac2ae085368da085098c69a13ea9f9';
