// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Current user profile notifier - manages the authenticated user's profile
/// This is the primary provider for the viewing user's session and profile management

@ProviderFor(CurrentUserProfile)
const currentUserProfileProvider = CurrentUserProfileProvider._();

/// Current user profile notifier - manages the authenticated user's profile
/// This is the primary provider for the viewing user's session and profile management
final class CurrentUserProfileProvider
    extends $AsyncNotifierProvider<CurrentUserProfile, UserModel?> {
  /// Current user profile notifier - manages the authenticated user's profile
  /// This is the primary provider for the viewing user's session and profile management
  const CurrentUserProfileProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentUserProfileProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentUserProfileHash();

  @$internal
  @override
  CurrentUserProfile create() => CurrentUserProfile();
}

String _$currentUserProfileHash() =>
    r'880f15f65f5db650a9952bd474cf7a7213db5f54';

/// Current user profile notifier - manages the authenticated user's profile
/// This is the primary provider for the viewing user's session and profile management

abstract class _$CurrentUserProfile extends $AsyncNotifier<UserModel?> {
  FutureOr<UserModel?> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<UserModel?>, UserModel?>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<UserModel?>, UserModel?>,
              AsyncValue<UserModel?>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// User profile viewer notifier - for viewing any user's profile (unified profile pages)
/// This provider is used when viewing other users' profiles or specific user profiles by ID

@ProviderFor(UserProfileViewer)
const userProfileViewerProvider = UserProfileViewerProvider._();

/// User profile viewer notifier - for viewing any user's profile (unified profile pages)
/// This provider is used when viewing other users' profiles or specific user profiles by ID
final class UserProfileViewerProvider
    extends $AsyncNotifierProvider<UserProfileViewer, UserModel?> {
  /// User profile viewer notifier - for viewing any user's profile (unified profile pages)
  /// This provider is used when viewing other users' profiles or specific user profiles by ID
  const UserProfileViewerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'userProfileViewerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$userProfileViewerHash();

  @$internal
  @override
  UserProfileViewer create() => UserProfileViewer();
}

String _$userProfileViewerHash() => r'18cee90021ab23e2a619d852acaac73a4d1c4793';

/// User profile viewer notifier - for viewing any user's profile (unified profile pages)
/// This provider is used when viewing other users' profiles or specific user profiles by ID

abstract class _$UserProfileViewer extends $AsyncNotifier<UserModel?> {
  FutureOr<UserModel?> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<UserModel?>, UserModel?>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<UserModel?>, UserModel?>,
              AsyncValue<UserModel?>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for easy access to user profile data
/// Provider for the current authenticated user's profile

@ProviderFor(currentUser)
const currentUserProvider = CurrentUserProvider._();

/// Convenience providers for easy access to user profile data
/// Provider for the current authenticated user's profile

final class CurrentUserProvider
    extends $FunctionalProvider<UserModel?, UserModel?, UserModel?>
    with $Provider<UserModel?> {
  /// Convenience providers for easy access to user profile data
  /// Provider for the current authenticated user's profile
  const CurrentUserProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentUserProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentUserHash();

  @$internal
  @override
  $ProviderElement<UserModel?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  UserModel? create(Ref ref) {
    return currentUser(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(UserModel? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<UserModel?>(value),
    );
  }
}

String _$currentUserHash() => r'f1ccf8f0a68ed323715bfc43855cb224014cef62';

/// Provider for the current user's ID

@ProviderFor(currentUserId)
const currentUserIdProvider = CurrentUserIdProvider._();

/// Provider for the current user's ID

final class CurrentUserIdProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  /// Provider for the current user's ID
  const CurrentUserIdProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentUserIdProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentUserIdHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentUserId(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentUserIdHash() => r'2f80883052a8fde0a565239298253e09b9b14479';

/// Provider to check if user is authenticated

@ProviderFor(isAuthenticated)
const isAuthenticatedProvider = IsAuthenticatedProvider._();

/// Provider to check if user is authenticated

final class IsAuthenticatedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Provider to check if user is authenticated
  const IsAuthenticatedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isAuthenticatedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isAuthenticatedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isAuthenticated(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isAuthenticatedHash() => r'ec341d95b490bda54e8278477e26f7b345844931';

/// Provider for current user's display name

@ProviderFor(currentUserDisplayName)
const currentUserDisplayNameProvider = CurrentUserDisplayNameProvider._();

/// Provider for current user's display name

final class CurrentUserDisplayNameProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  /// Provider for current user's display name
  const CurrentUserDisplayNameProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentUserDisplayNameProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentUserDisplayNameHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return currentUserDisplayName(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$currentUserDisplayNameHash() =>
    r'a1417e492b9fba10064c2894f9c1a7e5397de67f';

/// Provider to check if current user's profile is complete

@ProviderFor(isCurrentUserProfileComplete)
const isCurrentUserProfileCompleteProvider =
    IsCurrentUserProfileCompleteProvider._();

/// Provider to check if current user's profile is complete

final class IsCurrentUserProfileCompleteProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Provider to check if current user's profile is complete
  const IsCurrentUserProfileCompleteProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCurrentUserProfileCompleteProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCurrentUserProfileCompleteHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCurrentUserProfileComplete(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCurrentUserProfileCompleteHash() =>
    r'07382fe331f7375d5f8e772280743fc6d5e7232f';

/// Family provider for viewing any user's profile by ID

@ProviderFor(userProfile)
const userProfileProvider = UserProfileFamily._();

/// Family provider for viewing any user's profile by ID

final class UserProfileProvider
    extends
        $FunctionalProvider<
          AsyncValue<UserModel?>,
          UserModel?,
          FutureOr<UserModel?>
        >
    with $FutureModifier<UserModel?>, $FutureProvider<UserModel?> {
  /// Family provider for viewing any user's profile by ID
  const UserProfileProvider._({
    required UserProfileFamily super.from,
    required String super.argument,
  }) : super(
         retry: null,
         name: r'userProfileProvider',
         isAutoDispose: true,
         dependencies: null,
         $allTransitiveDependencies: null,
       );

  @override
  String debugGetCreateSourceHash() => _$userProfileHash();

  @override
  String toString() {
    return r'userProfileProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<UserModel?> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<UserModel?> create(Ref ref) {
    final argument = this.argument as String;
    return userProfile(ref, argument);
  }

  @override
  bool operator ==(Object other) {
    return other is UserProfileProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$userProfileHash() => r'478a4be50ea6a17399af05ed80738e448c537725';

/// Family provider for viewing any user's profile by ID

final class UserProfileFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<UserModel?>, String> {
  const UserProfileFamily._()
    : super(
        retry: null,
        name: r'userProfileProvider',
        dependencies: null,
        $allTransitiveDependencies: null,
        isAutoDispose: true,
      );

  /// Family provider for viewing any user's profile by ID

  UserProfileProvider call(String userId) =>
      UserProfileProvider._(argument: userId, from: this);

  @override
  String toString() => r'userProfileProvider';
}
