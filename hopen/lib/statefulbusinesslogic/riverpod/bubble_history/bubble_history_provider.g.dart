// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bubble_history_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Bubble history controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since bubble history is screen-specific and should be cleaned up when not in use

@ProviderFor(BubbleHistoryController)
const bubbleHistoryControllerProvider = BubbleHistoryControllerProvider._();

/// Bubble history controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since bubble history is screen-specific and should be cleaned up when not in use
final class BubbleHistoryControllerProvider
    extends
        $AsyncNotifierProvider<BubbleHistoryController, BubbleHistoryState> {
  /// Bubble history controller using Riverpod 3.0 AsyncNotifier pattern
  /// Uses autoDispose since bubble history is screen-specific and should be cleaned up when not in use
  const BubbleHistoryControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleHistoryControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleHistoryControllerHash();

  @$internal
  @override
  BubbleHistoryController create() => BubbleHistoryController();
}

String _$bubbleHistoryControllerHash() =>
    r'2e6c142f24935444a6e2fba4fdf288c215c28149';

/// Bubble history controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since bubble history is screen-specific and should be cleaned up when not in use

abstract class _$BubbleHistoryController
    extends $AsyncNotifier<BubbleHistoryState> {
  FutureOr<BubbleHistoryState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<BubbleHistoryState>, BubbleHistoryState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<BubbleHistoryState>, BubbleHistoryState>,
              AsyncValue<BubbleHistoryState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing bubble history state properties

@ProviderFor(isBubbleHistoryLoaded)
const isBubbleHistoryLoadedProvider = IsBubbleHistoryLoadedProvider._();

/// Convenience providers for accessing bubble history state properties

final class IsBubbleHistoryLoadedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing bubble history state properties
  const IsBubbleHistoryLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isBubbleHistoryLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isBubbleHistoryLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isBubbleHistoryLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isBubbleHistoryLoadedHash() =>
    r'37ab36afd6bf846850d5ee41ec82725e388afce4';

@ProviderFor(allBubbles)
const allBubblesProvider = AllBubblesProvider._();

final class AllBubblesProvider
    extends
        $FunctionalProvider<
          List<BubbleEntity>,
          List<BubbleEntity>,
          List<BubbleEntity>
        >
    with $Provider<List<BubbleEntity>> {
  const AllBubblesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'allBubblesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$allBubblesHash();

  @$internal
  @override
  $ProviderElement<List<BubbleEntity>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<BubbleEntity> create(Ref ref) {
    return allBubbles(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<BubbleEntity> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<BubbleEntity>>(value),
    );
  }
}

String _$allBubblesHash() => r'5b11c08ecdaba93d7e3664d5d431d0e05cc63c6f';

@ProviderFor(activeBubbles)
const activeBubblesProvider = ActiveBubblesProvider._();

final class ActiveBubblesProvider
    extends
        $FunctionalProvider<
          List<BubbleEntity>,
          List<BubbleEntity>,
          List<BubbleEntity>
        >
    with $Provider<List<BubbleEntity>> {
  const ActiveBubblesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'activeBubblesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$activeBubblesHash();

  @$internal
  @override
  $ProviderElement<List<BubbleEntity>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<BubbleEntity> create(Ref ref) {
    return activeBubbles(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<BubbleEntity> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<BubbleEntity>>(value),
    );
  }
}

String _$activeBubblesHash() => r'410cca0d44ca89bbae10667f131a50c801e72c00';

@ProviderFor(completedBubbles)
const completedBubblesProvider = CompletedBubblesProvider._();

final class CompletedBubblesProvider
    extends
        $FunctionalProvider<
          List<BubbleEntity>,
          List<BubbleEntity>,
          List<BubbleEntity>
        >
    with $Provider<List<BubbleEntity>> {
  const CompletedBubblesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'completedBubblesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$completedBubblesHash();

  @$internal
  @override
  $ProviderElement<List<BubbleEntity>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<BubbleEntity> create(Ref ref) {
    return completedBubbles(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<BubbleEntity> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<BubbleEntity>>(value),
    );
  }
}

String _$completedBubblesHash() => r'58089437501cd0cfd82753e62b0010b9aeb41f70';

@ProviderFor(totalBubbleCount)
const totalBubbleCountProvider = TotalBubbleCountProvider._();

final class TotalBubbleCountProvider extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const TotalBubbleCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'totalBubbleCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$totalBubbleCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return totalBubbleCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$totalBubbleCountHash() => r'334c70805fede447ecec7065f75060fe34d0def0';

@ProviderFor(activeBubbleCount)
const activeBubbleCountProvider = ActiveBubbleCountProvider._();

final class ActiveBubbleCountProvider extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const ActiveBubbleCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'activeBubbleCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$activeBubbleCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return activeBubbleCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$activeBubbleCountHash() => r'25fac41e4a3e3128e5f1205f4b6d7786995c65c0';

@ProviderFor(completedBubbleCount)
const completedBubbleCountProvider = CompletedBubbleCountProvider._();

final class CompletedBubbleCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const CompletedBubbleCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'completedBubbleCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$completedBubbleCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return completedBubbleCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$completedBubbleCountHash() =>
    r'a254576350210380d60f8495c6b70a16e64daf15';

@ProviderFor(hasBubbles)
const hasBubblesProvider = HasBubblesProvider._();

final class HasBubblesProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasBubblesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasBubblesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasBubblesHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasBubbles(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasBubblesHash() => r'7651fecadbfa3a4dcd44cad5f68279e63b8ac2b6';

@ProviderFor(hasActiveBubbles)
const hasActiveBubblesProvider = HasActiveBubblesProvider._();

final class HasActiveBubblesProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasActiveBubblesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasActiveBubblesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasActiveBubblesHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasActiveBubbles(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasActiveBubblesHash() => r'f56e22fdce375e6dbdc0471f2680785f35810344';

@ProviderFor(hasCompletedBubbles)
const hasCompletedBubblesProvider = HasCompletedBubblesProvider._();

final class HasCompletedBubblesProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasCompletedBubblesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasCompletedBubblesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasCompletedBubblesHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasCompletedBubbles(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasCompletedBubblesHash() =>
    r'3867e3caae14b76f4a1dd9cf3f772682d2108310';

@ProviderFor(currentBubbleFilter)
const currentBubbleFilterProvider = CurrentBubbleFilterProvider._();

final class CurrentBubbleFilterProvider
    extends $FunctionalProvider<BubbleFilter, BubbleFilter, BubbleFilter>
    with $Provider<BubbleFilter> {
  const CurrentBubbleFilterProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentBubbleFilterProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentBubbleFilterHash();

  @$internal
  @override
  $ProviderElement<BubbleFilter> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  BubbleFilter create(Ref ref) {
    return currentBubbleFilter(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleFilter value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleFilter>(value),
    );
  }
}

String _$currentBubbleFilterHash() =>
    r'2a95e9d706b09a73c40aa15a333ecb93e33276ff';

@ProviderFor(hasBubbleHistoryError)
const hasBubbleHistoryErrorProvider = HasBubbleHistoryErrorProvider._();

final class HasBubbleHistoryErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasBubbleHistoryErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasBubbleHistoryErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasBubbleHistoryErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasBubbleHistoryError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasBubbleHistoryErrorHash() =>
    r'e9b8fe45a53ea7b6fd6f0ee832529ee980a93f48';

@ProviderFor(bubbleHistoryError)
const bubbleHistoryErrorProvider = BubbleHistoryErrorProvider._();

final class BubbleHistoryErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const BubbleHistoryErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleHistoryErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleHistoryErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return bubbleHistoryError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$bubbleHistoryErrorHash() =>
    r'2d29ee1771a41e692c49714813a41e01e717441b';
