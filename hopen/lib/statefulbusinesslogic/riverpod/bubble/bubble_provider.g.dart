// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bubble_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Bubble controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(BubbleController)
const bubbleControllerProvider = BubbleControllerProvider._();

/// Bubble controller using Riverpod 3.0 AsyncNotifier pattern
final class BubbleControllerProvider
    extends $AsyncNotifierProvider<BubbleController, BubbleState> {
  /// Bubble controller using Riverpod 3.0 AsyncNotifier pattern
  const BubbleControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleControllerHash();

  @$internal
  @override
  BubbleController create() => BubbleController();
}

String _$bubbleControllerHash() => r'6fbd1f47506564985eb7faf2987e95a5f827e247';

/// Bubble controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$BubbleController extends $AsyncNotifier<BubbleState> {
  FutureOr<BubbleState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<BubbleState>, BubbleState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<BubbleState>, BubbleState>,
              AsyncValue<BubbleState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing bubble state properties

@ProviderFor(hasBubble)
const hasBubbleProvider = HasBubbleProvider._();

/// Convenience providers for accessing bubble state properties

final class HasBubbleProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing bubble state properties
  const HasBubbleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasBubbleProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasBubbleHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasBubble(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasBubbleHash() => r'32cd377cf50607f32c7f5dfab71e7d6ef0c40d24';

@ProviderFor(currentBubble)
const currentBubbleProvider = CurrentBubbleProvider._();

final class CurrentBubbleProvider
    extends $FunctionalProvider<BubbleEntity?, BubbleEntity?, BubbleEntity?>
    with $Provider<BubbleEntity?> {
  const CurrentBubbleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentBubbleProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentBubbleHash();

  @$internal
  @override
  $ProviderElement<BubbleEntity?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  BubbleEntity? create(Ref ref) {
    return currentBubble(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(BubbleEntity? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<BubbleEntity?>(value),
    );
  }
}

String _$currentBubbleHash() => r'059710b64d738c55c4b25bd3616481a28a9d5c68';

@ProviderFor(isBubbleLoading)
const isBubbleLoadingProvider = IsBubbleLoadingProvider._();

final class IsBubbleLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsBubbleLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isBubbleLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isBubbleLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isBubbleLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isBubbleLoadingHash() => r'7fc4bb49a179f05a55dc4c7d74a818c187523a84';

@ProviderFor(hasBubbleError)
const hasBubbleErrorProvider = HasBubbleErrorProvider._();

final class HasBubbleErrorProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasBubbleErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasBubbleErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasBubbleErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasBubbleError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasBubbleErrorHash() => r'e88bd286f248a3f969abec37272fb2ebbb0ba1dd';

@ProviderFor(bubbleError)
const bubbleErrorProvider = BubbleErrorProvider._();

final class BubbleErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const BubbleErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'bubbleErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$bubbleErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return bubbleError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$bubbleErrorHash() => r'ab7eee878f7410f6a2b32b4d913314ed3f975ec3';

@ProviderFor(currentOperation)
const currentOperationProvider = CurrentOperationProvider._();

final class CurrentOperationProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentOperationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentOperationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentOperationHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentOperation(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentOperationHash() => r'd9410fcfaeafeb356706ba2a8d461ee859b47950';

@ProviderFor(isBubbleExpired)
const isBubbleExpiredProvider = IsBubbleExpiredProvider._();

final class IsBubbleExpiredProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsBubbleExpiredProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isBubbleExpiredProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isBubbleExpiredHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isBubbleExpired(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isBubbleExpiredHash() => r'519ca91709f468aed6f9413354b9a6f86eed4848';

@ProviderFor(onlineMembersCount)
const onlineMembersCountProvider = OnlineMembersCountProvider._();

final class OnlineMembersCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const OnlineMembersCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'onlineMembersCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$onlineMembersCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return onlineMembersCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$onlineMembersCountHash() =>
    r'e91693b9e29d1525a263f9b61a5c2261ef89ca74';

@ProviderFor(totalUnreadMessages)
const totalUnreadMessagesProvider = TotalUnreadMessagesProvider._();

final class TotalUnreadMessagesProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const TotalUnreadMessagesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'totalUnreadMessagesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$totalUnreadMessagesHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return totalUnreadMessages(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$totalUnreadMessagesHash() =>
    r'f736aea9583da623237f57bad80eaff5dad62b60';

@ProviderFor(hasActiveCall)
const hasActiveCallProvider = HasActiveCallProvider._();

final class HasActiveCallProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasActiveCallProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasActiveCallProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasActiveCallHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasActiveCall(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasActiveCallHash() => r'b725284eae1f191623d005ca27d7cd1deec132d2';
