// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'availability_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Availability controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since availability checks are temporary operations

@ProviderFor(AvailabilityController)
const availabilityControllerProvider = AvailabilityControllerProvider._();

/// Availability controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since availability checks are temporary operations
final class AvailabilityControllerProvider
    extends $AsyncNotifierProvider<AvailabilityController, AvailabilityState> {
  /// Availability controller using Riverpod 3.0 AsyncNotifier pattern
  /// Uses autoDispose since availability checks are temporary operations
  const AvailabilityControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availabilityControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availabilityControllerHash();

  @$internal
  @override
  AvailabilityController create() => AvailabilityController();
}

String _$availabilityControllerHash() =>
    r'424c56e8cff13006d9346c9bfbf553d0ff2262eb';

/// Availability controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since availability checks are temporary operations

abstract class _$AvailabilityController
    extends $AsyncNotifier<AvailabilityState> {
  FutureOr<AvailabilityState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<AvailabilityState>, AvailabilityState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<AvailabilityState>, AvailabilityState>,
              AsyncValue<AvailabilityState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing availability state properties

@ProviderFor(isCheckingAvailability)
const isCheckingAvailabilityProvider = IsCheckingAvailabilityProvider._();

/// Convenience providers for accessing availability state properties

final class IsCheckingAvailabilityProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing availability state properties
  const IsCheckingAvailabilityProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCheckingAvailabilityProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCheckingAvailabilityHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCheckingAvailability(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCheckingAvailabilityHash() =>
    r'b20214b14e769b8e42a3f80e06b5b0b32fe1887a';

@ProviderFor(isEmailAvailable)
const isEmailAvailableProvider = IsEmailAvailableProvider._();

final class IsEmailAvailableProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsEmailAvailableProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isEmailAvailableProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isEmailAvailableHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isEmailAvailable(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isEmailAvailableHash() => r'338d2bb471b0fd901f998d0ea6432b25f16b1ac0';

@ProviderFor(isUsernameAvailable)
const isUsernameAvailableProvider = IsUsernameAvailableProvider._();

final class IsUsernameAvailableProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsUsernameAvailableProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isUsernameAvailableProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isUsernameAvailableHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isUsernameAvailable(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isUsernameAvailableHash() =>
    r'84c2a8de5c1deb71fbc6f3c752b3a51c4c1acc9d';

@ProviderFor(areBothAvailable)
const areBothAvailableProvider = AreBothAvailableProvider._();

final class AreBothAvailableProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const AreBothAvailableProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'areBothAvailableProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$areBothAvailableHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return areBothAvailable(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$areBothAvailableHash() => r'29812848448620a0e6f1b783d39628363e6d01ea';

@ProviderFor(emailAvailabilityResult)
const emailAvailabilityResultProvider = EmailAvailabilityResultProvider._();

final class EmailAvailabilityResultProvider
    extends
        $FunctionalProvider<
          core_models.AvailabilityResult?,
          core_models.AvailabilityResult?,
          core_models.AvailabilityResult?
        >
    with $Provider<core_models.AvailabilityResult?> {
  const EmailAvailabilityResultProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'emailAvailabilityResultProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$emailAvailabilityResultHash();

  @$internal
  @override
  $ProviderElement<core_models.AvailabilityResult?> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  core_models.AvailabilityResult? create(Ref ref) {
    return emailAvailabilityResult(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(core_models.AvailabilityResult? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<core_models.AvailabilityResult?>(
        value,
      ),
    );
  }
}

String _$emailAvailabilityResultHash() =>
    r'a4e257e4d5470a47e7fca457d13d74ae02ee6bdb';

@ProviderFor(usernameAvailabilityResult)
const usernameAvailabilityResultProvider =
    UsernameAvailabilityResultProvider._();

final class UsernameAvailabilityResultProvider
    extends
        $FunctionalProvider<
          core_models.AvailabilityResult?,
          core_models.AvailabilityResult?,
          core_models.AvailabilityResult?
        >
    with $Provider<core_models.AvailabilityResult?> {
  const UsernameAvailabilityResultProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'usernameAvailabilityResultProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$usernameAvailabilityResultHash();

  @$internal
  @override
  $ProviderElement<core_models.AvailabilityResult?> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  core_models.AvailabilityResult? create(Ref ref) {
    return usernameAvailabilityResult(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(core_models.AvailabilityResult? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<core_models.AvailabilityResult?>(
        value,
      ),
    );
  }
}

String _$usernameAvailabilityResultHash() =>
    r'2e391787610290a1c2a99342753b63bf7c13a684';

@ProviderFor(checkingType)
const checkingTypeProvider = CheckingTypeProvider._();

final class CheckingTypeProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CheckingTypeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'checkingTypeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$checkingTypeHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return checkingType(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$checkingTypeHash() => r'8933f19ec076f5d3e693d8889b09ac80c9096fbf';

@ProviderFor(hasAvailabilityError)
const hasAvailabilityErrorProvider = HasAvailabilityErrorProvider._();

final class HasAvailabilityErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasAvailabilityErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasAvailabilityErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasAvailabilityErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasAvailabilityError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasAvailabilityErrorHash() =>
    r'dbbff9da7c4f2a6bd0fceab75ce473690f9a79fc';

@ProviderFor(availabilityError)
const availabilityErrorProvider = AvailabilityErrorProvider._();

final class AvailabilityErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const AvailabilityErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availabilityErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availabilityErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return availabilityError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$availabilityErrorHash() => r'6900aff2335e9ddcfcb1941fdf66aefd1a046d44';
