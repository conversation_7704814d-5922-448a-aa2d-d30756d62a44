// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sync_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Sync controller using Riverpod 3.0 AsyncNotifier pattern with Clean Architecture

@ProviderFor(SyncController)
const syncControllerProvider = SyncControllerProvider._();

/// Sync controller using Riverpod 3.0 AsyncNotifier pattern with Clean Architecture
final class SyncControllerProvider
    extends $AsyncNotifierProvider<SyncController, SyncState> {
  /// Sync controller using Riverpod 3.0 AsyncNotifier pattern with Clean Architecture
  const SyncControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncControllerHash();

  @$internal
  @override
  SyncController create() => SyncController();
}

String _$syncControllerHash() => r'5ebd4e1b9a372f7aef966ce4411214bc167a4bf0';

/// Sync controller using Riverpod 3.0 AsyncNotifier pattern with Clean Architecture

abstract class _$SyncController extends $AsyncNotifier<SyncState> {
  FutureOr<SyncState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<SyncState>, SyncState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<SyncState>, SyncState>,
              AsyncValue<SyncState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing sync state properties

@ProviderFor(isSyncing)
const isSyncingProvider = IsSyncingProvider._();

/// Convenience providers for accessing sync state properties

final class IsSyncingProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing sync state properties
  const IsSyncingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSyncingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSyncingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSyncing(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSyncingHash() => r'8da11c74f40ad1e6c0e33e764bdf6d2c89c840bb';

@ProviderFor(isSyncSuccess)
const isSyncSuccessProvider = IsSyncSuccessProvider._();

final class IsSyncSuccessProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsSyncSuccessProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSyncSuccessProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSyncSuccessHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSyncSuccess(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSyncSuccessHash() => r'34e524a9018b8653780450dcbb717ed4f25d3000';

@ProviderFor(hasSyncError)
const hasSyncErrorProvider = HasSyncErrorProvider._();

final class HasSyncErrorProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasSyncErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasSyncErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasSyncErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasSyncError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasSyncErrorHash() => r'e43019cc63c3b4d664050699ae0d27f718fa3171';

@ProviderFor(currentSyncOperation)
const currentSyncOperationProvider = CurrentSyncOperationProvider._();

final class CurrentSyncOperationProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentSyncOperationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentSyncOperationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentSyncOperationHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentSyncOperation(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentSyncOperationHash() =>
    r'b2f3f4470cdfcb76098a3a54e67d1069c98a380d';

@ProviderFor(syncProgress)
const syncProgressProvider = SyncProgressProvider._();

final class SyncProgressProvider
    extends $FunctionalProvider<double, double, double>
    with $Provider<double> {
  const SyncProgressProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncProgressProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncProgressHash();

  @$internal
  @override
  $ProviderElement<double> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  double create(Ref ref) {
    return syncProgress(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(double value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<double>(value),
    );
  }
}

String _$syncProgressHash() => r'c6da0ecebe4d546c7e2197837968f6bb56be3d08';

@ProviderFor(lastSyncTime)
const lastSyncTimeProvider = LastSyncTimeProvider._();

final class LastSyncTimeProvider
    extends $FunctionalProvider<DateTime?, DateTime?, DateTime?>
    with $Provider<DateTime?> {
  const LastSyncTimeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'lastSyncTimeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$lastSyncTimeHash();

  @$internal
  @override
  $ProviderElement<DateTime?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  DateTime? create(Ref ref) {
    return lastSyncTime(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DateTime? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DateTime?>(value),
    );
  }
}

String _$lastSyncTimeHash() => r'8d5eeb5712e95710abfa968904be17ee02f21dd0';

@ProviderFor(syncedOperations)
const syncedOperationsProvider = SyncedOperationsProvider._();

final class SyncedOperationsProvider
    extends $FunctionalProvider<List<String>, List<String>, List<String>>
    with $Provider<List<String>> {
  const SyncedOperationsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncedOperationsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncedOperationsHash();

  @$internal
  @override
  $ProviderElement<List<String>> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<String> create(Ref ref) {
    return syncedOperations(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<String> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<String>>(value),
    );
  }
}

String _$syncedOperationsHash() => r'07a3809916afb214063a86143222d12028a02ddb';

@ProviderFor(syncError)
const syncErrorProvider = SyncErrorProvider._();

final class SyncErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const SyncErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'syncErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$syncErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return syncError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$syncErrorHash() => r'7b0a8f0b89a99deadb9b50ef4742029120edbd31';
