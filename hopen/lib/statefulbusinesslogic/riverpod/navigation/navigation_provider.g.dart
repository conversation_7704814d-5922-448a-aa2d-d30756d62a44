// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Navigation controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(NavigationController)
const navigationControllerProvider = NavigationControllerProvider._();

/// Navigation controller using Riverpod 3.0 AsyncNotifier pattern
final class NavigationControllerProvider
    extends $AsyncNotifierProvider<NavigationController, NavigationState> {
  /// Navigation controller using Riverpod 3.0 AsyncNotifier pattern
  const NavigationControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'navigationControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$navigationControllerHash();

  @$internal
  @override
  NavigationController create() => NavigationController();
}

String _$navigationControllerHash() =>
    r'b895e4137a1bf7d909a5b2b74b76b1c8e9787b60';

/// Navigation controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$NavigationController extends $AsyncNotifier<NavigationState> {
  FutureOr<NavigationState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<NavigationState>, NavigationState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<NavigationState>, NavigationState>,
              AsyncValue<NavigationState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing navigation state properties

@ProviderFor(isNavigationLoading)
const isNavigationLoadingProvider = IsNavigationLoadingProvider._();

/// Convenience providers for accessing navigation state properties

final class IsNavigationLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing navigation state properties
  const IsNavigationLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isNavigationLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isNavigationLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isNavigationLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isNavigationLoadingHash() =>
    r'ca55cc741c6a44ad2a7acfd3a41820ab1541da6f';

@ProviderFor(isNavigationReady)
const isNavigationReadyProvider = IsNavigationReadyProvider._();

final class IsNavigationReadyProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsNavigationReadyProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isNavigationReadyProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isNavigationReadyHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isNavigationReady(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isNavigationReadyHash() => r'e27c7eaae7e1126680eb48a7eea89af051a06288';

@ProviderFor(currentDestination)
const currentDestinationProvider = CurrentDestinationProvider._();

final class CurrentDestinationProvider
    extends
        $FunctionalProvider<
          HomeDestination?,
          HomeDestination?,
          HomeDestination?
        >
    with $Provider<HomeDestination?> {
  const CurrentDestinationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentDestinationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentDestinationHash();

  @$internal
  @override
  $ProviderElement<HomeDestination?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  HomeDestination? create(Ref ref) {
    return currentDestination(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(HomeDestination? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<HomeDestination?>(value),
    );
  }
}

String _$currentDestinationHash() =>
    r'02cb20a5914c96d120bb3673e1bf8a8c91ce943d';

@ProviderFor(hasNavigationError)
const hasNavigationErrorProvider = HasNavigationErrorProvider._();

final class HasNavigationErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasNavigationErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasNavigationErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasNavigationErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasNavigationError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasNavigationErrorHash() =>
    r'748aec63829e43e4ffc380fb5ae281eb96adeae7';

@ProviderFor(navigationError)
const navigationErrorProvider = NavigationErrorProvider._();

final class NavigationErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const NavigationErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'navigationErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$navigationErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return navigationError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$navigationErrorHash() => r'efe79f1e2aed8e72147a0d18a1ac951d738fda62';

@ProviderFor(navigationStatus)
const navigationStatusProvider = NavigationStatusProvider._();

final class NavigationStatusProvider
    extends
        $FunctionalProvider<
          HomeNavigationStatus,
          HomeNavigationStatus,
          HomeNavigationStatus
        >
    with $Provider<HomeNavigationStatus> {
  const NavigationStatusProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'navigationStatusProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$navigationStatusHash();

  @$internal
  @override
  $ProviderElement<HomeNavigationStatus> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  HomeNavigationStatus create(Ref ref) {
    return navigationStatus(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(HomeNavigationStatus value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<HomeNavigationStatus>(value),
    );
  }
}

String _$navigationStatusHash() => r'aed9aa6a3a2d56094be70c8cb760edfba25f7e2e';
