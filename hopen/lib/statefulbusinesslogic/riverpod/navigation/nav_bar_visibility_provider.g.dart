// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nav_bar_visibility_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Navigation bar visibility controller using Riverpod 3.0 Notifier pattern

@ProviderFor(NavBarVisibilityController)
const navBarVisibilityControllerProvider =
    NavBarVisibilityControllerProvider._();

/// Navigation bar visibility controller using Riverpod 3.0 Notifier pattern
final class NavBarVisibilityControllerProvider
    extends
        $NotifierProvider<NavBarVisibilityController, NavBarVisibilityState> {
  /// Navigation bar visibility controller using Riverpod 3.0 Notifier pattern
  const NavBarVisibilityControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'navBarVisibilityControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$navBarVisibilityControllerHash();

  @$internal
  @override
  NavBarVisibilityController create() => NavBarVisibilityController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(NavBarVisibilityState value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<NavBarVisibilityState>(value),
    );
  }
}

String _$navBarVisibilityControllerHash() =>
    r'cb73aba18f1d2200c7638e0cf7ddaa1e7a9bb685';

/// Navigation bar visibility controller using Riverpod 3.0 Notifier pattern

abstract class _$NavBarVisibilityController
    extends $Notifier<NavBarVisibilityState> {
  NavBarVisibilityState build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<NavBarVisibilityState, NavBarVisibilityState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<NavBarVisibilityState, NavBarVisibilityState>,
              NavBarVisibilityState,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing nav bar visibility state

@ProviderFor(isNavBarVisible)
const isNavBarVisibleProvider = IsNavBarVisibleProvider._();

/// Convenience providers for accessing nav bar visibility state

final class IsNavBarVisibleProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing nav bar visibility state
  const IsNavBarVisibleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isNavBarVisibleProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isNavBarVisibleHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isNavBarVisible(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isNavBarVisibleHash() => r'a74006255f164030d59f6334974452c4aed8e396';

@ProviderFor(isNavBarHidden)
const isNavBarHiddenProvider = IsNavBarHiddenProvider._();

final class IsNavBarHiddenProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsNavBarHiddenProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isNavBarHiddenProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isNavBarHiddenHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isNavBarHidden(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isNavBarHiddenHash() => r'a92e71aa867c574b0eed89f19f9493130bd1d812';
