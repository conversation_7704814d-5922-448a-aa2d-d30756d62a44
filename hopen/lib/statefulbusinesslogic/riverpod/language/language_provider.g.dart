// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'language_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Language controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(LanguageController)
const languageControllerProvider = LanguageControllerProvider._();

/// Language controller using Riverpod 3.0 AsyncNotifier pattern
final class LanguageControllerProvider
    extends $AsyncNotifierProvider<LanguageController, LanguageState> {
  /// Language controller using Riverpod 3.0 AsyncNotifier pattern
  const LanguageControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'languageControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$languageControllerHash();

  @$internal
  @override
  LanguageController create() => LanguageController();
}

String _$languageControllerHash() =>
    r'6a6d87ff605405daab5f903ca23e51a8581ed25d';

/// Language controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$LanguageController extends $AsyncNotifier<LanguageState> {
  FutureOr<LanguageState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<LanguageState>, LanguageState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<LanguageState>, LanguageState>,
              AsyncValue<LanguageState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing language state properties

@ProviderFor(isLanguageLoading)
const isLanguageLoadingProvider = IsLanguageLoadingProvider._();

/// Convenience providers for accessing language state properties

final class IsLanguageLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing language state properties
  const IsLanguageLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isLanguageLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isLanguageLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isLanguageLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isLanguageLoadingHash() => r'284452e391faf67d63f8ee98d4f1a6fcae63f6e3';

@ProviderFor(isLanguageLoaded)
const isLanguageLoadedProvider = IsLanguageLoadedProvider._();

final class IsLanguageLoadedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsLanguageLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isLanguageLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isLanguageLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isLanguageLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isLanguageLoadedHash() => r'80fe5a8ee527b55f75ad5a175bccd0f2f61ed4da';

@ProviderFor(currentLanguage)
const currentLanguageProvider = CurrentLanguageProvider._();

final class CurrentLanguageProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentLanguageProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentLanguageProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentLanguageHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentLanguage(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentLanguageHash() => r'5e5b693efe85b0730ec1244278d8d067634c4b1c';

@ProviderFor(currentLocale)
const currentLocaleProvider = CurrentLocaleProvider._();

final class CurrentLocaleProvider
    extends $FunctionalProvider<Locale?, Locale?, Locale?>
    with $Provider<Locale?> {
  const CurrentLocaleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentLocaleProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentLocaleHash();

  @$internal
  @override
  $ProviderElement<Locale?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Locale? create(Ref ref) {
    return currentLocale(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Locale? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Locale?>(value),
    );
  }
}

String _$currentLocaleHash() => r'55deb7d12805b8dc69e14f4b555a6c73c0563452';

@ProviderFor(availableLanguages)
const availableLanguagesProvider = AvailableLanguagesProvider._();

final class AvailableLanguagesProvider
    extends
        $FunctionalProvider<
          Map<String, String>,
          Map<String, String>,
          Map<String, String>
        >
    with $Provider<Map<String, String>> {
  const AvailableLanguagesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availableLanguagesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availableLanguagesHash();

  @$internal
  @override
  $ProviderElement<Map<String, String>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  Map<String, String> create(Ref ref) {
    return availableLanguages(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Map<String, String> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Map<String, String>>(value),
    );
  }
}

String _$availableLanguagesHash() =>
    r'a82e0a4540d74d440e9421e302fae8d74954a3f3';

@ProviderFor(currentLanguageDisplayName)
const currentLanguageDisplayNameProvider =
    CurrentLanguageDisplayNameProvider._();

final class CurrentLanguageDisplayNameProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentLanguageDisplayNameProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentLanguageDisplayNameProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentLanguageDisplayNameHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentLanguageDisplayName(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentLanguageDisplayNameHash() =>
    r'dfac977b27c6723effaaa086c9bd870ff9f06ff7';

@ProviderFor(hasLanguageError)
const hasLanguageErrorProvider = HasLanguageErrorProvider._();

final class HasLanguageErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasLanguageErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasLanguageErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasLanguageErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasLanguageError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasLanguageErrorHash() => r'c3b1162d485395449a88ce08a076d5b4ede4aa0c';

@ProviderFor(languageError)
const languageErrorProvider = LanguageErrorProvider._();

final class LanguageErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const LanguageErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'languageErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$languageErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return languageError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$languageErrorHash() => r'7197fc221e65258897392e09c6ae1eadb011b351';
