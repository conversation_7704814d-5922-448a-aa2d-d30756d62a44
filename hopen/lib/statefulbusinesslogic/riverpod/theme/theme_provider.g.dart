// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'theme_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Theme controller using Riverpod 3.0 Notifier pattern
/// This is much simpler than the BLoC approach!

@ProviderFor(ThemeController)
const themeControllerProvider = ThemeControllerProvider._();

/// Theme controller using Riverpod 3.0 Notifier pattern
/// This is much simpler than the BLoC approach!
final class ThemeControllerProvider
    extends $NotifierProvider<ThemeController, ThemeMode> {
  /// Theme controller using Riverpod 3.0 Notifier pattern
  /// This is much simpler than the BLoC approach!
  const ThemeControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'themeControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$themeControllerHash();

  @$internal
  @override
  ThemeController create() => ThemeController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ThemeMode value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ThemeMode>(value),
    );
  }
}

String _$themeControllerHash() => r'347193f5849cdfa35551f14f2abf224ec721a5cd';

/// Theme controller using Riverpod 3.0 Notifier pattern
/// This is much simpler than the BLoC approach!

abstract class _$ThemeController extends $Notifier<ThemeMode> {
  ThemeMode build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<ThemeMode, ThemeMode>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<ThemeMode, ThemeMode>,
              ThemeMode,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for theme state

@ProviderFor(isDarkMode)
const isDarkModeProvider = IsDarkModeProvider._();

/// Convenience providers for theme state

final class IsDarkModeProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for theme state
  const IsDarkModeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isDarkModeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isDarkModeHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isDarkMode(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isDarkModeHash() => r'b5003730b6934bca732a8b38aa6b07a38124c967';

@ProviderFor(isLightMode)
const isLightModeProvider = IsLightModeProvider._();

final class IsLightModeProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsLightModeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isLightModeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isLightModeHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isLightMode(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isLightModeHash() => r'6514f6157875fa0deb0fbaf6761620a063b6d938';

@ProviderFor(isSystemMode)
const isSystemModeProvider = IsSystemModeProvider._();

final class IsSystemModeProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsSystemModeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSystemModeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSystemModeHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSystemMode(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSystemModeHash() => r'c2f2db298f1e237abc743e37b14f3dba946e1689';
