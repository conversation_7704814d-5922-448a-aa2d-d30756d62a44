// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Stream providers for call repository events

@ProviderFor(remoteStreamAddedStream)
const remoteStreamAddedStreamProvider = RemoteStreamAddedStreamProvider._();

/// Stream providers for call repository events

final class RemoteStreamAddedStreamProvider
    extends
        $FunctionalProvider<
          AsyncValue<RemoteStreamAdded>,
          RemoteStreamAdded,
          Stream<RemoteStreamAdded>
        >
    with
        $FutureModifier<RemoteStreamAdded>,
        $StreamProvider<RemoteStreamAdded> {
  /// Stream providers for call repository events
  const RemoteStreamAddedStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'remoteStreamAddedStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$remoteStreamAddedStreamHash();

  @$internal
  @override
  $StreamProviderElement<RemoteStreamAdded> $createElement(
    $ProviderPointer pointer,
  ) => $StreamProviderElement(pointer);

  @override
  Stream<RemoteStreamAdded> create(Ref ref) {
    return remoteStreamAddedStream(ref);
  }
}

String _$remoteStreamAddedStreamHash() =>
    r'beab1d7fa68c2704c835fa97257f20f57f22f759';

@ProviderFor(iceConnectionStateChangedStream)
const iceConnectionStateChangedStreamProvider =
    IceConnectionStateChangedStreamProvider._();

final class IceConnectionStateChangedStreamProvider
    extends
        $FunctionalProvider<
          AsyncValue<IceConnectionStateChanged>,
          IceConnectionStateChanged,
          Stream<IceConnectionStateChanged>
        >
    with
        $FutureModifier<IceConnectionStateChanged>,
        $StreamProvider<IceConnectionStateChanged> {
  const IceConnectionStateChangedStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'iceConnectionStateChangedStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$iceConnectionStateChangedStreamHash();

  @$internal
  @override
  $StreamProviderElement<IceConnectionStateChanged> $createElement(
    $ProviderPointer pointer,
  ) => $StreamProviderElement(pointer);

  @override
  Stream<IceConnectionStateChanged> create(Ref ref) {
    return iceConnectionStateChangedStream(ref);
  }
}

String _$iceConnectionStateChangedStreamHash() =>
    r'91541350e60f464d4398a8af076f391f17c3ec1e';

@ProviderFor(callErrorOccurredStream)
const callErrorOccurredStreamProvider = CallErrorOccurredStreamProvider._();

final class CallErrorOccurredStreamProvider
    extends
        $FunctionalProvider<
          AsyncValue<CallErrorOccurred>,
          CallErrorOccurred,
          Stream<CallErrorOccurred>
        >
    with
        $FutureModifier<CallErrorOccurred>,
        $StreamProvider<CallErrorOccurred> {
  const CallErrorOccurredStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callErrorOccurredStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callErrorOccurredStreamHash();

  @$internal
  @override
  $StreamProviderElement<CallErrorOccurred> $createElement(
    $ProviderPointer pointer,
  ) => $StreamProviderElement(pointer);

  @override
  Stream<CallErrorOccurred> create(Ref ref) {
    return callErrorOccurredStream(ref);
  }
}

String _$callErrorOccurredStreamHash() =>
    r'e9ca7cab9f4a1d0b4ed2b8510e04d8e04e1e4be7';

@ProviderFor(callOutcomeOccurredStream)
const callOutcomeOccurredStreamProvider = CallOutcomeOccurredStreamProvider._();

final class CallOutcomeOccurredStreamProvider
    extends
        $FunctionalProvider<
          AsyncValue<CallOutcomeOccurred>,
          CallOutcomeOccurred,
          Stream<CallOutcomeOccurred>
        >
    with
        $FutureModifier<CallOutcomeOccurred>,
        $StreamProvider<CallOutcomeOccurred> {
  const CallOutcomeOccurredStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callOutcomeOccurredStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callOutcomeOccurredStreamHash();

  @$internal
  @override
  $StreamProviderElement<CallOutcomeOccurred> $createElement(
    $ProviderPointer pointer,
  ) => $StreamProviderElement(pointer);

  @override
  Stream<CallOutcomeOccurred> create(Ref ref) {
    return callOutcomeOccurredStream(ref);
  }
}

String _$callOutcomeOccurredStreamHash() =>
    r'73532206aaf48387e2c4798eff1cbc0306e8c75f';

@ProviderFor(joinCallAcknowledgedStream)
const joinCallAcknowledgedStreamProvider =
    JoinCallAcknowledgedStreamProvider._();

final class JoinCallAcknowledgedStreamProvider
    extends
        $FunctionalProvider<
          AsyncValue<JoinCallAcknowledged>,
          JoinCallAcknowledged,
          Stream<JoinCallAcknowledged>
        >
    with
        $FutureModifier<JoinCallAcknowledged>,
        $StreamProvider<JoinCallAcknowledged> {
  const JoinCallAcknowledgedStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'joinCallAcknowledgedStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$joinCallAcknowledgedStreamHash();

  @$internal
  @override
  $StreamProviderElement<JoinCallAcknowledged> $createElement(
    $ProviderPointer pointer,
  ) => $StreamProviderElement(pointer);

  @override
  Stream<JoinCallAcknowledged> create(Ref ref) {
    return joinCallAcknowledgedStream(ref);
  }
}

String _$joinCallAcknowledgedStreamHash() =>
    r'714c3df4f73672e4dc73c9dee047a471d3c74eb7';

@ProviderFor(joinCallFailedStream)
const joinCallFailedStreamProvider = JoinCallFailedStreamProvider._();

final class JoinCallFailedStreamProvider
    extends
        $FunctionalProvider<
          AsyncValue<JoinCallFailed>,
          JoinCallFailed,
          Stream<JoinCallFailed>
        >
    with $FutureModifier<JoinCallFailed>, $StreamProvider<JoinCallFailed> {
  const JoinCallFailedStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'joinCallFailedStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$joinCallFailedStreamHash();

  @$internal
  @override
  $StreamProviderElement<JoinCallFailed> $createElement(
    $ProviderPointer pointer,
  ) => $StreamProviderElement(pointer);

  @override
  Stream<JoinCallFailed> create(Ref ref) {
    return joinCallFailedStream(ref);
  }
}

String _$joinCallFailedStreamHash() =>
    r'e69127471e03aad65bdeeec308a06b0945fcab90';

@ProviderFor(incomingOfferOccurredStream)
const incomingOfferOccurredStreamProvider =
    IncomingOfferOccurredStreamProvider._();

final class IncomingOfferOccurredStreamProvider
    extends
        $FunctionalProvider<
          AsyncValue<IncomingOfferOccurred>,
          IncomingOfferOccurred,
          Stream<IncomingOfferOccurred>
        >
    with
        $FutureModifier<IncomingOfferOccurred>,
        $StreamProvider<IncomingOfferOccurred> {
  const IncomingOfferOccurredStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'incomingOfferOccurredStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$incomingOfferOccurredStreamHash();

  @$internal
  @override
  $StreamProviderElement<IncomingOfferOccurred> $createElement(
    $ProviderPointer pointer,
  ) => $StreamProviderElement(pointer);

  @override
  Stream<IncomingOfferOccurred> create(Ref ref) {
    return incomingOfferOccurredStream(ref);
  }
}

String _$incomingOfferOccurredStreamHash() =>
    r'16aa8f719bfda9b6174c8ad2f1c3e868642a7bb4';

/// Call controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(CallController)
const callControllerProvider = CallControllerProvider._();

/// Call controller using Riverpod 3.0 AsyncNotifier pattern
final class CallControllerProvider
    extends $AsyncNotifierProvider<CallController, CallState> {
  /// Call controller using Riverpod 3.0 AsyncNotifier pattern
  const CallControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callControllerHash();

  @$internal
  @override
  CallController create() => CallController();
}

String _$callControllerHash() => r'1c1110e8cb2493485f2a84590749cf89de39e209';

/// Call controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$CallController extends $AsyncNotifier<CallState> {
  FutureOr<CallState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<CallState>, CallState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<CallState>, CallState>,
              AsyncValue<CallState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing call state properties

@ProviderFor(isCallActive)
const isCallActiveProvider = IsCallActiveProvider._();

/// Convenience providers for accessing call state properties

final class IsCallActiveProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing call state properties
  const IsCallActiveProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCallActiveProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCallActiveHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCallActive(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCallActiveHash() => r'7570861ce0081bbe8366b48ed503be1fc3c92776';

@ProviderFor(isCallRinging)
const isCallRingingProvider = IsCallRingingProvider._();

final class IsCallRingingProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCallRingingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCallRingingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCallRingingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCallRinging(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCallRingingHash() => r'55b42e78eeb560a8455473d9adc6ee81dea4ac2d';

@ProviderFor(isCallDialing)
const isCallDialingProvider = IsCallDialingProvider._();

final class IsCallDialingProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCallDialingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCallDialingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCallDialingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCallDialing(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCallDialingHash() => r'39e1728637b2fedd5ca47bd6b14e4ffbcf0e7e01';

@ProviderFor(isCallConnecting)
const isCallConnectingProvider = IsCallConnectingProvider._();

final class IsCallConnectingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCallConnectingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCallConnectingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCallConnectingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCallConnecting(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCallConnectingHash() => r'e61d81ce06e67b566d161476aced23677a1297d8';

@ProviderFor(currentCallId)
const currentCallIdProvider = CurrentCallIdProvider._();

final class CurrentCallIdProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentCallIdProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentCallIdProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentCallIdHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentCallId(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentCallIdHash() => r'd2acf5a1268f2a0044edd77ee13e809023978cbd';

@ProviderFor(currentCallDuration)
const currentCallDurationProvider = CurrentCallDurationProvider._();

final class CurrentCallDurationProvider
    extends $FunctionalProvider<Duration?, Duration?, Duration?>
    with $Provider<Duration?> {
  const CurrentCallDurationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentCallDurationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentCallDurationHash();

  @$internal
  @override
  $ProviderElement<Duration?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Duration? create(Ref ref) {
    return currentCallDuration(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Duration? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Duration?>(value),
    );
  }
}

String _$currentCallDurationHash() =>
    r'42e8c91f5ee083be2141388163571169e9768501';

@ProviderFor(isCallMuted)
const isCallMutedProvider = IsCallMutedProvider._();

final class IsCallMutedProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCallMutedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCallMutedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCallMutedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCallMuted(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCallMutedHash() => r'f191482bda91c100c631461cec76e577775fab98';

@ProviderFor(isCallVideoEnabled)
const isCallVideoEnabledProvider = IsCallVideoEnabledProvider._();

final class IsCallVideoEnabledProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsCallVideoEnabledProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isCallVideoEnabledProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isCallVideoEnabledHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isCallVideoEnabled(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isCallVideoEnabledHash() =>
    r'64332efb1f012974e90e4065f52da450851bb234';

@ProviderFor(isScreenSharingEnabled)
const isScreenSharingEnabledProvider = IsScreenSharingEnabledProvider._();

final class IsScreenSharingEnabledProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsScreenSharingEnabledProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isScreenSharingEnabledProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isScreenSharingEnabledHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isScreenSharingEnabled(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isScreenSharingEnabledHash() =>
    r'26a8ad9117c28dcb28da95a63991c80f8094c552';

@ProviderFor(participantCount)
const participantCountProvider = ParticipantCountProvider._();

final class ParticipantCountProvider extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const ParticipantCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'participantCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$participantCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return participantCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$participantCountHash() => r'165bbf878b28622280f79c298eb063a7479a3647';

@ProviderFor(callParticipants)
const callParticipantsProvider = CallParticipantsProvider._();

final class CallParticipantsProvider
    extends
        $FunctionalProvider<
          List<Participant>,
          List<Participant>,
          List<Participant>
        >
    with $Provider<List<Participant>> {
  const CallParticipantsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callParticipantsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callParticipantsHash();

  @$internal
  @override
  $ProviderElement<List<Participant>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<Participant> create(Ref ref) {
    return callParticipants(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<Participant> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<Participant>>(value),
    );
  }
}

String _$callParticipantsHash() => r'8e5468ee00c2b080b2b4e656b5684b4f96587966';

@ProviderFor(localStream)
const localStreamProvider = LocalStreamProvider._();

final class LocalStreamProvider
    extends $FunctionalProvider<MediaStream?, MediaStream?, MediaStream?>
    with $Provider<MediaStream?> {
  const LocalStreamProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'localStreamProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$localStreamHash();

  @$internal
  @override
  $ProviderElement<MediaStream?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  MediaStream? create(Ref ref) {
    return localStream(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MediaStream? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MediaStream?>(value),
    );
  }
}

String _$localStreamHash() => r'07d8c74fbd76e63a38272b68f653690c07ac3413';

@ProviderFor(remoteStreams)
const remoteStreamsProvider = RemoteStreamsProvider._();

final class RemoteStreamsProvider
    extends
        $FunctionalProvider<
          Map<String, MediaStream>,
          Map<String, MediaStream>,
          Map<String, MediaStream>
        >
    with $Provider<Map<String, MediaStream>> {
  const RemoteStreamsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'remoteStreamsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$remoteStreamsHash();

  @$internal
  @override
  $ProviderElement<Map<String, MediaStream>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  Map<String, MediaStream> create(Ref ref) {
    return remoteStreams(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Map<String, MediaStream> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Map<String, MediaStream>>(value),
    );
  }
}

String _$remoteStreamsHash() => r'02845c145e995452e22b1dd54d4d45b25e9c3eb5';

@ProviderFor(hasCallError)
const hasCallErrorProvider = HasCallErrorProvider._();

final class HasCallErrorProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasCallErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasCallErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasCallErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasCallError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasCallErrorHash() => r'c3fd3aba613daaf3a5c1f42593c547e45ac5bdcc';

@ProviderFor(callError)
const callErrorProvider = CallErrorProvider._();

final class CallErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const CallErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'callErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$callErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return callError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$callErrorHash() => r'47ad8e5beb8ea4475a52c30d0cc752bcc706ff35';

@ProviderFor(currentCallStatus)
const currentCallStatusProvider = CurrentCallStatusProvider._();

final class CurrentCallStatusProvider
    extends $FunctionalProvider<CallStatus, CallStatus, CallStatus>
    with $Provider<CallStatus> {
  const CurrentCallStatusProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentCallStatusProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentCallStatusHash();

  @$internal
  @override
  $ProviderElement<CallStatus> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  CallStatus create(Ref ref) {
    return currentCallStatus(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(CallStatus value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<CallStatus>(value),
    );
  }
}

String _$currentCallStatusHash() => r'901868037dc7cf670d35e37a7a96da3ee8124f0b';
