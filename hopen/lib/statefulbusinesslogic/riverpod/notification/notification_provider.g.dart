// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Notification controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(NotificationController)
const notificationControllerProvider = NotificationControllerProvider._();

/// Notification controller using Riverpod 3.0 AsyncNotifier pattern
final class NotificationControllerProvider
    extends $AsyncNotifierProvider<NotificationController, NotificationState> {
  /// Notification controller using Riverpod 3.0 AsyncNotifier pattern
  const NotificationControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationControllerHash();

  @$internal
  @override
  NotificationController create() => NotificationController();
}

String _$notificationControllerHash() =>
    r'c932f0a6b68a38a0a9ef38c1027e14bd12e07f40';

/// Notification controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$NotificationController
    extends $AsyncNotifier<NotificationState> {
  FutureOr<NotificationState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<NotificationState>, NotificationState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<NotificationState>, NotificationState>,
              AsyncValue<NotificationState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing notification state properties

@ProviderFor(hasNotifications)
const hasNotificationsProvider = HasNotificationsProvider._();

/// Convenience providers for accessing notification state properties

final class HasNotificationsProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing notification state properties
  const HasNotificationsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasNotificationsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasNotificationsHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasNotifications(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasNotificationsHash() => r'7855f0886e4cd364d15c609d3ef36bd23d94df9a';

@ProviderFor(hasUnreadNotifications)
const hasUnreadNotificationsProvider = HasUnreadNotificationsProvider._();

final class HasUnreadNotificationsProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasUnreadNotificationsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasUnreadNotificationsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasUnreadNotificationsHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasUnreadNotifications(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasUnreadNotificationsHash() =>
    r'0d397c4ebcd2daae55f0ed992b07bf7e79e6409d';

@ProviderFor(unreadNotificationsCount)
const unreadNotificationsCountProvider = UnreadNotificationsCountProvider._();

final class UnreadNotificationsCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const UnreadNotificationsCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'unreadNotificationsCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$unreadNotificationsCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return unreadNotificationsCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$unreadNotificationsCountHash() =>
    r'b9206772039cec1d83324fc91b03781bfec8e6ce';

@ProviderFor(allNotifications)
const allNotificationsProvider = AllNotificationsProvider._();

final class AllNotificationsProvider
    extends
        $FunctionalProvider<
          List<Notification>,
          List<Notification>,
          List<Notification>
        >
    with $Provider<List<Notification>> {
  const AllNotificationsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'allNotificationsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$allNotificationsHash();

  @$internal
  @override
  $ProviderElement<List<Notification>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<Notification> create(Ref ref) {
    return allNotifications(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<Notification> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<Notification>>(value),
    );
  }
}

String _$allNotificationsHash() => r'0600f42a2e77aa7c377f6141529d18c502e16dd2';

@ProviderFor(unreadNotifications)
const unreadNotificationsProvider = UnreadNotificationsProvider._();

final class UnreadNotificationsProvider
    extends
        $FunctionalProvider<
          List<Notification>,
          List<Notification>,
          List<Notification>
        >
    with $Provider<List<Notification>> {
  const UnreadNotificationsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'unreadNotificationsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$unreadNotificationsHash();

  @$internal
  @override
  $ProviderElement<List<Notification>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<Notification> create(Ref ref) {
    return unreadNotifications(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<Notification> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<Notification>>(value),
    );
  }
}

String _$unreadNotificationsHash() =>
    r'eb5681ad2372a076b463eafa4875b967d3522712';

@ProviderFor(isNotificationLoading)
const isNotificationLoadingProvider = IsNotificationLoadingProvider._();

final class IsNotificationLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsNotificationLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isNotificationLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isNotificationLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isNotificationLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isNotificationLoadingHash() =>
    r'e175b5a792382e989ddca0ca4a466dcbde1cf43e';

@ProviderFor(hasNotificationError)
const hasNotificationErrorProvider = HasNotificationErrorProvider._();

final class HasNotificationErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasNotificationErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasNotificationErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasNotificationErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasNotificationError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasNotificationErrorHash() =>
    r'6b1bfa508b9963595421ee439253c41f2316c7bd';

@ProviderFor(notificationError)
const notificationErrorProvider = NotificationErrorProvider._();

final class NotificationErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const NotificationErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'notificationErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$notificationErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return notificationError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$notificationErrorHash() => r'8e5259ea27814ca8fb02082114b019511b18735b';
