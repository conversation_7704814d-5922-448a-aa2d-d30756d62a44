// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friend_selection_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Friend selection controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since friend selection is a temporary dialog operation

@ProviderFor(FriendSelectionController)
const friendSelectionControllerProvider = FriendSelectionControllerProvider._();

/// Friend selection controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since friend selection is a temporary dialog operation
final class FriendSelectionControllerProvider
    extends
        $AsyncNotifierProvider<
          FriendSelectionController,
          FriendSelectionState
        > {
  /// Friend selection controller using Riverpod 3.0 AsyncNotifier pattern
  /// Uses autoDispose since friend selection is a temporary dialog operation
  const FriendSelectionControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'friendSelectionControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$friendSelectionControllerHash();

  @$internal
  @override
  FriendSelectionController create() => FriendSelectionController();
}

String _$friendSelectionControllerHash() =>
    r'0578d1f80017525360d514572f84235998a02f86';

/// Friend selection controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since friend selection is a temporary dialog operation

abstract class _$FriendSelectionController
    extends $AsyncNotifier<FriendSelectionState> {
  FutureOr<FriendSelectionState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref
            as $Ref<AsyncValue<FriendSelectionState>, FriendSelectionState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<
                AsyncValue<FriendSelectionState>,
                FriendSelectionState
              >,
              AsyncValue<FriendSelectionState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing friend selection state properties

@ProviderFor(isFriendSelectionLoading)
const isFriendSelectionLoadingProvider = IsFriendSelectionLoadingProvider._();

/// Convenience providers for accessing friend selection state properties

final class IsFriendSelectionLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing friend selection state properties
  const IsFriendSelectionLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isFriendSelectionLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isFriendSelectionLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isFriendSelectionLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isFriendSelectionLoadingHash() =>
    r'f2a091ca4b3f753ac7a744f6cf360fe5b97800c6';

@ProviderFor(isFriendSelectionLoaded)
const isFriendSelectionLoadedProvider = IsFriendSelectionLoadedProvider._();

final class IsFriendSelectionLoadedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsFriendSelectionLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isFriendSelectionLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isFriendSelectionLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isFriendSelectionLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isFriendSelectionLoadedHash() =>
    r'783140b07149d899c6ea2e13526776d5aaeead58';

@ProviderFor(isFriendSelectionSubmitting)
const isFriendSelectionSubmittingProvider =
    IsFriendSelectionSubmittingProvider._();

final class IsFriendSelectionSubmittingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsFriendSelectionSubmittingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isFriendSelectionSubmittingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isFriendSelectionSubmittingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isFriendSelectionSubmitting(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isFriendSelectionSubmittingHash() =>
    r'a332e39f2a474be5cb69228070dcd7cb37ee7c8e';

@ProviderFor(hasFriendSelectionSubmitted)
const hasFriendSelectionSubmittedProvider =
    HasFriendSelectionSubmittedProvider._();

final class HasFriendSelectionSubmittedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasFriendSelectionSubmittedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasFriendSelectionSubmittedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasFriendSelectionSubmittedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasFriendSelectionSubmitted(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasFriendSelectionSubmittedHash() =>
    r'9ac9163c631922651c65d744a02bcd595f645d5c';

@ProviderFor(hasPendingFriendDialogs)
const hasPendingFriendDialogsProvider = HasPendingFriendDialogsProvider._();

final class HasPendingFriendDialogsProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasPendingFriendDialogsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasPendingFriendDialogsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasPendingFriendDialogsHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasPendingFriendDialogs(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasPendingFriendDialogsHash() =>
    r'd8fe98f3d4bfa2d544d89d209892789a8cb57817';

@ProviderFor(formerBubbleMembers)
const formerBubbleMembersProvider = FormerBubbleMembersProvider._();

final class FormerBubbleMembersProvider
    extends
        $FunctionalProvider<List<UserModel>, List<UserModel>, List<UserModel>>
    with $Provider<List<UserModel>> {
  const FormerBubbleMembersProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'formerBubbleMembersProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$formerBubbleMembersHash();

  @$internal
  @override
  $ProviderElement<List<UserModel>> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<UserModel> create(Ref ref) {
    return formerBubbleMembers(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<UserModel> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<UserModel>>(value),
    );
  }
}

String _$formerBubbleMembersHash() =>
    r'67b3e77ef3e7a41418aaa68c0493f6dfd3cf73b6';

@ProviderFor(selectedMemberIds)
const selectedMemberIdsProvider = SelectedMemberIdsProvider._();

final class SelectedMemberIdsProvider
    extends $FunctionalProvider<List<String>, List<String>, List<String>>
    with $Provider<List<String>> {
  const SelectedMemberIdsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'selectedMemberIdsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$selectedMemberIdsHash();

  @$internal
  @override
  $ProviderElement<List<String>> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<String> create(Ref ref) {
    return selectedMemberIds(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<String> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<String>>(value),
    );
  }
}

String _$selectedMemberIdsHash() => r'a31ef4c6ac0cdbe0333cc6530a63987a2fd0e2cd';

@ProviderFor(selectedMembersCount)
const selectedMembersCountProvider = SelectedMembersCountProvider._();

final class SelectedMembersCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const SelectedMembersCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'selectedMembersCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$selectedMembersCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return selectedMembersCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$selectedMembersCountHash() =>
    r'22fc69267e2d7f4ff4d327f9a5af2111fb0bc8d5';

@ProviderFor(totalMembersCount)
const totalMembersCountProvider = TotalMembersCountProvider._();

final class TotalMembersCountProvider extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const TotalMembersCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'totalMembersCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$totalMembersCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return totalMembersCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$totalMembersCountHash() => r'2b8bcd83be2fa04dec1ce4217c508c7bb9387fb3';

@ProviderFor(areAllMembersSelected)
const areAllMembersSelectedProvider = AreAllMembersSelectedProvider._();

final class AreAllMembersSelectedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const AreAllMembersSelectedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'areAllMembersSelectedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$areAllMembersSelectedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return areAllMembersSelected(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$areAllMembersSelectedHash() =>
    r'52242c910f6d0870b3b1fdbb0e388f26fcc17c07';

@ProviderFor(areNoMembersSelected)
const areNoMembersSelectedProvider = AreNoMembersSelectedProvider._();

final class AreNoMembersSelectedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const AreNoMembersSelectedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'areNoMembersSelectedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$areNoMembersSelectedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return areNoMembersSelected(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$areNoMembersSelectedHash() =>
    r'6b7c5ad6032ff3134bba255c775f504d9296cc56';

@ProviderFor(pendingDialogBubbleIds)
const pendingDialogBubbleIdsProvider = PendingDialogBubbleIdsProvider._();

final class PendingDialogBubbleIdsProvider
    extends $FunctionalProvider<List<String>, List<String>, List<String>>
    with $Provider<List<String>> {
  const PendingDialogBubbleIdsProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'pendingDialogBubbleIdsProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$pendingDialogBubbleIdsHash();

  @$internal
  @override
  $ProviderElement<List<String>> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  List<String> create(Ref ref) {
    return pendingDialogBubbleIds(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<String> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<String>>(value),
    );
  }
}

String _$pendingDialogBubbleIdsHash() =>
    r'6fa3e6a6ca15e6e7d2561a1859f2dc39e16d3c33';

@ProviderFor(currentBubbleId)
const currentBubbleIdProvider = CurrentBubbleIdProvider._();

final class CurrentBubbleIdProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentBubbleIdProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentBubbleIdProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentBubbleIdHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentBubbleId(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentBubbleIdHash() => r'd7e6b352308d782166564c5a4e6fc0e4701127f4';

@ProviderFor(currentBubbleName)
const currentBubbleNameProvider = CurrentBubbleNameProvider._();

final class CurrentBubbleNameProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentBubbleNameProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentBubbleNameProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentBubbleNameHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentBubbleName(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentBubbleNameHash() => r'0e63ea66117794eed265f8d7201c079efe3eb608';

@ProviderFor(hasFriendSelectionError)
const hasFriendSelectionErrorProvider = HasFriendSelectionErrorProvider._();

final class HasFriendSelectionErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasFriendSelectionErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasFriendSelectionErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasFriendSelectionErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasFriendSelectionError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasFriendSelectionErrorHash() =>
    r'a1c28f59e25a6e7dc8f006d16bf1bd1b5328d6a9';

@ProviderFor(friendSelectionError)
const friendSelectionErrorProvider = FriendSelectionErrorProvider._();

final class FriendSelectionErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const FriendSelectionErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'friendSelectionErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$friendSelectionErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return friendSelectionError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$friendSelectionErrorHash() =>
    r'2f593fa8be71fd757765515f699e4fa7ebfd5c3c';
