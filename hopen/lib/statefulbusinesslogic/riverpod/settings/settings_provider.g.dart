// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Settings controller using Riverpod 3.0 AsyncNotifier pattern
/// Manages language, activity status, privacy, and security settings

@ProviderFor(SettingsController)
const settingsControllerProvider = SettingsControllerProvider._();

/// Settings controller using Riverpod 3.0 AsyncNotifier pattern
/// Manages language, activity status, privacy, and security settings
final class SettingsControllerProvider
    extends $AsyncNotifierProvider<SettingsController, SettingsState> {
  /// Settings controller using Riverpod 3.0 AsyncNotifier pattern
  /// Manages language, activity status, privacy, and security settings
  const SettingsControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'settingsControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$settingsControllerHash();

  @$internal
  @override
  SettingsController create() => SettingsController();
}

String _$settingsControllerHash() =>
    r'd5bc7c0e9b5fb0693c857ef3fb03038deacb1fc4';

/// Settings controller using Riverpod 3.0 AsyncNotifier pattern
/// Manages language, activity status, privacy, and security settings

abstract class _$SettingsController extends $AsyncNotifier<SettingsState> {
  FutureOr<SettingsState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<SettingsState>, SettingsState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<SettingsState>, SettingsState>,
              AsyncValue<SettingsState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing settings state properties
// Language-related providers

@ProviderFor(currentLanguage)
const currentLanguageProvider = CurrentLanguageProvider._();

/// Convenience providers for accessing settings state properties
// Language-related providers

final class CurrentLanguageProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  /// Convenience providers for accessing settings state properties
  // Language-related providers
  const CurrentLanguageProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentLanguageProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentLanguageHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentLanguage(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentLanguageHash() => r'75e6c9eb067f40ee9eb322f40c8d4da62e852a41';

@ProviderFor(currentLocale)
const currentLocaleProvider = CurrentLocaleProvider._();

final class CurrentLocaleProvider
    extends $FunctionalProvider<Locale?, Locale?, Locale?>
    with $Provider<Locale?> {
  const CurrentLocaleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentLocaleProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentLocaleHash();

  @$internal
  @override
  $ProviderElement<Locale?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Locale? create(Ref ref) {
    return currentLocale(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Locale? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Locale?>(value),
    );
  }
}

String _$currentLocaleHash() => r'069c168ea8fdfc363cf99da0cf247c7ade595f31';

@ProviderFor(availableLanguages)
const availableLanguagesProvider = AvailableLanguagesProvider._();

final class AvailableLanguagesProvider
    extends
        $FunctionalProvider<
          Map<String, String>,
          Map<String, String>,
          Map<String, String>
        >
    with $Provider<Map<String, String>> {
  const AvailableLanguagesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'availableLanguagesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$availableLanguagesHash();

  @$internal
  @override
  $ProviderElement<Map<String, String>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  Map<String, String> create(Ref ref) {
    return availableLanguages(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Map<String, String> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Map<String, String>>(value),
    );
  }
}

String _$availableLanguagesHash() =>
    r'09b9021a678ab03f049710f9ab17bf46d8a5d7b4';

@ProviderFor(currentLanguageDisplayName)
const currentLanguageDisplayNameProvider =
    CurrentLanguageDisplayNameProvider._();

final class CurrentLanguageDisplayNameProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentLanguageDisplayNameProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentLanguageDisplayNameProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentLanguageDisplayNameHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentLanguageDisplayName(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentLanguageDisplayNameHash() =>
    r'8e1af3483a907fbce8a307516b1d46a79817c951';

@ProviderFor(isLanguageLoading)
const isLanguageLoadingProvider = IsLanguageLoadingProvider._();

final class IsLanguageLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsLanguageLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isLanguageLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isLanguageLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isLanguageLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isLanguageLoadingHash() => r'bb5ce6bb29bca1c30959dc8044d0619fdeba253c';

@ProviderFor(isLanguageLoaded)
const isLanguageLoadedProvider = IsLanguageLoadedProvider._();

final class IsLanguageLoadedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsLanguageLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isLanguageLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isLanguageLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isLanguageLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isLanguageLoadedHash() => r'be43528b16fe65867bb219545b3aedc6026dccf5';

@ProviderFor(currentActivityStatus)
const currentActivityStatusProvider = CurrentActivityStatusProvider._();

final class CurrentActivityStatusProvider
    extends
        $FunctionalProvider<
          ActivityStatusModel?,
          ActivityStatusModel?,
          ActivityStatusModel?
        >
    with $Provider<ActivityStatusModel?> {
  const CurrentActivityStatusProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentActivityStatusProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentActivityStatusHash();

  @$internal
  @override
  $ProviderElement<ActivityStatusModel?> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ActivityStatusModel? create(Ref ref) {
    return currentActivityStatus(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ActivityStatusModel? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ActivityStatusModel?>(value),
    );
  }
}

String _$currentActivityStatusHash() =>
    r'974462061ee7d36b2bafc5bf83ffaab51178ef2a';

@ProviderFor(isActivityStatusVisible)
const isActivityStatusVisibleProvider = IsActivityStatusVisibleProvider._();

final class IsActivityStatusVisibleProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsActivityStatusVisibleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isActivityStatusVisibleProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isActivityStatusVisibleHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isActivityStatusVisible(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isActivityStatusVisibleHash() =>
    r'b669a9e328598baf516a4ab53b7eb0052dde1452';

@ProviderFor(isUserOnline)
const isUserOnlineProvider = IsUserOnlineProvider._();

final class IsUserOnlineProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsUserOnlineProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isUserOnlineProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isUserOnlineHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isUserOnline(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isUserOnlineHash() => r'd1e734f62fdff98d3bdeb761114b7004ba6bf0c6';

@ProviderFor(currentUserStatus)
const currentUserStatusProvider = CurrentUserStatusProvider._();

final class CurrentUserStatusProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  const CurrentUserStatusProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentUserStatusProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentUserStatusHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return currentUserStatus(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$currentUserStatusHash() => r'34d13e752192fa72bf9b843b137d2638bc1c7e9e';

@ProviderFor(customUserStatus)
const customUserStatusProvider = CustomUserStatusProvider._();

final class CustomUserStatusProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CustomUserStatusProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'customUserStatusProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$customUserStatusHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return customUserStatus(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$customUserStatusHash() => r'7ba45da09012b1baef5cb6b888f53f5288b8515a';

@ProviderFor(lastSeenTime)
const lastSeenTimeProvider = LastSeenTimeProvider._();

final class LastSeenTimeProvider
    extends $FunctionalProvider<DateTime?, DateTime?, DateTime?>
    with $Provider<DateTime?> {
  const LastSeenTimeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'lastSeenTimeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$lastSeenTimeHash();

  @$internal
  @override
  $ProviderElement<DateTime?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  DateTime? create(Ref ref) {
    return lastSeenTime(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DateTime? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DateTime?>(value),
    );
  }
}

String _$lastSeenTimeHash() => r'4fb0c905ba5cb9ab7b358cb15ac48ad037ef5aaa';

@ProviderFor(isSettingsLoading)
const isSettingsLoadingProvider = IsSettingsLoadingProvider._();

final class IsSettingsLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsSettingsLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSettingsLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSettingsLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSettingsLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSettingsLoadingHash() => r'138622f7f63bd26c0f6176d0157cd4eae2f86160';

@ProviderFor(isSettingsLoaded)
const isSettingsLoadedProvider = IsSettingsLoadedProvider._();

final class IsSettingsLoadedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsSettingsLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSettingsLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSettingsLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSettingsLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSettingsLoadedHash() => r'9c407198783c391f35ef2f9a76f52a605afa5f20';

@ProviderFor(hasSettingsError)
const hasSettingsErrorProvider = HasSettingsErrorProvider._();

final class HasSettingsErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasSettingsErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasSettingsErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasSettingsErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasSettingsError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasSettingsErrorHash() => r'55e8b4f045ab3fe3bce4161a08028bc149631cf4';

@ProviderFor(hasLanguageError)
const hasLanguageErrorProvider = HasLanguageErrorProvider._();

final class HasLanguageErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasLanguageErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasLanguageErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasLanguageErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasLanguageError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasLanguageErrorHash() => r'735ffd7c55377fe0020656abd0e128c86822cb0e';

@ProviderFor(hasActivityStatusError)
const hasActivityStatusErrorProvider = HasActivityStatusErrorProvider._();

final class HasActivityStatusErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasActivityStatusErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasActivityStatusErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasActivityStatusErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasActivityStatusError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasActivityStatusErrorHash() =>
    r'fbf1148f27322976f2376e8b718b4fd528b5b118';

@ProviderFor(settingsError)
const settingsErrorProvider = SettingsErrorProvider._();

final class SettingsErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const SettingsErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'settingsErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$settingsErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return settingsError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$settingsErrorHash() => r'198129a3a5efb21e4cc88767e8f9197bd6633918';

@ProviderFor(languageError)
const languageErrorProvider = LanguageErrorProvider._();

final class LanguageErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const LanguageErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'languageErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$languageErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return languageError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$languageErrorHash() => r'b58922d7cf75cecfef071fb50a538a346d0bf5ec';

@ProviderFor(activityStatusError)
const activityStatusErrorProvider = ActivityStatusErrorProvider._();

final class ActivityStatusErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const ActivityStatusErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'activityStatusErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$activityStatusErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return activityStatusError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$activityStatusErrorHash() =>
    r'74df04079be0be2de980237f261080cfdc82f8c9';
