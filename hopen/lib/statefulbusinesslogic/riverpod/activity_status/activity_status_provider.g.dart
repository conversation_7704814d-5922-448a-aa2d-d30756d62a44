// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_status_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Activity status controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(ActivityStatusController)
const activityStatusControllerProvider = ActivityStatusControllerProvider._();

/// Activity status controller using Riverpod 3.0 AsyncNotifier pattern
final class ActivityStatusControllerProvider
    extends
        $AsyncNotifierProvider<ActivityStatusController, ActivityStatusState> {
  /// Activity status controller using Riverpod 3.0 AsyncNotifier pattern
  const ActivityStatusControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'activityStatusControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$activityStatusControllerHash();

  @$internal
  @override
  ActivityStatusController create() => ActivityStatusController();
}

String _$activityStatusControllerHash() =>
    r'09ff97ff94200c7fc3f9d18a758511f329300d2b';

/// Activity status controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$ActivityStatusController
    extends $AsyncNotifier<ActivityStatusState> {
  FutureOr<ActivityStatusState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<ActivityStatusState>, ActivityStatusState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<ActivityStatusState>, ActivityStatusState>,
              AsyncValue<ActivityStatusState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing activity status state properties

@ProviderFor(isActivityStatusLoading)
const isActivityStatusLoadingProvider = IsActivityStatusLoadingProvider._();

/// Convenience providers for accessing activity status state properties

final class IsActivityStatusLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing activity status state properties
  const IsActivityStatusLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isActivityStatusLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isActivityStatusLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isActivityStatusLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isActivityStatusLoadingHash() =>
    r'637ed4877565a5dcebdfce15617f0c83cee707bb';

@ProviderFor(isActivityStatusLoaded)
const isActivityStatusLoadedProvider = IsActivityStatusLoadedProvider._();

final class IsActivityStatusLoadedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsActivityStatusLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isActivityStatusLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isActivityStatusLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isActivityStatusLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isActivityStatusLoadedHash() =>
    r'd3f6553a8ffbad69e190d811e4757ca766dc7f9f';

@ProviderFor(isActivityStatusUpdating)
const isActivityStatusUpdatingProvider = IsActivityStatusUpdatingProvider._();

final class IsActivityStatusUpdatingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsActivityStatusUpdatingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isActivityStatusUpdatingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isActivityStatusUpdatingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isActivityStatusUpdating(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isActivityStatusUpdatingHash() =>
    r'5f44b0a1742e7c4a656baf693d8e4683bbe7e500';

@ProviderFor(currentActivityStatus)
const currentActivityStatusProvider = CurrentActivityStatusProvider._();

final class CurrentActivityStatusProvider
    extends
        $FunctionalProvider<
          ActivityStatusModel?,
          ActivityStatusModel?,
          ActivityStatusModel?
        >
    with $Provider<ActivityStatusModel?> {
  const CurrentActivityStatusProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentActivityStatusProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentActivityStatusHash();

  @$internal
  @override
  $ProviderElement<ActivityStatusModel?> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  ActivityStatusModel? create(Ref ref) {
    return currentActivityStatus(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ActivityStatusModel? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ActivityStatusModel?>(value),
    );
  }
}

String _$currentActivityStatusHash() =>
    r'465e5a2465a217efb831149cd45b44de22c50e53';

@ProviderFor(isActivityStatusVisible)
const isActivityStatusVisibleProvider = IsActivityStatusVisibleProvider._();

final class IsActivityStatusVisibleProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsActivityStatusVisibleProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isActivityStatusVisibleProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isActivityStatusVisibleHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isActivityStatusVisible(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isActivityStatusVisibleHash() =>
    r'5b8b5aab24ee507261b2bd4d63b459128a3bd4b5';

@ProviderFor(isUserOnline)
const isUserOnlineProvider = IsUserOnlineProvider._();

final class IsUserOnlineProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsUserOnlineProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isUserOnlineProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isUserOnlineHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isUserOnline(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isUserOnlineHash() => r'dee415fbf926caabc8ab2504370a1fac7457f898';

@ProviderFor(currentUserStatus)
const currentUserStatusProvider = CurrentUserStatusProvider._();

final class CurrentUserStatusProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  const CurrentUserStatusProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentUserStatusProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentUserStatusHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return currentUserStatus(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$currentUserStatusHash() => r'f6d1c0726070cc601e9f8ba44f197e056cc31966';

@ProviderFor(customUserStatus)
const customUserStatusProvider = CustomUserStatusProvider._();

final class CustomUserStatusProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CustomUserStatusProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'customUserStatusProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$customUserStatusHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return customUserStatus(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$customUserStatusHash() => r'48ad2a42c7227580d68878d671d25a855f4cbb0e';

@ProviderFor(lastSeenTime)
const lastSeenTimeProvider = LastSeenTimeProvider._();

final class LastSeenTimeProvider
    extends $FunctionalProvider<DateTime?, DateTime?, DateTime?>
    with $Provider<DateTime?> {
  const LastSeenTimeProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'lastSeenTimeProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$lastSeenTimeHash();

  @$internal
  @override
  $ProviderElement<DateTime?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  DateTime? create(Ref ref) {
    return lastSeenTime(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(DateTime? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<DateTime?>(value),
    );
  }
}

String _$lastSeenTimeHash() => r'7d0e46c63b34abed4280daf8d2cc4fbb4ca18c08';

@ProviderFor(hasActivityStatusError)
const hasActivityStatusErrorProvider = HasActivityStatusErrorProvider._();

final class HasActivityStatusErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasActivityStatusErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasActivityStatusErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasActivityStatusErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasActivityStatusError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasActivityStatusErrorHash() =>
    r'7e7c9a5f8f557a7ac3f11057d442379252b5f400';

@ProviderFor(activityStatusError)
const activityStatusErrorProvider = ActivityStatusErrorProvider._();

final class ActivityStatusErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const ActivityStatusErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'activityStatusErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$activityStatusErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return activityStatusError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$activityStatusErrorHash() =>
    r'1e89a24284157dfae03cf7d3059d10ccbaf3661f';
