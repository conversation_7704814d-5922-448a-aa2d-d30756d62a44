// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Report controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since reporting is a temporary operation

@ProviderFor(ReportController)
const reportControllerProvider = ReportControllerProvider._();

/// Report controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since reporting is a temporary operation
final class ReportControllerProvider
    extends $AsyncNotifierProvider<ReportController, ReportState> {
  /// Report controller using Riverpod 3.0 AsyncNotifier pattern
  /// Uses autoDispose since reporting is a temporary operation
  const ReportControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'reportControllerProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$reportControllerHash();

  @$internal
  @override
  ReportController create() => ReportController();
}

String _$reportControllerHash() => r'22304fb122857fda80f12b7aaca2169780812f9b';

/// Report controller using Riverpod 3.0 AsyncNotifier pattern
/// Uses autoDispose since reporting is a temporary operation

abstract class _$ReportController extends $AsyncNotifier<ReportState> {
  FutureOr<ReportState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<ReportState>, ReportState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<ReportState>, ReportState>,
              AsyncValue<ReportState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing report state properties

@ProviderFor(isReportLoading)
const isReportLoadingProvider = IsReportLoadingProvider._();

/// Convenience providers for accessing report state properties

final class IsReportLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing report state properties
  const IsReportLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isReportLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isReportLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isReportLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isReportLoadingHash() => r'bd05afb249634719fe1038c8d0f835b0554bd4e8';

@ProviderFor(isReportSuccess)
const isReportSuccessProvider = IsReportSuccessProvider._();

final class IsReportSuccessProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsReportSuccessProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isReportSuccessProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isReportSuccessHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isReportSuccess(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isReportSuccessHash() => r'588eb66293c0b982307b93db780edb2909037ffb';

@ProviderFor(hasReportError)
const hasReportErrorProvider = HasReportErrorProvider._();

final class HasReportErrorProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasReportErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasReportErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasReportErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasReportError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasReportErrorHash() => r'7d09d792b549fcd8e4fc5d3fd45e435280d85d41';

@ProviderFor(reportSuccessMessage)
const reportSuccessMessageProvider = ReportSuccessMessageProvider._();

final class ReportSuccessMessageProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const ReportSuccessMessageProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'reportSuccessMessageProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$reportSuccessMessageHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return reportSuccessMessage(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$reportSuccessMessageHash() =>
    r'4996ee619a9b8dfb816d93999419b63f001f04be';

@ProviderFor(currentReportOperation)
const currentReportOperationProvider = CurrentReportOperationProvider._();

final class CurrentReportOperationProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentReportOperationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentReportOperationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentReportOperationHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentReportOperation(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentReportOperationHash() =>
    r'2445592d201013e59f1104c638a750ce2f4f60ff';

@ProviderFor(reportError)
const reportErrorProvider = ReportErrorProvider._();

final class ReportErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const ReportErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'reportErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$reportErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return reportError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$reportErrorHash() => r'71c41023ee2e32189583dd6d37654faa779654e2';
