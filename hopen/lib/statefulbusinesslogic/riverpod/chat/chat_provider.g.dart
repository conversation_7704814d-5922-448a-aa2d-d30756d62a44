// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Chat controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(ChatController)
const chatControllerProvider = ChatControllerProvider._();

/// Chat controller using Riverpod 3.0 AsyncNotifier pattern
final class ChatControllerProvider
    extends $AsyncNotifierProvider<ChatController, ChatState> {
  /// Chat controller using Riverpod 3.0 AsyncNotifier pattern
  const Chat<PERSON>ontrollerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'chatControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$chatControllerHash();

  @$internal
  @override
  ChatController create() => ChatController();
}

String _$chatControllerHash() => r'25201c1d3acc9eb5c488c5cc62134144b005c9f3';

/// Chat controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$ChatController extends $AsyncNotifier<ChatState> {
  FutureOr<ChatState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<ChatState>, ChatState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<ChatState>, ChatState>,
              AsyncValue<ChatState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing chat state properties

@ProviderFor(isChatLoaded)
const isChatLoadedProvider = IsChatLoadedProvider._();

/// Convenience providers for accessing chat state properties

final class IsChatLoadedProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing chat state properties
  const IsChatLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isChatLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isChatLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isChatLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isChatLoadedHash() => r'de41c592848c2e0ccc832b4696276d5a3aa94a66';

@ProviderFor(chatMessages)
const chatMessagesProvider = ChatMessagesProvider._();

final class ChatMessagesProvider
    extends
        $FunctionalProvider<
          List<ChatMessage>,
          List<ChatMessage>,
          List<ChatMessage>
        >
    with $Provider<List<ChatMessage>> {
  const ChatMessagesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'chatMessagesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$chatMessagesHash();

  @$internal
  @override
  $ProviderElement<List<ChatMessage>> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  List<ChatMessage> create(Ref ref) {
    return chatMessages(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(List<ChatMessage> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<List<ChatMessage>>(value),
    );
  }
}

String _$chatMessagesHash() => r'f274f4555c9cd0f8a95411e1cf66c047d6337f10';

@ProviderFor(hasMessages)
const hasMessagesProvider = HasMessagesProvider._();

final class HasMessagesProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasMessagesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasMessagesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasMessagesHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasMessages(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasMessagesHash() => r'a8cd583c6d64e6016957194fea30c2900e3e65b0';

@ProviderFor(latestMessage)
const latestMessageProvider = LatestMessageProvider._();

final class LatestMessageProvider
    extends $FunctionalProvider<ChatMessage?, ChatMessage?, ChatMessage?>
    with $Provider<ChatMessage?> {
  const LatestMessageProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'latestMessageProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$latestMessageHash();

  @$internal
  @override
  $ProviderElement<ChatMessage?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  ChatMessage? create(Ref ref) {
    return latestMessage(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(ChatMessage? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<ChatMessage?>(value),
    );
  }
}

String _$latestMessageHash() => r'94e764aba750242838d83024c1a06ce9884e31c9';

@ProviderFor(unreadMessagesCount)
const unreadMessagesCountProvider = UnreadMessagesCountProvider._();

final class UnreadMessagesCountProvider
    extends $FunctionalProvider<int, int, int>
    with $Provider<int> {
  const UnreadMessagesCountProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'unreadMessagesCountProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$unreadMessagesCountHash();

  @$internal
  @override
  $ProviderElement<int> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  int create(Ref ref) {
    return unreadMessagesCount(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$unreadMessagesCountHash() =>
    r'ba35f97010557369732c95cc68431011b33880db';

@ProviderFor(isWebSocketConnected)
const isWebSocketConnectedProvider = IsWebSocketConnectedProvider._();

final class IsWebSocketConnectedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsWebSocketConnectedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isWebSocketConnectedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isWebSocketConnectedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isWebSocketConnected(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isWebSocketConnectedHash() =>
    r'd5c69beb1ebb3da5fa901a0662be4fc2efe8f15a';

@ProviderFor(isTyping)
const isTypingProvider = IsTypingProvider._();

final class IsTypingProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsTypingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isTypingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isTypingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isTyping(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isTypingHash() => r'cb3a1db27eedda07aceb456a33ba8eff7aae02ad';

@ProviderFor(typingUsers)
const typingUsersProvider = TypingUsersProvider._();

final class TypingUsersProvider
    extends $FunctionalProvider<Set<String>, Set<String>, Set<String>>
    with $Provider<Set<String>> {
  const TypingUsersProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'typingUsersProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$typingUsersHash();

  @$internal
  @override
  $ProviderElement<Set<String>> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Set<String> create(Ref ref) {
    return typingUsers(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Set<String> value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Set<String>>(value),
    );
  }
}

String _$typingUsersHash() => r'7bc3a1cb3c049636671efe6f747cfa5aeba688ad';

@ProviderFor(isRecording)
const isRecordingProvider = IsRecordingProvider._();

final class IsRecordingProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsRecordingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isRecordingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isRecordingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isRecording(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isRecordingHash() => r'd607c9e8825ad9f250aff2295c26b63f5acb51d4';

@ProviderFor(recordDuration)
const recordDurationProvider = RecordDurationProvider._();

final class RecordDurationProvider
    extends $FunctionalProvider<Duration, Duration, Duration>
    with $Provider<Duration> {
  const RecordDurationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'recordDurationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$recordDurationHash();

  @$internal
  @override
  $ProviderElement<Duration> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Duration create(Ref ref) {
    return recordDuration(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Duration value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Duration>(value),
    );
  }
}

String _$recordDurationHash() => r'4d52992e9418f95f6784919848df53baa9b1f78a';

@ProviderFor(formattedRecordDuration)
const formattedRecordDurationProvider = FormattedRecordDurationProvider._();

final class FormattedRecordDurationProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  const FormattedRecordDurationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'formattedRecordDurationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$formattedRecordDurationHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return formattedRecordDuration(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$formattedRecordDurationHash() =>
    r'01d43f17dc667ba392a656528ded9ca1c0730407';

@ProviderFor(hasMoreMessages)
const hasMoreMessagesProvider = HasMoreMessagesProvider._();

final class HasMoreMessagesProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasMoreMessagesProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasMoreMessagesProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasMoreMessagesHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasMoreMessages(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasMoreMessagesHash() => r'd098f5e95e4cff31cc52f6d813a621fc5b84b01b';

@ProviderFor(isSearchActive)
const isSearchActiveProvider = IsSearchActiveProvider._();

final class IsSearchActiveProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsSearchActiveProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isSearchActiveProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isSearchActiveHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isSearchActive(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isSearchActiveHash() => r'faf33904789a5645d81e93a838dffeb8e9b58375';

@ProviderFor(currentSearchQuery)
const currentSearchQueryProvider = CurrentSearchQueryProvider._();

final class CurrentSearchQueryProvider
    extends $FunctionalProvider<String, String, String>
    with $Provider<String> {
  const CurrentSearchQueryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentSearchQueryProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentSearchQueryHash();

  @$internal
  @override
  $ProviderElement<String> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String create(Ref ref) {
    return currentSearchQuery(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String>(value),
    );
  }
}

String _$currentSearchQueryHash() =>
    r'930aca1638ae9f4947e359ba19665caaafe87b5d';

@ProviderFor(isChatLoading)
const isChatLoadingProvider = IsChatLoadingProvider._();

final class IsChatLoadingProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsChatLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isChatLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isChatLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isChatLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isChatLoadingHash() => r'92280ad3f476854b6d5679eab7fd0978e5965484';

@ProviderFor(hasChatError)
const hasChatErrorProvider = HasChatErrorProvider._();

final class HasChatErrorProvider extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasChatErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasChatErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasChatErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasChatError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasChatErrorHash() => r'e8e6c340270ef625640c1d145d26b85172dec803';

@ProviderFor(chatError)
const chatErrorProvider = ChatErrorProvider._();

final class ChatErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const ChatErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'chatErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$chatErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return chatError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$chatErrorHash() => r'dec7fcffaf43dc8473d8df8b5c5ba8de9547182d';

@ProviderFor(chatInfo)
const chatInfoProvider = ChatInfoProvider._();

final class ChatInfoProvider
    extends
        $FunctionalProvider<
          Map<String, dynamic>?,
          Map<String, dynamic>?,
          Map<String, dynamic>?
        >
    with $Provider<Map<String, dynamic>?> {
  const ChatInfoProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'chatInfoProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$chatInfoHash();

  @$internal
  @override
  $ProviderElement<Map<String, dynamic>?> $createElement(
    $ProviderPointer pointer,
  ) => $ProviderElement(pointer);

  @override
  Map<String, dynamic>? create(Ref ref) {
    return chatInfo(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Map<String, dynamic>? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Map<String, dynamic>?>(value),
    );
  }
}

String _$chatInfoHash() => r'25219fb9fe0d6c7152fc9e98b94ba1f72167a3f7';
