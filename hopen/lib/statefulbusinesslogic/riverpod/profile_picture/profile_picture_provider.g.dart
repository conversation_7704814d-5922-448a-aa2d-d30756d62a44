// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_picture_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, type=warning
/// Profile picture controller using Riverpod 3.0 AsyncNotifier pattern

@ProviderFor(ProfilePictureController)
const profilePictureControllerProvider = ProfilePictureControllerProvider._();

/// Profile picture controller using Riverpod 3.0 AsyncNotifier pattern
final class ProfilePictureControllerProvider
    extends
        $AsyncNotifierProvider<ProfilePictureController, ProfilePictureState> {
  /// Profile picture controller using Riverpod 3.0 AsyncNotifier pattern
  const ProfilePictureControllerProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePictureControllerProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePictureControllerHash();

  @$internal
  @override
  ProfilePictureController create() => ProfilePictureController();
}

String _$profilePictureControllerHash() =>
    r'65fd7a0b1e6ce3b99dfae75d449b53b81ab5c41c';

/// Profile picture controller using Riverpod 3.0 AsyncNotifier pattern

abstract class _$ProfilePictureController
    extends $AsyncNotifier<ProfilePictureState> {
  FutureOr<ProfilePictureState> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<ProfilePictureState>, ProfilePictureState>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<ProfilePictureState>, ProfilePictureState>,
              AsyncValue<ProfilePictureState>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

/// Convenience providers for accessing profile picture state properties

@ProviderFor(isProfilePictureLoading)
const isProfilePictureLoadingProvider = IsProfilePictureLoadingProvider._();

/// Convenience providers for accessing profile picture state properties

final class IsProfilePictureLoadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  /// Convenience providers for accessing profile picture state properties
  const IsProfilePictureLoadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isProfilePictureLoadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isProfilePictureLoadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isProfilePictureLoading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isProfilePictureLoadingHash() =>
    r'6374a39ff1eef4355f8944f0dd67a0efeb135ff7';

@ProviderFor(isProfilePictureLoaded)
const isProfilePictureLoadedProvider = IsProfilePictureLoadedProvider._();

final class IsProfilePictureLoadedProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsProfilePictureLoadedProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isProfilePictureLoadedProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isProfilePictureLoadedHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isProfilePictureLoaded(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isProfilePictureLoadedHash() =>
    r'29f0776481bcd8225f9fa5413bbb94371e476990';

@ProviderFor(isProfilePictureUploading)
const isProfilePictureUploadingProvider = IsProfilePictureUploadingProvider._();

final class IsProfilePictureUploadingProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsProfilePictureUploadingProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isProfilePictureUploadingProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isProfilePictureUploadingHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isProfilePictureUploading(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isProfilePictureUploadingHash() =>
    r'e1c4e9f56d527c23353893265d82749f41965872';

@ProviderFor(isProfilePictureProcessedLocally)
const isProfilePictureProcessedLocallyProvider =
    IsProfilePictureProcessedLocallyProvider._();

final class IsProfilePictureProcessedLocallyProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsProfilePictureProcessedLocallyProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isProfilePictureProcessedLocallyProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isProfilePictureProcessedLocallyHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isProfilePictureProcessedLocally(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isProfilePictureProcessedLocallyHash() =>
    r'396c5effbcf57b57dfabbfeb8d22ab9044517f4f';

@ProviderFor(isProfilePictureSuccess)
const isProfilePictureSuccessProvider = IsProfilePictureSuccessProvider._();

final class IsProfilePictureSuccessProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsProfilePictureSuccessProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isProfilePictureSuccessProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isProfilePictureSuccessHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isProfilePictureSuccess(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isProfilePictureSuccessHash() =>
    r'580c68471e70a08033af1c3bbc2a6fe6c5c1afdd';

@ProviderFor(isProfilePictureCancelled)
const isProfilePictureCancelledProvider = IsProfilePictureCancelledProvider._();

final class IsProfilePictureCancelledProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const IsProfilePictureCancelledProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'isProfilePictureCancelledProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$isProfilePictureCancelledHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return isProfilePictureCancelled(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$isProfilePictureCancelledHash() =>
    r'2f919ec7c643cf4c9515bc550e6ba9bbc1aa9c3b';

@ProviderFor(profilePictureImageData)
const profilePictureImageDataProvider = ProfilePictureImageDataProvider._();

final class ProfilePictureImageDataProvider
    extends $FunctionalProvider<Uint8List?, Uint8List?, Uint8List?>
    with $Provider<Uint8List?> {
  const ProfilePictureImageDataProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePictureImageDataProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePictureImageDataHash();

  @$internal
  @override
  $ProviderElement<Uint8List?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  Uint8List? create(Ref ref) {
    return profilePictureImageData(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(Uint8List? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<Uint8List?>(value),
    );
  }
}

String _$profilePictureImageDataHash() =>
    r'7da4ea9460af6a98a7d68397449239598320f7fc';

@ProviderFor(profilePictureUrl)
const profilePictureUrlProvider = ProfilePictureUrlProvider._();

final class ProfilePictureUrlProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const ProfilePictureUrlProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePictureUrlProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePictureUrlHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return profilePictureUrl(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$profilePictureUrlHash() => r'd65d3fe27cd8933195991effb71a5c6bb48a287d';

@ProviderFor(localImagePath)
const localImagePathProvider = LocalImagePathProvider._();

final class LocalImagePathProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const LocalImagePathProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'localImagePathProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$localImagePathHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return localImagePath(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$localImagePathHash() => r'798153271705ad33cf7f50685026fb4424e89ee5';

@ProviderFor(uploadProgress)
const uploadProgressProvider = UploadProgressProvider._();

final class UploadProgressProvider
    extends $FunctionalProvider<double, double, double>
    with $Provider<double> {
  const UploadProgressProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'uploadProgressProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$uploadProgressHash();

  @$internal
  @override
  $ProviderElement<double> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  double create(Ref ref) {
    return uploadProgress(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(double value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<double>(value),
    );
  }
}

String _$uploadProgressHash() => r'575a3a9be01ba180ed6d4fae47c2b04f80e3014e';

@ProviderFor(currentOperation)
const currentOperationProvider = CurrentOperationProvider._();

final class CurrentOperationProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const CurrentOperationProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'currentOperationProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$currentOperationHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return currentOperation(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$currentOperationHash() => r'ff786579b9ae7c7adb6705daf271507e742227a1';

@ProviderFor(hasProfilePictureError)
const hasProfilePictureErrorProvider = HasProfilePictureErrorProvider._();

final class HasProfilePictureErrorProvider
    extends $FunctionalProvider<bool, bool, bool>
    with $Provider<bool> {
  const HasProfilePictureErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'hasProfilePictureErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$hasProfilePictureErrorHash();

  @$internal
  @override
  $ProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  bool create(Ref ref) {
    return hasProfilePictureError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }
}

String _$hasProfilePictureErrorHash() =>
    r'4707ce5df9cadfde0a04608233b9b80c3e1f146f';

@ProviderFor(profilePictureError)
const profilePictureErrorProvider = ProfilePictureErrorProvider._();

final class ProfilePictureErrorProvider
    extends $FunctionalProvider<AppError?, AppError?, AppError?>
    with $Provider<AppError?> {
  const ProfilePictureErrorProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePictureErrorProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePictureErrorHash();

  @$internal
  @override
  $ProviderElement<AppError?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AppError? create(Ref ref) {
    return profilePictureError(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AppError? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AppError?>(value),
    );
  }
}

String _$profilePictureErrorHash() =>
    r'1cbd8cd2e868f8a37911835aef82ec2a13b02c2a';

@ProviderFor(profilePictureSuccessMessage)
const profilePictureSuccessMessageProvider =
    ProfilePictureSuccessMessageProvider._();

final class ProfilePictureSuccessMessageProvider
    extends $FunctionalProvider<String?, String?, String?>
    with $Provider<String?> {
  const ProfilePictureSuccessMessageProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'profilePictureSuccessMessageProvider',
        isAutoDispose: true,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$profilePictureSuccessMessageHash();

  @$internal
  @override
  $ProviderElement<String?> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  String? create(Ref ref) {
    return profilePictureSuccessMessage(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(String? value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<String?>(value),
    );
  }
}

String _$profilePictureSuccessMessageHash() =>
    r'a2963b9f65459c04f4cfb45c4b2790d398cdc55a';
