package com.hopenapp.hopen

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi

/**
 * Audio Session Manager for VoIP Calls
 *
 * Handles audio focus management following Android best practices:
 * - Proper audio focus request/abandon for different Android versions
 * - Audio mode management for VoIP calls
 * - Background and foreground audio session handling
 */
object AudioSessionManager {

    private const val TAG = "AudioSessionManager"

    private var audioManager: AudioManager? = null
    private var audioFocusRequest: AudioFocusRequest? = null
    private var hasAudioFocus = false

    /**
     * Request audio focus for VoIP call
     *
     * @param context Application context
     * @return true if audio focus was granted, false otherwise
     */
    fun requestAudioFocus(context: Context): Boolean {
        audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            requestAudioFocusApi26(context)
        } else {
            requestAudioFocusLegacy()
        }
    }

    /**
     * Abandon audio focus when call ends
     *
     * @param context Application context
     * @return true if audio focus was abandoned successfully, false otherwise
     */
    fun abandonAudioFocus(context: Context): Boolean {
        audioManager = audioManager ?: (context.getSystemService(Context.AUDIO_SERVICE) as AudioManager)

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            abandonAudioFocusApi26()
        } else {
            abandonAudioFocusLegacy()
        }
    }

    /**
     * Set audio mode for VoIP calls
     *
     * @param context Application context
     * @param mode Audio mode: "call", "normal", "background"
     */
    fun setAudioMode(context: Context, mode: String) {
        audioManager = audioManager ?: (context.getSystemService(Context.AUDIO_SERVICE) as AudioManager)

        when (mode) {
            "call" -> {
                audioManager?.mode = AudioManager.MODE_IN_COMMUNICATION
                audioManager?.isSpeakerphoneOn = false
                Log.d(TAG, "Set audio mode to IN_COMMUNICATION")
            }
            "background" -> {
                // Keep communication mode for background calls
                audioManager?.mode = AudioManager.MODE_IN_COMMUNICATION
                Log.d(TAG, "Set audio mode to IN_COMMUNICATION (background)")
            }
            "normal" -> {
                audioManager?.mode = AudioManager.MODE_NORMAL
                audioManager?.isSpeakerphoneOn = false
                Log.d(TAG, "Set audio mode to NORMAL")
            }
            else -> {
                Log.w(TAG, "Unknown audio mode: $mode")
            }
        }
    }

    /**
     * Request audio focus for Android API 26+
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun requestAudioFocusApi26(context: Context): Boolean {
        val audioAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
            .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
            .build()

        audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
            .setAudioAttributes(audioAttributes)
            .setAcceptsDelayedFocusGain(true)
            .setOnAudioFocusChangeListener { focusChange ->
                handleAudioFocusChange(focusChange)
            }
            .build()

        val result = audioManager?.requestAudioFocus(audioFocusRequest!!)
        hasAudioFocus = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED

        Log.d(TAG, "Audio focus request result (API 26+): $result")
        return hasAudioFocus
    }

    /**
     * Request audio focus for Android API < 26
     */
    @Suppress("DEPRECATION")
    private fun requestAudioFocusLegacy(): Boolean {
        val result = audioManager?.requestAudioFocus(
            { focusChange -> handleAudioFocusChange(focusChange) },
            AudioManager.STREAM_VOICE_CALL,
            AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
        )

        hasAudioFocus = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED

        Log.d(TAG, "Audio focus request result (Legacy): $result")
        return hasAudioFocus
    }

    /**
     * Abandon audio focus for Android API 26+
     */
    @RequiresApi(Build.VERSION_CODES.O)
    private fun abandonAudioFocusApi26(): Boolean {
        return if (audioFocusRequest != null) {
            val result = audioManager?.abandonAudioFocusRequest(audioFocusRequest!!)
            hasAudioFocus = false
            audioFocusRequest = null

            Log.d(TAG, "Audio focus abandon result (API 26+): $result")
            result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
        } else {
            Log.w(TAG, "No audio focus request to abandon")
            false
        }
    }

    /**
     * Abandon audio focus for Android API < 26
     */
    @Suppress("DEPRECATION")
    private fun abandonAudioFocusLegacy(): Boolean {
        val result = audioManager?.abandonAudioFocus { focusChange ->
            handleAudioFocusChange(focusChange)
        }

        hasAudioFocus = false

        Log.d(TAG, "Audio focus abandon result (Legacy): $result")
        return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
    }

    /**
     * Handle audio focus changes
     */
    private fun handleAudioFocusChange(focusChange: Int) {
        when (focusChange) {
            AudioManager.AUDIOFOCUS_GAIN -> {
                Log.d(TAG, "Audio focus gained")
                hasAudioFocus = true
            }
            AudioManager.AUDIOFOCUS_LOSS -> {
                Log.d(TAG, "Audio focus lost permanently")
                hasAudioFocus = false
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                Log.d(TAG, "Audio focus lost temporarily")
                hasAudioFocus = false
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                Log.d(TAG, "Audio focus lost temporarily (can duck)")
                // For VoIP calls, we typically don't duck, we pause
                hasAudioFocus = false
            }
            else -> {
                Log.w(TAG, "Unknown audio focus change: $focusChange")
            }
        }
    }
}
