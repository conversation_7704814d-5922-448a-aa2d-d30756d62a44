package com.hopen.app

import android.content.Context
import android.content.Intent
import io.flutter.plugin.common.MethodChannel

class CallPlatformChannel(private val context: Context) {
    companion object {
        const val CHANNEL_NAME = "com.hopen.call/foreground_service"
    }

    fun handleMethodCall(call: MethodChannel.Call, result: MethodChannel.Result) {
        when (call.method) {
            "startService" -> startForegroundService(call, result)
            "updateService" -> updateForegroundService(call, result)
            "stopService" -> stopForegroundService(result)
            else -> result.notImplemented()
        }
    }

    private fun startForegroundService(call: MethodChannel.Call, result: MethodChannel.Result) {
        try {
            val callId = call.argument<String>("callId")
            val callerName = call.argument<String>("callerName")

            val intent = Intent(context, VoIPForegroundService::class.java).apply {
                action = VoIPForegroundService.ACTION_START_CALL
                putExtra(VoIPForegroundService.EXTRA_CALL_ID, callId)
                putExtra(VoIPForegroundService.EXTRA_CALLER_NAME, callerName)
            }

            context.startForegroundService(intent)
            result.success(true)
        } catch (e: Exception) {
            result.error("SERVICE_ERROR", "Failed to start foreground service", e.message)
        }
    }

    private fun updateForegroundService(call: MethodChannel.Call, result: MethodChannel.Result) {
        try {
            val duration = call.argument<String>("duration")

            val intent = Intent(context, VoIPForegroundService::class.java).apply {
                action = VoIPForegroundService.ACTION_UPDATE_CALL
                putExtra(VoIPForegroundService.EXTRA_CALL_DURATION, duration)
            }

            context.startService(intent)
            result.success(true)
        } catch (e: Exception) {
            result.error("UPDATE_ERROR", "Failed to update service", e.message)
        }
    }

    private fun stopForegroundService(result: MethodChannel.Result) {
        try {
            val intent = Intent(context, VoIPForegroundService::class.java).apply {
                action = VoIPForegroundService.ACTION_END_CALL
            }

            context.startService(intent)
            result.success(true)
        } catch (e: Exception) {
            result.error("STOP_ERROR", "Failed to stop service", e.message)
        }
    }
}
