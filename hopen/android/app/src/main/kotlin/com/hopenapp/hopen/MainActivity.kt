package com.hopenapp.hopen

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.core.view.WindowCompat
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.util.Log
import androidx.annotation.NonNull

class MainActivity : FlutterActivity() {
    private val CALL_CHANNEL = "com.hopenapp.hopen/call"
    private val AUDIO_CHANNEL = "com.hopenapp.hopen/audio"
    private var callMethodChannel: MethodChannel? = null
    private var audioMethodChannel: MethodChannel? = null
    private var broadcastReceiver: BroadcastReceiver? = null

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Set up call platform channel following official best practices
        callMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CALL_CHANNEL)
        callMethodChannel?.setMethodCallHandler { call, result ->
            // This method is invoked on the main thread
            when (call.method) {
                "startForegroundService" -> {
                    val callId = call.argument<String>("callId")
                    val callerName = call.argument<String>("callerName") ?: "Unknown"
                    val isVideo = call.argument<Boolean>("isVideo") ?: false

                    if (callId != null) {
                        startVoIPForegroundService(callId, callerName, isVideo)
                        result.success(true)
                    } else {
                        result.error("INVALID_ARGUMENTS", "Call ID is required", null)
                    }
                }
                "stopForegroundService" -> {
                    stopVoIPForegroundService()
                    result.success(true)
                }
                "updateForegroundService" -> {
                    val callId = call.argument<String>("callId")
                    val callerName = call.argument<String>("callerName") ?: "Unknown"
                    val isVideo = call.argument<Boolean>("isVideo") ?: false

                    if (callId != null) {
                        updateVoIPForegroundService(callId, callerName, isVideo)
                        result.success(true)
                    } else {
                        result.error("INVALID_ARGUMENTS", "Call ID is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Set up audio platform channel
        audioMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, AUDIO_CHANNEL)
        audioMethodChannel?.setMethodCallHandler { call, result ->
            // This method is invoked on the main thread
            when (call.method) {
                "requestAudioFocus" -> {
                    val success = AudioSessionManager.requestAudioFocus(this)
                    result.success(success)
                }
                "abandonAudioFocus" -> {
                    val success = AudioSessionManager.abandonAudioFocus(this)
                    result.success(success)
                }
                "setAudioMode" -> {
                    val mode = call.argument<String>("mode") ?: "normal"
                    AudioSessionManager.setAudioMode(this, mode)
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Register to receive broadcasts from the FCM service
        registerBroadcastReceiver()

        // Check if app was launched from a notification
        handleIncomingCallIntent(intent)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Make navigation bar transparent
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.isNavigationBarContrastEnforced = false
        }
        
        // Make the app draw behind system bars (edge-to-edge)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Make the status bar and navigation bar transparent
        window.setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        )
        
        // For incoming calls, turn on screen and show even when locked
        if (intent?.action == HopenFirebaseMessagingService.ACTION_INCOMING_CALL) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                setShowWhenLocked(true)
                setTurnScreenOn(true)
            } else {
                window.addFlags(
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                )
            }
        }
        
        // Make navigation bar icons light
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.systemBarsBehavior = androidx.core.view.WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
    }
    
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        
        // Handle the incoming call if present
        handleIncomingCallIntent(intent)
    }
    
    private fun registerBroadcastReceiver() {
        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == HopenFirebaseMessagingService.ACTION_INCOMING_CALL) {
                    handleIncomingCallIntent(intent)
                }
            }
        }
        
        val intentFilter = IntentFilter(HopenFirebaseMessagingService.ACTION_INCOMING_CALL)
        registerReceiver(broadcastReceiver, intentFilter)
    }
    
    private fun handleIncomingCallIntent(intent: Intent?) {
        if (intent?.action == HopenFirebaseMessagingService.ACTION_INCOMING_CALL) {
            Log.d("HopenCall", "Received incoming call intent in MainActivity")
            
            // Extract all call information from the intent
            val callId = intent.getStringExtra("call_id")
            val callerId = intent.getStringExtra("caller_id")
            val callerName = intent.getStringExtra("caller_name")
            val callerAvatar = intent.getStringExtra("caller_avatar")
            val targetType = intent.getStringExtra("target_type") ?: "user"
            val targetId = intent.getStringExtra("target_id")
            val targetName = intent.getStringExtra("target_name")
            val targetAvatarUrl = intent.getStringExtra("target_avatar_url")
            val sdpOffer = intent.getStringExtra("sdp_offer")
            val isVideoOffered = intent.getBooleanExtra("video_offered", false)
            val isAudioOffered = intent.getBooleanExtra("audio_offered", true)
            val isScreenShareOffered = intent.getBooleanExtra("screen_share_offered", false)
            
            if (callId == null || callerId == null) {
                Log.e("HopenCall", "Missing required call information in intent")
                return
            }
            
            // Prepare data to send to Flutter
            val callData = HashMap<String, Any>().apply {
                put("call_id", callId)
                put("caller_id", callerId)
                put("caller_name", callerName ?: "Unknown")
                callerAvatar?.let { put("caller_avatar", it) }
                put("is_group", targetType == "bubble")
                targetId?.let { put("group_id", it) }
                targetName?.let { put("group_name", it) }
                targetAvatarUrl?.let { put("group_avatar_url", it) }
                sdpOffer?.let { put("remote_offer_sdp", it) }
                put("is_video_offered", isVideoOffered)
                put("is_audio_offered", isAudioOffered)
                put("is_screen_share_offered", isScreenShareOffered)
            }
            
            // Send data to Flutter via method channel
            callMethodChannel?.invokeMethod("incomingCall", callData, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d("HopenCall", "Successfully sent call data to Flutter")
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e("HopenCall", "Error sending call data to Flutter: $errorCode - $errorMessage")
                }

                override fun notImplemented() {
                    Log.e("HopenCall", "Method channel method not implemented in Flutter")
                }
            })
        }
    }

    // VoIP Foreground Service Management (Android 14+ Compliant)
    private fun startVoIPForegroundService(callId: String, callerName: String, isVideo: Boolean) {
        val intent = Intent(this, VoIPForegroundService::class.java).apply {
            action = VoIPForegroundService.ACTION_START_FOREGROUND_SERVICE
            putExtra("call_id", callId)
            putExtra("caller_name", callerName)
            putExtra("is_video", isVideo)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }

        Log.d("HopenCall", "Started VoIP foreground service for call: $callId")
    }

    private fun stopVoIPForegroundService() {
        val intent = Intent(this, VoIPForegroundService::class.java).apply {
            action = VoIPForegroundService.ACTION_STOP_FOREGROUND_SERVICE
        }
        stopService(intent)
        Log.d("HopenCall", "Stopped VoIP foreground service")
    }

    private fun updateVoIPForegroundService(callId: String, callerName: String, isVideo: Boolean) {
        val intent = Intent(this, VoIPForegroundService::class.java).apply {
            action = VoIPForegroundService.ACTION_UPDATE_FOREGROUND_SERVICE
            putExtra("call_id", callId)
            putExtra("caller_name", callerName)
            putExtra("is_video", isVideo)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }

        Log.d("HopenCall", "Updated VoIP foreground service for call: $callId")
    }

    override fun onDestroy() {
        super.onDestroy()
        if (broadcastReceiver != null) {
            unregisterReceiver(broadcastReceiver)
            broadcastReceiver = null
        }
    }
}