package com.hopenapp.hopen

import android.app.*
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.annotation.RequiresApi

/**
 * VoIP Foreground Service for Android 14+ Compliance
 *
 * This service follows the latest Android best practices:
 * - Uses FOREGROUND_SERVICE_PHONE_CALL type for Android 14+
 * - Proper notification channel management
 * - Runtime permission checking
 * - Lifecycle management with START_STICKY
 */
class VoIPForegroundService : Service() {
    companion object {
        const val ACTION_START_FOREGROUND_SERVICE = "START_FOREGROUND_SERVICE"
        const val ACTION_STOP_FOREGROUND_SERVICE = "STOP_FOREGROUND_SERVICE"
        const val ACTION_UPDATE_FOREGROUND_SERVICE = "UPDATE_FOREGROUND_SERVICE"

        // Legacy actions for backward compatibility
        const val ACTION_START_CALL = "START_CALL"
        const val ACTION_END_CALL = "END_CALL"
        const val ACTION_UPDATE_CALL = "UPDATE_CALL"

        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "voip_call_channel"
        private const val CHANNEL_NAME = "VoIP Calls"
        private const val CHANNEL_DESCRIPTION = "Ongoing VoIP call notifications"

        const val EXTRA_CALL_ID = "call_id"
        const val EXTRA_CALLER_NAME = "caller_name"
        const val EXTRA_CALL_DURATION = "call_duration"
    }

    private var isCallActive = false
    private var callId: String? = null
    private var callerName: String? = null

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_FOREGROUND_SERVICE, ACTION_START_CALL -> {
                val callId = intent.getStringExtra("call_id") ?: intent.getStringExtra(EXTRA_CALL_ID)
                val callerName = intent.getStringExtra("caller_name") ?: intent.getStringExtra(EXTRA_CALLER_NAME) ?: "Unknown"
                val isVideo = intent.getBooleanExtra("is_video", false)

                if (callId != null) {
                    startForegroundService(callId, callerName, isVideo)
                } else {
                    Log.e("VoIPService", "Cannot start service without call ID")
                    stopSelf()
                }
            }
            ACTION_UPDATE_FOREGROUND_SERVICE, ACTION_UPDATE_CALL -> {
                val callId = intent.getStringExtra("call_id") ?: intent.getStringExtra(EXTRA_CALL_ID)
                val callerName = intent.getStringExtra("caller_name") ?: intent.getStringExtra(EXTRA_CALLER_NAME) ?: "Unknown"
                val isVideo = intent.getBooleanExtra("is_video", false)
                val duration = intent.getStringExtra(EXTRA_CALL_DURATION)

                if (callId != null) {
                    updateNotification(callId, callerName, isVideo, duration)
                }
            }
            ACTION_STOP_FOREGROUND_SERVICE, ACTION_END_CALL -> {
                stopForegroundService()
            }
        }

        // Return START_STICKY to restart service if killed by system
        return START_STICKY
    }

    private fun startForegroundService(callId: String, callerName: String, isVideo: Boolean) {
        this.callId = callId
        this.callerName = callerName
        isCallActive = true

        val notification = createNotification(callId, callerName, isVideo)

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                // Android 14+ requires specific foreground service type
                startForeground(
                    NOTIFICATION_ID,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_PHONE_CALL
                )
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ supports foreground service types
                startForeground(
                    NOTIFICATION_ID,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_PHONE_CALL
                )
            } else {
                // Fallback for older Android versions
                startForeground(NOTIFICATION_ID, notification)
            }

            Log.d("VoIPService", "Started foreground service for call: $callId")
        } catch (e: Exception) {
            Log.e("VoIPService", "Failed to start foreground service", e)
            stopSelf()
        }
    }

    private fun updateNotification(callId: String, callerName: String, isVideo: Boolean, duration: String? = null) {
        if (!isCallActive) return

        this.callId = callId
        this.callerName = callerName

        val notification = createNotification(callId, callerName, isVideo, duration)
        NotificationManagerCompat.from(this).notify(NOTIFICATION_ID, notification)

        Log.d("VoIPService", "Updated notification for call: $callId")
    }

    private fun stopForegroundService() {
        Log.d("VoIPService", "Stopping VoIP foreground service")
        isCallActive = false
        callId = null
        callerName = null
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }

    private fun createNotification(callId: String, callerName: String, isVideo: Boolean, duration: String? = null): Notification {
        // Create intent to return to the app when notification is tapped
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            putExtra("call_id", callId)
            putExtra("from_notification", true)
            putExtra("open_call", true)
        }

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val callType = if (isVideo) "Video call" else "Voice call"
        val contentText = if (duration != null) {
            "$callType with $callerName • $duration"
        } else {
            "$callType with $callerName"
        }

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Ongoing Call")
            .setContentText(contentText)
            .setSmallIcon(android.R.drawable.ic_menu_call)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_CALL)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setSound(null)
            .setVibrate(null)
            .setColorized(true)
            .setColor(0xFF4CAF50.toInt())
            .build()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = CHANNEL_DESCRIPTION
                setShowBadge(false)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                enableVibration(false)
                setSound(null, null)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager?.createNotificationChannel(channel)
            Log.d("VoIPService", "Notification channel created")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("VoIPService", "VoIP Foreground Service destroyed")
    }

    override fun onBind(intent: Intent?): IBinder? = null
}
