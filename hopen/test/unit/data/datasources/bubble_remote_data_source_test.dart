import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../../lib/data/datasources/bubble_remote_data_source.dart';
import '../../../lib/data/services/api/grpc_client_service.dart';
import '../../../lib/generated/grpc/bubble.pbgrpc.dart' as bubble_proto;
import '../../../lib/generated/grpc/bubble.pb.dart' as bubble_proto;
import '../../../lib/generated/grpc/user.pbgrpc.dart' as user_proto;
import '../../../lib/generated/grpc/user.pb.dart' as user_proto;
import '../../../lib/generated/grpc/common.pb.dart' as common;

// Generate mocks
@GenerateMocks([
  GrpcClientService, 
  bubble_proto.BubbleServiceClient,
  user_proto.UserServiceClient,
])
import 'bubble_remote_data_source_test.mocks.dart';

void main() {
  group('BubbleRemoteDataSource', () {
    late BubbleRemoteDataSource dataSource;
    late MockGrpcClientService mockGrpcClientService;
    late MockBubbleServiceClient mockBubbleServiceClient;
    late MockUserServiceClient mockUserServiceClient;

    setUp(() {
      mockGrpcClientService = MockGrpcClientService();
      mockBubbleServiceClient = MockBubbleServiceClient();
      mockUserServiceClient = MockUserServiceClient();
      
      when(mockGrpcClientService.bubbleService).thenReturn(mockBubbleServiceClient);
      when(mockGrpcClientService.userService).thenReturn(mockUserServiceClient);
      when(mockGrpcClientService.createCallOptions()).thenReturn(null);
      
      dataSource = BubbleRemoteDataSourceImpl(mockGrpcClientService);
    });

    group('getUserJoinRequests', () {
      test('should fetch actual usernames instead of hardcoded values', () async {
        // Arrange
        const bubbleId = 'test-bubble-id';
        const userId1 = 'user-1';
        const userId2 = 'user-2';
        
        final mockJoinRequest1 = bubble_proto.BubbleRequest()
          ..id = 'request-1'
          ..bubbleId = bubbleId
          ..requesterId = userId1
          ..status = bubble_proto.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_PENDING;
          
        final mockJoinRequest2 = bubble_proto.BubbleRequest()
          ..id = 'request-2'
          ..bubbleId = bubbleId
          ..requesterId = userId2
          ..status = bubble_proto.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_PENDING;

        final mockJoinRequestsResponse = bubble_proto.GetBubbleJoinRequestsResponse()
          ..requests.addAll([mockJoinRequest1, mockJoinRequest2]);

        final mockUser1 = user_proto.User()
          ..id = userId1
          ..username = 'alice_smith';
          
        final mockUser2 = user_proto.User()
          ..id = userId2
          ..username = 'bob_jones';

        final mockUserBatchResponse = user_proto.GetUsersBatchResponse()
          ..users.addAll([mockUser1, mockUser2]);

        when(mockBubbleServiceClient.getBubbleJoinRequests(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockJoinRequestsResponse);
            
        when(mockUserServiceClient.getUsersBatch(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockUserBatchResponse);

        // Act
        final result = await dataSource.getUserJoinRequests(bubbleId);

        // Assert
        expect(result.length, 2);
        expect(result[0].userName, 'alice_smith');
        expect(result[1].userName, 'bob_jones');
        
        // Verify that getUsersBatch was called to fetch actual usernames
        verify(mockUserServiceClient.getUsersBatch(any, options: anyNamed('options'))).called(1);
      });

      test('should fallback to user IDs when username fetch fails', () async {
        // Arrange
        const bubbleId = 'test-bubble-id';
        const userId1 = 'user-1';
        
        final mockJoinRequest = bubble_proto.BubbleRequest()
          ..id = 'request-1'
          ..bubbleId = bubbleId
          ..requesterId = userId1
          ..status = bubble_proto.BubbleRequestStatus.BUBBLE_REQUEST_STATUS_PENDING;

        final mockJoinRequestsResponse = bubble_proto.GetBubbleJoinRequestsResponse()
          ..requests.add(mockJoinRequest);

        when(mockBubbleServiceClient.getBubbleJoinRequests(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockJoinRequestsResponse);
            
        when(mockUserServiceClient.getUsersBatch(any, options: anyNamed('options')))
            .thenThrow(Exception('Network error'));

        // Act
        final result = await dataSource.getUserJoinRequests(bubbleId);

        // Assert
        expect(result.length, 1);
        expect(result[0].userName, 'User $userId1'); // Fallback to user ID
      });
    });

    group('searchBubbleHistory', () {
      test('should use GetSortedBubbleHistory for advanced filtering', () async {
        // Arrange
        const userId = 'test-user';
        const status = 'active';
        final startDate = DateTime(2024, 1, 1);
        final endDate = DateTime(2024, 12, 31);

        final mockBubble = bubble_proto.Bubble()
          ..id = 'bubble-1'
          ..name = 'Test Bubble';

        final mockSortedResponse = bubble_proto.GetSortedBubbleHistoryResponse()
          ..bubbles.add(mockBubble);

        when(mockBubbleServiceClient.getSortedBubbleHistory(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockSortedResponse);

        // Act
        final result = await dataSource.searchBubbleHistory(
          userId: userId,
          status: status,
          startDate: startDate,
          endDate: endDate,
        );

        // Assert
        expect(result.bubbles.length, 1);
        verify(mockBubbleServiceClient.getSortedBubbleHistory(any, options: anyNamed('options'))).called(1);
        
        // Verify that the correct filters were applied
        final captured = verify(mockBubbleServiceClient.getSortedBubbleHistory(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as bubble_proto.GetSortedBubbleHistoryRequest;
        expect(request.statusFilter.contains(common.BubbleStatus.BUBBLE_STATUS_ACTIVE), true);
      });

      test('should use simple search for text-only queries', () async {
        // Arrange
        const userId = 'test-user';
        const query = 'test query';

        final mockResponse = bubble_proto.SearchBubbleHistoryResponse();

        when(mockBubbleServiceClient.searchBubbleHistory(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.searchBubbleHistory(
          userId: userId,
          query: query,
        );

        // Assert
        verify(mockBubbleServiceClient.searchBubbleHistory(any, options: anyNamed('options'))).called(1);
        verifyNever(mockBubbleServiceClient.getSortedBubbleHistory(any, options: anyNamed('options')));
      });
    });

    group('voting methods', () {
      test('should include reason in vote field when provided', () async {
        // Arrange
        const requestId = 'test-request';
        const voterId = 'voter-id';
        const reason = 'Good candidate';
        
        final mockResponse = bubble_proto.VoteOnJoinRequestResponse();

        when(mockBubbleServiceClient.voteOnJoinRequest(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.voteOnJoinRequest(requestId, voterId, true, reason: reason);

        // Assert
        final captured = verify(mockBubbleServiceClient.voteOnJoinRequest(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as bubble_proto.VoteOnJoinRequestRequest;
        expect(request.vote, 'approve:$reason');
      });
    });

    group('allowUserToRejoin', () {
      test('should use UnbanUser endpoint instead of addMemberToBubble', () async {
        // Arrange
        const bubbleId = 'test-bubble';
        const userId = 'test-user';
        
        final mockResponse = bubble_proto.UnbanUserResponse();

        when(mockBubbleServiceClient.unbanUser(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.allowUserToRejoin(
          bubbleId: bubbleId,
          userId: userId,
        );

        // Assert
        expect(result.isSuccess, true);
        verify(mockBubbleServiceClient.unbanUser(any, options: anyNamed('options'))).called(1);
        
        // Verify that the correct request was made
        final captured = verify(mockBubbleServiceClient.unbanUser(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as bubble_proto.UnbanUserRequest;
        expect(request.bubbleId, bubbleId);
        expect(request.userId, userId);
      });
    });
  });
}
