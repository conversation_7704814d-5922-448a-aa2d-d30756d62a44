import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:hopen/data/datasources/auth_remote_data_source.dart';
import 'package:hopen/data/services/api/grpc_client_service.dart';
import 'package:hopen/generated/grpc/auth.pbgrpc.dart' as auth_proto;

// Generate mocks
@GenerateMocks([GrpcClientService, auth_proto.AuthServiceClient])
import 'auth_remote_data_source_test.mocks.dart';

void main() {
  group('AuthRemoteDataSource', () {
    late AuthRemoteDataSource dataSource;
    late MockGrpcClientService mockGrpcClientService;
    late MockAuthServiceClient mockAuthServiceClient;

    setUp(() {
      mockGrpcClientService = MockGrpcClientService();
      mockAuthServiceClient = MockAuthServiceClient();
      
      // Setup the mock to return the auth service client
      when(mockGrpcClientService.authService).thenReturn(mockAuthServiceClient);
      when(mockGrpcClientService.createCallOptions()).thenReturn(null);
      
      dataSource = AuthRemoteDataSourceImpl(mockGrpcClientService);
    });

    group('checkPasswordBreach', () {
      test('should use proper gRPC client service instead of undefined _authServiceClient', () async {
        // Arrange
        const testPassword = 'testPassword123';
        const expectedSha1Hash = 'a94a8fe5ccb19ba61c4c0873d391e987982fbbd3'; // SHA1 of 'test'
        
        final expectedRequest = auth_proto.CheckPasswordBreachRequest()
          ..passwordSha1Hash = expectedSha1Hash;
        
        final mockResponse = auth_proto.CheckPasswordBreachResponse()
          ..isBreached = false
          ..breachCount = 0;

        when(mockAuthServiceClient.checkPasswordBreach(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.checkPasswordBreach(testPassword);

        // Assert
        expect(result.isBreached, false);
        expect(result.breachCount, 0);
        
        // Verify that the correct gRPC service was called
        verify(mockGrpcClientService.authService).called(1);
        verify(mockAuthServiceClient.checkPasswordBreach(any, options: anyNamed('options'))).called(1);
      });

      test('should handle breached password correctly', () async {
        // Arrange
        const testPassword = 'password123';
        
        final mockResponse = auth_proto.CheckPasswordBreachResponse()
          ..isBreached = true
          ..breachCount = 12345;

        when(mockAuthServiceClient.checkPasswordBreach(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.checkPasswordBreach(testPassword);

        // Assert
        expect(result.isBreached, true);
        expect(result.breachCount, 12345);
      });

      test('should handle gRPC errors gracefully', () async {
        // Arrange
        const testPassword = 'testPassword';
        
        when(mockAuthServiceClient.checkPasswordBreach(any, options: anyNamed('options')))
            .thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => dataSource.checkPasswordBreach(testPassword),
          throwsException,
        );
      });

      test('should create correct SHA1 hash for password', () async {
        // Arrange
        const testPassword = 'hello';
        // SHA1 of 'hello' is 'aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d'
        
        final mockResponse = auth_proto.CheckPasswordBreachResponse()
          ..isBreached = false
          ..breachCount = 0;

        when(mockAuthServiceClient.checkPasswordBreach(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.checkPasswordBreach(testPassword);

        // Assert
        final captured = verify(mockAuthServiceClient.checkPasswordBreach(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as auth_proto.CheckPasswordBreachRequest;
        expect(request.passwordSha1Hash, 'aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d');
      });
    });
  });
}
