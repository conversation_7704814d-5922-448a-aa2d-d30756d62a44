// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in hopen/test/unit/data/datasources/auth_remote_data_source_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i16;

import 'package:grpc/grpc.dart' as _i14;
import 'package:hopen/data/services/api/grpc_client_service.dart' as _i15;
import 'package:hopen/generated/grpc/auth.pbgrpc.dart' as _i2;
import 'package:hopen/generated/grpc/bubble.pbgrpc.dart' as _i4;
import 'package:hopen/generated/grpc/call.pbgrpc.dart' as _i5;
import 'package:hopen/generated/grpc/common.pb.dart' as _i17;
import 'package:hopen/generated/grpc/email.pbgrpc.dart' as _i6;
import 'package:hopen/generated/grpc/media.pbgrpc.dart' as _i7;
import 'package:hopen/generated/grpc/notification.pbgrpc.dart' as _i8;
import 'package:hopen/generated/grpc/presence.pbgrpc.dart' as _i9;
import 'package:hopen/generated/grpc/realtime.pbgrpc.dart' as _i10;
import 'package:hopen/generated/grpc/search.pbgrpc.dart' as _i11;
import 'package:hopen/generated/grpc/social_analytics.pbgrpc.dart' as _i12;
import 'package:hopen/generated/grpc/sync.pbgrpc.dart' as _i13;
import 'package:hopen/generated/grpc/user.pbgrpc.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthServiceClient_0 extends _i1.SmartFake
    implements _i2.AuthServiceClient {
  _FakeAuthServiceClient_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserServiceClient_1 extends _i1.SmartFake
    implements _i3.UserServiceClient {
  _FakeUserServiceClient_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBubbleServiceClient_2 extends _i1.SmartFake
    implements _i4.BubbleServiceClient {
  _FakeBubbleServiceClient_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCallServiceClient_3 extends _i1.SmartFake
    implements _i5.CallServiceClient {
  _FakeCallServiceClient_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEmailServiceClient_4 extends _i1.SmartFake
    implements _i6.EmailServiceClient {
  _FakeEmailServiceClient_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMediaServiceClient_5 extends _i1.SmartFake
    implements _i7.MediaServiceClient {
  _FakeMediaServiceClient_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNotificationServiceClient_6 extends _i1.SmartFake
    implements _i8.NotificationServiceClient {
  _FakeNotificationServiceClient_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePresenceServiceClient_7 extends _i1.SmartFake
    implements _i9.PresenceServiceClient {
  _FakePresenceServiceClient_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRealtimeServiceClient_8 extends _i1.SmartFake
    implements _i10.RealtimeServiceClient {
  _FakeRealtimeServiceClient_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSearchServiceClient_9 extends _i1.SmartFake
    implements _i11.SearchServiceClient {
  _FakeSearchServiceClient_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSocialAnalyticsServiceClient_10 extends _i1.SmartFake
    implements _i12.SocialAnalyticsServiceClient {
  _FakeSocialAnalyticsServiceClient_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeSyncServiceClient_11 extends _i1.SmartFake
    implements _i13.SyncServiceClient {
  _FakeSyncServiceClient_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCallOptions_12 extends _i1.SmartFake implements _i14.CallOptions {
  _FakeCallOptions_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResponseFuture_13<R> extends _i1.SmartFake
    implements _i14.ResponseFuture<R> {
  _FakeResponseFuture_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeClientCall_14<Q1, R1> extends _i1.SmartFake
    implements _i14.ClientCall<Q1, R1> {
  _FakeClientCall_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResponseStream_15<R1> extends _i1.SmartFake
    implements _i14.ResponseStream<R1> {
  _FakeResponseStream_15(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GrpcClientService].
///
/// See the documentation for Mockito's code generation for more information.
class MockGrpcClientService extends _i1.Mock implements _i15.GrpcClientService {
  MockGrpcClientService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthServiceClient get authService =>
      (super.noSuchMethod(
            Invocation.getter(#authService),
            returnValue: _FakeAuthServiceClient_0(
              this,
              Invocation.getter(#authService),
            ),
          )
          as _i2.AuthServiceClient);

  @override
  _i3.UserServiceClient get userService =>
      (super.noSuchMethod(
            Invocation.getter(#userService),
            returnValue: _FakeUserServiceClient_1(
              this,
              Invocation.getter(#userService),
            ),
          )
          as _i3.UserServiceClient);

  @override
  _i4.BubbleServiceClient get bubbleService =>
      (super.noSuchMethod(
            Invocation.getter(#bubbleService),
            returnValue: _FakeBubbleServiceClient_2(
              this,
              Invocation.getter(#bubbleService),
            ),
          )
          as _i4.BubbleServiceClient);

  @override
  _i5.CallServiceClient get callService =>
      (super.noSuchMethod(
            Invocation.getter(#callService),
            returnValue: _FakeCallServiceClient_3(
              this,
              Invocation.getter(#callService),
            ),
          )
          as _i5.CallServiceClient);

  @override
  _i6.EmailServiceClient get emailService =>
      (super.noSuchMethod(
            Invocation.getter(#emailService),
            returnValue: _FakeEmailServiceClient_4(
              this,
              Invocation.getter(#emailService),
            ),
          )
          as _i6.EmailServiceClient);

  @override
  _i7.MediaServiceClient get mediaService =>
      (super.noSuchMethod(
            Invocation.getter(#mediaService),
            returnValue: _FakeMediaServiceClient_5(
              this,
              Invocation.getter(#mediaService),
            ),
          )
          as _i7.MediaServiceClient);

  @override
  _i8.NotificationServiceClient get notificationService =>
      (super.noSuchMethod(
            Invocation.getter(#notificationService),
            returnValue: _FakeNotificationServiceClient_6(
              this,
              Invocation.getter(#notificationService),
            ),
          )
          as _i8.NotificationServiceClient);

  @override
  _i9.PresenceServiceClient get presenceService =>
      (super.noSuchMethod(
            Invocation.getter(#presenceService),
            returnValue: _FakePresenceServiceClient_7(
              this,
              Invocation.getter(#presenceService),
            ),
          )
          as _i9.PresenceServiceClient);

  @override
  _i10.RealtimeServiceClient get realtimeService =>
      (super.noSuchMethod(
            Invocation.getter(#realtimeService),
            returnValue: _FakeRealtimeServiceClient_8(
              this,
              Invocation.getter(#realtimeService),
            ),
          )
          as _i10.RealtimeServiceClient);

  @override
  _i11.SearchServiceClient get searchService =>
      (super.noSuchMethod(
            Invocation.getter(#searchService),
            returnValue: _FakeSearchServiceClient_9(
              this,
              Invocation.getter(#searchService),
            ),
          )
          as _i11.SearchServiceClient);

  @override
  _i12.SocialAnalyticsServiceClient get socialAnalyticsService =>
      (super.noSuchMethod(
            Invocation.getter(#socialAnalyticsService),
            returnValue: _FakeSocialAnalyticsServiceClient_10(
              this,
              Invocation.getter(#socialAnalyticsService),
            ),
          )
          as _i12.SocialAnalyticsServiceClient);

  @override
  _i13.SyncServiceClient get syncService =>
      (super.noSuchMethod(
            Invocation.getter(#syncService),
            returnValue: _FakeSyncServiceClient_11(
              this,
              Invocation.getter(#syncService),
            ),
          )
          as _i13.SyncServiceClient);

  @override
  bool get hasAuthToken =>
      (super.noSuchMethod(Invocation.getter(#hasAuthToken), returnValue: false)
          as bool);

  @override
  _i16.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i16.Future<void>.value(),
            returnValueForMissingStub: _i16.Future<void>.value(),
          )
          as _i16.Future<void>);

  @override
  void setAuthToken(String? token) => super.noSuchMethod(
    Invocation.method(#setAuthToken, [token]),
    returnValueForMissingStub: null,
  );

  @override
  void clearAuthToken() => super.noSuchMethod(
    Invocation.method(#clearAuthToken, []),
    returnValueForMissingStub: null,
  );

  @override
  _i16.Future<Map<String, bool>> testAllConnections() =>
      (super.noSuchMethod(
            Invocation.method(#testAllConnections, []),
            returnValue: _i16.Future<Map<String, bool>>.value(<String, bool>{}),
          )
          as _i16.Future<Map<String, bool>>);

  @override
  _i14.CallOptions createCallOptions() =>
      (super.noSuchMethod(
            Invocation.method(#createCallOptions, []),
            returnValue: _FakeCallOptions_12(
              this,
              Invocation.method(#createCallOptions, []),
            ),
          )
          as _i14.CallOptions);

  @override
  _i16.Future<void> close() =>
      (super.noSuchMethod(
            Invocation.method(#close, []),
            returnValue: _i16.Future<void>.value(),
            returnValueForMissingStub: _i16.Future<void>.value(),
          )
          as _i16.Future<void>);
}

/// A class which mocks [AuthServiceClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthServiceClient extends _i1.Mock implements _i2.AuthServiceClient {
  MockAuthServiceClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i14.ResponseFuture<_i2.RegisterUserResponse> registerUser(
    _i2.RegisterUserRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerUser, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.RegisterUserResponse>(
              this,
              Invocation.method(#registerUser, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.RegisterUserResponse>);

  @override
  _i14.ResponseFuture<_i2.LoginUserResponse> loginUser(
    _i2.LoginUserRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginUser, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.LoginUserResponse>(
              this,
              Invocation.method(#loginUser, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.LoginUserResponse>);

  @override
  _i14.ResponseFuture<_i2.InitiateLoginFlowResponse> initiateKratosLoginFlow(
    _i2.InitiateLoginFlowRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #initiateKratosLoginFlow,
              [request],
              {#options: options},
            ),
            returnValue: _FakeResponseFuture_13<_i2.InitiateLoginFlowResponse>(
              this,
              Invocation.method(
                #initiateKratosLoginFlow,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.InitiateLoginFlowResponse>);

  @override
  _i14.ResponseFuture<_i2.InitiateRegistrationFlowResponse>
  initiateKratosRegistrationFlow(
    _i2.InitiateRegistrationFlowRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #initiateKratosRegistrationFlow,
              [request],
              {#options: options},
            ),
            returnValue:
                _FakeResponseFuture_13<_i2.InitiateRegistrationFlowResponse>(
                  this,
                  Invocation.method(
                    #initiateKratosRegistrationFlow,
                    [request],
                    {#options: options},
                  ),
                ),
          )
          as _i14.ResponseFuture<_i2.InitiateRegistrationFlowResponse>);

  @override
  _i14.ResponseFuture<_i2.CompleteKratosFlowResponse> completeKratosFlow(
    _i2.CompleteKratosFlowRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #completeKratosFlow,
              [request],
              {#options: options},
            ),
            returnValue: _FakeResponseFuture_13<_i2.CompleteKratosFlowResponse>(
              this,
              Invocation.method(
                #completeKratosFlow,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.CompleteKratosFlowResponse>);

  @override
  _i14.ResponseFuture<_i2.ValidateSessionResponse> validateSession(
    _i2.ValidateSessionRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateSession, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.ValidateSessionResponse>(
              this,
              Invocation.method(
                #validateSession,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.ValidateSessionResponse>);

  @override
  _i14.ResponseFuture<_i2.RefreshTokenResponse> refreshToken(
    _i2.RefreshTokenRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#refreshToken, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.RefreshTokenResponse>(
              this,
              Invocation.method(#refreshToken, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.RefreshTokenResponse>);

  @override
  _i14.ResponseFuture<_i2.LogoutResponse> logout(
    _i2.LogoutRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logout, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.LogoutResponse>(
              this,
              Invocation.method(#logout, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.LogoutResponse>);

  @override
  _i14.ResponseFuture<_i2.GetUserProfileResponse> getUserProfile(
    _i2.GetUserProfileRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserProfile, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.GetUserProfileResponse>(
              this,
              Invocation.method(
                #getUserProfile,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.GetUserProfileResponse>);

  @override
  _i14.ResponseFuture<_i2.GetUserPermissionsResponse> getUserPermissions(
    _i2.GetUserPermissionsRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUserPermissions,
              [request],
              {#options: options},
            ),
            returnValue: _FakeResponseFuture_13<_i2.GetUserPermissionsResponse>(
              this,
              Invocation.method(
                #getUserPermissions,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.GetUserPermissionsResponse>);

  @override
  _i14.ResponseFuture<_i2.HasPermissionResponse> hasPermission(
    _i2.HasPermissionRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#hasPermission, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.HasPermissionResponse>(
              this,
              Invocation.method(#hasPermission, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.HasPermissionResponse>);

  @override
  _i14.ResponseFuture<_i2.GetUserRolesResponse> getUserRoles(
    _i2.GetUserRolesRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserRoles, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.GetUserRolesResponse>(
              this,
              Invocation.method(#getUserRoles, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.GetUserRolesResponse>);

  @override
  _i14.ResponseFuture<_i2.GetDefaultRoleResponse> getDefaultRole(
    _i2.GetDefaultRoleRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getDefaultRole, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.GetDefaultRoleResponse>(
              this,
              Invocation.method(
                #getDefaultRole,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.GetDefaultRoleResponse>);

  @override
  _i14.ResponseFuture<_i2.SetDefaultRoleResponse> setDefaultRole(
    _i2.SetDefaultRoleRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setDefaultRole, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.SetDefaultRoleResponse>(
              this,
              Invocation.method(
                #setDefaultRole,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.SetDefaultRoleResponse>);

  @override
  _i14.ResponseFuture<_i2.ValidateMqttConnectionResponse>
  validateMqttConnection(
    _i2.ValidateMqttConnectionRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #validateMqttConnection,
              [request],
              {#options: options},
            ),
            returnValue:
                _FakeResponseFuture_13<_i2.ValidateMqttConnectionResponse>(
                  this,
                  Invocation.method(
                    #validateMqttConnection,
                    [request],
                    {#options: options},
                  ),
                ),
          )
          as _i14.ResponseFuture<_i2.ValidateMqttConnectionResponse>);

  @override
  _i14.ResponseFuture<_i2.CheckMqttTopicPermissionResponse>
  checkMqttTopicPermission(
    _i2.CheckMqttTopicPermissionRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #checkMqttTopicPermission,
              [request],
              {#options: options},
            ),
            returnValue:
                _FakeResponseFuture_13<_i2.CheckMqttTopicPermissionResponse>(
                  this,
                  Invocation.method(
                    #checkMqttTopicPermission,
                    [request],
                    {#options: options},
                  ),
                ),
          )
          as _i14.ResponseFuture<_i2.CheckMqttTopicPermissionResponse>);

  @override
  _i14.ResponseFuture<_i2.GetAuthStatusResponse> getAuthStatus(
    _i2.GetAuthStatusRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAuthStatus, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.GetAuthStatusResponse>(
              this,
              Invocation.method(#getAuthStatus, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.GetAuthStatusResponse>);

  @override
  _i14.ResponseFuture<_i2.CheckPasswordBreachResponse> checkPasswordBreach(
    _i2.CheckPasswordBreachRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #checkPasswordBreach,
              [request],
              {#options: options},
            ),
            returnValue:
                _FakeResponseFuture_13<_i2.CheckPasswordBreachResponse>(
                  this,
                  Invocation.method(
                    #checkPasswordBreach,
                    [request],
                    {#options: options},
                  ),
                ),
          )
          as _i14.ResponseFuture<_i2.CheckPasswordBreachResponse>);

  @override
  _i14.ResponseFuture<_i2.ForgotPasswordResponse> forgotPassword(
    _i2.ForgotPasswordRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#forgotPassword, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.ForgotPasswordResponse>(
              this,
              Invocation.method(
                #forgotPassword,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.ForgotPasswordResponse>);

  @override
  _i14.ResponseFuture<_i2.ResetPasswordResponse> resetPassword(
    _i2.ResetPasswordRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.ResetPasswordResponse>(
              this,
              Invocation.method(#resetPassword, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.ResetPasswordResponse>);

  @override
  _i14.ResponseFuture<_i2.ChangePasswordResponse> changePassword(
    _i2.ChangePasswordRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.ChangePasswordResponse>(
              this,
              Invocation.method(
                #changePassword,
                [request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<_i2.ChangePasswordResponse>);

  @override
  _i14.ResponseFuture<_i2.EnableTwoFactorAuthResponse> enableTwoFactorAuth(
    _i2.EnableTwoFactorAuthRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #enableTwoFactorAuth,
              [request],
              {#options: options},
            ),
            returnValue:
                _FakeResponseFuture_13<_i2.EnableTwoFactorAuthResponse>(
                  this,
                  Invocation.method(
                    #enableTwoFactorAuth,
                    [request],
                    {#options: options},
                  ),
                ),
          )
          as _i14.ResponseFuture<_i2.EnableTwoFactorAuthResponse>);

  @override
  _i14.ResponseFuture<_i2.VerifyTwoFactorAuthResponse> verifyTwoFactorAuth(
    _i2.VerifyTwoFactorAuthRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #verifyTwoFactorAuth,
              [request],
              {#options: options},
            ),
            returnValue:
                _FakeResponseFuture_13<_i2.VerifyTwoFactorAuthResponse>(
                  this,
                  Invocation.method(
                    #verifyTwoFactorAuth,
                    [request],
                    {#options: options},
                  ),
                ),
          )
          as _i14.ResponseFuture<_i2.VerifyTwoFactorAuthResponse>);

  @override
  _i14.ResponseFuture<_i2.DisableTwoFactorAuthResponse> disableTwoFactorAuth(
    _i2.DisableTwoFactorAuthRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #disableTwoFactorAuth,
              [request],
              {#options: options},
            ),
            returnValue:
                _FakeResponseFuture_13<_i2.DisableTwoFactorAuthResponse>(
                  this,
                  Invocation.method(
                    #disableTwoFactorAuth,
                    [request],
                    {#options: options},
                  ),
                ),
          )
          as _i14.ResponseFuture<_i2.DisableTwoFactorAuthResponse>);

  @override
  _i14.ResponseFuture<_i2.DeleteAccountResponse> deleteAccount(
    _i2.DeleteAccountRequest? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i2.DeleteAccountResponse>(
              this,
              Invocation.method(#deleteAccount, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i2.DeleteAccountResponse>);

  @override
  _i14.ResponseFuture<_i17.HealthResponse> health(
    _i17.Empty? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#health, [request], {#options: options}),
            returnValue: _FakeResponseFuture_13<_i17.HealthResponse>(
              this,
              Invocation.method(#health, [request], {#options: options}),
            ),
          )
          as _i14.ResponseFuture<_i17.HealthResponse>);

  @override
  _i14.ClientCall<Q, R> $createCall<Q, R>(
    _i14.ClientMethod<Q, R>? method,
    _i16.Stream<Q>? requests, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #$createCall,
              [method, requests],
              {#options: options},
            ),
            returnValue: _FakeClientCall_14<Q, R>(
              this,
              Invocation.method(
                #$createCall,
                [method, requests],
                {#options: options},
              ),
            ),
          )
          as _i14.ClientCall<Q, R>);

  @override
  _i14.ResponseFuture<R> $createUnaryCall<Q, R>(
    _i14.ClientMethod<Q, R>? method,
    Q? request, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #$createUnaryCall,
              [method, request],
              {#options: options},
            ),
            returnValue: _FakeResponseFuture_13<R>(
              this,
              Invocation.method(
                #$createUnaryCall,
                [method, request],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseFuture<R>);

  @override
  _i14.ResponseStream<R> $createStreamingCall<Q, R>(
    _i14.ClientMethod<Q, R>? method,
    _i16.Stream<Q>? requests, {
    _i14.CallOptions? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #$createStreamingCall,
              [method, requests],
              {#options: options},
            ),
            returnValue: _FakeResponseStream_15<R>(
              this,
              Invocation.method(
                #$createStreamingCall,
                [method, requests],
                {#options: options},
              ),
            ),
          )
          as _i14.ResponseStream<R>);
}
