import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../../lib/data/datasources/realtime_remote_data_source.dart';
import '../../../lib/data/services/api/grpc_client_service.dart';
import '../../../lib/generated/grpc/realtime.pbgrpc.dart' as realtime_proto;
import '../../../lib/generated/grpc/realtime.pb.dart' as realtime_proto;

// Generate mocks
@GenerateMocks([
  GrpcClientService, 
  realtime_proto.RealtimeServiceClient,
])
import 'realtime_remote_data_source_test.mocks.dart';

void main() {
  group('RealtimeRemoteDataSource', () {
    late RealtimeRemoteDataSource dataSource;
    late MockGrpcClientService mockGrpcClientService;
    late MockRealtimeServiceClient mockRealtimeServiceClient;

    setUp(() {
      mockGrpcClientService = MockGrpcClientService();
      mockRealtimeServiceClient = MockRealtimeServiceClient();
      
      when(mockGrpcClientService.realtimeService).thenReturn(mockRealtimeServiceClient);
      when(mockGrpcClientService.createCallOptions()).thenReturn(null);
      
      dataSource = RealtimeRemoteDataSourceImpl(mockGrpcClientService);
    });

    group('messageStream', () {
      test('should throw UnsupportedError for hardcoded userId issue', () {
        // Arrange
        const chatId = 'test-chat-id';

        // Act & Assert
        expect(
          () => dataSource.messageStream(chatId),
          throwsA(isA<UnsupportedError>()),
        );
      });
    });

    group('messageStreamForUser', () {
      test('should create proper subscription request with userId', () {
        // Arrange
        const chatId = 'test-chat-id';
        const userId = 'test-user-id';
        
        final mockStream = Stream<realtime_proto.MessageUpdate>.empty();

        when(mockRealtimeServiceClient.subscribeToMessages(any, options: anyNamed('options')))
            .thenAnswer((_) => mockStream);

        // Act
        final result = dataSource.messageStreamForUser(chatId, userId);

        // Assert
        expect(result, isA<Stream<realtime_proto.MessageUpdate>>());
        
        // Verify that the correct request was made
        final captured = verify(mockRealtimeServiceClient.subscribeToMessages(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as realtime_proto.SubscribeToMessagesRequest;
        expect(request.userId, userId);
        expect(request.subscriptionType, 'bubble');
        expect(request.bubbleId, chatId);
      });
    });

    group('addReaction', () {
      test('should map reaction types to proper emojis', () async {
        // Arrange
        const messageId = 'test-message';
        const userId = 'test-user';
        
        final mockResponse = realtime_proto.ReactToMessageResponse();

        when(mockRealtimeServiceClient.reactToMessage(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Test cases for different reaction types
        final testCases = [
          {'type': 'like', 'expectedEmoji': '👍'},
          {'type': 'love', 'expectedEmoji': '❤️'},
          {'type': 'laugh', 'expectedEmoji': '😂'},
          {'type': 'wow', 'expectedEmoji': '😮'},
          {'type': 'sad', 'expectedEmoji': '😢'},
          {'type': 'angry', 'expectedEmoji': '😡'},
          {'type': 'fire', 'expectedEmoji': '🔥'},
          {'type': 'clap', 'expectedEmoji': '👏'},
          {'type': 'thinking', 'expectedEmoji': '🤔'},
          {'type': 'party', 'expectedEmoji': '🎉'},
          {'type': 'eyes', 'expectedEmoji': '👀'},
        ];

        for (final testCase in testCases) {
          // Act
          await dataSource.addReaction(messageId, testCase['type']!, userId);

          // Assert
          final captured = verify(mockRealtimeServiceClient.reactToMessage(
            captureAny, 
            options: anyNamed('options')
          )).captured;
          
          final request = captured.last as realtime_proto.ReactToMessageRequest;
          expect(request.reactionEmoji, testCase['expectedEmoji']);
          expect(request.reactionType, testCase['type']);
          expect(request.messageId, messageId);
          expect(request.userId, userId);
        }
      });

      test('should return unknown reaction types as-is', () async {
        // Arrange
        const messageId = 'test-message';
        const userId = 'test-user';
        const unknownReaction = 'custom_reaction';
        
        final mockResponse = realtime_proto.ReactToMessageResponse();

        when(mockRealtimeServiceClient.reactToMessage(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.addReaction(messageId, unknownReaction, userId);

        // Assert
        final captured = verify(mockRealtimeServiceClient.reactToMessage(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as realtime_proto.ReactToMessageRequest;
        expect(request.reactionEmoji, unknownReaction);
        expect(request.reactionType, unknownReaction);
      });

      test('should handle emoji input correctly', () async {
        // Arrange
        const messageId = 'test-message';
        const userId = 'test-user';
        const emojiReaction = '🚀';
        
        final mockResponse = realtime_proto.ReactToMessageResponse();

        when(mockRealtimeServiceClient.reactToMessage(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.addReaction(messageId, emojiReaction, userId);

        // Assert
        final captured = verify(mockRealtimeServiceClient.reactToMessage(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as realtime_proto.ReactToMessageRequest;
        expect(request.reactionEmoji, emojiReaction);
        expect(request.reactionType, emojiReaction);
      });

      test('should handle case insensitive reaction types', () async {
        // Arrange
        const messageId = 'test-message';
        const userId = 'test-user';
        
        final mockResponse = realtime_proto.ReactToMessageResponse();

        when(mockRealtimeServiceClient.reactToMessage(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Test uppercase reaction type
        await dataSource.addReaction(messageId, 'LIKE', userId);

        // Assert
        final captured = verify(mockRealtimeServiceClient.reactToMessage(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as realtime_proto.ReactToMessageRequest;
        expect(request.reactionEmoji, '👍'); // Should still map to thumbs up
        expect(request.reactionType, 'LIKE');
      });
    });
  });
}
