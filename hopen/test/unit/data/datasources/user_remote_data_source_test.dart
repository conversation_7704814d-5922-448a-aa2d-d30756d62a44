import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../../lib/data/datasources/user_remote_data_source.dart';
import '../../../lib/data/services/api/grpc_client_service.dart';
import '../../../lib/generated/grpc/user.pbgrpc.dart' as user_proto;
import '../../../lib/generated/grpc/user.pb.dart' as user_proto;

// Generate mocks
@GenerateMocks([
  GrpcClientService, 
  user_proto.UserServiceClient,
])
import 'user_remote_data_source_test.mocks.dart';

void main() {
  group('UserRemoteDataSource', () {
    late UserRemoteDataSource dataSource;
    late MockGrpcClientService mockGrpcClientService;
    late MockUserServiceClient mockUserServiceClient;

    setUp(() {
      mockGrpcClientService = MockGrpcClientService();
      mockUserServiceClient = MockUserServiceClient();
      
      when(mockGrpcClientService.userService).thenReturn(mockUserServiceClient);
      when(mockGrpcClientService.createCallOptions()).thenReturn(null);
      
      dataSource = UserRemoteDataSourceImpl(mockGrpcClientService);
    });

    group('updateUserLocation', () {
      test('should attempt address retrieval from coordinates', () async {
        // Arrange
        const userId = 'test-user';
        const latitude = 37.7749;
        const longitude = -122.4194; // San Francisco coordinates
        const accuracy = 10.0;
        
        final mockResponse = user_proto.UpdateUserLocationResponse();

        when(mockUserServiceClient.updateUserLocation(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.updateUserLocation(
          userId: userId,
          latitude: latitude,
          longitude: longitude,
          accuracy: accuracy,
        );

        // Assert
        verify(mockUserServiceClient.updateUserLocation(any, options: anyNamed('options'))).called(1);
        
        // Verify that the correct request was made
        final captured = verify(mockUserServiceClient.updateUserLocation(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as user_proto.UpdateUserLocationRequest;
        expect(request.userId, userId);
        expect(request.latitude, latitude);
        expect(request.longitude, longitude);
        expect(request.accuracy, accuracy);
      });

      test('should handle invalid coordinates gracefully', () async {
        // Arrange
        const userId = 'test-user';
        const invalidLatitude = 91.0; // Invalid latitude (> 90)
        const longitude = -122.4194;
        
        final mockResponse = user_proto.UpdateUserLocationResponse();

        when(mockUserServiceClient.updateUserLocation(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.updateUserLocation(
          userId: userId,
          latitude: invalidLatitude,
          longitude: longitude,
        );

        // Assert - should still call the service but address retrieval should be skipped
        verify(mockUserServiceClient.updateUserLocation(any, options: anyNamed('options'))).called(1);
      });

      test('should continue when address retrieval fails', () async {
        // Arrange
        const userId = 'test-user';
        const latitude = 37.7749;
        const longitude = -122.4194;
        
        final mockResponse = user_proto.UpdateUserLocationResponse();

        when(mockUserServiceClient.updateUserLocation(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act - should not throw even if address retrieval fails internally
        await dataSource.updateUserLocation(
          userId: userId,
          latitude: latitude,
          longitude: longitude,
        );

        // Assert
        verify(mockUserServiceClient.updateUserLocation(any, options: anyNamed('options'))).called(1);
      });

      test('should use provided address when available', () async {
        // Arrange
        const userId = 'test-user';
        const latitude = 37.7749;
        const longitude = -122.4194;
        const providedAddress = 'San Francisco, CA';
        
        final mockResponse = user_proto.UpdateUserLocationResponse();

        when(mockUserServiceClient.updateUserLocation(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.updateUserLocation(
          userId: userId,
          latitude: latitude,
          longitude: longitude,
          address: providedAddress,
        );

        // Assert - should not attempt address retrieval when address is provided
        verify(mockUserServiceClient.updateUserLocation(any, options: anyNamed('options'))).called(1);
      });
    });

    group('updateLocationPrivacy', () {
      test('should force private mode when shareLocation is false', () async {
        // Arrange
        const userId = 'test-user';
        const shareLocation = false;
        const privacyLevel = 'public'; // Should be overridden
        
        final mockResponse = user_proto.UpdateLocationPrivacyResponse();

        when(mockUserServiceClient.updateLocationPrivacy(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.updateLocationPrivacy(
          userId: userId,
          shareLocation: shareLocation,
          privacyLevel: privacyLevel,
        );

        // Assert
        verify(mockUserServiceClient.updateLocationPrivacy(any, options: anyNamed('options'))).called(1);
        
        // Verify that privacy level was forced to private
        final captured = verify(mockUserServiceClient.updateLocationPrivacy(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as user_proto.UpdateLocationPrivacyRequest;
        expect(request.privacyLevel, user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_PRIVATE);
      });

      test('should respect privacy level when shareLocation is true', () async {
        // Arrange
        const userId = 'test-user';
        const shareLocation = true;
        const privacyLevel = 'friends';
        
        final mockResponse = user_proto.UpdateLocationPrivacyResponse();

        when(mockUserServiceClient.updateLocationPrivacy(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.updateLocationPrivacy(
          userId: userId,
          shareLocation: shareLocation,
          privacyLevel: privacyLevel,
        );

        // Assert
        verify(mockUserServiceClient.updateLocationPrivacy(any, options: anyNamed('options'))).called(1);
        
        // Verify that privacy level was set correctly
        final captured = verify(mockUserServiceClient.updateLocationPrivacy(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as user_proto.UpdateLocationPrivacyRequest;
        expect(request.privacyLevel, user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_FRIENDS);
      });

      test('should handle public privacy level correctly', () async {
        // Arrange
        const userId = 'test-user';
        const shareLocation = true;
        const privacyLevel = 'public';
        
        final mockResponse = user_proto.UpdateLocationPrivacyResponse();

        when(mockUserServiceClient.updateLocationPrivacy(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.updateLocationPrivacy(
          userId: userId,
          shareLocation: shareLocation,
          privacyLevel: privacyLevel,
        );

        // Assert
        final captured = verify(mockUserServiceClient.updateLocationPrivacy(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as user_proto.UpdateLocationPrivacyRequest;
        expect(request.privacyLevel, user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_PUBLIC);
      });

      test('should handle invalid privacy level gracefully', () async {
        // Arrange
        const userId = 'test-user';
        const shareLocation = true;
        const invalidPrivacyLevel = 'invalid_level';
        
        final mockResponse = user_proto.UpdateLocationPrivacyResponse();

        when(mockUserServiceClient.updateLocationPrivacy(any, options: anyNamed('options')))
            .thenAnswer((_) async => mockResponse);

        // Act
        await dataSource.updateLocationPrivacy(
          userId: userId,
          shareLocation: shareLocation,
          privacyLevel: invalidPrivacyLevel,
        );

        // Assert - should still call the service with default private level
        verify(mockUserServiceClient.updateLocationPrivacy(any, options: anyNamed('options'))).called(1);
        
        final captured = verify(mockUserServiceClient.updateLocationPrivacy(
          captureAny, 
          options: anyNamed('options')
        )).captured;
        
        final request = captured[0] as user_proto.UpdateLocationPrivacyRequest;
        expect(request.privacyLevel, user_proto.LocationPrivacyLevel.LOCATION_PRIVACY_LEVEL_PRIVATE);
      });
    });
  });
}
