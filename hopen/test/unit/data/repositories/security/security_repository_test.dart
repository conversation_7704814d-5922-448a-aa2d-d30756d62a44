import 'package:flutter_test/flutter_test.dart';
import 'package:grpc/grpc.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:hopen/data/repositories/security/security_repository_impl.dart';
import 'package:hopen/data/services/api/grpc_client_service.dart';
import 'package:hopen/generated/grpc/auth.pbgrpc.dart';

import 'security_repository_test.mocks.dart';

@GenerateMocks([
  GrpcClientService,
  AuthServiceClient,
])
void main() {
  group('SecurityRepositoryImpl', () {
    late SecurityRepositoryImpl repository;
    late MockGrpcClientService mockGrpcClientService;
    late MockAuthServiceClient mockAuthServiceClient;

    setUp(() {
      mockGrpcClientService = MockGrpcClientService();
      mockAuthServiceClient = MockAuthServiceClient();
      
      when(mockGrpcClientService.authService).thenReturn(mockAuthServiceClient);
      
      repository = SecurityRepositoryImpl(
        grpcClientService: mockGrpcClientService,
      );
    });

    group('checkPasswordBreach', () {
      test('should return true when password is compromised', () async {
        // Arrange
        const testPassword = 'password123';
        final expectedResponse = CheckPasswordBreachResponse(
          isCompromised: true,
          breachCount: 42,
          message: 'Password found in 42 data breaches',
        );

        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenAnswer((_) => ResponseFuture.value(expectedResponse));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isTrue);
        
        // Verify the request was made with correct parameters
        final captured = verify(mockAuthServiceClient.checkPasswordBreach(captureAny))
            .captured.single as CheckPasswordBreachRequest;
        
        // Verify SHA-1 hash is sent (not plaintext password)
        expect(captured.sha1Hash, isNotEmpty);
        expect(captured.sha1Hash.length, equals(40)); // SHA-1 is 40 characters
        expect(captured.hashPrefix, isNotEmpty);
        expect(captured.hashPrefix.length, equals(5)); // K-anonymity prefix
        expect(captured.hashSuffix, isNotEmpty);
        expect(captured.hashSuffix.length, equals(35)); // Remaining hash
        
        // Verify no plaintext password is sent
        expect(captured.password, isEmpty);
      });

      test('should return false when password is not compromised', () async {
        // Arrange
        const testPassword = 'verySecurePassword123!@#';
        final expectedResponse = CheckPasswordBreachResponse(
          isCompromised: false,
          breachCount: 0,
          message: 'Password is secure',
        );

        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenAnswer((_) => ResponseFuture.value(expectedResponse));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isFalse);
      });

      test('should return false when gRPC call fails', () async {
        // Arrange
        const testPassword = 'testPassword';
        
        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenThrow(const GrpcError.unavailable('Service unavailable'));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isFalse); // Safe default when service fails
      });

      test('should handle network timeout gracefully', () async {
        // Arrange
        const testPassword = 'testPassword';
        
        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenThrow(const GrpcError.deadlineExceeded('Request timeout'));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isFalse); // Safe default when timeout occurs
      });

      test('should handle authentication errors gracefully', () async {
        // Arrange
        const testPassword = 'testPassword';
        
        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenThrow(const GrpcError.unauthenticated('Authentication failed'));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isFalse); // Safe default when auth fails
      });

      test('should generate consistent SHA-1 hash for same password', () async {
        // Arrange
        const testPassword = 'consistentPassword';
        final expectedResponse = CheckPasswordBreachResponse(
          isCompromised: false,
          breachCount: 0,
          message: 'Password is secure',
        );

        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenAnswer((_) => ResponseFuture.value(expectedResponse));

        // Act - Call twice with same password
        await repository.checkPasswordBreach(testPassword);
        await repository.checkPasswordBreach(testPassword);

        // Assert - Verify both calls generated same hash
        final capturedCalls = verify(mockAuthServiceClient.checkPasswordBreach(captureAny))
            .captured.cast<CheckPasswordBreachRequest>();
        
        expect(capturedCalls.length, equals(2));
        expect(capturedCalls[0].sha1Hash, equals(capturedCalls[1].sha1Hash));
        expect(capturedCalls[0].hashPrefix, equals(capturedCalls[1].hashPrefix));
        expect(capturedCalls[0].hashSuffix, equals(capturedCalls[1].hashSuffix));
      });

      test('should generate different hashes for different passwords', () async {
        // Arrange
        const password1 = 'password1';
        const password2 = 'password2';
        final expectedResponse = CheckPasswordBreachResponse(
          isCompromised: false,
          breachCount: 0,
          message: 'Password is secure',
        );

        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenAnswer((_) => ResponseFuture.value(expectedResponse));

        // Act
        await repository.checkPasswordBreach(password1);
        await repository.checkPasswordBreach(password2);

        // Assert
        final capturedCalls = verify(mockAuthServiceClient.checkPasswordBreach(captureAny))
            .captured.cast<CheckPasswordBreachRequest>();
        
        expect(capturedCalls.length, equals(2));
        expect(capturedCalls[0].sha1Hash, isNot(equals(capturedCalls[1].sha1Hash)));
        expect(capturedCalls[0].hashPrefix, isNot(equals(capturedCalls[1].hashPrefix)));
      });

      test('should handle empty password', () async {
        // Arrange
        const testPassword = '';
        final expectedResponse = CheckPasswordBreachResponse(
          isCompromised: false,
          breachCount: 0,
          message: 'Password is secure',
        );

        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenAnswer((_) => ResponseFuture.value(expectedResponse));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isFalse);
        
        // Verify request was still made (empty password has valid SHA-1)
        verify(mockAuthServiceClient.checkPasswordBreach(any)).called(1);
      });

      test('should handle very long password', () async {
        // Arrange
        final testPassword = 'a' * 1000; // Very long password
        final expectedResponse = CheckPasswordBreachResponse(
          isCompromised: false,
          breachCount: 0,
          message: 'Password is secure',
        );

        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenAnswer((_) => ResponseFuture.value(expectedResponse));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isFalse);
        
        // Verify SHA-1 hash is still 40 characters regardless of input length
        final captured = verify(mockAuthServiceClient.checkPasswordBreach(captureAny))
            .captured.single as CheckPasswordBreachRequest;
        expect(captured.sha1Hash.length, equals(40));
      });

      test('should handle special characters in password', () async {
        // Arrange
        const testPassword = r'!@#$%^&*()_+-=[]{}|;:,.<>?/~`';
        final expectedResponse = CheckPasswordBreachResponse(
          isCompromised: false,
          breachCount: 0,
          message: 'Password is secure',
        );

        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenAnswer((_) => ResponseFuture.value(expectedResponse));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isFalse);
        
        // Verify request was made successfully
        verify(mockAuthServiceClient.checkPasswordBreach(any)).called(1);
      });

      test('should handle unicode characters in password', () async {
        // Arrange
        const testPassword = 'пароль123🔒🛡️'; // Unicode password
        final expectedResponse = CheckPasswordBreachResponse(
          isCompromised: false,
          breachCount: 0,
          message: 'Password is secure',
        );

        when(mockAuthServiceClient.checkPasswordBreach(any))
            .thenAnswer((_) => ResponseFuture.value(expectedResponse));

        // Act
        final result = await repository.checkPasswordBreach(testPassword);

        // Assert
        expect(result, isFalse);
        
        // Verify SHA-1 hash is valid
        final captured = verify(mockAuthServiceClient.checkPasswordBreach(captureAny))
            .captured.single as CheckPasswordBreachRequest;
        expect(captured.sha1Hash.length, equals(40));
        expect(captured.sha1Hash, matches(RegExp(r'^[A-F0-9]{40}$')));
      });
    });
  });
}
