version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 10
    rebase-strategy: auto
    labels:
      - "dependencies"
      - "github-actions"

  - package-ecosystem: "gomod"
    directory: "/hopenbackend"
    schedule:
      interval: "daily"
    ignore:
      - dependency-name: "github.com/golangci/golangci-lint"
        versions: [">= v1.58.1"]  # Managed by workflow
    rebase-strategy: auto
    labels:
      - "dependencies"
      - "go"

  - package-ecosystem: "pub"
    directory: "/hopen"
    schedule:
      interval: "daily"
    rebase-strategy: auto
    labels:
      - "dependencies"
      - "flutter"

  - package-ecosystem: "docker"
    directory: "/hopenbackend"
    schedule:
      interval: "weekly"
    rebase-strategy: auto
    labels:
      - "dependencies"
      - "docker"
    target-branch: "develop"

  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
    rebase-strategy: auto
    labels:
      - "dependencies"
      - "docker"
    target-branch: "develop"
