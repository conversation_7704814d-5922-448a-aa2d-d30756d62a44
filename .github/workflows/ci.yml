# .github/workflows/ci.yml
name: Backend CI

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'hopenbackend/**'
      - '.github/workflows/ci.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'hopenbackend/**'
      - '.github/workflows/ci.yml'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/hopen-backend

jobs:
  # Job 1: Lint & Unit Test (Fast Feedback)
  lint-and-unit-test:
    name:  Lint & Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go-version: [1.22, 1.23]
    steps:
      - name: Common Setup
        uses: ./.github/actions/common-setup
        with:
          go-version: ${{ matrix.go-version }}

      - name: Run Go Linter
        uses: golangci/golangci-lint-action@v6
        with:
          version: v1.58.1
          working-directory: ./hopenbackend
          args: --timeout=5m

      - name: Run Unit Tests
        working-directory: ./hopenbackend
        run: go test -v -race -coverprofile=coverage.out ./...

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./hopenbackend/coverage.out
          flags: backend-unittests-${{ matrix.go-version }}

  # Job 2: Build, Push, and Scan Docker Image
  build-push-scan:
    name: Build, Push & Scan Image
    runs-on: ubuntu-latest
    needs: [lint-and-unit-test]
    # Grant permissions to write to the GitHub container registry
    permissions:
      contents: read
      packages: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            # Unique tag for the specific Git commit
            type=sha,prefix=,suffix=,format=short

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        id: build-and-push
        uses: docker/build-push-action@v5
        with:
          context: ./hopenbackend
          file: ./hopenbackend/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Scan pushed image for vulnerabilities with Trivy
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: '${{ steps.meta.outputs.tags }}'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH' # Fail the build on critical/high vulnerabilities
      
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Job 3: CodeQL Analysis (Security Scanning)
  codeql-analysis:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    needs: [lint-and-unit-test]
    permissions:
      actions: read
      contents: read
      security-events: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: go

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Job 4: Semantic Release (on main push)
  semantic-release:
    name: Semantic Release
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    needs: [build-push-scan, codeql-analysis]
    permissions:
      contents: write
      pull-requests: write
      issues: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # For commit history

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run Semantic Release
        id: semantic
        uses: cycjimmy/semantic-release-action@v4
        with:
          semantic_version: 23
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}  # If publishing to npm

      - name: Docker Build and Push for Release
        if: steps.semantic.outputs.new_release_published == 'true'
        run: |
          # Build and push with release tag
          docker build -t ghcr.io/${{ github.repository }}/hopen-backend:${{ steps.semantic.outputs.new_release_version }} ./hopenbackend
          docker push ghcr.io/${{ github.repository }}/hopen-backend:${{ steps.semantic.outputs.new_release_version }}
