name: Flutter CI/CD

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'hopen/**'
      - '.github/workflows/flutter-ci-cd.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'hopen/**'
      - '.github/workflows/flutter-ci-cd.yml'

env:
  FLUTTER_VERSION: '3.24.0'

jobs:
  # Test Job
  test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Install dependencies
      working-directory: ./hopen
      run: flutter pub get

    - name: Verify formatting
      working-directory: ./hopen
      run: dart format --output=none --set-exit-if-changed .

    - name: Analyze project source
      working-directory: ./hopen
      run: dart analyze --fatal-infos

    - name: Run unit & widget tests (including goldens)
      working-directory: ./hopen
      run: flutter test --coverage

    # -------------------------------------------------------------------
    # End-to-End / Integration Tests
    # -------------------------------------------------------------------
    - name: Run integration (end-to-end) tests
      working-directory: ./hopen
      run: |
        # These tests live under integration_test/ and execute with the
        # flutter test runner using the "flutter-tester" device, which
        # is available in headless CI environments.
        flutter test integration_test -d flutter-tester

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./hopen/coverage/lcov.info
        flags: flutter
        name: flutter-coverage

  # Build Android
  build-android:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'

    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Install dependencies
      working-directory: ./hopen
      run: flutter pub get

    - name: Build APK
      working-directory: ./hopen
      run: flutter build apk --release

    - name: Build App Bundle
      working-directory: ./hopen
      run: flutter build appbundle --release

    - name: Upload APK artifact
      uses: actions/upload-artifact@v4
      with:
        name: android-apk
        path: ./hopen/build/app/outputs/flutter-apk/app-release.apk

    - name: Upload App Bundle artifact
      uses: actions/upload-artifact@v4
      with:
        name: android-aab
        path: ./hopen/build/app/outputs/bundle/release/app-release.aab

  # Build iOS
  build-ios:
    needs: test
    runs-on: macos-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Install dependencies
      working-directory: ./hopen
      run: flutter pub get

    - name: Build iOS (no codesign)
      working-directory: ./hopen
      run: flutter build ios --release --no-codesign

    - name: Upload iOS build artifact
      uses: actions/upload-artifact@v4
      with:
        name: ios-build
        path: ./hopen/build/ios/iphoneos/Runner.app

  # Deploy Android to Play Store (Production only)
  deploy-android:
    needs: build-android
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download App Bundle
      uses: actions/download-artifact@v4
      with:
        name: android-aab
        path: ./

    - name: Deploy to Play Store
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
        packageName: com.hopenapp.hopen
        releaseFiles: app-release.aab
        track: production
        status: completed

  # Deploy iOS to App Store (Production only)
  deploy-ios:
    needs: build-ios
    runs-on: macos-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        cache: true

    - name: Install dependencies
      working-directory: ./hopen
      run: flutter pub get

    - name: Import Code-Signing Certificates
      uses: Apple-Actions/import-codesign-certs@v1
      with:
        p12-file-base64: ${{ secrets.IOS_CERTIFICATES }}
        p12-password: ${{ secrets.IOS_CERTIFICATES_PASSWORD }}

    - name: Install Provisioning Profile
      uses: Apple-Actions/download-provisioning-profiles@v1
      with:
        bundle-id: com.hopenapp.hopen
        issuer-id: ${{ secrets.APPSTORE_ISSUER_ID }}
        api-key-id: ${{ secrets.APPSTORE_KEY_ID }}
        api-private-key: ${{ secrets.APPSTORE_PRIVATE_KEY }}

    - name: Build and Archive iOS
      working-directory: ./hopen
      run: |
        flutter build ios --release
        xcodebuild -workspace ios/Runner.xcworkspace \
          -scheme Runner \
          -configuration Release \
          -destination generic/platform=iOS \
          -archivePath build/Runner.xcarchive \
          archive

    - name: Export IPA
      working-directory: ./hopen
      run: |
        xcodebuild -exportArchive \
          -archivePath build/Runner.xcarchive \
          -exportOptionsPlist ios/ExportOptions.plist \
          -exportPath build/

    - name: Upload to App Store
      uses: Apple-Actions/upload-testflight-build@v1
      with:
        app-path: ./hopen/build/Runner.ipa
        issuer-id: ${{ secrets.APPSTORE_ISSUER_ID }}
        api-key-id: ${{ secrets.APPSTORE_KEY_ID }}
        api-private-key: ${{ secrets.APPSTORE_PRIVATE_KEY }}

# ---------------------------------------------------------------------------
# Static vulnerability scanning (Docker, Go, Dart)
# ---------------------------------------------------------------------------

  security-scan:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # --- Trivy filesystem scan ---
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@0.19.0
        with:
          scan-type: 'fs'
          scan-ref: '.'
          severity: 'HIGH,CRITICAL'
          ignore-unfixed: true

      # --- Go vulnerability check ---
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21'

      - name: Run govulncheck
        working-directory: ./hopenbackend
        run: |
          go install golang.org/x/vuln/cmd/govulncheck@latest
          govulncheck ./...

      # --- Dart pub audit ---
      - name: Run dart pub audit
        working-directory: ./hopen
        run: |
          dart pub global activate dart_pub_audit 0.3.0
          ~/.pub-cache/bin/dart_pub_audit
