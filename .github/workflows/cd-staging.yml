# .github/workflows/cd-staging.yml
name: Deploy to Staging

on:
  push:
    branches: [ develop ]
    paths:
      - 'hopenbackend/**'
      - '.github/workflows/**' # Trigger on any workflow change

jobs:
  deploy-staging:
    name: 🚀 Deploy to Staging Environment
    # PERFECT: This job will ONLY be picked up by your self-hosted runner with the 'staging' label.
    runs-on: [self-hosted, staging]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: 🛑 Stop current application
        run: docker-compose -f /opt/hopen-backend/docker-compose.yml down

      - name: 🧹 Prune old Docker images
        run: docker system prune -af

      - name: 🚀 Start new application version
        # The image tag is the Git SHA of the commit.
        # We pass this into docker-compose to ensure it pulls the correct, immutable image.
        env:
          IMAGE_TAG: ${{ github.sha }}
        run: docker-compose -f /opt/hopen-backend/docker-compose.yml up -d --pull always

      - name: 🚦 Health Check with Retry
        run: |
          echo "Waiting for services to become healthy..."
          max_attempts=10
          attempt=1
          while [ $attempt -le $max_attempts ]; do
            if curl --fail --max-time 10 http://localhost:4000/health; then
              echo "Staging deployment is healthy!"
              break
            fi
            echo "Attempt $attempt/$max_attempts failed. Retrying in 5 seconds..."
            sleep 5
            attempt=$((attempt + 1))
          done
          if [ $attempt -gt $max_attempts ]; then
            echo "Health check failed after $max_attempts attempts"
            exit 1
          fi
