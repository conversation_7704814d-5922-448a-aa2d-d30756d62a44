# .github/workflows/cd-production.yml
name: Release to Production

on:
  workflow_dispatch:
    inputs:
      git-ref:
        description: 'Git SHA or tag to deploy (e.g., v1.2.0)'
        required: true

jobs:
  deploy-production:
    name: Release to Production Environment
    # PERFECT: This job will ONL<PERSON> be picked up by your self-hosted runner with the 'production' label.
    runs-on: [self-hosted, production]
    environment: production
    
    steps:
      - name: Checkout the specific Git ref for deployment
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.git-ref }}

      - name: 🔒 Set up environment for deployment
        id: setup
        run: |
          # The image tag is the Git SHA of the release commit.
          IMAGE_TAG=${{ github.sha }}
          echo "DEPLOY_TAG=${IMAGE_TAG}" >> $GITHUB_ENV
          
          # Define project names for Blue/Green deployment
          # We check which service is currently active by checking a file.
          if [[ -f /opt/hopen-backend/active_is_blue ]]; then
            echo "CURRENT_COLOR=blue" >> $GITHUB_ENV
            echo "NEXT_COLOR=green" >> $GITHUB_ENV
          else
            echo "CURRENT_COLOR=green" >> $GITHUB_ENV
            echo "NEXT_COLOR=blue" >> $GITHUB_ENV
          fi
          
      - name: 🚀 Deploy new version (Green/Blue)
        env:
          IMAGE_TAG: ${{ env.DEPLOY_TAG }}
          PROJECT_NAME: hopen_${{ env.NEXT_COLOR }}
        run: |
          echo "Deploying new version to ${{ env.NEXT_COLOR }} environment..."
          # We use docker-compose's project name (-p) to run a second, isolated copy of the app
          docker-compose -f /opt/hopen-backend/docker-compose.yml -p ${PROJECT_NAME} up -d --pull always
          
      - name: 🚦 Health Check with Retry
        run: |
          echo "Waiting for new version to become healthy..."
          max_attempts=10
          attempt=1
          while [ $attempt -le $max_attempts ]; do
            if curl --fail --max-time 10 http://localhost:${HEALTH_PORT}/health; then
              echo "New version is healthy!"
              break
            fi
            echo "Attempt $attempt/$max_attempts failed. Retrying in 5 seconds..."
            sleep 5
            attempt=$((attempt + 1))
          done
          if [ $attempt -gt $max_attempts ]; then
            echo "Health check failed after $max_attempts attempts"
            exit 1
          fi

      - name: 🔄 Switch traffic
        run: |
          echo "Switching reverse proxy to point to ${{ env.NEXT_COLOR }}..."
          # Switch traffic using our comprehensive traffic switch script
          sudo /opt/hopen-backend/scripts/switch-traffic.sh ${{ env.NEXT_COLOR }}
          
          # Update the state file
          rm -f /opt/hopen-backend/active_is_${{ env.CURRENT_COLOR }}
          touch /opt/hopen-backend/active_is_${{ env.NEXT_COLOR }}

      - name: 🧹 Shutdown old version
        env:
          PROJECT_NAME: hopen_${{ env.CURRENT_COLOR }}
        run: |
          echo "Shutting down old ${{ env.CURRENT_COLOR }} environment..."
          docker-compose -f /opt/hopen-backend/docker-compose.yml -p ${PROJECT_NAME} down
          docker system prune -af

      - name: 🎉 Notify deployment success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: '🚀 Hopen Backend release `${{ github.event.inputs.git-ref }}` deployed to production successfully!'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

      - name: 🚨 Notify deployment failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: '🚨 Hopen Backend release `${{ github.event.inputs.git-ref }}` failed to deploy to production. No traffic was switched.'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
