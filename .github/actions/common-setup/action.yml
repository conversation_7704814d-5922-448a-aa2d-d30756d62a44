name: 'Common Setup'
description: 'Reusable composite for checkout and caching in CI/CD'
inputs:
  go-version:
    description: 'Go version'
    required: false
    default: '1.23'
  node-version:
    description: 'Node version'
    required: false
    default: '20'
  cache-key-prefix:
    description: 'Cache key prefix'
    required: false
    default: 'setup'
outputs:
  cache-hit-go:
    description: 'Go cache hit'
    value: ${{ steps.cache-go.outputs.cache-hit }}
  cache-hit-node:
    description: 'Node cache hit'
    value: ${{ steps.cache-node.outputs.cache-hit }}

runs:
  using: 'composite'
  steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Go
      id: cache-go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ inputs.go-version }}
        cache: true
      shell: bash

    - name: Setup Node.js
      id: cache-node
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'npm'
      shell: bash

    - name: Cache Go modules
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Cache npm
      uses: actions/cache@v4
      with:
        path: ~/.npm
        key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-node-
